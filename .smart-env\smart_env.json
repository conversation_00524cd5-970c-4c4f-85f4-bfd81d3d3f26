{"is_obsidian_vault": true, "smart_blocks": {"embed_blocks": true, "min_chars": 200}, "smart_sources": {"single_file_data_path": ".smart-env/smart_sources.json", "min_chars": 200, "embed_model": {"adapter": "transformers", "transformers": {"legacy_transformers": false, "model_key": "TaylorAI/bge-micro-v2"}, "TaylorAI/bge-micro-v2": {}}, "smart_change": {"active": true}}, "file_exclusions": "Untitled", "folder_exclusions": "smart-chats", "smart_view_filter": {"render_markdown": true, "show_full_path": false, "expanded_view": false, "results_limit": "20"}, "excluded_headings": "", "language": "en", "new_user": true, "re_import_wait_time": 13, "smart_chat_threads": {"chat_model": {"adapter": "gemini", "ollama": {"model_key": "undefined"}, "anthropic": {"model_key": "claude-3-7-sonnet-latest"}, "gemini": {"model_key": "", "api_key": "http://45.154.1.22:7860"}}, "active_thread_key": "Untitled Chat 2025-07-18 20-01-05", "system_prompt": "", "detect_self_referential": true, "review_context": true, "stream": true, "language": "zh", "modifier_key_to_send": "shift", "use_tool_calls": true}, "smart_notices": {"muted": {"done_import": true, "embedding_complete": true}}, "version": "", "smart_threads": {"chat_model": {"adapter": "custom", "openai": {"model_key": ""}, "custom": {"api_adapter": "openai", "port": 0}}, "embed_model": {"model_key": "None"}, "language": "zh", "review_context": true, "lookup_limit": "10", "send_tool_output_in_user_message": false}}