/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin: [object Object]
*/

var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var main_exports={};__export(main_exports,{default:()=>BartenderPlugin});module.exports=__toCommonJS(main_exports);function around(obj,factories){let removers=Object.keys(factories).map(key=>around1(obj,key,factories[key]));return removers.length===1?removers[0]:function(){removers.forEach(r=>r())}}function around1(obj,method,createWrapper){let inherited=obj[method],hadOwn=obj.hasOwnProperty(method),original=hadOwn?inherited:function(){return Object.getPrototypeOf(obj)[method].apply(this,arguments)},current=createWrapper(original);return inherited&&Object.setPrototypeOf(current,inherited),Object.setPrototypeOf(wrapper,current),obj[method]=wrapper,remove;function wrapper(...args){return current===original&&obj[method]===wrapper&&remove(),current.apply(this,args)}function remove(){obj[method]===wrapper&&(hadOwn?obj[method]=original:delete obj[method]),current!==original&&(current=original,Object.setPrototypeOf(wrapper,inherited||Function))}}var import_obsidian6=require("obsidian");function ownKeys(object,enumerableOnly){var keys=Object.keys(object);if(Object.getOwnPropertySymbols){var symbols=Object.getOwnPropertySymbols(object);enumerableOnly&&(symbols=symbols.filter(function(sym){return Object.getOwnPropertyDescriptor(object,sym).enumerable})),keys.push.apply(keys,symbols)}return keys}function _objectSpread2(target){for(var i=1;i<arguments.length;i++){var source=arguments[i]!=null?arguments[i]:{};i%2?ownKeys(Object(source),!0).forEach(function(key){_defineProperty(target,key,source[key])}):Object.getOwnPropertyDescriptors?Object.defineProperties(target,Object.getOwnPropertyDescriptors(source)):ownKeys(Object(source)).forEach(function(key){Object.defineProperty(target,key,Object.getOwnPropertyDescriptor(source,key))})}return target}function _typeof(obj){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof=function(obj2){return typeof obj2}:_typeof=function(obj2){return obj2&&typeof Symbol=="function"&&obj2.constructor===Symbol&&obj2!==Symbol.prototype?"symbol":typeof obj2},_typeof(obj)}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}function _extends(){return _extends=Object.assign||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source)Object.prototype.hasOwnProperty.call(source,key)&&(target[key]=source[key])}return target},_extends.apply(this,arguments)}function _objectWithoutPropertiesLoose(source,excluded){if(source==null)return{};var target={},sourceKeys=Object.keys(source),key,i;for(i=0;i<sourceKeys.length;i++)key=sourceKeys[i],!(excluded.indexOf(key)>=0)&&(target[key]=source[key]);return target}function _objectWithoutProperties(source,excluded){if(source==null)return{};var target=_objectWithoutPropertiesLoose(source,excluded),key,i;if(Object.getOwnPropertySymbols){var sourceSymbolKeys=Object.getOwnPropertySymbols(source);for(i=0;i<sourceSymbolKeys.length;i++)key=sourceSymbolKeys[i],!(excluded.indexOf(key)>=0)&&Object.prototype.propertyIsEnumerable.call(source,key)&&(target[key]=source[key])}return target}function _toConsumableArray(arr){return _arrayWithoutHoles(arr)||_iterableToArray(arr)||_unsupportedIterableToArray(arr)||_nonIterableSpread()}function _arrayWithoutHoles(arr){if(Array.isArray(arr))return _arrayLikeToArray(arr)}function _iterableToArray(iter){if(typeof Symbol<"u"&&iter[Symbol.iterator]!=null||iter["@@iterator"]!=null)return Array.from(iter)}function _unsupportedIterableToArray(o,minLen){if(o){if(typeof o=="string")return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);if(n==="Object"&&o.constructor&&(n=o.constructor.name),n==="Map"||n==="Set")return Array.from(o);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(o,minLen)}}function _arrayLikeToArray(arr,len){(len==null||len>arr.length)&&(len=arr.length);for(var i=0,arr2=new Array(len);i<len;i++)arr2[i]=arr[i];return arr2}function _nonIterableSpread(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var version="1.15.3";function userAgent(pattern){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(pattern)}var IE11OrLess=userAgent(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Edge=userAgent(/Edge/i),FireFox=userAgent(/firefox/i),Safari=userAgent(/safari/i)&&!userAgent(/chrome/i)&&!userAgent(/android/i),IOS=userAgent(/iP(ad|od|hone)/i),ChromeForAndroid=userAgent(/chrome/i)&&userAgent(/android/i),captureMode={capture:!1,passive:!1};function on(el,event,fn){el.addEventListener(event,fn,!IE11OrLess&&captureMode)}function off(el,event,fn){el.removeEventListener(event,fn,!IE11OrLess&&captureMode)}function matches(el,selector){if(selector){if(selector[0]===">"&&(selector=selector.substring(1)),el)try{if(el.matches)return el.matches(selector);if(el.msMatchesSelector)return el.msMatchesSelector(selector);if(el.webkitMatchesSelector)return el.webkitMatchesSelector(selector)}catch{return!1}return!1}}function getParentOrHost(el){return el.host&&el!==document&&el.host.nodeType?el.host:el.parentNode}function closest(el,selector,ctx,includeCTX){if(el){ctx=ctx||document;do{if(selector!=null&&(selector[0]===">"?el.parentNode===ctx&&matches(el,selector):matches(el,selector))||includeCTX&&el===ctx)return el;if(el===ctx)break}while(el=getParentOrHost(el))}return null}var R_SPACE=/\s+/g;function toggleClass(el,name,state){if(el&&name)if(el.classList)el.classList[state?"add":"remove"](name);else{var className=(" "+el.className+" ").replace(R_SPACE," ").replace(" "+name+" "," ");el.className=(className+(state?" "+name:"")).replace(R_SPACE," ")}}function css(el,prop,val){var style=el&&el.style;if(style){if(val===void 0)return document.defaultView&&document.defaultView.getComputedStyle?val=document.defaultView.getComputedStyle(el,""):el.currentStyle&&(val=el.currentStyle),prop===void 0?val:val[prop];!(prop in style)&&prop.indexOf("webkit")===-1&&(prop="-webkit-"+prop),style[prop]=val+(typeof val=="string"?"":"px")}}function matrix(el,selfOnly){var appliedTransforms="";if(typeof el=="string")appliedTransforms=el;else do{var transform=css(el,"transform");transform&&transform!=="none"&&(appliedTransforms=transform+" "+appliedTransforms)}while(!selfOnly&&(el=el.parentNode));var matrixFn=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return matrixFn&&new matrixFn(appliedTransforms)}function find(ctx,tagName,iterator){if(ctx){var list=ctx.getElementsByTagName(tagName),i=0,n=list.length;if(iterator)for(;i<n;i++)iterator(list[i],i);return list}return[]}function getWindowScrollingElement(){var scrollingElement=document.scrollingElement;return scrollingElement||document.documentElement}function getRect(el,relativeToContainingBlock,relativeToNonStaticParent,undoScale,container){if(!(!el.getBoundingClientRect&&el!==window)){var elRect,top,left,bottom,right,height,width;if(el!==window&&el.parentNode&&el!==getWindowScrollingElement()?(elRect=el.getBoundingClientRect(),top=elRect.top,left=elRect.left,bottom=elRect.bottom,right=elRect.right,height=elRect.height,width=elRect.width):(top=0,left=0,bottom=window.innerHeight,right=window.innerWidth,height=window.innerHeight,width=window.innerWidth),(relativeToContainingBlock||relativeToNonStaticParent)&&el!==window&&(container=container||el.parentNode,!IE11OrLess))do if(container&&container.getBoundingClientRect&&(css(container,"transform")!=="none"||relativeToNonStaticParent&&css(container,"position")!=="static")){var containerRect=container.getBoundingClientRect();top-=containerRect.top+parseInt(css(container,"border-top-width")),left-=containerRect.left+parseInt(css(container,"border-left-width")),bottom=top+elRect.height,right=left+elRect.width;break}while(container=container.parentNode);if(undoScale&&el!==window){var elMatrix=matrix(container||el),scaleX=elMatrix&&elMatrix.a,scaleY=elMatrix&&elMatrix.d;elMatrix&&(top/=scaleY,left/=scaleX,width/=scaleX,height/=scaleY,bottom=top+height,right=left+width)}return{top,left,bottom,right,width,height}}}function isScrolledPast(el,elSide,parentSide){for(var parent=getParentAutoScrollElement(el,!0),elSideVal=getRect(el)[elSide];parent;){var parentSideVal=getRect(parent)[parentSide],visible=void 0;if(parentSide==="top"||parentSide==="left"?visible=elSideVal>=parentSideVal:visible=elSideVal<=parentSideVal,!visible)return parent;if(parent===getWindowScrollingElement())break;parent=getParentAutoScrollElement(parent,!1)}return!1}function getChild(el,childNum,options,includeDragEl){for(var currentChild=0,i=0,children=el.children;i<children.length;){if(children[i].style.display!=="none"&&children[i]!==Sortable.ghost&&(includeDragEl||children[i]!==Sortable.dragged)&&closest(children[i],options.draggable,el,!1)){if(currentChild===childNum)return children[i];currentChild++}i++}return null}function lastChild(el,selector){for(var last=el.lastElementChild;last&&(last===Sortable.ghost||css(last,"display")==="none"||selector&&!matches(last,selector));)last=last.previousElementSibling;return last||null}function index(el,selector){var index2=0;if(!el||!el.parentNode)return-1;for(;el=el.previousElementSibling;)el.nodeName.toUpperCase()!=="TEMPLATE"&&el!==Sortable.clone&&(!selector||matches(el,selector))&&index2++;return index2}function getRelativeScrollOffset(el){var offsetLeft=0,offsetTop=0,winScroller=getWindowScrollingElement();if(el)do{var elMatrix=matrix(el),scaleX=elMatrix.a,scaleY=elMatrix.d;offsetLeft+=el.scrollLeft*scaleX,offsetTop+=el.scrollTop*scaleY}while(el!==winScroller&&(el=el.parentNode));return[offsetLeft,offsetTop]}function indexOfObject(arr,obj){for(var i in arr)if(arr.hasOwnProperty(i)){for(var key in obj)if(obj.hasOwnProperty(key)&&obj[key]===arr[i][key])return Number(i)}return-1}function getParentAutoScrollElement(el,includeSelf){if(!el||!el.getBoundingClientRect)return getWindowScrollingElement();var elem=el,gotSelf=!1;do if(elem.clientWidth<elem.scrollWidth||elem.clientHeight<elem.scrollHeight){var elemCSS=css(elem);if(elem.clientWidth<elem.scrollWidth&&(elemCSS.overflowX=="auto"||elemCSS.overflowX=="scroll")||elem.clientHeight<elem.scrollHeight&&(elemCSS.overflowY=="auto"||elemCSS.overflowY=="scroll")){if(!elem.getBoundingClientRect||elem===document.body)return getWindowScrollingElement();if(gotSelf||includeSelf)return elem;gotSelf=!0}}while(elem=elem.parentNode);return getWindowScrollingElement()}function extend(dst,src){if(dst&&src)for(var key in src)src.hasOwnProperty(key)&&(dst[key]=src[key]);return dst}function isRectEqual(rect1,rect2){return Math.round(rect1.top)===Math.round(rect2.top)&&Math.round(rect1.left)===Math.round(rect2.left)&&Math.round(rect1.height)===Math.round(rect2.height)&&Math.round(rect1.width)===Math.round(rect2.width)}var _throttleTimeout;function throttle(callback,ms){return function(){if(!_throttleTimeout){var args=arguments,_this=this;args.length===1?callback.call(_this,args[0]):callback.apply(_this,args),_throttleTimeout=setTimeout(function(){_throttleTimeout=void 0},ms)}}}function cancelThrottle(){clearTimeout(_throttleTimeout),_throttleTimeout=void 0}function scrollBy(el,x,y){el.scrollLeft+=x,el.scrollTop+=y}function clone(el){var Polymer=window.Polymer,$=window.jQuery||window.Zepto;return Polymer&&Polymer.dom?Polymer.dom(el).cloneNode(!0):$?$(el).clone(!0)[0]:el.cloneNode(!0)}function setRect(el,rect){css(el,"position","absolute"),css(el,"top",rect.top),css(el,"left",rect.left),css(el,"width",rect.width),css(el,"height",rect.height)}function unsetRect(el){css(el,"position",""),css(el,"top",""),css(el,"left",""),css(el,"width",""),css(el,"height","")}function getChildContainingRectFromElement(container,options,ghostEl2){var rect={};return Array.from(container.children).forEach(function(child){var _rect$left,_rect$top,_rect$right,_rect$bottom;if(!(!closest(child,options.draggable,container,!1)||child.animated||child===ghostEl2)){var childRect=getRect(child);rect.left=Math.min((_rect$left=rect.left)!==null&&_rect$left!==void 0?_rect$left:1/0,childRect.left),rect.top=Math.min((_rect$top=rect.top)!==null&&_rect$top!==void 0?_rect$top:1/0,childRect.top),rect.right=Math.max((_rect$right=rect.right)!==null&&_rect$right!==void 0?_rect$right:-1/0,childRect.right),rect.bottom=Math.max((_rect$bottom=rect.bottom)!==null&&_rect$bottom!==void 0?_rect$bottom:-1/0,childRect.bottom)}}),rect.width=rect.right-rect.left,rect.height=rect.bottom-rect.top,rect.x=rect.left,rect.y=rect.top,rect}var expando="Sortable"+new Date().getTime();function AnimationStateManager(){var animationStates=[],animationCallbackId;return{captureAnimationState:function(){if(animationStates=[],!!this.options.animation){var children=[].slice.call(this.el.children);children.forEach(function(child){if(!(css(child,"display")==="none"||child===Sortable.ghost)){animationStates.push({target:child,rect:getRect(child)});var fromRect=_objectSpread2({},animationStates[animationStates.length-1].rect);if(child.thisAnimationDuration){var childMatrix=matrix(child,!0);childMatrix&&(fromRect.top-=childMatrix.f,fromRect.left-=childMatrix.e)}child.fromRect=fromRect}})}},addAnimationState:function(state){animationStates.push(state)},removeAnimationState:function(target){animationStates.splice(indexOfObject(animationStates,{target}),1)},animateAll:function(callback){var _this=this;if(!this.options.animation){clearTimeout(animationCallbackId),typeof callback=="function"&&callback();return}var animating=!1,animationTime=0;animationStates.forEach(function(state){var time=0,target=state.target,fromRect=target.fromRect,toRect=getRect(target),prevFromRect=target.prevFromRect,prevToRect=target.prevToRect,animatingRect=state.rect,targetMatrix=matrix(target,!0);targetMatrix&&(toRect.top-=targetMatrix.f,toRect.left-=targetMatrix.e),target.toRect=toRect,target.thisAnimationDuration&&isRectEqual(prevFromRect,toRect)&&!isRectEqual(fromRect,toRect)&&(animatingRect.top-toRect.top)/(animatingRect.left-toRect.left)===(fromRect.top-toRect.top)/(fromRect.left-toRect.left)&&(time=calculateRealTime(animatingRect,prevFromRect,prevToRect,_this.options)),isRectEqual(toRect,fromRect)||(target.prevFromRect=fromRect,target.prevToRect=toRect,time||(time=_this.options.animation),_this.animate(target,animatingRect,toRect,time)),time&&(animating=!0,animationTime=Math.max(animationTime,time),clearTimeout(target.animationResetTimer),target.animationResetTimer=setTimeout(function(){target.animationTime=0,target.prevFromRect=null,target.fromRect=null,target.prevToRect=null,target.thisAnimationDuration=null},time),target.thisAnimationDuration=time)}),clearTimeout(animationCallbackId),animating?animationCallbackId=setTimeout(function(){typeof callback=="function"&&callback()},animationTime):typeof callback=="function"&&callback(),animationStates=[]},animate:function(target,currentRect,toRect,duration){if(duration){css(target,"transition",""),css(target,"transform","");var elMatrix=matrix(this.el),scaleX=elMatrix&&elMatrix.a,scaleY=elMatrix&&elMatrix.d,translateX=(currentRect.left-toRect.left)/(scaleX||1),translateY=(currentRect.top-toRect.top)/(scaleY||1);target.animatingX=!!translateX,target.animatingY=!!translateY,css(target,"transform","translate3d("+translateX+"px,"+translateY+"px,0)"),this.forRepaintDummy=repaint(target),css(target,"transition","transform "+duration+"ms"+(this.options.easing?" "+this.options.easing:"")),css(target,"transform","translate3d(0,0,0)"),typeof target.animated=="number"&&clearTimeout(target.animated),target.animated=setTimeout(function(){css(target,"transition",""),css(target,"transform",""),target.animated=!1,target.animatingX=!1,target.animatingY=!1},duration)}}}}function repaint(target){return target.offsetWidth}function calculateRealTime(animatingRect,fromRect,toRect,options){return Math.sqrt(Math.pow(fromRect.top-animatingRect.top,2)+Math.pow(fromRect.left-animatingRect.left,2))/Math.sqrt(Math.pow(fromRect.top-toRect.top,2)+Math.pow(fromRect.left-toRect.left,2))*options.animation}var plugins=[],defaults={initializeByDefault:!0},PluginManager={mount:function(plugin){for(var option2 in defaults)defaults.hasOwnProperty(option2)&&!(option2 in plugin)&&(plugin[option2]=defaults[option2]);plugins.forEach(function(p){if(p.pluginName===plugin.pluginName)throw"Sortable: Cannot mount plugin ".concat(plugin.pluginName," more than once")}),plugins.push(plugin)},pluginEvent:function(eventName,sortable,evt){var _this=this;this.eventCanceled=!1,evt.cancel=function(){_this.eventCanceled=!0};var eventNameGlobal=eventName+"Global";plugins.forEach(function(plugin){sortable[plugin.pluginName]&&(sortable[plugin.pluginName][eventNameGlobal]&&sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({sortable},evt)),sortable.options[plugin.pluginName]&&sortable[plugin.pluginName][eventName]&&sortable[plugin.pluginName][eventName](_objectSpread2({sortable},evt)))})},initializePlugins:function(sortable,el,defaults2,options){plugins.forEach(function(plugin){var pluginName=plugin.pluginName;if(!(!sortable.options[pluginName]&&!plugin.initializeByDefault)){var initialized=new plugin(sortable,el,sortable.options);initialized.sortable=sortable,initialized.options=sortable.options,sortable[pluginName]=initialized,_extends(defaults2,initialized.defaults)}});for(var option2 in sortable.options)if(sortable.options.hasOwnProperty(option2)){var modified=this.modifyOption(sortable,option2,sortable.options[option2]);typeof modified<"u"&&(sortable.options[option2]=modified)}},getEventProperties:function(name,sortable){var eventProperties={};return plugins.forEach(function(plugin){typeof plugin.eventProperties=="function"&&_extends(eventProperties,plugin.eventProperties.call(sortable[plugin.pluginName],name))}),eventProperties},modifyOption:function(sortable,name,value){var modifiedValue;return plugins.forEach(function(plugin){sortable[plugin.pluginName]&&plugin.optionListeners&&typeof plugin.optionListeners[name]=="function"&&(modifiedValue=plugin.optionListeners[name].call(sortable[plugin.pluginName],value))}),modifiedValue}};function dispatchEvent(_ref){var sortable=_ref.sortable,rootEl2=_ref.rootEl,name=_ref.name,targetEl=_ref.targetEl,cloneEl2=_ref.cloneEl,toEl=_ref.toEl,fromEl=_ref.fromEl,oldIndex2=_ref.oldIndex,newIndex2=_ref.newIndex,oldDraggableIndex2=_ref.oldDraggableIndex,newDraggableIndex2=_ref.newDraggableIndex,originalEvent=_ref.originalEvent,putSortable2=_ref.putSortable,extraEventProperties=_ref.extraEventProperties;if(sortable=sortable||rootEl2&&rootEl2[expando],!!sortable){var evt,options=sortable.options,onName="on"+name.charAt(0).toUpperCase()+name.substr(1);window.CustomEvent&&!IE11OrLess&&!Edge?evt=new CustomEvent(name,{bubbles:!0,cancelable:!0}):(evt=document.createEvent("Event"),evt.initEvent(name,!0,!0)),evt.to=toEl||rootEl2,evt.from=fromEl||rootEl2,evt.item=targetEl||rootEl2,evt.clone=cloneEl2,evt.oldIndex=oldIndex2,evt.newIndex=newIndex2,evt.oldDraggableIndex=oldDraggableIndex2,evt.newDraggableIndex=newDraggableIndex2,evt.originalEvent=originalEvent,evt.pullMode=putSortable2?putSortable2.lastPutMode:void 0;var allEventProperties=_objectSpread2(_objectSpread2({},extraEventProperties),PluginManager.getEventProperties(name,sortable));for(var option2 in allEventProperties)evt[option2]=allEventProperties[option2];rootEl2&&rootEl2.dispatchEvent(evt),options[onName]&&options[onName].call(sortable,evt)}}var _excluded=["evt"],pluginEvent2=function(eventName,sortable){var _ref=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},originalEvent=_ref.evt,data=_objectWithoutProperties(_ref,_excluded);PluginManager.pluginEvent.bind(Sortable)(eventName,sortable,_objectSpread2({dragEl,parentEl,ghostEl,rootEl,nextEl,lastDownEl,cloneEl,cloneHidden,dragStarted:moved,putSortable,activeSortable:Sortable.active,originalEvent,oldIndex,oldDraggableIndex,newIndex,newDraggableIndex,hideGhostForTarget:_hideGhostForTarget,unhideGhostForTarget:_unhideGhostForTarget,cloneNowHidden:function(){cloneHidden=!0},cloneNowShown:function(){cloneHidden=!1},dispatchSortableEvent:function(name){_dispatchEvent({sortable,name,originalEvent})}},data))};function _dispatchEvent(info){dispatchEvent(_objectSpread2({putSortable,cloneEl,targetEl:dragEl,rootEl,oldIndex,oldDraggableIndex,newIndex,newDraggableIndex},info))}var dragEl,parentEl,ghostEl,rootEl,nextEl,lastDownEl,cloneEl,cloneHidden,oldIndex,newIndex,oldDraggableIndex,newDraggableIndex,activeGroup,putSortable,awaitingDragStarted=!1,ignoreNextClick=!1,sortables=[],tapEvt,touchEvt,lastDx,lastDy,tapDistanceLeft,tapDistanceTop,moved,lastTarget,lastDirection,pastFirstInvertThresh=!1,isCircumstantialInvert=!1,targetMoveDistance,ghostRelativeParent,ghostRelativeParentInitialScroll=[],_silent=!1,savedInputChecked=[],documentExists=typeof document<"u",PositionGhostAbsolutely=IOS,CSSFloatProperty=Edge||IE11OrLess?"cssFloat":"float",supportDraggable=documentExists&&!ChromeForAndroid&&!IOS&&"draggable"in document.createElement("div"),supportCssPointerEvents=function(){if(documentExists){if(IE11OrLess)return!1;var el=document.createElement("x");return el.style.cssText="pointer-events:auto",el.style.pointerEvents==="auto"}}(),_detectDirection=function(el,options){var elCSS=css(el),elWidth=parseInt(elCSS.width)-parseInt(elCSS.paddingLeft)-parseInt(elCSS.paddingRight)-parseInt(elCSS.borderLeftWidth)-parseInt(elCSS.borderRightWidth),child1=getChild(el,0,options),child2=getChild(el,1,options),firstChildCSS=child1&&css(child1),secondChildCSS=child2&&css(child2),firstChildWidth=firstChildCSS&&parseInt(firstChildCSS.marginLeft)+parseInt(firstChildCSS.marginRight)+getRect(child1).width,secondChildWidth=secondChildCSS&&parseInt(secondChildCSS.marginLeft)+parseInt(secondChildCSS.marginRight)+getRect(child2).width;if(elCSS.display==="flex")return elCSS.flexDirection==="column"||elCSS.flexDirection==="column-reverse"?"vertical":"horizontal";if(elCSS.display==="grid")return elCSS.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(child1&&firstChildCSS.float&&firstChildCSS.float!=="none"){var touchingSideChild2=firstChildCSS.float==="left"?"left":"right";return child2&&(secondChildCSS.clear==="both"||secondChildCSS.clear===touchingSideChild2)?"vertical":"horizontal"}return child1&&(firstChildCSS.display==="block"||firstChildCSS.display==="flex"||firstChildCSS.display==="table"||firstChildCSS.display==="grid"||firstChildWidth>=elWidth&&elCSS[CSSFloatProperty]==="none"||child2&&elCSS[CSSFloatProperty]==="none"&&firstChildWidth+secondChildWidth>elWidth)?"vertical":"horizontal"},_dragElInRowColumn=function(dragRect,targetRect,vertical){var dragElS1Opp=vertical?dragRect.left:dragRect.top,dragElS2Opp=vertical?dragRect.right:dragRect.bottom,dragElOppLength=vertical?dragRect.width:dragRect.height,targetS1Opp=vertical?targetRect.left:targetRect.top,targetS2Opp=vertical?targetRect.right:targetRect.bottom,targetOppLength=vertical?targetRect.width:targetRect.height;return dragElS1Opp===targetS1Opp||dragElS2Opp===targetS2Opp||dragElS1Opp+dragElOppLength/2===targetS1Opp+targetOppLength/2},_detectNearestEmptySortable=function(x,y){var ret;return sortables.some(function(sortable){var threshold=sortable[expando].options.emptyInsertThreshold;if(!(!threshold||lastChild(sortable))){var rect=getRect(sortable),insideHorizontally=x>=rect.left-threshold&&x<=rect.right+threshold,insideVertically=y>=rect.top-threshold&&y<=rect.bottom+threshold;if(insideHorizontally&&insideVertically)return ret=sortable}}),ret},_prepareGroup=function(options){function toFn(value,pull){return function(to,from,dragEl2,evt){var sameGroup=to.options.group.name&&from.options.group.name&&to.options.group.name===from.options.group.name;if(value==null&&(pull||sameGroup))return!0;if(value==null||value===!1)return!1;if(pull&&value==="clone")return value;if(typeof value=="function")return toFn(value(to,from,dragEl2,evt),pull)(to,from,dragEl2,evt);var otherGroup=(pull?to:from).options.group.name;return value===!0||typeof value=="string"&&value===otherGroup||value.join&&value.indexOf(otherGroup)>-1}}var group={},originalGroup=options.group;(!originalGroup||_typeof(originalGroup)!="object")&&(originalGroup={name:originalGroup}),group.name=originalGroup.name,group.checkPull=toFn(originalGroup.pull,!0),group.checkPut=toFn(originalGroup.put),group.revertClone=originalGroup.revertClone,options.group=group},_hideGhostForTarget=function(){!supportCssPointerEvents&&ghostEl&&css(ghostEl,"display","none")},_unhideGhostForTarget=function(){!supportCssPointerEvents&&ghostEl&&css(ghostEl,"display","")};documentExists&&!ChromeForAndroid&&document.addEventListener("click",function(evt){if(ignoreNextClick)return evt.preventDefault(),evt.stopPropagation&&evt.stopPropagation(),evt.stopImmediatePropagation&&evt.stopImmediatePropagation(),ignoreNextClick=!1,!1},!0);var nearestEmptyInsertDetectEvent=function(evt){if(dragEl){evt=evt.touches?evt.touches[0]:evt;var nearest=_detectNearestEmptySortable(evt.clientX,evt.clientY);if(nearest){var event={};for(var i in evt)evt.hasOwnProperty(i)&&(event[i]=evt[i]);event.target=event.rootEl=nearest,event.preventDefault=void 0,event.stopPropagation=void 0,nearest[expando]._onDragOver(event)}}},_checkOutsideTargetEl=function(evt){dragEl&&dragEl.parentNode[expando]._isOutsideThisEl(evt.target)};function Sortable(el,options){if(!(el&&el.nodeType&&el.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(el));this.el=el,this.options=options=_extends({},options),el[expando]=this;var defaults2={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(el.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return _detectDirection(el,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(dataTransfer,dragEl2){dataTransfer.setData("Text",dragEl2.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:Sortable.supportPointer!==!1&&"PointerEvent"in window&&!Safari,emptyInsertThreshold:5};PluginManager.initializePlugins(this,el,defaults2);for(var name in defaults2)!(name in options)&&(options[name]=defaults2[name]);_prepareGroup(options);for(var fn in this)fn.charAt(0)==="_"&&typeof this[fn]=="function"&&(this[fn]=this[fn].bind(this));this.nativeDraggable=options.forceFallback?!1:supportDraggable,this.nativeDraggable&&(this.options.touchStartThreshold=1),options.supportPointer?on(el,"pointerdown",this._onTapStart):(on(el,"mousedown",this._onTapStart),on(el,"touchstart",this._onTapStart)),this.nativeDraggable&&(on(el,"dragover",this),on(el,"dragenter",this)),sortables.push(this.el),options.store&&options.store.get&&this.sort(options.store.get(this)||[]),_extends(this,AnimationStateManager())}Sortable.prototype={constructor:Sortable,_isOutsideThisEl:function(target){!this.el.contains(target)&&target!==this.el&&(lastTarget=null)},_getDirection:function(evt,target){return typeof this.options.direction=="function"?this.options.direction.call(this,evt,target,dragEl):this.options.direction},_onTapStart:function(evt){if(evt.cancelable){var _this=this,el=this.el,options=this.options,preventOnFilter=options.preventOnFilter,type=evt.type,touch=evt.touches&&evt.touches[0]||evt.pointerType&&evt.pointerType==="touch"&&evt,target=(touch||evt).target,originalTarget=evt.target.shadowRoot&&(evt.path&&evt.path[0]||evt.composedPath&&evt.composedPath()[0])||target,filter=options.filter;if(_saveInputCheckedState(el),!dragEl&&!(/mousedown|pointerdown/.test(type)&&evt.button!==0||options.disabled)&&!originalTarget.isContentEditable&&!(!this.nativeDraggable&&Safari&&target&&target.tagName.toUpperCase()==="SELECT")&&(target=closest(target,options.draggable,el,!1),!(target&&target.animated)&&lastDownEl!==target)){if(oldIndex=index(target),oldDraggableIndex=index(target,options.draggable),typeof filter=="function"){if(filter.call(this,evt,target,this)){_dispatchEvent({sortable:_this,rootEl:originalTarget,name:"filter",targetEl:target,toEl:el,fromEl:el}),pluginEvent2("filter",_this,{evt}),preventOnFilter&&evt.cancelable&&evt.preventDefault();return}}else if(filter&&(filter=filter.split(",").some(function(criteria){if(criteria=closest(originalTarget,criteria.trim(),el,!1),criteria)return _dispatchEvent({sortable:_this,rootEl:criteria,name:"filter",targetEl:target,fromEl:el,toEl:el}),pluginEvent2("filter",_this,{evt}),!0}),filter)){preventOnFilter&&evt.cancelable&&evt.preventDefault();return}options.handle&&!closest(originalTarget,options.handle,el,!1)||this._prepareDragStart(evt,touch,target)}}},_prepareDragStart:function(evt,touch,target){var _this=this,el=_this.el,options=_this.options,ownerDocument=el.ownerDocument,dragStartFn;if(target&&!dragEl&&target.parentNode===el){var dragRect=getRect(target);if(rootEl=el,dragEl=target,parentEl=dragEl.parentNode,nextEl=dragEl.nextSibling,lastDownEl=target,activeGroup=options.group,Sortable.dragged=dragEl,tapEvt={target:dragEl,clientX:(touch||evt).clientX,clientY:(touch||evt).clientY},tapDistanceLeft=tapEvt.clientX-dragRect.left,tapDistanceTop=tapEvt.clientY-dragRect.top,this._lastX=(touch||evt).clientX,this._lastY=(touch||evt).clientY,dragEl.style["will-change"]="all",dragStartFn=function(){if(pluginEvent2("delayEnded",_this,{evt}),Sortable.eventCanceled){_this._onDrop();return}_this._disableDelayedDragEvents(),!FireFox&&_this.nativeDraggable&&(dragEl.draggable=!0),_this._triggerDragStart(evt,touch),_dispatchEvent({sortable:_this,name:"choose",originalEvent:evt}),toggleClass(dragEl,options.chosenClass,!0)},options.ignore.split(",").forEach(function(criteria){find(dragEl,criteria.trim(),_disableDraggable)}),on(ownerDocument,"dragover",nearestEmptyInsertDetectEvent),on(ownerDocument,"mousemove",nearestEmptyInsertDetectEvent),on(ownerDocument,"touchmove",nearestEmptyInsertDetectEvent),on(ownerDocument,"mouseup",_this._onDrop),on(ownerDocument,"touchend",_this._onDrop),on(ownerDocument,"touchcancel",_this._onDrop),FireFox&&this.nativeDraggable&&(this.options.touchStartThreshold=4,dragEl.draggable=!0),pluginEvent2("delayStart",this,{evt}),options.delay&&(!options.delayOnTouchOnly||touch)&&(!this.nativeDraggable||!(Edge||IE11OrLess))){if(Sortable.eventCanceled){this._onDrop();return}on(ownerDocument,"mouseup",_this._disableDelayedDrag),on(ownerDocument,"touchend",_this._disableDelayedDrag),on(ownerDocument,"touchcancel",_this._disableDelayedDrag),on(ownerDocument,"mousemove",_this._delayedDragTouchMoveHandler),on(ownerDocument,"touchmove",_this._delayedDragTouchMoveHandler),options.supportPointer&&on(ownerDocument,"pointermove",_this._delayedDragTouchMoveHandler),_this._dragStartTimer=setTimeout(dragStartFn,options.delay)}else dragStartFn()}},_delayedDragTouchMoveHandler:function(e){var touch=e.touches?e.touches[0]:e;Math.max(Math.abs(touch.clientX-this._lastX),Math.abs(touch.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){dragEl&&_disableDraggable(dragEl),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var ownerDocument=this.el.ownerDocument;off(ownerDocument,"mouseup",this._disableDelayedDrag),off(ownerDocument,"touchend",this._disableDelayedDrag),off(ownerDocument,"touchcancel",this._disableDelayedDrag),off(ownerDocument,"mousemove",this._delayedDragTouchMoveHandler),off(ownerDocument,"touchmove",this._delayedDragTouchMoveHandler),off(ownerDocument,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(evt,touch){touch=touch||evt.pointerType=="touch"&&evt,!this.nativeDraggable||touch?this.options.supportPointer?on(document,"pointermove",this._onTouchMove):touch?on(document,"touchmove",this._onTouchMove):on(document,"mousemove",this._onTouchMove):(on(dragEl,"dragend",this),on(rootEl,"dragstart",this._onDragStart));try{document.selection?_nextTick(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(fallback,evt){if(awaitingDragStarted=!1,rootEl&&dragEl){pluginEvent2("dragStarted",this,{evt}),this.nativeDraggable&&on(document,"dragover",_checkOutsideTargetEl);var options=this.options;!fallback&&toggleClass(dragEl,options.dragClass,!1),toggleClass(dragEl,options.ghostClass,!0),Sortable.active=this,fallback&&this._appendGhost(),_dispatchEvent({sortable:this,name:"start",originalEvent:evt})}else this._nulling()},_emulateDragOver:function(){if(touchEvt){this._lastX=touchEvt.clientX,this._lastY=touchEvt.clientY,_hideGhostForTarget();for(var target=document.elementFromPoint(touchEvt.clientX,touchEvt.clientY),parent=target;target&&target.shadowRoot&&(target=target.shadowRoot.elementFromPoint(touchEvt.clientX,touchEvt.clientY),target!==parent);)parent=target;if(dragEl.parentNode[expando]._isOutsideThisEl(target),parent)do{if(parent[expando]){var inserted=void 0;if(inserted=parent[expando]._onDragOver({clientX:touchEvt.clientX,clientY:touchEvt.clientY,target,rootEl:parent}),inserted&&!this.options.dragoverBubble)break}target=parent}while(parent=getParentOrHost(parent));_unhideGhostForTarget()}},_onTouchMove:function(evt){if(tapEvt){var options=this.options,fallbackTolerance=options.fallbackTolerance,fallbackOffset=options.fallbackOffset,touch=evt.touches?evt.touches[0]:evt,ghostMatrix=ghostEl&&matrix(ghostEl,!0),scaleX=ghostEl&&ghostMatrix&&ghostMatrix.a,scaleY=ghostEl&&ghostMatrix&&ghostMatrix.d,relativeScrollOffset=PositionGhostAbsolutely&&ghostRelativeParent&&getRelativeScrollOffset(ghostRelativeParent),dx=(touch.clientX-tapEvt.clientX+fallbackOffset.x)/(scaleX||1)+(relativeScrollOffset?relativeScrollOffset[0]-ghostRelativeParentInitialScroll[0]:0)/(scaleX||1),dy=(touch.clientY-tapEvt.clientY+fallbackOffset.y)/(scaleY||1)+(relativeScrollOffset?relativeScrollOffset[1]-ghostRelativeParentInitialScroll[1]:0)/(scaleY||1);if(!Sortable.active&&!awaitingDragStarted){if(fallbackTolerance&&Math.max(Math.abs(touch.clientX-this._lastX),Math.abs(touch.clientY-this._lastY))<fallbackTolerance)return;this._onDragStart(evt,!0)}if(ghostEl){ghostMatrix?(ghostMatrix.e+=dx-(lastDx||0),ghostMatrix.f+=dy-(lastDy||0)):ghostMatrix={a:1,b:0,c:0,d:1,e:dx,f:dy};var cssMatrix="matrix(".concat(ghostMatrix.a,",").concat(ghostMatrix.b,",").concat(ghostMatrix.c,",").concat(ghostMatrix.d,",").concat(ghostMatrix.e,",").concat(ghostMatrix.f,")");css(ghostEl,"webkitTransform",cssMatrix),css(ghostEl,"mozTransform",cssMatrix),css(ghostEl,"msTransform",cssMatrix),css(ghostEl,"transform",cssMatrix),lastDx=dx,lastDy=dy,touchEvt=touch}evt.cancelable&&evt.preventDefault()}},_appendGhost:function(){if(!ghostEl){var container=this.options.fallbackOnBody?document.body:rootEl,rect=getRect(dragEl,!0,PositionGhostAbsolutely,!0,container),options=this.options;if(PositionGhostAbsolutely){for(ghostRelativeParent=container;css(ghostRelativeParent,"position")==="static"&&css(ghostRelativeParent,"transform")==="none"&&ghostRelativeParent!==document;)ghostRelativeParent=ghostRelativeParent.parentNode;ghostRelativeParent!==document.body&&ghostRelativeParent!==document.documentElement?(ghostRelativeParent===document&&(ghostRelativeParent=getWindowScrollingElement()),rect.top+=ghostRelativeParent.scrollTop,rect.left+=ghostRelativeParent.scrollLeft):ghostRelativeParent=getWindowScrollingElement(),ghostRelativeParentInitialScroll=getRelativeScrollOffset(ghostRelativeParent)}ghostEl=dragEl.cloneNode(!0),toggleClass(ghostEl,options.ghostClass,!1),toggleClass(ghostEl,options.fallbackClass,!0),toggleClass(ghostEl,options.dragClass,!0),css(ghostEl,"transition",""),css(ghostEl,"transform",""),css(ghostEl,"box-sizing","border-box"),css(ghostEl,"margin",0),css(ghostEl,"top",rect.top),css(ghostEl,"left",rect.left),css(ghostEl,"width",rect.width),css(ghostEl,"height",rect.height),css(ghostEl,"opacity","0.8"),css(ghostEl,"position",PositionGhostAbsolutely?"absolute":"fixed"),css(ghostEl,"zIndex","100000"),css(ghostEl,"pointerEvents","none"),Sortable.ghost=ghostEl,container.appendChild(ghostEl),css(ghostEl,"transform-origin",tapDistanceLeft/parseInt(ghostEl.style.width)*100+"% "+tapDistanceTop/parseInt(ghostEl.style.height)*100+"%")}},_onDragStart:function(evt,fallback){var _this=this,dataTransfer=evt.dataTransfer,options=_this.options;if(pluginEvent2("dragStart",this,{evt}),Sortable.eventCanceled){this._onDrop();return}pluginEvent2("setupClone",this),Sortable.eventCanceled||(cloneEl=clone(dragEl),cloneEl.removeAttribute("id"),cloneEl.draggable=!1,cloneEl.style["will-change"]="",this._hideClone(),toggleClass(cloneEl,this.options.chosenClass,!1),Sortable.clone=cloneEl),_this.cloneId=_nextTick(function(){pluginEvent2("clone",_this),!Sortable.eventCanceled&&(_this.options.removeCloneOnHide||rootEl.insertBefore(cloneEl,dragEl),_this._hideClone(),_dispatchEvent({sortable:_this,name:"clone"}))}),!fallback&&toggleClass(dragEl,options.dragClass,!0),fallback?(ignoreNextClick=!0,_this._loopId=setInterval(_this._emulateDragOver,50)):(off(document,"mouseup",_this._onDrop),off(document,"touchend",_this._onDrop),off(document,"touchcancel",_this._onDrop),dataTransfer&&(dataTransfer.effectAllowed="move",options.setData&&options.setData.call(_this,dataTransfer,dragEl)),on(document,"drop",_this),css(dragEl,"transform","translateZ(0)")),awaitingDragStarted=!0,_this._dragStartId=_nextTick(_this._dragStarted.bind(_this,fallback,evt)),on(document,"selectstart",_this),moved=!0,Safari&&css(document.body,"user-select","none")},_onDragOver:function(evt){var el=this.el,target=evt.target,dragRect,targetRect,revert,options=this.options,group=options.group,activeSortable=Sortable.active,isOwner=activeGroup===group,canSort=options.sort,fromSortable=putSortable||activeSortable,vertical,_this=this,completedFired=!1;if(_silent)return;function dragOverEvent(name,extra){pluginEvent2(name,_this,_objectSpread2({evt,isOwner,axis:vertical?"vertical":"horizontal",revert,dragRect,targetRect,canSort,fromSortable,target,completed,onMove:function(target2,after2){return _onMove(rootEl,el,dragEl,dragRect,target2,getRect(target2),evt,after2)},changed},extra))}function capture(){dragOverEvent("dragOverAnimationCapture"),_this.captureAnimationState(),_this!==fromSortable&&fromSortable.captureAnimationState()}function completed(insertion){return dragOverEvent("dragOverCompleted",{insertion}),insertion&&(isOwner?activeSortable._hideClone():activeSortable._showClone(_this),_this!==fromSortable&&(toggleClass(dragEl,putSortable?putSortable.options.ghostClass:activeSortable.options.ghostClass,!1),toggleClass(dragEl,options.ghostClass,!0)),putSortable!==_this&&_this!==Sortable.active?putSortable=_this:_this===Sortable.active&&putSortable&&(putSortable=null),fromSortable===_this&&(_this._ignoreWhileAnimating=target),_this.animateAll(function(){dragOverEvent("dragOverAnimationComplete"),_this._ignoreWhileAnimating=null}),_this!==fromSortable&&(fromSortable.animateAll(),fromSortable._ignoreWhileAnimating=null)),(target===dragEl&&!dragEl.animated||target===el&&!target.animated)&&(lastTarget=null),!options.dragoverBubble&&!evt.rootEl&&target!==document&&(dragEl.parentNode[expando]._isOutsideThisEl(evt.target),!insertion&&nearestEmptyInsertDetectEvent(evt)),!options.dragoverBubble&&evt.stopPropagation&&evt.stopPropagation(),completedFired=!0}function changed(){newIndex=index(dragEl),newDraggableIndex=index(dragEl,options.draggable),_dispatchEvent({sortable:_this,name:"change",toEl:el,newIndex,newDraggableIndex,originalEvent:evt})}if(evt.preventDefault!==void 0&&evt.cancelable&&evt.preventDefault(),target=closest(target,options.draggable,el,!0),dragOverEvent("dragOver"),Sortable.eventCanceled)return completedFired;if(dragEl.contains(evt.target)||target.animated&&target.animatingX&&target.animatingY||_this._ignoreWhileAnimating===target)return completed(!1);if(ignoreNextClick=!1,activeSortable&&!options.disabled&&(isOwner?canSort||(revert=parentEl!==rootEl):putSortable===this||(this.lastPutMode=activeGroup.checkPull(this,activeSortable,dragEl,evt))&&group.checkPut(this,activeSortable,dragEl,evt))){if(vertical=this._getDirection(evt,target)==="vertical",dragRect=getRect(dragEl),dragOverEvent("dragOverValid"),Sortable.eventCanceled)return completedFired;if(revert)return parentEl=rootEl,capture(),this._hideClone(),dragOverEvent("revert"),Sortable.eventCanceled||(nextEl?rootEl.insertBefore(dragEl,nextEl):rootEl.appendChild(dragEl)),completed(!0);var elLastChild=lastChild(el,options.draggable);if(!elLastChild||_ghostIsLast(evt,vertical,this)&&!elLastChild.animated){if(elLastChild===dragEl)return completed(!1);if(elLastChild&&el===evt.target&&(target=elLastChild),target&&(targetRect=getRect(target)),_onMove(rootEl,el,dragEl,dragRect,target,targetRect,evt,!!target)!==!1)return capture(),elLastChild&&elLastChild.nextSibling?el.insertBefore(dragEl,elLastChild.nextSibling):el.appendChild(dragEl),parentEl=el,changed(),completed(!0)}else if(elLastChild&&_ghostIsFirst(evt,vertical,this)){var firstChild=getChild(el,0,options,!0);if(firstChild===dragEl)return completed(!1);if(target=firstChild,targetRect=getRect(target),_onMove(rootEl,el,dragEl,dragRect,target,targetRect,evt,!1)!==!1)return capture(),el.insertBefore(dragEl,firstChild),parentEl=el,changed(),completed(!0)}else if(target.parentNode===el){targetRect=getRect(target);var direction=0,targetBeforeFirstSwap,differentLevel=dragEl.parentNode!==el,differentRowCol=!_dragElInRowColumn(dragEl.animated&&dragEl.toRect||dragRect,target.animated&&target.toRect||targetRect,vertical),side1=vertical?"top":"left",scrolledPastTop=isScrolledPast(target,"top","top")||isScrolledPast(dragEl,"top","top"),scrollBefore=scrolledPastTop?scrolledPastTop.scrollTop:void 0;lastTarget!==target&&(targetBeforeFirstSwap=targetRect[side1],pastFirstInvertThresh=!1,isCircumstantialInvert=!differentRowCol&&options.invertSwap||differentLevel),direction=_getSwapDirection(evt,target,targetRect,vertical,differentRowCol?1:options.swapThreshold,options.invertedSwapThreshold==null?options.swapThreshold:options.invertedSwapThreshold,isCircumstantialInvert,lastTarget===target);var sibling;if(direction!==0){var dragIndex=index(dragEl);do dragIndex-=direction,sibling=parentEl.children[dragIndex];while(sibling&&(css(sibling,"display")==="none"||sibling===ghostEl))}if(direction===0||sibling===target)return completed(!1);lastTarget=target,lastDirection=direction;var nextSibling=target.nextElementSibling,after=!1;after=direction===1;var moveVector=_onMove(rootEl,el,dragEl,dragRect,target,targetRect,evt,after);if(moveVector!==!1)return(moveVector===1||moveVector===-1)&&(after=moveVector===1),_silent=!0,setTimeout(_unsilent,30),capture(),after&&!nextSibling?el.appendChild(dragEl):target.parentNode.insertBefore(dragEl,after?nextSibling:target),scrolledPastTop&&scrollBy(scrolledPastTop,0,scrollBefore-scrolledPastTop.scrollTop),parentEl=dragEl.parentNode,targetBeforeFirstSwap!==void 0&&!isCircumstantialInvert&&(targetMoveDistance=Math.abs(targetBeforeFirstSwap-getRect(target)[side1])),changed(),completed(!0)}if(el.contains(dragEl))return completed(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){off(document,"mousemove",this._onTouchMove),off(document,"touchmove",this._onTouchMove),off(document,"pointermove",this._onTouchMove),off(document,"dragover",nearestEmptyInsertDetectEvent),off(document,"mousemove",nearestEmptyInsertDetectEvent),off(document,"touchmove",nearestEmptyInsertDetectEvent)},_offUpEvents:function(){var ownerDocument=this.el.ownerDocument;off(ownerDocument,"mouseup",this._onDrop),off(ownerDocument,"touchend",this._onDrop),off(ownerDocument,"pointerup",this._onDrop),off(ownerDocument,"touchcancel",this._onDrop),off(document,"selectstart",this)},_onDrop:function(evt){var el=this.el,options=this.options;if(newIndex=index(dragEl),newDraggableIndex=index(dragEl,options.draggable),pluginEvent2("drop",this,{evt}),parentEl=dragEl&&dragEl.parentNode,newIndex=index(dragEl),newDraggableIndex=index(dragEl,options.draggable),Sortable.eventCanceled){this._nulling();return}awaitingDragStarted=!1,isCircumstantialInvert=!1,pastFirstInvertThresh=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),_cancelNextTick(this.cloneId),_cancelNextTick(this._dragStartId),this.nativeDraggable&&(off(document,"drop",this),off(el,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Safari&&css(document.body,"user-select",""),css(dragEl,"transform",""),evt&&(moved&&(evt.cancelable&&evt.preventDefault(),!options.dropBubble&&evt.stopPropagation()),ghostEl&&ghostEl.parentNode&&ghostEl.parentNode.removeChild(ghostEl),(rootEl===parentEl||putSortable&&putSortable.lastPutMode!=="clone")&&cloneEl&&cloneEl.parentNode&&cloneEl.parentNode.removeChild(cloneEl),dragEl&&(this.nativeDraggable&&off(dragEl,"dragend",this),_disableDraggable(dragEl),dragEl.style["will-change"]="",moved&&!awaitingDragStarted&&toggleClass(dragEl,putSortable?putSortable.options.ghostClass:this.options.ghostClass,!1),toggleClass(dragEl,this.options.chosenClass,!1),_dispatchEvent({sortable:this,name:"unchoose",toEl:parentEl,newIndex:null,newDraggableIndex:null,originalEvent:evt}),rootEl!==parentEl?(newIndex>=0&&(_dispatchEvent({rootEl:parentEl,name:"add",toEl:parentEl,fromEl:rootEl,originalEvent:evt}),_dispatchEvent({sortable:this,name:"remove",toEl:parentEl,originalEvent:evt}),_dispatchEvent({rootEl:parentEl,name:"sort",toEl:parentEl,fromEl:rootEl,originalEvent:evt}),_dispatchEvent({sortable:this,name:"sort",toEl:parentEl,originalEvent:evt})),putSortable&&putSortable.save()):newIndex!==oldIndex&&newIndex>=0&&(_dispatchEvent({sortable:this,name:"update",toEl:parentEl,originalEvent:evt}),_dispatchEvent({sortable:this,name:"sort",toEl:parentEl,originalEvent:evt})),Sortable.active&&((newIndex==null||newIndex===-1)&&(newIndex=oldIndex,newDraggableIndex=oldDraggableIndex),_dispatchEvent({sortable:this,name:"end",toEl:parentEl,originalEvent:evt}),this.save()))),this._nulling()},_nulling:function(){pluginEvent2("nulling",this),rootEl=dragEl=parentEl=ghostEl=nextEl=cloneEl=lastDownEl=cloneHidden=tapEvt=touchEvt=moved=newIndex=newDraggableIndex=oldIndex=oldDraggableIndex=lastTarget=lastDirection=putSortable=activeGroup=Sortable.dragged=Sortable.ghost=Sortable.clone=Sortable.active=null,savedInputChecked.forEach(function(el){el.checked=!0}),savedInputChecked.length=lastDx=lastDy=0},handleEvent:function(evt){switch(evt.type){case"drop":case"dragend":this._onDrop(evt);break;case"dragenter":case"dragover":dragEl&&(this._onDragOver(evt),_globalDragOver(evt));break;case"selectstart":evt.preventDefault();break}},toArray:function(){for(var order=[],el,children=this.el.children,i=0,n=children.length,options=this.options;i<n;i++)el=children[i],closest(el,options.draggable,this.el,!1)&&order.push(el.getAttribute(options.dataIdAttr)||_generateId(el));return order},sort:function(order,useAnimation){var items={},rootEl2=this.el;this.toArray().forEach(function(id,i){var el=rootEl2.children[i];closest(el,this.options.draggable,rootEl2,!1)&&(items[id]=el)},this),useAnimation&&this.captureAnimationState(),order.forEach(function(id){items[id]&&(rootEl2.removeChild(items[id]),rootEl2.appendChild(items[id]))}),useAnimation&&this.animateAll()},save:function(){var store=this.options.store;store&&store.set&&store.set(this)},closest:function(el,selector){return closest(el,selector||this.options.draggable,this.el,!1)},option:function(name,value){var options=this.options;if(value===void 0)return options[name];var modifiedValue=PluginManager.modifyOption(this,name,value);typeof modifiedValue<"u"?options[name]=modifiedValue:options[name]=value,name==="group"&&_prepareGroup(options)},destroy:function(){pluginEvent2("destroy",this);var el=this.el;el[expando]=null,off(el,"mousedown",this._onTapStart),off(el,"touchstart",this._onTapStart),off(el,"pointerdown",this._onTapStart),this.nativeDraggable&&(off(el,"dragover",this),off(el,"dragenter",this)),Array.prototype.forEach.call(el.querySelectorAll("[draggable]"),function(el2){el2.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),sortables.splice(sortables.indexOf(this.el),1),this.el=el=null},_hideClone:function(){if(!cloneHidden){if(pluginEvent2("hideClone",this),Sortable.eventCanceled)return;css(cloneEl,"display","none"),this.options.removeCloneOnHide&&cloneEl.parentNode&&cloneEl.parentNode.removeChild(cloneEl),cloneHidden=!0}},_showClone:function(putSortable2){if(putSortable2.lastPutMode!=="clone"){this._hideClone();return}if(cloneHidden){if(pluginEvent2("showClone",this),Sortable.eventCanceled)return;dragEl.parentNode==rootEl&&!this.options.group.revertClone?rootEl.insertBefore(cloneEl,dragEl):nextEl?rootEl.insertBefore(cloneEl,nextEl):rootEl.appendChild(cloneEl),this.options.group.revertClone&&this.animate(dragEl,cloneEl),css(cloneEl,"display",""),cloneHidden=!1}}};function _globalDragOver(evt){evt.dataTransfer&&(evt.dataTransfer.dropEffect="move"),evt.cancelable&&evt.preventDefault()}function _onMove(fromEl,toEl,dragEl2,dragRect,targetEl,targetRect,originalEvent,willInsertAfter){var evt,sortable=fromEl[expando],onMoveFn=sortable.options.onMove,retVal;return window.CustomEvent&&!IE11OrLess&&!Edge?evt=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(evt=document.createEvent("Event"),evt.initEvent("move",!0,!0)),evt.to=toEl,evt.from=fromEl,evt.dragged=dragEl2,evt.draggedRect=dragRect,evt.related=targetEl||toEl,evt.relatedRect=targetRect||getRect(toEl),evt.willInsertAfter=willInsertAfter,evt.originalEvent=originalEvent,fromEl.dispatchEvent(evt),onMoveFn&&(retVal=onMoveFn.call(sortable,evt,originalEvent)),retVal}function _disableDraggable(el){el.draggable=!1}function _unsilent(){_silent=!1}function _ghostIsFirst(evt,vertical,sortable){var firstElRect=getRect(getChild(sortable.el,0,sortable.options,!0)),childContainingRect=getChildContainingRectFromElement(sortable.el,sortable.options,ghostEl),spacer=10;return vertical?evt.clientX<childContainingRect.left-spacer||evt.clientY<firstElRect.top&&evt.clientX<firstElRect.right:evt.clientY<childContainingRect.top-spacer||evt.clientY<firstElRect.bottom&&evt.clientX<firstElRect.left}function _ghostIsLast(evt,vertical,sortable){var lastElRect=getRect(lastChild(sortable.el,sortable.options.draggable)),childContainingRect=getChildContainingRectFromElement(sortable.el,sortable.options,ghostEl),spacer=10;return vertical?evt.clientX>childContainingRect.right+spacer||evt.clientY>lastElRect.bottom&&evt.clientX>lastElRect.left:evt.clientY>childContainingRect.bottom+spacer||evt.clientX>lastElRect.right&&evt.clientY>lastElRect.top}function _getSwapDirection(evt,target,targetRect,vertical,swapThreshold,invertedSwapThreshold,invertSwap,isLastTarget){var mouseOnAxis=vertical?evt.clientY:evt.clientX,targetLength=vertical?targetRect.height:targetRect.width,targetS1=vertical?targetRect.top:targetRect.left,targetS2=vertical?targetRect.bottom:targetRect.right,invert=!1;if(!invertSwap){if(isLastTarget&&targetMoveDistance<targetLength*swapThreshold){if(!pastFirstInvertThresh&&(lastDirection===1?mouseOnAxis>targetS1+targetLength*invertedSwapThreshold/2:mouseOnAxis<targetS2-targetLength*invertedSwapThreshold/2)&&(pastFirstInvertThresh=!0),pastFirstInvertThresh)invert=!0;else if(lastDirection===1?mouseOnAxis<targetS1+targetMoveDistance:mouseOnAxis>targetS2-targetMoveDistance)return-lastDirection}else if(mouseOnAxis>targetS1+targetLength*(1-swapThreshold)/2&&mouseOnAxis<targetS2-targetLength*(1-swapThreshold)/2)return _getInsertDirection(target)}return invert=invert||invertSwap,invert&&(mouseOnAxis<targetS1+targetLength*invertedSwapThreshold/2||mouseOnAxis>targetS2-targetLength*invertedSwapThreshold/2)?mouseOnAxis>targetS1+targetLength/2?1:-1:0}function _getInsertDirection(target){return index(dragEl)<index(target)?1:-1}function _generateId(el){for(var str=el.tagName+el.className+el.src+el.href+el.textContent,i=str.length,sum=0;i--;)sum+=str.charCodeAt(i);return sum.toString(36)}function _saveInputCheckedState(root){savedInputChecked.length=0;for(var inputs=root.getElementsByTagName("input"),idx=inputs.length;idx--;){var el=inputs[idx];el.checked&&savedInputChecked.push(el)}}function _nextTick(fn){return setTimeout(fn,0)}function _cancelNextTick(id){return clearTimeout(id)}documentExists&&on(document,"touchmove",function(evt){(Sortable.active||awaitingDragStarted)&&evt.cancelable&&evt.preventDefault()});Sortable.utils={on,off,css,find,is:function(el,selector){return!!closest(el,selector,el,!1)},extend,throttle,closest,toggleClass,clone,index,nextTick:_nextTick,cancelNextTick:_cancelNextTick,detectDirection:_detectDirection,getChild,expando};Sortable.get=function(element){return element[expando]};Sortable.mount=function(){for(var _len=arguments.length,plugins2=new Array(_len),_key=0;_key<_len;_key++)plugins2[_key]=arguments[_key];plugins2[0].constructor===Array&&(plugins2=plugins2[0]),plugins2.forEach(function(plugin){if(!plugin.prototype||!plugin.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(plugin));plugin.utils&&(Sortable.utils=_objectSpread2(_objectSpread2({},Sortable.utils),plugin.utils)),PluginManager.mount(plugin)})};Sortable.create=function(el,options){return new Sortable(el,options)};Sortable.version=version;var autoScrolls=[],scrollEl,scrollRootEl,scrolling=!1,lastAutoScrollX,lastAutoScrollY,touchEvt$1,pointerElemChangedInterval;function AutoScrollPlugin(){function AutoScroll(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var fn in this)fn.charAt(0)==="_"&&typeof this[fn]=="function"&&(this[fn]=this[fn].bind(this))}return AutoScroll.prototype={dragStarted:function(_ref){var originalEvent=_ref.originalEvent;this.sortable.nativeDraggable?on(document,"dragover",this._handleAutoScroll):this.options.supportPointer?on(document,"pointermove",this._handleFallbackAutoScroll):originalEvent.touches?on(document,"touchmove",this._handleFallbackAutoScroll):on(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(_ref2){var originalEvent=_ref2.originalEvent;!this.options.dragOverBubble&&!originalEvent.rootEl&&this._handleAutoScroll(originalEvent)},drop:function(){this.sortable.nativeDraggable?off(document,"dragover",this._handleAutoScroll):(off(document,"pointermove",this._handleFallbackAutoScroll),off(document,"touchmove",this._handleFallbackAutoScroll),off(document,"mousemove",this._handleFallbackAutoScroll)),clearPointerElemChangedInterval(),clearAutoScrolls(),cancelThrottle()},nulling:function(){touchEvt$1=scrollRootEl=scrollEl=scrolling=pointerElemChangedInterval=lastAutoScrollX=lastAutoScrollY=null,autoScrolls.length=0},_handleFallbackAutoScroll:function(evt){this._handleAutoScroll(evt,!0)},_handleAutoScroll:function(evt,fallback){var _this=this,x=(evt.touches?evt.touches[0]:evt).clientX,y=(evt.touches?evt.touches[0]:evt).clientY,elem=document.elementFromPoint(x,y);if(touchEvt$1=evt,fallback||this.options.forceAutoScrollFallback||Edge||IE11OrLess||Safari){autoScroll(evt,this.options,elem,fallback);var ogElemScroller=getParentAutoScrollElement(elem,!0);scrolling&&(!pointerElemChangedInterval||x!==lastAutoScrollX||y!==lastAutoScrollY)&&(pointerElemChangedInterval&&clearPointerElemChangedInterval(),pointerElemChangedInterval=setInterval(function(){var newElem=getParentAutoScrollElement(document.elementFromPoint(x,y),!0);newElem!==ogElemScroller&&(ogElemScroller=newElem,clearAutoScrolls()),autoScroll(evt,_this.options,newElem,fallback)},10),lastAutoScrollX=x,lastAutoScrollY=y)}else{if(!this.options.bubbleScroll||getParentAutoScrollElement(elem,!0)===getWindowScrollingElement()){clearAutoScrolls();return}autoScroll(evt,this.options,getParentAutoScrollElement(elem,!1),!1)}}},_extends(AutoScroll,{pluginName:"scroll",initializeByDefault:!0})}function clearAutoScrolls(){autoScrolls.forEach(function(autoScroll2){clearInterval(autoScroll2.pid)}),autoScrolls=[]}function clearPointerElemChangedInterval(){clearInterval(pointerElemChangedInterval)}var autoScroll=throttle(function(evt,options,rootEl2,isFallback){if(options.scroll){var x=(evt.touches?evt.touches[0]:evt).clientX,y=(evt.touches?evt.touches[0]:evt).clientY,sens=options.scrollSensitivity,speed=options.scrollSpeed,winScroller=getWindowScrollingElement(),scrollThisInstance=!1,scrollCustomFn;scrollRootEl!==rootEl2&&(scrollRootEl=rootEl2,clearAutoScrolls(),scrollEl=options.scroll,scrollCustomFn=options.scrollFn,scrollEl===!0&&(scrollEl=getParentAutoScrollElement(rootEl2,!0)));var layersOut=0,currentParent=scrollEl;do{var el=currentParent,rect=getRect(el),top=rect.top,bottom=rect.bottom,left=rect.left,right=rect.right,width=rect.width,height=rect.height,canScrollX=void 0,canScrollY=void 0,scrollWidth=el.scrollWidth,scrollHeight=el.scrollHeight,elCSS=css(el),scrollPosX=el.scrollLeft,scrollPosY=el.scrollTop;el===winScroller?(canScrollX=width<scrollWidth&&(elCSS.overflowX==="auto"||elCSS.overflowX==="scroll"||elCSS.overflowX==="visible"),canScrollY=height<scrollHeight&&(elCSS.overflowY==="auto"||elCSS.overflowY==="scroll"||elCSS.overflowY==="visible")):(canScrollX=width<scrollWidth&&(elCSS.overflowX==="auto"||elCSS.overflowX==="scroll"),canScrollY=height<scrollHeight&&(elCSS.overflowY==="auto"||elCSS.overflowY==="scroll"));var vx=canScrollX&&(Math.abs(right-x)<=sens&&scrollPosX+width<scrollWidth)-(Math.abs(left-x)<=sens&&!!scrollPosX),vy=canScrollY&&(Math.abs(bottom-y)<=sens&&scrollPosY+height<scrollHeight)-(Math.abs(top-y)<=sens&&!!scrollPosY);if(!autoScrolls[layersOut])for(var i=0;i<=layersOut;i++)autoScrolls[i]||(autoScrolls[i]={});(autoScrolls[layersOut].vx!=vx||autoScrolls[layersOut].vy!=vy||autoScrolls[layersOut].el!==el)&&(autoScrolls[layersOut].el=el,autoScrolls[layersOut].vx=vx,autoScrolls[layersOut].vy=vy,clearInterval(autoScrolls[layersOut].pid),(vx!=0||vy!=0)&&(scrollThisInstance=!0,autoScrolls[layersOut].pid=setInterval(function(){isFallback&&this.layer===0&&Sortable.active._onTouchMove(touchEvt$1);var scrollOffsetY=autoScrolls[this.layer].vy?autoScrolls[this.layer].vy*speed:0,scrollOffsetX=autoScrolls[this.layer].vx?autoScrolls[this.layer].vx*speed:0;typeof scrollCustomFn=="function"&&scrollCustomFn.call(Sortable.dragged.parentNode[expando],scrollOffsetX,scrollOffsetY,evt,touchEvt$1,autoScrolls[this.layer].el)!=="continue"||scrollBy(autoScrolls[this.layer].el,scrollOffsetX,scrollOffsetY)}.bind({layer:layersOut}),24))),layersOut++}while(options.bubbleScroll&&currentParent!==winScroller&&(currentParent=getParentAutoScrollElement(currentParent,!1)));scrolling=scrollThisInstance}},30),drop=function(_ref){var originalEvent=_ref.originalEvent,putSortable2=_ref.putSortable,dragEl2=_ref.dragEl,activeSortable=_ref.activeSortable,dispatchSortableEvent=_ref.dispatchSortableEvent,hideGhostForTarget=_ref.hideGhostForTarget,unhideGhostForTarget=_ref.unhideGhostForTarget;if(originalEvent){var toSortable=putSortable2||activeSortable;hideGhostForTarget();var touch=originalEvent.changedTouches&&originalEvent.changedTouches.length?originalEvent.changedTouches[0]:originalEvent,target=document.elementFromPoint(touch.clientX,touch.clientY);unhideGhostForTarget(),toSortable&&!toSortable.el.contains(target)&&(dispatchSortableEvent("spill"),this.onSpill({dragEl:dragEl2,putSortable:putSortable2}))}};function Revert(){}Revert.prototype={startIndex:null,dragStart:function(_ref2){var oldDraggableIndex2=_ref2.oldDraggableIndex;this.startIndex=oldDraggableIndex2},onSpill:function(_ref3){var dragEl2=_ref3.dragEl,putSortable2=_ref3.putSortable;this.sortable.captureAnimationState(),putSortable2&&putSortable2.captureAnimationState();var nextSibling=getChild(this.sortable.el,this.startIndex,this.options);nextSibling?this.sortable.el.insertBefore(dragEl2,nextSibling):this.sortable.el.appendChild(dragEl2),this.sortable.animateAll(),putSortable2&&putSortable2.animateAll()},drop};_extends(Revert,{pluginName:"revertOnSpill"});function Remove(){}Remove.prototype={onSpill:function(_ref4){var dragEl2=_ref4.dragEl,putSortable2=_ref4.putSortable,parentSortable=putSortable2||this.sortable;parentSortable.captureAnimationState(),dragEl2.parentNode&&dragEl2.parentNode.removeChild(dragEl2),parentSortable.animateAll()},drop};_extends(Remove,{pluginName:"removeOnSpill"});var multiDragElements=[],multiDragClones=[],lastMultiDragSelect,multiDragSortable,initialFolding=!1,folding=!1,dragStarted=!1,dragEl$1,clonesFromRect,clonesHidden;function MultiDragPlugin(){function MultiDrag(sortable){for(var fn in this)fn.charAt(0)==="_"&&typeof this[fn]=="function"&&(this[fn]=this[fn].bind(this));sortable.options.avoidImplicitDeselect||(sortable.options.supportPointer?on(document,"pointerup",this._deselectMultiDrag):(on(document,"mouseup",this._deselectMultiDrag),on(document,"touchend",this._deselectMultiDrag))),on(document,"keydown",this._checkKeyDown),on(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,avoidImplicitDeselect:!1,setData:function(dataTransfer,dragEl2){var data="";multiDragElements.length&&multiDragSortable===sortable?multiDragElements.forEach(function(multiDragElement,i){data+=(i?", ":"")+multiDragElement.textContent}):data=dragEl2.textContent,dataTransfer.setData("Text",data)}}}return MultiDrag.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(_ref){var dragged=_ref.dragEl;dragEl$1=dragged},delayEnded:function(){this.isMultiDrag=~multiDragElements.indexOf(dragEl$1)},setupClone:function(_ref2){var sortable=_ref2.sortable,cancel=_ref2.cancel;if(this.isMultiDrag){for(var i=0;i<multiDragElements.length;i++)multiDragClones.push(clone(multiDragElements[i])),multiDragClones[i].sortableIndex=multiDragElements[i].sortableIndex,multiDragClones[i].draggable=!1,multiDragClones[i].style["will-change"]="",toggleClass(multiDragClones[i],this.options.selectedClass,!1),multiDragElements[i]===dragEl$1&&toggleClass(multiDragClones[i],this.options.chosenClass,!1);sortable._hideClone(),cancel()}},clone:function(_ref3){var sortable=_ref3.sortable,rootEl2=_ref3.rootEl,dispatchSortableEvent=_ref3.dispatchSortableEvent,cancel=_ref3.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||multiDragElements.length&&multiDragSortable===sortable&&(insertMultiDragClones(!0,rootEl2),dispatchSortableEvent("clone"),cancel()))},showClone:function(_ref4){var cloneNowShown=_ref4.cloneNowShown,rootEl2=_ref4.rootEl,cancel=_ref4.cancel;this.isMultiDrag&&(insertMultiDragClones(!1,rootEl2),multiDragClones.forEach(function(clone2){css(clone2,"display","")}),cloneNowShown(),clonesHidden=!1,cancel())},hideClone:function(_ref5){var _this=this,sortable=_ref5.sortable,cloneNowHidden=_ref5.cloneNowHidden,cancel=_ref5.cancel;this.isMultiDrag&&(multiDragClones.forEach(function(clone2){css(clone2,"display","none"),_this.options.removeCloneOnHide&&clone2.parentNode&&clone2.parentNode.removeChild(clone2)}),cloneNowHidden(),clonesHidden=!0,cancel())},dragStartGlobal:function(_ref6){var sortable=_ref6.sortable;!this.isMultiDrag&&multiDragSortable&&multiDragSortable.multiDrag._deselectMultiDrag(),multiDragElements.forEach(function(multiDragElement){multiDragElement.sortableIndex=index(multiDragElement)}),multiDragElements=multiDragElements.sort(function(a,b){return a.sortableIndex-b.sortableIndex}),dragStarted=!0},dragStarted:function(_ref7){var _this2=this,sortable=_ref7.sortable;if(this.isMultiDrag){if(this.options.sort&&(sortable.captureAnimationState(),this.options.animation)){multiDragElements.forEach(function(multiDragElement){multiDragElement!==dragEl$1&&css(multiDragElement,"position","absolute")});var dragRect=getRect(dragEl$1,!1,!0,!0);multiDragElements.forEach(function(multiDragElement){multiDragElement!==dragEl$1&&setRect(multiDragElement,dragRect)}),folding=!0,initialFolding=!0}sortable.animateAll(function(){folding=!1,initialFolding=!1,_this2.options.animation&&multiDragElements.forEach(function(multiDragElement){unsetRect(multiDragElement)}),_this2.options.sort&&removeMultiDragElements()})}},dragOver:function(_ref8){var target=_ref8.target,completed=_ref8.completed,cancel=_ref8.cancel;folding&&~multiDragElements.indexOf(target)&&(completed(!1),cancel())},revert:function(_ref9){var fromSortable=_ref9.fromSortable,rootEl2=_ref9.rootEl,sortable=_ref9.sortable,dragRect=_ref9.dragRect;multiDragElements.length>1&&(multiDragElements.forEach(function(multiDragElement){sortable.addAnimationState({target:multiDragElement,rect:folding?getRect(multiDragElement):dragRect}),unsetRect(multiDragElement),multiDragElement.fromRect=dragRect,fromSortable.removeAnimationState(multiDragElement)}),folding=!1,insertMultiDragElements(!this.options.removeCloneOnHide,rootEl2))},dragOverCompleted:function(_ref10){var sortable=_ref10.sortable,isOwner=_ref10.isOwner,insertion=_ref10.insertion,activeSortable=_ref10.activeSortable,parentEl2=_ref10.parentEl,putSortable2=_ref10.putSortable,options=this.options;if(insertion){if(isOwner&&activeSortable._hideClone(),initialFolding=!1,options.animation&&multiDragElements.length>1&&(folding||!isOwner&&!activeSortable.options.sort&&!putSortable2)){var dragRectAbsolute=getRect(dragEl$1,!1,!0,!0);multiDragElements.forEach(function(multiDragElement){multiDragElement!==dragEl$1&&(setRect(multiDragElement,dragRectAbsolute),parentEl2.appendChild(multiDragElement))}),folding=!0}if(!isOwner)if(folding||removeMultiDragElements(),multiDragElements.length>1){var clonesHiddenBefore=clonesHidden;activeSortable._showClone(sortable),activeSortable.options.animation&&!clonesHidden&&clonesHiddenBefore&&multiDragClones.forEach(function(clone2){activeSortable.addAnimationState({target:clone2,rect:clonesFromRect}),clone2.fromRect=clonesFromRect,clone2.thisAnimationDuration=null})}else activeSortable._showClone(sortable)}},dragOverAnimationCapture:function(_ref11){var dragRect=_ref11.dragRect,isOwner=_ref11.isOwner,activeSortable=_ref11.activeSortable;if(multiDragElements.forEach(function(multiDragElement){multiDragElement.thisAnimationDuration=null}),activeSortable.options.animation&&!isOwner&&activeSortable.multiDrag.isMultiDrag){clonesFromRect=_extends({},dragRect);var dragMatrix=matrix(dragEl$1,!0);clonesFromRect.top-=dragMatrix.f,clonesFromRect.left-=dragMatrix.e}},dragOverAnimationComplete:function(){folding&&(folding=!1,removeMultiDragElements())},drop:function(_ref12){var evt=_ref12.originalEvent,rootEl2=_ref12.rootEl,parentEl2=_ref12.parentEl,sortable=_ref12.sortable,dispatchSortableEvent=_ref12.dispatchSortableEvent,oldIndex2=_ref12.oldIndex,putSortable2=_ref12.putSortable,toSortable=putSortable2||this.sortable;if(evt){var options=this.options,children=parentEl2.children;if(!dragStarted)if(options.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),toggleClass(dragEl$1,options.selectedClass,!~multiDragElements.indexOf(dragEl$1)),~multiDragElements.indexOf(dragEl$1))multiDragElements.splice(multiDragElements.indexOf(dragEl$1),1),lastMultiDragSelect=null,dispatchEvent({sortable,rootEl:rootEl2,name:"deselect",targetEl:dragEl$1,originalEvent:evt});else{if(multiDragElements.push(dragEl$1),dispatchEvent({sortable,rootEl:rootEl2,name:"select",targetEl:dragEl$1,originalEvent:evt}),evt.shiftKey&&lastMultiDragSelect&&sortable.el.contains(lastMultiDragSelect)){var lastIndex=index(lastMultiDragSelect),currentIndex=index(dragEl$1);if(~lastIndex&&~currentIndex&&lastIndex!==currentIndex){var n,i;for(currentIndex>lastIndex?(i=lastIndex,n=currentIndex):(i=currentIndex,n=lastIndex+1);i<n;i++)~multiDragElements.indexOf(children[i])||(toggleClass(children[i],options.selectedClass,!0),multiDragElements.push(children[i]),dispatchEvent({sortable,rootEl:rootEl2,name:"select",targetEl:children[i],originalEvent:evt}))}}else lastMultiDragSelect=dragEl$1;multiDragSortable=toSortable}if(dragStarted&&this.isMultiDrag){if(folding=!1,(parentEl2[expando].options.sort||parentEl2!==rootEl2)&&multiDragElements.length>1){var dragRect=getRect(dragEl$1),multiDragIndex=index(dragEl$1,":not(."+this.options.selectedClass+")");if(!initialFolding&&options.animation&&(dragEl$1.thisAnimationDuration=null),toSortable.captureAnimationState(),!initialFolding&&(options.animation&&(dragEl$1.fromRect=dragRect,multiDragElements.forEach(function(multiDragElement){if(multiDragElement.thisAnimationDuration=null,multiDragElement!==dragEl$1){var rect=folding?getRect(multiDragElement):dragRect;multiDragElement.fromRect=rect,toSortable.addAnimationState({target:multiDragElement,rect})}})),removeMultiDragElements(),multiDragElements.forEach(function(multiDragElement){children[multiDragIndex]?parentEl2.insertBefore(multiDragElement,children[multiDragIndex]):parentEl2.appendChild(multiDragElement),multiDragIndex++}),oldIndex2===index(dragEl$1))){var update=!1;multiDragElements.forEach(function(multiDragElement){if(multiDragElement.sortableIndex!==index(multiDragElement)){update=!0;return}}),update&&(dispatchSortableEvent("update"),dispatchSortableEvent("sort"))}multiDragElements.forEach(function(multiDragElement){unsetRect(multiDragElement)}),toSortable.animateAll()}multiDragSortable=toSortable}(rootEl2===parentEl2||putSortable2&&putSortable2.lastPutMode!=="clone")&&multiDragClones.forEach(function(clone2){clone2.parentNode&&clone2.parentNode.removeChild(clone2)})}},nullingGlobal:function(){this.isMultiDrag=dragStarted=!1,multiDragClones.length=0},destroyGlobal:function(){this._deselectMultiDrag(),off(document,"pointerup",this._deselectMultiDrag),off(document,"mouseup",this._deselectMultiDrag),off(document,"touchend",this._deselectMultiDrag),off(document,"keydown",this._checkKeyDown),off(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(evt){if(!(typeof dragStarted<"u"&&dragStarted)&&multiDragSortable===this.sortable&&!(evt&&closest(evt.target,this.options.draggable,this.sortable.el,!1))&&!(evt&&evt.button!==0))for(;multiDragElements.length;){var el=multiDragElements[0];toggleClass(el,this.options.selectedClass,!1),multiDragElements.shift(),dispatchEvent({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:el,originalEvent:evt})}},_checkKeyDown:function(evt){evt.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(evt){evt.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},_extends(MultiDrag,{pluginName:"multiDrag",utils:{select:function(el){var sortable=el.parentNode[expando];!sortable||!sortable.options.multiDrag||~multiDragElements.indexOf(el)||(multiDragSortable&&multiDragSortable!==sortable&&(multiDragSortable.multiDrag._deselectMultiDrag(),multiDragSortable=sortable),toggleClass(el,sortable.options.selectedClass,!0),multiDragElements.push(el))},deselect:function(el){var sortable=el.parentNode[expando],index2=multiDragElements.indexOf(el);!sortable||!sortable.options.multiDrag||!~index2||(toggleClass(el,sortable.options.selectedClass,!1),multiDragElements.splice(index2,1))}},eventProperties:function(){var _this3=this,oldIndicies=[],newIndicies=[];return multiDragElements.forEach(function(multiDragElement){oldIndicies.push({multiDragElement,index:multiDragElement.sortableIndex});var newIndex2;folding&&multiDragElement!==dragEl$1?newIndex2=-1:folding?newIndex2=index(multiDragElement,":not(."+_this3.options.selectedClass+")"):newIndex2=index(multiDragElement),newIndicies.push({multiDragElement,index:newIndex2})}),{items:_toConsumableArray(multiDragElements),clones:[].concat(multiDragClones),oldIndicies,newIndicies}},optionListeners:{multiDragKey:function(key){return key=key.toLowerCase(),key==="ctrl"?key="Control":key.length>1&&(key=key.charAt(0).toUpperCase()+key.substr(1)),key}}})}function insertMultiDragElements(clonesInserted,rootEl2){multiDragElements.forEach(function(multiDragElement,i){var target=rootEl2.children[multiDragElement.sortableIndex+(clonesInserted?Number(i):0)];target?rootEl2.insertBefore(multiDragElement,target):rootEl2.appendChild(multiDragElement)})}function insertMultiDragClones(elementsInserted,rootEl2){multiDragClones.forEach(function(clone2,i){var target=rootEl2.children[clone2.sortableIndex+(elementsInserted?Number(i):0)];target?rootEl2.insertBefore(clone2,target):rootEl2.appendChild(clone2)})}function removeMultiDragElements(){multiDragElements.forEach(function(multiDragElement){multiDragElement!==dragEl$1&&multiDragElement.parentNode&&multiDragElement.parentNode.removeChild(multiDragElement)})}Sortable.mount(new AutoScrollPlugin);Sortable.mount(Remove,Revert);var sortable_esm_default=Sortable;var import_obsidian=require("obsidian"),Collator=new Intl.Collator(void 0,{usage:"sort",sensitivity:"base",numeric:!0}).compare;var Translate=i18next.t.bind(i18next),sortOptionStrings={alphabetical:"plugins.file-explorer.label-sort-a-to-z",alphabeticalReverse:"plugins.file-explorer.label-sort-z-to-a",byModifiedTime:"plugins.file-explorer.label-sort-new-to-old",byModifiedTimeReverse:"plugins.file-explorer.label-sort-old-to-new",byCreatedTime:"plugins.file-explorer.label-sort-created-new-to-old",byCreatedTimeReverse:"plugins.file-explorer.label-sort-created-old-to-new",custom:"Custom"},sortOptionGroups=[["alphabetical","alphabeticalReverse"],["byModifiedTime","byModifiedTimeReverse"],["byCreatedTime","byCreatedTimeReverse"],["custom"]],folderSortV2=function(settings,e,foldersOnBottom){let children=e.children.slice();children.sort((firstEl,secondEl)=>{let firstIsFolder,secondIsFolder;if(foldersOnBottom&&((firstIsFolder=firstEl instanceof import_obsidian.TFolder)||(secondIsFolder=secondEl instanceof import_obsidian.TFolder)))return firstIsFolder&&!secondIsFolder?1:secondIsFolder&&!firstIsFolder?-1:Collator(firstEl.name,secondEl.name);let order=firstEl.parent&&secondEl.parent&&firstEl.parent===secondEl.parent&&!firstEl.parent.isRoot()?settings.fileExplorerOrder[firstEl.parent.path]||void 0:settings.fileExplorerOrder[""];if(!order)return Collator(firstEl.name,secondEl.name);let index1=order.indexOf(firstEl.path),index2=order.indexOf(secondEl.path);return(index1>-1?index1:1/0)-(index2>-1?index2:1/0)});let i=[];for(let r=0,o=children;r<o.length;r++){let a=o[r],s=this.fileItems[a.path];s&&i.push(s)}return i};function addButton(icon,addSortButton2,title,onClick){let leaf=addSortButton2.app.workspace.getLeavesOfType("file-explorer")?.first()?.view,button=createDiv({cls:"clickable-icon nav-action-button custom-sort",attr:{"aria-label":title}});if((0,import_obsidian.setIcon)(button,icon),button.addEventListener("click",onClick),leaf){let oldChild=leaf.containerEl.querySelector("div.nav-buttons-container").querySelectorAll(`[aria-label="${title}"]`);for(let i=0;i<oldChild.length;i++)oldChild[i].hasClass("custom-sort")?oldChild[i].remove():oldChild[i].addClass("hide");if(icon==="move"||icon==="arrow-up-narrow-wide"||icon==="three-horizontal-bars"){let devAll=leaf.containerEl.querySelector(`div.nav-buttons-container > .nav-action-button[aria-label='${Translate("plugins.file-explorer.action-collapse-all")}']`),expandAll=leaf.containerEl.querySelector(`div.nav-buttons-container > .nav-action-button[aria-label='${Translate("plugins.file-explorer.action-expand-all")}']`);devAll?leaf.containerEl.querySelector("div.nav-buttons-container").insertBefore(button,devAll):expandAll?leaf.containerEl.querySelector("div.nav-buttons-container").insertBefore(button,expandAll):leaf.containerEl.querySelector("div.nav-buttons-container").appendChild(button)}else leaf.containerEl.querySelector("div.nav-buttons-container").appendChild(button)}return button}var addSortButton=function(bartender,_sorter,sortOption,_setSortOrder,_currentSort){let plugin=this,settings=bartender.settings,sortEl=addButton(settings.sortOrder==="custom"?"move":"arrow-up-narrow-wide",this,Translate("plugins.file-explorer.action-change-sort"),event=>{event.preventDefault();let menu=new import_obsidian.Menu;for(let currentSortOption=settings.sortOrder,groupIndex=0,_sortOptionGroups=sortOptionGroups;groupIndex<_sortOptionGroups.length;groupIndex++){for(let addMenuItem=_sortOption=>{let label=Translate(sortOptionStrings[_sortOption]);menu.addItem(item=>item.setTitle(label).setChecked(_sortOption===currentSortOption).onClick(()=>{if(_sortOption!==currentSortOption){sortEl.setAttribute("data-sort-method",_sortOption),plugin.app.workspace.trigger("file-explorer-sort-change",_sortOption);let leaf=plugin.app.workspace.getLeavesOfType("file-explorer")?.first()?.view;leaf&&leaf.sort()}_sortOption==="custom"?(0,import_obsidian.setIcon)(sortEl,"move"):(0,import_obsidian.setIcon)(sortEl,"arrow-up-narrow-wide")}))},itemIndex=0,sortOptionGroup=_sortOptionGroups[groupIndex];itemIndex<sortOptionGroup.length;itemIndex++)addMenuItem(sortOptionGroup[itemIndex]);menu.addSeparator()}menu.showAtMouseEvent(event)});return setTimeout(()=>{sortEl.setAttribute("data-sort-method",settings.sortOrder)},100),addButton("three-horizontal-bars",this,"Drag to rearrange",function(event){event.preventDefault();let value=!this.hasClass("is-active");this.toggleClass("is-active",value),plugin.app.workspace.trigger("file-explorer-draggable-change",value)}).addClass("drag-to-rearrange"),addButton("search",this,"Filter items",function(event){event.preventDefault();let value=!this.hasClass("is-active");this.toggleClass("is-active",value);let filterEl=document.body.querySelector('.workspace-leaf-content[data-type="file-explorer"] .search-input-container > input');filterEl&&!value?(filterEl.parentElement?.hide(),filterEl.value="",filterEl.dispatchEvent(new Event("input"))):(filterEl?.parentElement?.show(),filterEl?.focus())}),sortEl};var import_obsidian2=require("obsidian");var STATUS_BAR_SELECTOR="body > div.app-container div.status-bar",RIBBON_BAR_SELECTOR="body > div.app-container div.side-dock-actions";function getPreviousSiblings(el,filter){let sibs=[];for(;el=el.previousSibling;)el.nodeType!==3&&(!filter||filter(el))&&sibs.push(el);return sibs}function getNextSiblings(el,filter){let sibs=[];for(;el=el.nextSibling;)el.nodeType!==3&&(!filter||filter(el))&&sibs.push(el);return sibs}function generateId(el,options){let classes=options?.useClass?Array.from(el.classList).filter(c=>!c.startsWith("is-")).sort().join(" "):"",str=(options?.useTag?el.tagName:"")+(options?.useClass?classes:"")+(options?.useText?el.textContent:"")+(options?.useAria?el.getAttr("aria-label"):"")+(options?.useIcon?el.querySelector("svg")?.className?.baseVal:"");return cyrb53(str)}var cyrb53=function(str,seed=0){let h1=3735928559^seed,h2=1103547991^seed;for(let i=0,ch;i<str.length;i++)ch=str.charCodeAt(i),h1=Math.imul(h1^ch,2654435761),h2=Math.imul(h2^ch,1597334677);return h1=Math.imul(h1^h1>>>16,2246822507)^Math.imul(h2^h2>>>13,3266489909),h2=Math.imul(h2^h2>>>16,2246822507)^Math.imul(h1^h1>>>13,3266489909),4294967296*(2097151&h2)+(h1>>>0).toString()};function reorderArray(array,from,to,on2=1){return array.splice(to,0,...array.splice(from,on2)),array}var Collapse=class{plugin;settings;ribbonBarSorter;statusBarSorter;app;constructor(plugin){this.plugin=plugin,this.settings=plugin.settings,this.app=plugin.app}insertSeparator(selector,className,rtl){document.body.querySelectorAll(selector).forEach(el=>{let getSiblings=rtl?getPreviousSiblings:getNextSiblings;if(!el)return;let separator=el.createDiv(`${className} separator`);rtl&&el.prepend(separator);let glyphEl=separator.createDiv("glyph");(0,import_obsidian2.setIcon)(glyphEl,"plus-with-circle"),separator.addClass("is-collapsed"),this.plugin.register(()=>separator.detach());let hideTimeout;separator.onClickEvent(event=>{separator.hasClass("is-collapsed")?(Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden")),separator.removeClass("is-collapsed")):(getSiblings(separator).forEach(el2=>el2.addClass("is-hidden")),separator.addClass("is-collapsed"))}),el.onmouseenter=ev=>{hideTimeout&&clearTimeout(hideTimeout)},el.onmouseleave=ev=>{this.settings.autoHide&&(hideTimeout=setTimeout(()=>{getSiblings(separator).forEach(el2=>el2.addClass("is-hidden")),separator.addClass("is-collapsed")},this.plugin.settings.autoHideDelay))},setTimeout(()=>{getSiblings(separator).forEach(el2=>el2.addClass("is-hidden")),separator.addClass("is-collapsed")},0)})}initCollapse(){if(import_obsidian2.Platform.isDesktop&&this.settings.useCollapse&&(this.insertSeparator(STATUS_BAR_SELECTOR,"status-bar-item",!0),this.setStatusBarSorter(),import_obsidian2.requireApiVersion&&!(0,import_obsidian2.requireApiVersion)("0.15.3"))){let left=this.app.workspace.leftSplit.children,right=this.app.workspace.rightSplit.children;left.concat(right).forEach(child=>{child.hasOwnProperty("tabsInnerEl")&&!child.iconSorter&&(child.iconSorter=this.setTabBarSorter(child.tabsInnerEl,child))})}}setTabBarSorter(element,leaf){return this.setElementIDs(element,{useClass:!0,useIcon:!0}),sortable_esm_default.create(element,{group:"leftTabBar",dataIdAttr:"data-id",chosenClass:"bt-sortable-chosen",delay:import_obsidian2.Platform.isMobile?200:this.plugin.settings.dragDelay,dropBubble:!1,dragoverBubble:!1,animation:500,onChoose:()=>element.parentElement?.addClass("is-dragging"),onUnchoose:()=>element.parentElement?.removeClass("is-dragging"),onStart:()=>{document.body.addClass("is-dragging"),element.querySelector(".separator")?.removeClass("is-collapsed"),Array.from(element.children).forEach(el=>el.removeClass("is-hidden"))},onEnd:event=>{document.body.removeClass("is-dragging"),event.oldIndex!==void 0&&event.newIndex!==void 0&&(reorderArray(leaf.children,event.oldIndex,event.newIndex),leaf.currentTab=event.newIndex,leaf.recomputeChildrenDimensions()),this.app.workspace.requestSaveLayout()}})}setViewActionSorter(el,view){if(this.setElementIDs(el,{useClass:!0,useIcon:!0}),Object.values(el).find(value=>value?.hasOwnProperty("nativeDraggable")))return;let viewType=view?.getViewType()||"unknown";return new sortable_esm_default(el,{group:"actionBar",dataIdAttr:"data-id",chosenClass:"bt-sortable-chosen",delay:import_obsidian2.Platform.isMobile?200:this.plugin.settings.dragDelay,sort:!0,animation:500,onStart:()=>{el.querySelector(".separator")?.removeClass("is-collapsed"),Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden"))},store:{get:()=>this.settings.actionBarOrder[viewType],set:s=>{this.settings.actionBarOrder[viewType]=s.toArray(),this.plugin.saveSettings()}}})}setRibbonBarSorter(){let el=document.body.querySelector("body > div.app-container div.side-dock-actions");el&&(this.setElementIDs(el,{useClass:!0,useAria:!0,useIcon:!0}),this.ribbonBarSorter=sortable_esm_default.create(el,{group:"ribbonBar",dataIdAttr:"data-id",delay:import_obsidian2.Platform.isMobile?200:this.plugin.settings.dragDelay,chosenClass:"bt-sortable-chosen",animation:500,onChoose:()=>{Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden"))},onStart:()=>{el.querySelector(".separator")?.removeClass("is-collapsed"),Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden"))},store:{get:sortable=>this.settings.ribbonBarOrder,set:s=>{this.settings.ribbonBarOrder=s.toArray(),this.plugin.saveSettings()}}}))}setElementIDs(parentEl2,options){Array.from(parentEl2.children).forEach(child=>{child instanceof HTMLElement&&!child.getAttribute("data-id")&&child.setAttribute("data-id",generateId(child,options))})}setStatusBarSorter(){let el=document.body.querySelector("body > div.app-container > div.status-bar");el&&(this.setElementIDs(el,{useClass:!0,useAria:!0,useIcon:!0}),this.statusBarSorter=sortable_esm_default.create(el,{group:"statusBar",dataIdAttr:"data-id",chosenClass:"bt-sortable-chosen",delay:import_obsidian2.Platform.isMobile?200:this.plugin.settings.dragDelay,animation:500,onChoose:()=>{Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden"))},onStart:()=>{el.querySelector(".separator")?.removeClass("is-collapsed"),Array.from(el.children).forEach(el2=>el2.removeClass("is-hidden"))},store:{get:sortable=>this.settings.statusBarOrder,set:s=>{this.settings.statusBarOrder=s.toArray(),this.plugin.saveSettings()}}}))}};function isArray(value){return Array.isArray?Array.isArray(value):getTag(value)==="[object Array]"}var INFINITY=1/0;function baseToString(value){if(typeof value=="string")return value;let result=value+"";return result=="0"&&1/value==-INFINITY?"-0":result}function toString(value){return value==null?"":baseToString(value)}function isString(value){return typeof value=="string"}function isNumber(value){return typeof value=="number"}function isBoolean(value){return value===!0||value===!1||isObjectLike(value)&&getTag(value)=="[object Boolean]"}function isObject(value){return typeof value=="object"}function isObjectLike(value){return isObject(value)&&value!==null}function isDefined(value){return value!=null}function isBlank(value){return!value.trim().length}function getTag(value){return value==null?value===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(value)}var INCORRECT_INDEX_TYPE="Incorrect 'index' type",LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY=key=>`Invalid value for key ${key}`,PATTERN_LENGTH_TOO_LARGE=max=>`Pattern length exceeds max of ${max}.`,MISSING_KEY_PROPERTY=name=>`Missing ${name} property in key`,INVALID_KEY_WEIGHT_VALUE=key=>`Property 'weight' in key '${key}' must be a positive integer`,hasOwn=Object.prototype.hasOwnProperty,KeyStore=class{constructor(keys){this._keys=[],this._keyMap={};let totalWeight=0;keys.forEach(key=>{let obj=createKey(key);this._keys.push(obj),this._keyMap[obj.id]=obj,totalWeight+=obj.weight}),this._keys.forEach(key=>{key.weight/=totalWeight})}get(keyId){return this._keyMap[keyId]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}};function createKey(key){let path=null,id=null,src=null,weight=1,getFn=null;if(isString(key)||isArray(key))src=key,path=createKeyPath(key),id=createKeyId(key);else{if(!hasOwn.call(key,"name"))throw new Error(MISSING_KEY_PROPERTY("name"));let name=key.name;if(src=name,hasOwn.call(key,"weight")&&(weight=key.weight,weight<=0))throw new Error(INVALID_KEY_WEIGHT_VALUE(name));path=createKeyPath(name),id=createKeyId(name),getFn=key.getFn}return{path,id,weight,src,getFn}}function createKeyPath(key){return isArray(key)?key:key.split(".")}function createKeyId(key){return isArray(key)?key.join("."):key}function get(obj,path){let list=[],arr=!1,deepGet=(obj2,path2,index2)=>{if(isDefined(obj2))if(!path2[index2])list.push(obj2);else{let key=path2[index2],value=obj2[key];if(!isDefined(value))return;if(index2===path2.length-1&&(isString(value)||isNumber(value)||isBoolean(value)))list.push(toString(value));else if(isArray(value)){arr=!0;for(let i=0,len=value.length;i<len;i+=1)deepGet(value[i],path2,index2+1)}else path2.length&&deepGet(value,path2,index2+1)}};return deepGet(obj,isString(path)?path.split("."):path,0),arr?list:list[0]}var MatchOptions={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},BasicOptions={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(a,b)=>a.score===b.score?a.idx<b.idx?-1:1:a.score<b.score?-1:1},FuzzyOptions={location:0,threshold:.6,distance:100},AdvancedOptions={useExtendedSearch:!1,getFn:get,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},Config={...BasicOptions,...MatchOptions,...FuzzyOptions,...AdvancedOptions},SPACE=/[^ ]+/g;function norm(weight=1,mantissa=3){let cache=new Map,m=Math.pow(10,mantissa);return{get(value){let numTokens=value.match(SPACE).length;if(cache.has(numTokens))return cache.get(numTokens);let norm2=1/Math.pow(numTokens,.5*weight),n=parseFloat(Math.round(norm2*m)/m);return cache.set(numTokens,n),n},clear(){cache.clear()}}}var FuseIndex=class{constructor({getFn=Config.getFn,fieldNormWeight=Config.fieldNormWeight}={}){this.norm=norm(fieldNormWeight,3),this.getFn=getFn,this.isCreated=!1,this.setIndexRecords()}setSources(docs=[]){this.docs=docs}setIndexRecords(records=[]){this.records=records}setKeys(keys=[]){this.keys=keys,this._keysMap={},keys.forEach((key,idx)=>{this._keysMap[key.id]=idx})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,isString(this.docs[0])?this.docs.forEach((doc,docIndex)=>{this._addString(doc,docIndex)}):this.docs.forEach((doc,docIndex)=>{this._addObject(doc,docIndex)}),this.norm.clear())}add(doc){let idx=this.size();isString(doc)?this._addString(doc,idx):this._addObject(doc,idx)}removeAt(idx){this.records.splice(idx,1);for(let i=idx,len=this.size();i<len;i+=1)this.records[i].i-=1}getValueForItemAtKeyId(item,keyId){return item[this._keysMap[keyId]]}size(){return this.records.length}_addString(doc,docIndex){if(!isDefined(doc)||isBlank(doc))return;let record={v:doc,i:docIndex,n:this.norm.get(doc)};this.records.push(record)}_addObject(doc,docIndex){let record={i:docIndex,$:{}};this.keys.forEach((key,keyIndex)=>{let value=key.getFn?key.getFn(doc):this.getFn(doc,key.path);if(isDefined(value)){if(isArray(value)){let subRecords=[],stack=[{nestedArrIndex:-1,value}];for(;stack.length;){let{nestedArrIndex,value:value2}=stack.pop();if(isDefined(value2))if(isString(value2)&&!isBlank(value2)){let subRecord={v:value2,i:nestedArrIndex,n:this.norm.get(value2)};subRecords.push(subRecord)}else isArray(value2)&&value2.forEach((item,k)=>{stack.push({nestedArrIndex:k,value:item})})}record.$[keyIndex]=subRecords}else if(isString(value)&&!isBlank(value)){let subRecord={v:value,n:this.norm.get(value)};record.$[keyIndex]=subRecord}}}),this.records.push(record)}toJSON(){return{keys:this.keys,records:this.records}}};function createIndex(keys,docs,{getFn=Config.getFn,fieldNormWeight=Config.fieldNormWeight}={}){let myIndex=new FuseIndex({getFn,fieldNormWeight});return myIndex.setKeys(keys.map(createKey)),myIndex.setSources(docs),myIndex.create(),myIndex}function parseIndex(data,{getFn=Config.getFn,fieldNormWeight=Config.fieldNormWeight}={}){let{keys,records}=data,myIndex=new FuseIndex({getFn,fieldNormWeight});return myIndex.setKeys(keys),myIndex.setIndexRecords(records),myIndex}function computeScore$1(pattern,{errors=0,currentLocation=0,expectedLocation=0,distance=Config.distance,ignoreLocation=Config.ignoreLocation}={}){let accuracy=errors/pattern.length;if(ignoreLocation)return accuracy;let proximity=Math.abs(expectedLocation-currentLocation);return distance?accuracy+proximity/distance:proximity?1:accuracy}function convertMaskToIndices(matchmask=[],minMatchCharLength=Config.minMatchCharLength){let indices=[],start=-1,end=-1,i=0;for(let len=matchmask.length;i<len;i+=1){let match=matchmask[i];match&&start===-1?start=i:!match&&start!==-1&&(end=i-1,end-start+1>=minMatchCharLength&&indices.push([start,end]),start=-1)}return matchmask[i-1]&&i-start>=minMatchCharLength&&indices.push([start,i-1]),indices}var MAX_BITS=32;function search(text,pattern,patternAlphabet,{location=Config.location,distance=Config.distance,threshold=Config.threshold,findAllMatches=Config.findAllMatches,minMatchCharLength=Config.minMatchCharLength,includeMatches=Config.includeMatches,ignoreLocation=Config.ignoreLocation}={}){if(pattern.length>MAX_BITS)throw new Error(PATTERN_LENGTH_TOO_LARGE(MAX_BITS));let patternLen=pattern.length,textLen=text.length,expectedLocation=Math.max(0,Math.min(location,textLen)),currentThreshold=threshold,bestLocation=expectedLocation,computeMatches=minMatchCharLength>1||includeMatches,matchMask=computeMatches?Array(textLen):[],index2;for(;(index2=text.indexOf(pattern,bestLocation))>-1;){let score=computeScore$1(pattern,{currentLocation:index2,expectedLocation,distance,ignoreLocation});if(currentThreshold=Math.min(score,currentThreshold),bestLocation=index2+patternLen,computeMatches){let i=0;for(;i<patternLen;)matchMask[index2+i]=1,i+=1}}bestLocation=-1;let lastBitArr=[],finalScore=1,binMax=patternLen+textLen,mask=1<<patternLen-1;for(let i=0;i<patternLen;i+=1){let binMin=0,binMid=binMax;for(;binMin<binMid;)computeScore$1(pattern,{errors:i,currentLocation:expectedLocation+binMid,expectedLocation,distance,ignoreLocation})<=currentThreshold?binMin=binMid:binMax=binMid,binMid=Math.floor((binMax-binMin)/2+binMin);binMax=binMid;let start=Math.max(1,expectedLocation-binMid+1),finish=findAllMatches?textLen:Math.min(expectedLocation+binMid,textLen)+patternLen,bitArr=Array(finish+2);bitArr[finish+1]=(1<<i)-1;for(let j=finish;j>=start;j-=1){let currentLocation=j-1,charMatch=patternAlphabet[text.charAt(currentLocation)];if(computeMatches&&(matchMask[currentLocation]=+!!charMatch),bitArr[j]=(bitArr[j+1]<<1|1)&charMatch,i&&(bitArr[j]|=(lastBitArr[j+1]|lastBitArr[j])<<1|1|lastBitArr[j+1]),bitArr[j]&mask&&(finalScore=computeScore$1(pattern,{errors:i,currentLocation,expectedLocation,distance,ignoreLocation}),finalScore<=currentThreshold)){if(currentThreshold=finalScore,bestLocation=currentLocation,bestLocation<=expectedLocation)break;start=Math.max(1,2*expectedLocation-bestLocation)}}if(computeScore$1(pattern,{errors:i+1,currentLocation:expectedLocation,expectedLocation,distance,ignoreLocation})>currentThreshold)break;lastBitArr=bitArr}let result={isMatch:bestLocation>=0,score:Math.max(.001,finalScore)};if(computeMatches){let indices=convertMaskToIndices(matchMask,minMatchCharLength);indices.length?includeMatches&&(result.indices=indices):result.isMatch=!1}return result}function createPatternAlphabet(pattern){let mask={};for(let i=0,len=pattern.length;i<len;i+=1){let char=pattern.charAt(i);mask[char]=(mask[char]||0)|1<<len-i-1}return mask}var BitapSearch=class{constructor(pattern,{location=Config.location,threshold=Config.threshold,distance=Config.distance,includeMatches=Config.includeMatches,findAllMatches=Config.findAllMatches,minMatchCharLength=Config.minMatchCharLength,isCaseSensitive=Config.isCaseSensitive,ignoreLocation=Config.ignoreLocation}={}){if(this.options={location,threshold,distance,includeMatches,findAllMatches,minMatchCharLength,isCaseSensitive,ignoreLocation},this.pattern=isCaseSensitive?pattern:pattern.toLowerCase(),this.chunks=[],!this.pattern.length)return;let addChunk=(pattern2,startIndex)=>{this.chunks.push({pattern:pattern2,alphabet:createPatternAlphabet(pattern2),startIndex})},len=this.pattern.length;if(len>MAX_BITS){let i=0,remainder=len%MAX_BITS,end=len-remainder;for(;i<end;)addChunk(this.pattern.substr(i,MAX_BITS),i),i+=MAX_BITS;if(remainder){let startIndex=len-MAX_BITS;addChunk(this.pattern.substr(startIndex),startIndex)}}else addChunk(this.pattern,0)}searchIn(text){let{isCaseSensitive,includeMatches}=this.options;if(isCaseSensitive||(text=text.toLowerCase()),this.pattern===text){let result2={isMatch:!0,score:0};return includeMatches&&(result2.indices=[[0,text.length-1]]),result2}let{location,distance,threshold,findAllMatches,minMatchCharLength,ignoreLocation}=this.options,allIndices=[],totalScore=0,hasMatches=!1;this.chunks.forEach(({pattern,alphabet,startIndex})=>{let{isMatch,score,indices}=search(text,pattern,alphabet,{location:location+startIndex,distance,threshold,findAllMatches,minMatchCharLength,includeMatches,ignoreLocation});isMatch&&(hasMatches=!0),totalScore+=score,isMatch&&indices&&(allIndices=[...allIndices,...indices])});let result={isMatch:hasMatches,score:hasMatches?totalScore/this.chunks.length:1};return hasMatches&&includeMatches&&(result.indices=allIndices),result}},BaseMatch=class{constructor(pattern){this.pattern=pattern}static isMultiMatch(pattern){return getMatch(pattern,this.multiRegex)}static isSingleMatch(pattern){return getMatch(pattern,this.singleRegex)}search(){}};function getMatch(pattern,exp){let matches2=pattern.match(exp);return matches2?matches2[1]:null}var ExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(text){let isMatch=text===this.pattern;return{isMatch,score:isMatch?0:1,indices:[0,this.pattern.length-1]}}},InverseExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(text){let isMatch=text.indexOf(this.pattern)===-1;return{isMatch,score:isMatch?0:1,indices:[0,text.length-1]}}},PrefixExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(text){let isMatch=text.startsWith(this.pattern);return{isMatch,score:isMatch?0:1,indices:[0,this.pattern.length-1]}}},InversePrefixExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(text){let isMatch=!text.startsWith(this.pattern);return{isMatch,score:isMatch?0:1,indices:[0,text.length-1]}}},SuffixExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(text){let isMatch=text.endsWith(this.pattern);return{isMatch,score:isMatch?0:1,indices:[text.length-this.pattern.length,text.length-1]}}},InverseSuffixExactMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(text){let isMatch=!text.endsWith(this.pattern);return{isMatch,score:isMatch?0:1,indices:[0,text.length-1]}}},FuzzyMatch=class extends BaseMatch{constructor(pattern,{location=Config.location,threshold=Config.threshold,distance=Config.distance,includeMatches=Config.includeMatches,findAllMatches=Config.findAllMatches,minMatchCharLength=Config.minMatchCharLength,isCaseSensitive=Config.isCaseSensitive,ignoreLocation=Config.ignoreLocation}={}){super(pattern),this._bitapSearch=new BitapSearch(pattern,{location,threshold,distance,includeMatches,findAllMatches,minMatchCharLength,isCaseSensitive,ignoreLocation})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(text){return this._bitapSearch.searchIn(text)}},IncludeMatch=class extends BaseMatch{constructor(pattern){super(pattern)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(text){let location=0,index2,indices=[],patternLen=this.pattern.length;for(;(index2=text.indexOf(this.pattern,location))>-1;)location=index2+patternLen,indices.push([index2,location-1]);let isMatch=!!indices.length;return{isMatch,score:isMatch?0:1,indices}}},searchers=[ExactMatch,IncludeMatch,PrefixExactMatch,InversePrefixExactMatch,InverseSuffixExactMatch,SuffixExactMatch,InverseExactMatch,FuzzyMatch],searchersLen=searchers.length,SPACE_RE=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,OR_TOKEN="|";function parseQuery(pattern,options={}){return pattern.split(OR_TOKEN).map(item=>{let query=item.trim().split(SPACE_RE).filter(item2=>item2&&!!item2.trim()),results=[];for(let i=0,len=query.length;i<len;i+=1){let queryItem=query[i],found=!1,idx=-1;for(;!found&&++idx<searchersLen;){let searcher=searchers[idx],token=searcher.isMultiMatch(queryItem);token&&(results.push(new searcher(token,options)),found=!0)}if(!found)for(idx=-1;++idx<searchersLen;){let searcher=searchers[idx],token=searcher.isSingleMatch(queryItem);if(token){results.push(new searcher(token,options));break}}}return results})}var MultiMatchSet=new Set([FuzzyMatch.type,IncludeMatch.type]),ExtendedSearch=class{constructor(pattern,{isCaseSensitive=Config.isCaseSensitive,includeMatches=Config.includeMatches,minMatchCharLength=Config.minMatchCharLength,ignoreLocation=Config.ignoreLocation,findAllMatches=Config.findAllMatches,location=Config.location,threshold=Config.threshold,distance=Config.distance}={}){this.query=null,this.options={isCaseSensitive,includeMatches,minMatchCharLength,findAllMatches,ignoreLocation,location,threshold,distance},this.pattern=isCaseSensitive?pattern:pattern.toLowerCase(),this.query=parseQuery(this.pattern,this.options)}static condition(_,options){return options.useExtendedSearch}searchIn(text){let query=this.query;if(!query)return{isMatch:!1,score:1};let{includeMatches,isCaseSensitive}=this.options;text=isCaseSensitive?text:text.toLowerCase();let numMatches=0,allIndices=[],totalScore=0;for(let i=0,qLen=query.length;i<qLen;i+=1){let searchers2=query[i];allIndices.length=0,numMatches=0;for(let j=0,pLen=searchers2.length;j<pLen;j+=1){let searcher=searchers2[j],{isMatch,indices,score}=searcher.search(text);if(isMatch){if(numMatches+=1,totalScore+=score,includeMatches){let type=searcher.constructor.type;MultiMatchSet.has(type)?allIndices=[...allIndices,...indices]:allIndices.push(indices)}}else{totalScore=0,numMatches=0,allIndices.length=0;break}}if(numMatches){let result={isMatch:!0,score:totalScore/numMatches};return includeMatches&&(result.indices=allIndices),result}}return{isMatch:!1,score:1}}},registeredSearchers=[];function register(...args){registeredSearchers.push(...args)}function createSearcher(pattern,options){for(let i=0,len=registeredSearchers.length;i<len;i+=1){let searcherClass=registeredSearchers[i];if(searcherClass.condition(pattern,options))return new searcherClass(pattern,options)}return new BitapSearch(pattern,options)}var LogicalOperator={AND:"$and",OR:"$or"},KeyType={PATH:"$path",PATTERN:"$val"},isExpression=query=>!!(query[LogicalOperator.AND]||query[LogicalOperator.OR]),isPath=query=>!!query[KeyType.PATH],isLeaf=query=>!isArray(query)&&isObject(query)&&!isExpression(query),convertToExplicit=query=>({[LogicalOperator.AND]:Object.keys(query).map(key=>({[key]:query[key]}))});function parse(query,options,{auto=!0}={}){let next=query2=>{let keys=Object.keys(query2),isQueryPath=isPath(query2);if(!isQueryPath&&keys.length>1&&!isExpression(query2))return next(convertToExplicit(query2));if(isLeaf(query2)){let key=isQueryPath?query2[KeyType.PATH]:keys[0],pattern=isQueryPath?query2[KeyType.PATTERN]:query2[key];if(!isString(pattern))throw new Error(LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY(key));let obj={keyId:createKeyId(key),pattern};return auto&&(obj.searcher=createSearcher(pattern,options)),obj}let node={children:[],operator:keys[0]};return keys.forEach(key=>{let value=query2[key];isArray(value)&&value.forEach(item=>{node.children.push(next(item))})}),node};return isExpression(query)||(query=convertToExplicit(query)),next(query)}function computeScore(results,{ignoreFieldNorm=Config.ignoreFieldNorm}){results.forEach(result=>{let totalScore=1;result.matches.forEach(({key,norm:norm2,score})=>{let weight=key?key.weight:null;totalScore*=Math.pow(score===0&&weight?Number.EPSILON:score,(weight||1)*(ignoreFieldNorm?1:norm2))}),result.score=totalScore})}function transformMatches(result,data){let matches2=result.matches;data.matches=[],isDefined(matches2)&&matches2.forEach(match=>{if(!isDefined(match.indices)||!match.indices.length)return;let{indices,value}=match,obj={indices,value};match.key&&(obj.key=match.key.src),match.idx>-1&&(obj.refIndex=match.idx),data.matches.push(obj)})}function transformScore(result,data){data.score=result.score}function format(results,docs,{includeMatches=Config.includeMatches,includeScore=Config.includeScore}={}){let transformers=[];return includeMatches&&transformers.push(transformMatches),includeScore&&transformers.push(transformScore),results.map(result=>{let{idx}=result,data={item:docs[idx],refIndex:idx};return transformers.length&&transformers.forEach(transformer=>{transformer(result,data)}),data})}var Fuse=class{constructor(docs,options={},index2){this.options={...Config,...options},this.options.useExtendedSearch,this._keyStore=new KeyStore(this.options.keys),this.setCollection(docs,index2)}setCollection(docs,index2){if(this._docs=docs,index2&&!(index2 instanceof FuseIndex))throw new Error(INCORRECT_INDEX_TYPE);this._myIndex=index2||createIndex(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(doc){isDefined(doc)&&(this._docs.push(doc),this._myIndex.add(doc))}remove(predicate=()=>!1){let results=[];for(let i=0,len=this._docs.length;i<len;i+=1){let doc=this._docs[i];predicate(doc,i)&&(this.removeAt(i),i-=1,len-=1,results.push(doc))}return results}removeAt(idx){this._docs.splice(idx,1),this._myIndex.removeAt(idx)}getIndex(){return this._myIndex}search(query,{limit=-1}={}){let{includeMatches,includeScore,shouldSort,sortFn,ignoreFieldNorm}=this.options,results=isString(query)?isString(this._docs[0])?this._searchStringList(query):this._searchObjectList(query):this._searchLogical(query);return computeScore(results,{ignoreFieldNorm}),shouldSort&&results.sort(sortFn),isNumber(limit)&&limit>-1&&(results=results.slice(0,limit)),format(results,this._docs,{includeMatches,includeScore})}_searchStringList(query){let searcher=createSearcher(query,this.options),{records}=this._myIndex,results=[];return records.forEach(({v:text,i:idx,n:norm2})=>{if(!isDefined(text))return;let{isMatch,score,indices}=searcher.searchIn(text);isMatch&&results.push({item:text,idx,matches:[{score,value:text,norm:norm2,indices}]})}),results}_searchLogical(query){let expression=parse(query,this.options),evaluate=(node,item,idx)=>{if(!node.children){let{keyId,searcher}=node,matches2=this._findMatches({key:this._keyStore.get(keyId),value:this._myIndex.getValueForItemAtKeyId(item,keyId),searcher});return matches2&&matches2.length?[{idx,item,matches:matches2}]:[]}let res=[];for(let i=0,len=node.children.length;i<len;i+=1){let child=node.children[i],result=evaluate(child,item,idx);if(result.length)res.push(...result);else if(node.operator===LogicalOperator.AND)return[]}return res},records=this._myIndex.records,resultMap={},results=[];return records.forEach(({$:item,i:idx})=>{if(isDefined(item)){let expResults=evaluate(expression,item,idx);expResults.length&&(resultMap[idx]||(resultMap[idx]={idx,item,matches:[]},results.push(resultMap[idx])),expResults.forEach(({matches:matches2})=>{resultMap[idx].matches.push(...matches2)}))}}),results}_searchObjectList(query){let searcher=createSearcher(query,this.options),{keys,records}=this._myIndex,results=[];return records.forEach(({$:item,i:idx})=>{if(!isDefined(item))return;let matches2=[];keys.forEach((key,keyIndex)=>{matches2.push(...this._findMatches({key,value:item[keyIndex],searcher}))}),matches2.length&&results.push({idx,item,matches:matches2})}),results}_findMatches({key,value,searcher}){if(!isDefined(value))return[];let matches2=[];if(isArray(value))value.forEach(({v:text,i:idx,n:norm2})=>{if(!isDefined(text))return;let{isMatch,score,indices}=searcher.searchIn(text);isMatch&&matches2.push({score,key,value:text,idx,norm:norm2,indices})});else{let{v:text,n:norm2}=value,{isMatch,score,indices}=searcher.searchIn(text);isMatch&&matches2.push({score,key,value:text,norm:norm2,indices})}return matches2}};Fuse.version="7.0.0";Fuse.createIndex=createIndex;Fuse.parseIndex=parseIndex;Fuse.config=Config;Fuse.parseQuery=parse;register(ExtendedSearch);var import_obsidian3=require("obsidian"),CustomFilter=class{settings;plugin;app;constructor(plugin){this.plugin=plugin,this.settings=plugin.settings,this.app=plugin.app}setFileExplorerFilter(fileExplorer){fileExplorer=fileExplorer??this.plugin.getFileExplorer();let fileExplorerNav=fileExplorer?.headerDom?.navHeaderEl;if(!fileExplorerNav)return;let fileExplorerFilter=fileExplorerNav.createDiv("search-input-container filter");fileExplorerNav.insertAdjacentElement("afterend",fileExplorerFilter);let fileExplorerFilterInput=fileExplorerFilter.createEl("input");fileExplorerFilterInput.placeholder="Type to filter...",fileExplorerFilterInput.type="text",fileExplorerFilter.hide();let filterScope=new import_obsidian3.Scope(this.app.scope);fileExplorerFilterInput.onfocus=()=>{this.app.keymap.pushScope(filterScope)},fileExplorerFilterInput.onblur=()=>{this.app.keymap.popScope(filterScope)},fileExplorerFilterInput.oninput=ev=>{ev.target instanceof HTMLInputElement&&(ev.target.value.length?clearButtonEl.show():clearButtonEl.hide(),fileExplorer.tree.infinityScroll.filter=ev.target.value),fileExplorer.tree.infinityScroll.compute()};let clearButtonEl=fileExplorerFilter.createDiv("search-input-clear-button",el=>{el.addEventListener("click",()=>{fileExplorerFilterInput.value="",clearButtonEl.hide(),fileExplorerFilterInput.focus(),fileExplorerFilterInput.dispatchEvent(new Event("input"))}),el.hide()})}clearFileExplorerFilter(){let fileExplorer=this.plugin.getFileExplorer(),fileExplorerFilterEl=fileExplorer.containerEl.querySelector('.workspace-leaf-content[data-type="file-explorer"] .search-input-container > input');fileExplorerFilterEl?.remove(),fileExplorerFilterEl&&(fileExplorerFilterEl.value=""),fileExplorer.tree.infinityScroll.filter="",fileExplorer.tree.infinityScroll.compute()}getItems=(items,app)=>{let children=[],supportsVirtualChildren=import_obsidian3.requireApiVersion&&(0,import_obsidian3.requireApiVersion)("0.15.0"),excluded=app.vault.config.userIgnoreFilters;return excluded&&(items=items.filter(item=>!excluded.some(exclude=>item.file.path.startsWith(exclude)))),supportsVirtualChildren?items.reduce((res,item)=>(item.vChildren?._children?children=[...children,...item.vChildren._children]:res.push(item),res),[]).concat(children.length?this.getItems(children,app):children):items.reduce((res,item)=>(item.children?children=[...children,...item.children]:res.push(item),res),[]).concat(children.length?this.getItems(children,app):children)};fileExplorerFilter=function(fileExplorer,filter=this){let supportsVirtualChildren=(0,import_obsidian3.requireApiVersion)?.("0.15.0");if(!fileExplorer)return;let _children=supportsVirtualChildren?this.rootEl?.vChildren._children:this.rootEl?.children;if(!_children)return;if(this.filter?.length>=1){this.filtered||(this.rootEl._children=_children,this.filtered=!0);let options={includeScore:!0,includeMatches:!0,useExtendedSearch:!0,getFn:filter.getFn,threshold:.1,ignoreLocation:!0,keys:["file.path"]},flattenedItems2=filter.getItems(this.rootEl._children,fileExplorer.app),results=new Fuse(flattenedItems2,options).search(this.filter).slice(0,200);supportsVirtualChildren?this.rootEl.vChildren._children=filter.highlight(results):this.rootEl.children=filter.highlight(results);return}if(!(this.filter?.length<1&&this.filtered))return;this.rootEl._children&&(supportsVirtualChildren?this.rootEl.vChildren._children=this.rootEl._children:this.rootEl.children=this.rootEl._children),filter.getItems(this.rootEl._children,fileExplorer.app).map(match=>{match.innerEl.origContent&&(match.innerEl.setText(match.innerEl.origContent),delete match.innerEl.origContent,match.innerEl.removeClass("has-matches"))}),this.filtered=!1};highlight=(fuseSearchResult,highlightClassName="suggestion-highlight")=>{let set=(obj,path,value)=>{let pathValue=path.split("."),i;for(i=0;i<pathValue.length-1;i++)obj=obj[pathValue[i]];obj[pathValue[i]]=value},generateHighlightedText=(inputText,regions=[])=>regions.reduce((str,[start,end])=>(str[start]=`<span class="${highlightClassName}">${str[start]}`,str[end]=`${str[end]}</span>`,str),inputText.split("")).join("");return fuseSearchResult.filter(({matches:matches2})=>matches2&&matches2.length).map(({item,matches:matches2})=>{let highlightedItem={...item};return matches2.forEach(match=>{highlightedItem.innerEl.origContent||(highlightedItem.innerEl.origContent=highlightedItem.innerEl.textContent),set(highlightedItem,"innerEl.innerHTML",generateHighlightedText(match.value,match.indices)),highlightedItem.innerEl?.addClass("has-matches")}),highlightedItem})};getFn(obj,path){let removeExt=function(obj2){if(typeof obj2=="string"||obj2 instanceof String){let parts=obj2.split("/"),newObj=obj2;if(parts.length>=3){for(let i=1;i<parts.length-1;i+=2)parts[i]="\u2026";newObj=parts.join("/")}return newObj.replace(/.md$/,"")}return obj2},value=Fuse.config.getFn(obj,path);return Array.isArray(value)?value.map(el=>removeExt(el)):removeExt(value)}};var import_obsidian4=require("obsidian");var CustomSorter=class{plugin;settings;app;constructor(plugin){this.plugin=plugin,this.settings=plugin.settings,this.app=plugin.app}getRootFolders(fileExplorer){if(fileExplorer||(fileExplorer=this.plugin.getFileExplorer()),!fileExplorer)return;let root=fileExplorer.tree?.infinityScroll?.rootEl;return root&&this.traverseRoots(root)}traverseRoots(root,items){items||(items=[root]);let _children=(0,import_obsidian4.requireApiVersion)?.("0.15.0")?root.vChildren?._children:root.children;for(let child of _children||[])(child.children||child.vChildren?._children)&&items.push(child),this.traverseRoots(child,items);return items}toggleFileExplorerSorters(enable){let fileExplorer=this.plugin.getFileExplorer(),roots=this.getRootFolders(fileExplorer);if(roots?.length)for(let root of roots)root.sorter&&(root.sorter.option("sort",enable),root.sorter.option("disabled",!enable))}cleanupFileExplorerSorters(){let fileExplorer=this.plugin.getFileExplorer(),roots=this.getRootFolders(fileExplorer);if(roots?.length)for(let root of roots)root.sorter&&(root.sorter.destroy(),delete root.sorter,Object.keys(root.childrenEl).forEach(key=>key.startsWith("Sortable")&&delete root.childrenEl[key]),root.childrenEl.querySelectorAll("div.nav-file-title").forEach(el=>el.draggable=!0),root.childrenEl.querySelectorAll("div.nav-folder-title").forEach(el=>el.draggable=!0));delete fileExplorer.hasCustomSorter,this.app.vault.getConfig("fileSortOrder")==="custom"||this.settings.sortOrder==="custom"?(fileExplorer.setSortOrder("alphabetical"),this.settings.sortOrder="alphabetical"):fileExplorer.setSortOrder(this.settings.sortOrder)}setFileExplorerSorter(fileExplorer){if(fileExplorer||(fileExplorer=this.plugin.getFileExplorer()),!fileExplorer||this.settings.sortOrder!=="custom"||fileExplorer.hasCustomSorter)return;let roots=this.getRootFolders(fileExplorer);if(!(!roots||!roots.length))for(let root of roots){let el=root?.childrenEl;if(!el)continue;let draggedItems;fileExplorer.hasCustomSorter=!0;let dragEnabled=!!document.body.querySelector("div.nav-action-button.drag-to-rearrange")?.hasClass("is-active"),path=root.file?.path??"";root.sorter=sortable_esm_default.create(el,{group:`fileExplorer${path}`,forceFallback:!0,multiDrag:!0,multiDragKey:"alt",chosenClass:"bt-sortable-chosen",delay:0,disabled:!dragEnabled,sort:dragEnabled,animation:500,onStart:evt=>{draggedItems=evt.items.length?evt.items:[evt.item];for(let elem of draggedItems){let it=elem.querySelector(".tree-item-self");it&&(it.draggable=!1,it.classList.add("is-moved"))}},onMove:evt=>{let _children=(0,import_obsidian4.requireApiVersion)?.("0.15.0")?root.vChildren?._children:root.children;if(!_children||!draggedItems?.length)return;let children=_children.map(child=>child.el),adjacentEl=evt.related,targetIndex=children.indexOf(adjacentEl),firstItem=draggedItems.first(),firstItemIndex=children.indexOf(firstItem),_draggedItems=draggedItems.slice();firstItemIndex>targetIndex&&_draggedItems.reverse();for(let item of _draggedItems){let itemIndex=children.indexOf(item);_children=reorderArray(_children,itemIndex,targetIndex),children=reorderArray(children,itemIndex,targetIndex)}this.settings.fileExplorerOrder[path]=_children.map(child=>child.file.path),this.plugin.saveSettings()},onEnd:_evt=>{for(let elem of draggedItems){let it=elem.querySelector(".tree-item-self");it&&(it.draggable=!0,it.classList.remove("is-moved"))}draggedItems=[],document.querySelector("body>div.drag-ghost")?.detach()}})}}};var import_obsidian5=require("obsidian"),DEFAULT_SETTINGS={statusBarOrder:[],ribbonBarOrder:[],fileExplorerOrder:{},actionBarOrder:{},autoHide:!1,autoHideDelay:2e3,dragDelay:200,sortOrder:"alphabetical",useCollapse:!0},SettingTab=class extends import_obsidian5.PluginSettingTab{plugin;constructor(app,plugin){super(app,plugin),this.plugin=plugin}display(){let{containerEl}=this;containerEl.empty(),new import_obsidian5.Setting(containerEl).setName("Support collapse").setDesc("Add a button to collapse the ribbon and status bar items").addToggle(toggle=>toggle.setValue(this.plugin.settings.useCollapse).onChange(value=>{this.plugin.settings.useCollapse=value,this.plugin.saveSettings(),this.display()})),this.plugin.settings.useCollapse&&(new import_obsidian5.Setting(containerEl).setName("Auto Collapse").setDesc("Automatically hide ribbon and status bar items once your mouse leaves the icon container").addToggle(toggle=>toggle.setValue(this.plugin.settings.autoHide).onChange(value=>{this.plugin.settings.autoHide=value,this.plugin.saveSettings()})),new import_obsidian5.Setting(containerEl).setName("Auto Collapse Delay").setDesc("How long to wait before auto collapsing hidden icons on the ribbon and status bar").addText(textfield=>{textfield.setPlaceholder(String(2e3)),textfield.inputEl.type="number",textfield.setValue(String(this.plugin.settings.autoHideDelay)),textfield.onChange(async value=>{this.plugin.settings.autoHideDelay=Number(value),this.plugin.saveSettings()})})),new import_obsidian5.Setting(containerEl).setName("Drag Start Delay (ms)").setDesc("How long to wait before triggering the drag behavior after clicking. \u26A0\uFE0F Requires an app restart.").addText(textfield=>{textfield.setPlaceholder(String(200)),textfield.inputEl.type="number",textfield.setValue(String(this.plugin.settings.dragDelay)),textfield.onChange(async value=>{this.plugin.settings.dragDelay=Number(value),this.plugin.saveSettings()})})}};sortable_esm_default.mount(new MultiDragPlugin);var BartenderPlugin=class extends import_obsidian6.Plugin{settings;settingsTab;collapse;customFilter;customSorter;async onload(){await this.loadSettings(),this.collapse=new Collapse(this),this.customFilter=new CustomFilter(this),this.customSorter=new CustomSorter(this),console.log("Bartender loaded"),this.registerMonkeyPatches(),this.registerEventHandlers(),this.registerSettingsTab(),this.initialize(),this.app.vault.on("rename",async(file,oldFile)=>{for(let key in this.settings.fileExplorerOrder){for(let item of this.settings.fileExplorerOrder[key])if(item===oldFile){let index2=this.settings.fileExplorerOrder[key].indexOf(item);this.settings.fileExplorerOrder[key][index2]=file.path}key.startsWith(oldFile)&&(this.settings.fileExplorerOrder[file.path]=this.settings.fileExplorerOrder[key],delete this.settings.fileExplorerOrder[key])}await this.saveSettings()}),this.app.vault.on("delete",async file=>{for(let key in this.settings.fileExplorerOrder){for(let item of this.settings.fileExplorerOrder[key])if(item===file.path){let index2=this.settings.fileExplorerOrder[key].indexOf(item);this.settings.fileExplorerOrder[key].splice(index2,1)}key.startsWith(file.path)&&delete this.settings.fileExplorerOrder[key]}await this.saveSettings()})}async loadSettings(){this.settings=Object.assign({},DEFAULT_SETTINGS,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}patchFileExplorerFolder(){let plugin=this,leaf=plugin.app.workspace.getLeaf(!0),fileExplorer=plugin.app.viewRegistry.viewByType["file-explorer"](leaf);this.register(around(fileExplorer.constructor.prototype,{getSortedFolderItems:old=>function(...args){return plugin.settings.sortOrder==="custom"?folderSortV2.call(this,plugin.settings,...args):old.call(this,...args)}})),leaf.detach()}initialize(){this.app.workspace.onLayoutReady(()=>{this.patchFileExplorerFolder();let fileExplorer=this.getFileExplorer();this.customFilter.setFileExplorerFilter(fileExplorer),setTimeout(()=>{this.customSorter.setFileExplorerSorter(),this.settings.useCollapse&&(this.collapse.insertSeparator(RIBBON_BAR_SELECTOR,"side-dock-ribbon-action",!1),this.collapse.setRibbonBarSorter()),addSortButton(this,null,null,null,null),this.app.workspace.iterateRootLeaves(leaf=>{leaf?.view?.hasOwnProperty("actionsEl")&&!leaf?.view?.hasOwnProperty("iconSorter")&&(leaf.view.iconSorter=this.collapse.setViewActionSorter(leaf.view.actionsEl,leaf.view))})},import_obsidian6.Platform.isMobile?3e3:400)})}registerSettingsTab(){this.settingsTab=new SettingTab(this.app,this),this.addSettingTab(this.settingsTab)}registerEventHandlers(){this.registerEvent(this.app.workspace.on("file-explorer-draggable-change",value=>{this.customSorter.toggleFileExplorerSorters(value)})),this.registerEvent(this.app.workspace.on("file-explorer-sort-change",sortMethod=>{this.settings.sortOrder=sortMethod,this.saveSettings(),sortMethod==="custom"?setTimeout(()=>{this.customSorter.setFileExplorerSorter()},10):this.customSorter.cleanupFileExplorerSorters()})),this.registerEvent(this.app.workspace.on("file-explorer-load",fileExplorer=>{setTimeout(()=>{this.customSorter.setFileExplorerSorter()},1e3)})),this.registerEvent(this.app.workspace.on("bartender-leaf-split",(_originLeaf,newLeaf)=>{let element=newLeaf.tabsInnerEl;newLeaf.type==="tabs"&&newLeaf instanceof import_obsidian6.WorkspaceTabs&&import_obsidian6.requireApiVersion&&!(0,import_obsidian6.requireApiVersion)("0.15.3")&&this.collapse.setTabBarSorter(element,newLeaf)})),this.registerEvent(this.app.workspace.on("ribbon-bar-updated",()=>{setTimeout(()=>{this.settings.ribbonBarOrder&&this.collapse.ribbonBarSorter&&(this.collapse.setElementIDs(this.collapse.ribbonBarSorter.el,{useClass:!0,useAria:!0,useIcon:!0}),this.collapse.ribbonBarSorter.sort(this.settings.ribbonBarOrder))},0)})),this.registerEvent(this.app.workspace.on("status-bar-updated",()=>{setTimeout(()=>{this.settings.statusBarOrder&&this.collapse.statusBarSorter&&(this.collapse.setElementIDs(this.collapse.statusBarSorter.el,{useClass:!0,useIcon:!0}),this.collapse.statusBarSorter.sort(this.settings.statusBarOrder))},0)}))}registerMonkeyPatches(){let plugin=this;this.register(around(this.app.viewRegistry.constructor.prototype,{registerView(old){return function(type,viewCreator,...args){return plugin.app.workspace.trigger("view-registered",type,viewCreator),old.call(this,type,viewCreator,...args)}}})),this.app.workspace.layoutReady?this.patchFileExplorer():this.registerEvent(this.app.workspace.on("layout-ready",()=>{this.patchFileExplorer()})),this.register(around(import_obsidian6.View.prototype,{onunload(old){return function(...args){try{this.iconSorter&&(this.iconSorter.destroy(),this.iconSorter=null)}catch{}return old.call(this,...args)}},onload(old){return function(...args){return setTimeout(()=>{if(this.app.workspace.layoutReady)try{!(this.leaf.parentSplit instanceof import_obsidian6.WorkspaceTabs)&&this.hasOwnProperty("actionsEl")&&!this.iconSorter&&(this.iconSorter=plugin.collapse.setViewActionSorter(this.actionsEl,this))}catch{}},200),old.call(this,...args)}}})),import_obsidian6.Platform.isDesktop&&this.register(around(HTMLDivElement.prototype,{addEventListener(old){return function(type,listener,options){if(type==="mousedown"&&listener instanceof Function&&this.hasClass("workspace-tab-header")){let origListener=listener;listener=event=>{event instanceof MouseEvent&&(event?.altKey||event?.metaKey)||origListener(event)}}return old.call(this,type,listener,options)}}})),this.register(around(import_obsidian6.Workspace.prototype,{splitLeaf(old){return function(source,newLeaf,direction,before,...args){let result=old.call(this,source,newLeaf,direction,before,...args);return this.trigger("bartender-leaf-split",source,newLeaf),result}},changeLayout(old){return async function(workspace,...args){let result=await old.call(this,workspace,...args);return this.trigger("bartender-workspace-change"),result}}})),this.register(around(import_obsidian6.Plugin.prototype,{addStatusBarItem(old){return function(...args){let result=old.call(this,...args);return this.app.workspace.trigger("status-bar-updated"),result}},addRibbonIcon(old){return function(...args){let result=old.call(this,...args);return this.app.workspace.trigger("ribbon-bar-updated"),result}}}))}patchFileExplorer(fileExplorer){let plugin=this,customFilter=this.customFilter;fileExplorer||(fileExplorer=this.getFileExplorer());let InfinityScroll=fileExplorer.tree.infinityScroll.constructor;this.register(()=>this.customFilter.clearFileExplorerFilter()),this.register(around(InfinityScroll.prototype,{compute(old){return function(...args){try{this.scrollEl.hasClass("nav-files-container")&&plugin.customFilter.fileExplorerFilter.call(this,fileExplorer,customFilter,plugin.app)}catch(err){console.log(err)}return old.call(this,...args)}}})),this.register(around(fileExplorer.headerDom.constructor.prototype,{addSortButton(old){return function(...args){return this.navHeaderEl?.parentElement?.dataset?.type==="file-explorer"?(plugin.customFilter.setFileExplorerFilter(this),addSortButton.call(this,plugin,...args)):old.call(this,...args)}}}))}getFileExplorer(){return this.app.workspace.getLeavesOfType("file-explorer")?.first()?.view}onunload(){this.collapse.statusBarSorter?.destroy(),this.collapse.ribbonBarSorter?.destroy(),this.app.workspace.iterateAllLeaves(leaf2=>{let sorterParent;if((sorterParent=leaf2?.iconSorter?leaf2:!1)||(sorterParent=leaf2?.view?.iconSorter?leaf2.view:!1)||(sorterParent=leaf2?.parentSplit instanceof import_obsidian6.WorkspaceTabs&&leaf2?.parentSplit?.iconSorter?leaf2?.parentSplit:!1))try{sorterParent.iconSorter?.destroy()}finally{delete sorterParent.iconSorter}}),this.customSorter.cleanupFileExplorerSorters();let leaf=this.app.workspace.getLeavesOfType("file-explorer")?.first()?.view;if(leaf){let oldChild=leaf.containerEl.querySelector("div.nav-buttons-container")?.querySelectorAll("div.nav-action-button.custom-sort")||[];for(let el of oldChild)if(el.ariaLabel==="move"||el.ariaLabel==="arrow-up-narrow-wide"){let hiddenButton=leaf.containerEl.querySelector(`div.nav-buttons-container > div.nav-action-button.hide[aria-label="${el.ariaLabel}"]`);hiddenButton&&hiddenButton.removeClass("hide"),el.remove()}else el.remove();let filterEl=leaf.containerEl.querySelectorAll(".search-input-container.filter");for(let el of filterEl)el.remove()}}};
/*! Bundled license information:

sortablejs/modular/sortable.esm.js:
  (**!
   * Sortable 1.15.3
   * <AUTHOR>   <<EMAIL>>
   * <AUTHOR>    <<EMAIL>>
   * @license MIT
   *)
*/
