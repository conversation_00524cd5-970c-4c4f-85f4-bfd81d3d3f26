{"manifest": {"translationVersion": 1755607408073, "pluginVersion": "2.9.4"}, "description": {"original": "A ChatGPT Copilot in Obsidian.", "translation": "Obsidian 中的 ChatGPT Copilot。"}, "dict": {"Notice(\"Local Copilot index cleared successfully.\")": "Notice(\"本地 Copilot 索引已成功清除。\")", "Notice(\"No valid content to index.\")": "Notice(\"没有有效内容可供索引。\")", "Notice(\"Error executing search. Check console for details.\")": "Notice(\"执行搜索时出错。请检查控制台以获取详细信息。\")", "Notice(\"Error searching database. Check console for details.\")": "Notice(\"搜索数据库时出错。请检查控制台以获取详细信息。\")", "Notice(\"An error occurred.\")": "Notice(\"发生了一个错误。\")", "Notice(\"No messages to save.\")": "Notice(\"没有要保存的消息。\")", "Notice(\"Cannot regenerate the first message or a user message.\")": "Notice(\"无法重新生成第一条消息或用户消息。\")", "Notice(\"Plugin reloaded successfully.\")": "Notice(\"插件已成功重新加载。\")", ".log(\"Using RetryOperation.try()": ".log(\"Using RetryOperation.try()", ".log(\"Using RetryOperation.start()": ".log(\"Using RetryOperation.start()", ".log(\"Copilot Plus: Initializing event listeners\")": ".log(\"Copilot Plus：初始化事件监听器\")", "\"No relevant notes found\"": "\"未找到相关笔记\"", "Notice(\"No active leaf found.\")": "Notice(\"未找到活动的标签页。\")", "Notice(\"Failed to open a markdown view.\")": "Notice(\"打开 markdown 视图失败。\")", "Notice(\"Message inserted into the active note.\")": "Notice(\"消息已插入当前笔记。\")", "Notice(\"Failed to initialize Copilot database. Some features may be limited.\")": "Notice(\"初始化 Copilot 数据库失败。部分功能可能受限。\")", "Notice(\"An error occurred while clearing the local Copilot index.\")": "Notice(\"清除本地 Copilot 索引时出错。\")", "Notice(\"New embedding model detected. Rebuilding Copilot index from scratch.\")": "Notice(\"检测到新的嵌入模型。正在从头开始重建 Copilot 索引。\")", "Notice(\"Copilot vault index is up-to-date.\")": "Notice(\"Copilot 库索引已是最新。\")", "Notice(\"No files to index with current filters\")": "Notice(\"当前筛选条件下没有文件可供索引。\")", "Notice(\"Indexing completed successfully!\")": "Notice(\"索引成功完成！\")", "Notice(\"Indexing is disabled on mobile devices\")": "Notice(\"在移动设备上已禁用索引。\")", "Notice(\"An error occurred while executing the tool. Check console for details.\")": "Notice(\"执行工具时出错。请检查控制台以获取详细信息。\")", "Notice(\"Database not found\")": "Notice(\"未找到数据库。\")", "Notice(\"No valid note paths found. Use format: - [[Note Name]]\")": "Notice(\"未找到有效的笔记路径。请使用格式： - [[笔记名称]]\")", "Notice(\"An error occurred while counting tokens.\")": "Notice(\"计算标记时出错。\")", "Notice(\"An error occurred while garbage collecting the Copilot index.\")": "Notice(\"回收 Copilot 索引时出错。\")", "Notice(\"An error occurred while indexing vault to Copilot index.\")": "Notice(\"将库文件索引到 Copilot 索引时出错。\")", "Notice(\"An error occurred while re-indexing vault to Copilot index.\")": "Notice(\"重新索引库文件到 Copilot 索引时出错。\")", "Notice(\"No files found to list.\")": "Notice(\"未找到要列出的文件。\")", "Notice(\"Failed to list indexed files.\")": "Notice(\"列出索引文件失败。\")", "Notice(\"An error occurred while removing files from the index.\")": "Notice(\"从索引中删除文件时出错。\")", "Notice(\"No active file\")": "Notice(\"无活动文件。\")", "Notice(\"No active note found.\")": "Notice(\"未找到活动的笔记。\")", "Notice(\"Vault index refreshed.\")": "Notice(\"库索引已刷新。\")", "Notice(\"Failed to refresh vault index. Check console for details.\")": "Notice(\"刷新库索引失败。请检查控制台以获取详细信息。\")", "Notice(\"Please fill in all required fields\")": "Notice(\"请填写所有必填字段。\")", "Notice(\"Failed to save chat as note. Check console for details.\")": "Notice(\"将对话另存为笔记失败。请检查控制台以获取详细信息。\")", "Notice(\"Failed to regenerate message. Please try again.\")": "Notice(\"重新生成消息失败。请重试。\")", "Notice(\"API key verified successfully!\")": "Notice(\"API 密钥验证成功！\")", "Notice(\"Model verification successful!\")": "Notice(\"模型验证成功！\")", "Notice(\"Failed to reload the plugin. Please reload manually.\")": "Notice(\"重新加载插件失败。请手动重新加载。\")", "Notice(\"Copied to clipboard\")": "Notice(\"已复制到剪贴板。\")", "Notice(\"No chat history found.\")": "Notice(\"未找到对话记录。\")", ".log(\"New LLM chain created.\")": ".log(\"已创建新的 LLM 链。\")", ".log(\"Failed to parse error response as JSON\")": ".log(\"解析 JSON 格式的错误响应失败。\")", ".log(\"Getting text from response\")": ".log(\"正在从响应中获取文本。\")", ".log(\"Failed to get text from error response\")": ".log(\"从错误响应中获取文本失败。\")", ".log(\"[WARNING]: Received non-string content from OpenAI. This is currently not supported.\")": ".log(\"[警告]：从 OpenAI 收到了非字符串内容。目前不支持此操作。\")", ".log(\"First ping attempt failed, trying with CORS...\")": ".log(\"首次 ping 尝试失败，正在尝试使用 CORS…\")", ".log(\"Saved empty database state\")": ".log(\"已保存空的数据库状态。\")", ".log(\"Saved all partitions\")": ".log(\"已保存所有分区。\")", ".log(\"Copilot index checkpoint save completed.\")": ".log(\"Copilot索引检查点保存已完成。\")", ".log(\"No files to index after filter change, stopping indexing\")": ".log(\"更改筛选器后没有要索引的文件，停止索引\")", ".log(\"Indexing cancelled by user\")": ".log(\"用户已取消索引\")", ".log(\"*** HYBRID RETRIEVER DEBUG INFO: ***\")": ".log(\"***混合检索器调试信息：***\")", ".log(\"Failed to reinitialize model due to missing API key\")": ".log(\"由于缺少API密钥，重新初始化模型失败\")", ".log(\"Clearing chat memory\")": ".log(\"正在清除聊天记忆\")", ".log(\"New Vault QA chain with hybrid retriever created for entire vault\")": ".log(\"已为整个仓库创建带有混合检索器的新Vault QA链\")", ".log(\"Message regenerated successfully\")": ".log(\"消息重新生成成功\")", ".error(\"Embedding instance not found.\")": ".error(\"未找到嵌入模型实例。\")", ".error(\"setChain failed: No chat model set.\")": ".error(\"set<PERSON><PERSON>n失败：未设置聊天模型。\")", ".error(\"App instance is not available.\")": ".error(\"应用实例不可用。\")", ".setButtonText(\"Remove\")": ".setButtonText(\"移除\")", ".setName(\"File paths\")": ".set<PERSON>ame(\"文件路径\")", ".appendText(\"Tip: turn on debug mode to show the processed prompt in the chat window.\")": ".appendText(\"提示：打开调试模式，在对话窗口中显示已处理的提示。\")", "\"New Chat\"": "\"新对话\"", "\"Relevance is a combination of semantic similarity and links.\"": "\"关联是语义相似性和链接的结合。\"", "Notice(\"Plus-only model, please consider upgrading to Plus to access it.\")": "Notice(\"此为 Plus 会员专属模型，请升级到 Plus 以使用。\")", "Notice(\"Believer-only model, please consider upgrading to Believer to access it.\")": "Notice(\"此为 Believer 会员专属模型，请升级到 Believer 以使用。\")", "Notice(\"Connection successful, but requires CORS to be enabled. Please enable CORS for this model once you add it above.\")": "Notice(\"连接成功，但需要启用 CORS。请在上方添加此模型后为其启用 CORS。\")", "Notice(\"Vault is too large for 1 partition, please increase the number of partitions in your Copilot QA settings!\",1e4)": "Notice(\"库对于 1 个分区来说太大了，请在 Copilot QA 设置中增加分区数量！\",1e4)", "Notice(\"Indexing cancelled\")": "Notice(\"索引已取消。\")", "Notice(`Indexing completed with ${e.length} errors. Check console for details.`)": "Notice(`索引完成，有 ${e.length} 个错误。请检查控制台以获取详细信息。`)", "Notice(\"Failed to initialize vector store. Please make sure you have a valid API key for your embedding model and restart the plugin.\")": "Notice(\"初始化向量存储失败。请确保您的嵌入模型有有效的 API 密钥并重启插件。\")", "Notice(\"Failed to update Copilot index. Please try force reindexing from the command palette.\")": "Notice(\"更新 Copilot 索引失败。请尝试从命令面板强制重建索引。\")", "Notice(\"Copilot Plus license key not found. Please enter your license key in the settings.\")": "Notice(\"未找到 Copilot Plus 许可证密钥。请在设置中输入您的许可证密钥。\")", "Notice(`Failed to process images:\n${e.join(`\n`)": "Notice(`处理图像失败:\n${e.join(`\n`)", "Notice(`\\u26A0\\uFE0F Rate limit exceeded for document processing. Please try again in ${n}. Having fewer non-markdown files in the project will help.`,1e4)": "Notice(`\\u26A0\\uFE0F 文档处理超出速率限制。请在 ${n} 后重试。项目中非 markdown 文件越少越好。`,1e4)", "Notice(`Pomodoro timer (${t})": "Notice(`番茄钟 (${t})", "Notice(`Error creating model: ${r}`)": "Notice(`创建模型时出错：${r}`)", "Notice(\"No model key found\")": "Notice(\"未找到模型密钥。\")", "Notice(`Model ${a.name} is not available in project mode. Switching to ${u.name}.`)": "Notice(`模型 ${a.name} 在项目模式下不可用。正在切换到 ${u.name}。`)", "Notice(`Failed to process web URLs: ${ei(r)": "Notice(`处理网络 URL 失败：${ei(r)", "Notice(`Failed to process YouTube URL ${i}: ${ei(a)": "Notice(`处理 YouTube URL 失败：${i}: ${ei(a)", "Notice(`Selected text contains ${i} words and ${a} tokens.`)": "Notice(`所选文本包含 ${i} 个单词和 ${a} 个标记。`)", "Notice(`Total tokens in your vault: ${o}`)": "Notice(`您的库中的总标记数：${o}`)", "Notice(`${n} documents removed from Copilot index.`)": "Notice(`已从 Copilot 索引中删除 ${n} 个文档。`)", "Notice(`${n} vault files indexed to Copilot index.`)": "Notice(`已将 ${n} 个库文件索引到 Copilot 索引。`)", "Notice(`${n} vault files re-indexed to Copilot index.`)": "Notice(`已将 ${n} 个库文件重新索引到 Copilot 索引。`)", "Notice(`Listed ${n.length} indexed files`)": "Notice(`已列出 ${n.length} 个索引文件。`)", "Notice(`Successfully removed ${n.length} files from the index.`)": "Notice(`已成功从索引中删除 ${n.length} 个文件。`)", "Notice(\"All Copilot caches cleared successfully\")": "Notice(\"所有 Copilot 缓存已成功清除。\")", "Notice(\"Failed to clear Copilot caches\")": "Notice(\"清除 Copilot 缓存失败。\")", "Notice(`Copilot autocomplete ${o?\"enabled\":\"disabled\"}`)": "Notice(`Copilot 自动补全已${o?\"启用\":\"禁用\"}`)", "Notice(\"Selected text context is only available in Copilot Plus and Project modes\")": "Notice(\"所选文本上下文仅在 Copilot Plus 和项目模式下可用。\")", "Notice(\"No text selected\")": "Notice(\"未选择文本。\")", "Notice(\"Could not determine selection range\")": "Notice(\"无法确定选择范围。\")", "Notice(\"Vault force reindexed.\")": "Notice(\"库已强制重新索引。\")", "Notice(\"Failed to force reindex vault. Check console for details.\")": "Notice(\"强制重新索引库失败。请检查控制台以获取详细信息。\")", "Notice(\"No project is currently selected to reload.\")": "Notice(\"当前未选择要重新加载的项目。\")", "Notice(`Project context for \"${t.name}\" reloaded successfully.`)": "Notice(`\"${t.name}\"的项目上下文重新加载成功。`)", "Notice(\"Failed to reload project context. Check console for details.\")": "Notice(\"重新加载项目上下文失败。请检查控制台以获取详细信息。\")", "Notice(\"No project is currently selected to rebuild.\")": "Notice(\"当前未选择要重建的项目。\")", "Notice(`Force rebuilding context for project: ${t.name}... This will take some time and re-fetch all data.`,1e4)": "Notice(`正在为项目强制重建上下文：${t.name}……这将需要一些时间并重新获取所有数据。`,1e4)", "Notice(`Cache for project \"${t.name}\" has been cleared.`)": "Notice(`已清除项目\"${t.name}\"的缓存。`)", "Notice(`Project context for \"${t.name}\" rebuilt successfully from scratch.`)": "Notice(`\"${t.name}\"的项目上下文已成功从头重建。`)", "Notice(\"Failed to force rebuild project context. Check console for details.\")": "Notice(\"强制重建项目上下文失败。请检查控制台以获取详细信息。\")", "Notice(`Error applying changes: ${p.message}`)": "Notice(`应用更改时出错：${p.message}`)", "Notice(\"File not found:\"+e.path)": "Notice(\"未找到文件：\"+e.path)", "Notice(\"Changes applied successfully\")": "Notice(\"更改已成功应用。\")", "Notice(\"Content copied to clipboard\")": "Notice(\"内容已复制到剪贴板。\")", "Notice(`Failed to copy: ${i.message}`)": "Notice(`复制失败：${i.message}`)", "Notice(`Created new file: ${t}`)": "Notice(`已创建新文件：${t}`)", "Notice(`Failed to create file: ${t}`)": "Notice(`创建文件失败：${t}`)", "Notice(`Failed to create file: ${l.message}`)": "Notice(`创建文件失败：${l.message}`)", "Notice(`Path is not a file: ${t}`)": "Notice(`路径不是文件：${t}`)", "Notice(`Switched to ${i.name}`)": "Notice(`已切换到 ${i.name}`)", "Notice(`Error processing code: ${i.message}`)": "Notice(`处理代码时出错：${i.message}`)", "Notice(`Reindexed ${u.name}`)": "Notice(`已重新索引 ${u.name}`)", "Notice(\"Adding extension is temporarily not supported.\")": "Notice(\"暂时不支持添加扩展。\")", "Notice(`Project \"${A.name}\" deleted successfully`)": "Notice(`项目\"${A.name}\"已成功删除。`)", "Notice(`Chat saved as note: ${ta}`)": "Notice(`已另存为笔记：${ta}`)", "Notice(`${te.name} added and context loaded`)": "Notice(`${te.name} 已添加且上下文已加载`)", "Notice(`${te.name} added but context loading failed`)": "Notice(`${te.name} 已添加但上下文加载失败`)", "Notice(`${te.name} added successfully`)": "Notice(`${te.name} 已成功添加`)", "Notice(`${te.name} updated and context reloaded`)": "Notice(`${te.name} 已更新且上下文已重新加载`)", "Notice(`${te.name} updated but context reload failed`)": "Notice(`${te.name} 已更新但上下文重新加载失败`)", "Notice(`${te.name} updated successfully`)": "Notice(`${te.name} 已成功更新`)", "Notice(`Command \"${r.trim()": "Notice(`Command \"${r.trim()", "Notice(\"Failed to create command. Please try again.\")": "Notice(\"创建命令失败。请重试。\")", "Notice(`Command \"${p.title}\" deleted successfully!`)": "Notice(`命令 \"${p.title}\" 已成功删除！`)", "Notice(\"Failed to delete command. Please try again.\")": "Notice(\"删除命令失败。请重试。\")", "Notice(\"API key verification failed: No default test model found for the selected provider.\",1e4)": "Notice(\"API 密钥验证失败：未找到所选提供商的默认测试模型。\",1e4)", "Notice(\"API key verification failed: \"+ei(N)": "Notice(\"API密钥验证失败：\"+ei(N)", "Notice(`Failed to load models for ${kf(I)": "Notice(`加载模型失败 ${kf(I)", "Notice(\"Please select a model first\")": "Notice(\"请先选择一个模型。\")", "Notice(`Model ${m.name} verified successfully! It already exists in your models list.`)": "Notice(`模型 ${m.name} 验证成功！它已存在于您的模型列表中。`)", "Notice(`Model ${m.name} verified successfully and added to your models list!`)": "Notice(`模型 ${m.name} 验证成功并已添加到您的模型列表中！`)", "Notice(\"Model verification failed: \"+ei(I)": "Notice(\"模型验证失败：\"+ei(I)", "Notice(`Error: Missing required variables: ${d.join(\", \")": "Notice(`错误： 缺少所需变量： ${d.join(\", \")", "Notice('Error: Format contains illegal characters (\\\\/:*?\"<>|)": "Notice('错误： 格式包含非法字符s (\\\\/:*?\"<>|)", "Notice(`Format applied successfully! Example: ${b}`,4e3)": "Notice(`格式应用成功！示例：${b}`,4e3)", "Notice(`Error applying format: ${l.message}`,4e3)": "Notice(`应用格式时出错：${l.message}`,4e3)", "Notice(`Autocomplete accept key set to: ${u}`)": "Notice(`自动补全接受键已设置为：${u}`)", "Notice(`Autocomplete accept key reset to: ${Nh.KEYBIND}`)": "Notice(`自动补全接受键已重置为：${Nh.KEYBIND}`)", "Notice(\"Rebuilding word index...\")": "Notice(\"正在重建单词索引…\")", "Notice(`Word index complete! Found ${c.foundWords} words from ${c.processedFiles} files.`)": "Notice(`单词索引完成！从 ${c.processedFiles} 个文件中找到 ${c.foundWords} 个单词。`)", "Notice(`Word index rebuilt successfully! ${l.wordCount} unique words indexed.`)": "Notice(`单词索引重建成功！已索引 ${l.wordCount} 个独立单词。`)", "Notice(\"Failed to refresh word index. Check console for details.\")": "Notice(\"刷新单词索引失败。请检查控制台以获取详细信息。\")", "Notice(\"Model verification failed: \"+B)": "Notice(\"模型验证失败：\"+B)", "Notice(\"Could not find model to update\")": "Notice(\"找不到要更新的模型。\")", "Notice(\"Chat models refreshed successfully\")": "Notice(\"对话模型已成功刷新。\")", "Notice(\"Embedding models refreshed successfully\")": "Notice(\"嵌入模型已成功刷新。\")", "Notice(\"Chat engine not ready. Please try again.\")": "Notice(\"对话引擎未准备好。请重试。\")", ".log(`Dataset ${l} already exists in your tenant. Skipping.`)": ".log(`数据集 ${l} 已存在于您的租户中。正在跳过。`)", ".log(\"Input Question: \",b.question)": ".log(\"输入问题：\",b.question)", ".log(\"Formatted Chat History: \",g)": ".log(\"格式化的对话记录：\",g)", ".log(\"Standalone Question: \",g)": ".log(\"独立问题：\",g)", ".log(`OpenAI:DEBUG:${t}`,...r)": ".log(`OpenAI:调试:${t}`,...r)", ".log(`Total documents to distribute: ${e.length}`)": ".log(`要分发的文档总数：${e.length}`)", ".log(`Partition ${s+1}: ${a.length} documents`)": ".log(`分区 ${s+1}：${a.length} 个文档`)", ".log(`Total documents distributed: ${i}`)": ".log(`分发的文档总数：${i}`)", ".log(`Starting save with ${i.length??0} total documents`)": ".log(`开始保存，共有 ${i.length??0} 个文档`)", ".log(`Saved partition ${l+1}/${n}`)": ".log(`已保存分区 ${l+1}/${n}`)", ".log(`Detected vector length: ${r.length} for model: ${Xs.getModelName(t)": ".log(`检测到模型的向量长度：${r.length}： ${Xs.getModelName(t)", ".log(\"Copilot Plus: Triggering reindex for file \",e.path)": ".log(\"Copilot Plus：正在为文件触发重新索引 \",e.path)", ".log(\"Total files to index:\",this.state.totalFilesToIndex)": ".log(\"要索引的文件总数：\",this.state.totalFilesToIndex)", ".log(\"Files to index:\",e)": ".log(\"要索引的文件：\",e)", ".log(`Reindexed file: ${e.path}`)": ".log(`重新索引的文件：${e.path}`)", ".log(\"Failed to log request:\",s)": ".log(\"未能记录请求：\",s)", ".log(\"Failed to log response:\",s)": ".log(\"未能记录响应：\",s)", ".log(\"Status Code:\",e.status,e.statusText)": ".log(\"状态代码：\",e.status,e.statusText)", ".log(\"No chunks found for query:\",r)": ".log(\"未找到用于查询的块：\",r)", ".log(`\nOriginal Query: `,r)": ".log(`\n原始查询: `,r)", ".log(\"Rewritten Query: \",u)": ".log(\"重写后的查询: \",u)", ".log(`\nExplicit Chunks: `,s)": ".log(`\n显式数据块: `,s)", ".log(\"Orama Chunks: \",l)": ".log(\"Orama数据块: \",l)", ".log(\"Combined Chunks: \",c)": ".log(\"合并后的数据块: \",c)", ".log(\"Max Orama Score: \",f)": ".log(\"最大Orama分数: \",f)", ".log(\"Reranked Chunks: \",d)": ".log(\"重新排序的数据块: \",d)", ".log(\"No reranking applied.\")": ".log(\"未应用重新排序。\")", ".log(\"Tag only query detected, setting textWeight to 1 and vectorWeight to 0.\")": ".log(\"检测到仅标签查询，将textWeight设为1，vectorWeight设为0。\")", ".log(`==== Orama Search Params: ====\n`,s)": ".log(`==== Orama搜索参数: ====\n`,s)", ".log(`${this.constructor.name} merged ${u} and ${l} into ${c}`)": ".log(`${this.constructor.name} 将 ${u} 和 ${l} 合并为 ${c}`)", ".log(`Extracting timezone: '${s}' into: ${c} for: ${o.start}`)": ".log(`将时区 '${s}' 提取到: ${c} 中，用于: ${o.start}`)", ".log(`Extracting timezone: '${i[0]}' into : ${n}`)": ".log(`将时区 '${i[0]}' 提取到: ${n}`)", ".log(`${this.constructor.name} remove ${u} by ${s}`)": ".log(`${this.constructor.name} 通过 ${s} 移除 ${u}`)", ".log(`${this.constructor.name} adjusted ${n} time result (${n.start})": ".log(`${this.constructor.name} 调整了 ${n} 时间结果 (${n.start})", ".log(`${this.constructor.name} adjusted ${n} weekday (${n.start})": ".log(`${this.constructor.name} 调整了 ${n} 工作日 (${n.start})", ".log(`${this.constructor.name} adjusted ${n} weekday (${n.end})": ".log(`${this.constructor.name} 调整了 ${n} 工作日 (${n.end})", ".log(`${this.constructor.name} adjusted ${n} year (${n.start})": ".log(`${this.constructor.name} 调整了 ${n} 年份 (${n.start})", ".log(`${this.constructor.name} adjusted ${n} month (${n.start})": ".log(`${this.constructor.name} 调整了 ${n} 月份 (${n.start})", ".log(`Removing unlikely result '${r.text}'`)": ".log(`移除不太可能的结果 '${r.text}'`)", ".log(`Removing invalid result: ${r} (${r.end})": ".log(`移除无效结果: ${r} (${r.end})", ".log(`Removing invalid result: ${r} (${r.start})": ".log(`移除无效结果: ${r} (${r.start})", ".log(`(Strict)": ".log(`(严格模式)", ".log(`Extracting year: '${i[0]}' into : ${n}`)": ".log(`将年份 '${i[0]}' 提取到: ${n}`)", ".log(`${r.constructor.name} extracted (at index=${d})": ".log(`${r.constructor.name} 提取于 (索引=${d})", ".log(`Anthropic:DEBUG:${t}`,...e)": ".log(`Anthropic:调试:${t}`,...e)", ".log(`Groq:DEBUG:${t}`,...e)": ".log(`Groq:调试:${t}`,...e)", ".log(\"Memory initialized with context turns:\",r)": ".log(\"内存已初始化，上下文轮次:\",r)", ".log(\"Loaded memory variables:\",e)": ".log(\"已加载内存变量:\",e)", ".log(\"Saving to memory - Input:\",e,\"Output:\",r)": ".log(\"保存到内存 - 输入:\",e,\"输出:\",r)", ".log(\"Set chain:\",\"vault_qa\")": ".log(\"设置链:\",\"vault_qa\")", ".log(`==== Step 0: Initial user message ====\n`,e)": ".log(`==== 步骤0: 初始用户消息 ====\n`,e)", ".log(\"No hits found for note:\",t)": ".log(\"未找到笔记的匹配项:\",t)", ".log(\"No embedding found for note:\",t)": ".log(\"未找到笔记的嵌入:\",t)", ".log(\"No embeddings found for note:\",e)": ".log(\"未找到笔记的嵌入:\",e)", ".log(`Skipping note ${d.path} as it was included via custom prompt.`)": ".log(`跳过笔记 ${d.path}，因为它已通过自定义提示包含。`)", ".log(`Processing note: ${d.path}, extension: ${d.extension}, chain: ${s}`)": ".log(`正在处理笔记: ${d.path}, 扩展名: ${d.extension}, 链: ${s}`)", ".log(`stopping generation..., reason: ${te}`)": ".log(`停止生成..., 原因: ${te}`)", ".log(\"First fetch attempt failed, trying with safeFetch...\")": ".log(\"首次获取尝试失败，正在尝试使用safeFetch...\")", ".error(\"SEMVER\",...t)": ".error(\"语义化版本(SEMVER)\",...t)", ".error(`An error occurred while creating dataset ${l}. You should delete it manually.`)": ".error(`创建数据集 ${l} 时发生错误，请手动删除。`)", ".error(`Error in postRun for run ${this.id}:`,r)": ".error(`运行 ${this.id} 的 postRun 过程中出错：`,r)", ".error(`Error in patchRun for run ${this.id}`,e)": ".error(`运行 ${this.id} 的 patchRun 过程中出错：`,e)", ".error(\"This browser lacks typed array (Uint8Array)": ".error(\"此浏览器不支持类型化数组 (Uint8Array)", ".error(\"Encryption failed:\",e)": ".error(\"加密失败：\",e)", ".error(\"Decryption failed:\",r)": ".error(\"解密失败：\",r)", ".error(\"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\")": ".error(\"forceFrameRate 需要0到125之间的正整数，不支持强制设置高于125 fps的帧率\")", ".error(`Unable to parse ${JSON.stringify(t)": ".error(`无法解析 ${JSON.stringify(t)", ".error(`Failed to construct URL with ${r}`,n)": ".error(`使用 ${r} 构建URL失败：`,n)", ".error(\"Could not parse message into JSON:\",d.data)": ".error(\"无法将消息解析为JSON：\",d.data)", ".error(\"From chunk:\",d.raw)": ".error(\"来自数据块：\",d.raw)", ".error(new ef(\"Failed to parse stream\")": ".error(new ef(\"解析数据流失败\")", ".error(new ef(`Error parsing JSON response: \"${u[1]}\"`)": ".error(new ef(`解析JSON响应错误：\"${u[1]}\"`)", ".error(\"Could not parse message into JSON:\",a.data)": ".error(\"无法将消息解析为JSON：\",a.data)", ".error(\"From chunk:\",a.raw)": ".error(\"来自数据块：\",a.raw)", ".error(\"Error parsing function arguments\",f,JSON.stringify(d.additional_kwargs.function_call)": ".error(\"解析函数参数错误\",f,JSON.stringify(d.additional_kwargs.function_call)", ".error(\"Caught error in getAllPluginsByHook:\",i)": ".error(\"在getAllPluginsByHook中捕获错误：\",i)", ".error(`Document count mismatch! Original: ${e.length}, Distributed: ${i}`)": ".error(`文档数量不匹配！原始：${e.length}，分发：${i}`)", ".error(\"Error saving database:\",r)": ".error(\"保存数据库错误：\",r)", ".error(\"Error loading database:\",e)": ".error(\"加载数据库错误：\",e)", ".error(\"Error clearing storage:\",e)": ".error(\"清除存储错误：\",e)", ".error(\"Error getting vector length:\",e)": ".error(\"获取向量长度错误：\",e)", ".error(\"Batch processing error:\",{error:e,batchSize:r.batch.length||0,firstChunk:r.batch[0]?{path:r.batch[0].fileInfo?.path,contentLength:r.batch[0].content?.length,hasFileInfo:!!r.batch[0].fileInfo}:\"No chunks in batch\",errorType:e?.constructor?.name,errorMessage:e?.message})": ".error(\"批处理错误：\",{error:e,batchSize:r.batch.length||0,firstChunk:r.batch[0]?{path:r.batch[0].fileInfo?.path,contentLength:r.batch[0].content?.length,hasFileInfo:!!r.batch[0].fileInfo}:\"批次中无数据块\",errorType:e?.constructor?.name,errorMessage:e?.message})", ".error(`Error indexing file ${n}:`,e)": ".error(`索引文件 ${n} 错误：`,e)", ".error(\"Fatal error during indexing:\",e)": ".error(\"索引过程中发生致命错误：\",e)", ".error(\"Failed to initialize vector store:\",r)": ".error(\"初始化向量存储失败：\",r)", ".error(\"Failed to initialize vector store:\",e)": ".error(\"初始化向量存储失败：\",e)", ".error(`Error processing URL ${e}:`,r)": ".error(`处理URL ${e} 错误：`,r)", ".error(\"Error in rewriteQuery:\",n)": ".error(\"重写查询错误：\",n)", ".error(\"Error in convertQueryToVector, please ensure your embedding model is working and has an adequate context length:\",l,`\nQuery:`,r)": ".error(\"转换查询到向量错误，请确保您的嵌入模型正常工作且有足够的上下文长度：\",l,`\n查询内容：`,r)", ".error(\"Error calling tool:\",n)": ".error(\"调用工具错误：\",n)", ".error(\"Error indexing vault:\",t)": ".error(\"索引仓库错误：\",t)", ".error(`Error processing web search query ${t}:`,r)": ".error(`处理网络搜索查询 ${t} 错误：`,r)", ".error(`Error transcribing YouTube video ${t}:`,e)": ".error(`转录YouTube视频 ${t} 错误：`,e)", ".error(\"Error in intent analysis:\",r)": ".error(\"意图分析错误：\",r)", ".error(\"Could not parse message into JSON:\",u.data)": ".error(\"无法将消息解析为JSON：\",u.data)", ".error(\"From chunk:\",u.raw)": ".error(\"来自数据块：\",u.raw)", ".error(\"Chain is not initialized properly, re-initializing chain: \",Fh()": ".error(\"链未正确初始化，正在重新初始化链：\",Fh()", ".error(\"Resetting default model. No model configuration found for: \",i)": ".error(\"重置默认模型。未找到模型配置：\",i)", ".error(\"Error in debug search:\",i)": ".error(\"调试搜索错误：\",i)", ".error(\"Error searching DB:\",a)": ".error(\"搜索数据库错误：\",a)", ".error(\"Error counting tokens: \",n)": ".error(\"计算token错误：\",n)", ".error(\"Error garbage collecting the Copilot index:\",n)": ".error(\"清理Copilot索引垃圾错误：\",n)", ".error(\"Error indexing vault to Copilot index:\",n)": ".error(\"将仓库索引到Copilot索引错误：\",n)", ".error(\"Error re-indexing vault to Copilot index:\",n)": ".error(\"重新将仓库索引到Copilot索引错误：\",n)", ".error(\"Error listing indexed files:\",n)": ".error(\"列出已索引文件错误：\",n)", ".error(\"Error removing files from index:\",i)": ".error(\"从索引中移除文件错误：\",i)", ".error(\"Error clearing Copilot caches:\",n)": ".error(\"清除Copilot缓存错误：\",n)", ".error(\"aria-hidden\",r,\"in not contained inside\",t,\". Doing nothing\")": ".error(\"aria-hidden\",r,\"未包含在\",t,\"中。不执行任何操作\")", ".error(\"aria-hidden: cannot operate on \",f,g)": ".error(\"aria-hidden: 无法操作\",f,g)", ".error(\"Error refreshing vault index:\",t)": ".error(\"刷新仓库索引错误：\",t)", ".error(\"Error force reindexing vault:\",t)": ".error(\"强制重新索引仓库错误：\",t)", ".error(`Error processing embedded PDF ${s}:`,l)": ".error(`处理嵌入式PDF ${s} 错误：`,l)", ".error(`Error processing file ${d.path}:`,p)": ".error(`处理文件 ${d.path} 错误：`,p)", ".error(\"Failed to parse composer JSON:\",D)": ".error(\"解析composer <PERSON><PERSON><PERSON>失败：\",D)", ".error(\"Error saving chat as note:\",ei(Pe)": ".error(\"将会话保存为笔记错误：\",ei(Pe)", ".error(\"Error regenerating message:\",oe)": ".error(\"重新生成消息错误：\",oe)", ".error(\"Error loading project context:\",se)": ".error(\"加载项目上下文错误：\",se)", ".error(\"Error reloading project context:\",ce)": ".error(\"重新加载项目上下文错误：\",ce)", ".error(\"Failed to create command:\",p)": ".error(\"创建命令失败：\",p)", ".error(\"Failed to delete command:\",m)": ".error(\"删除命令失败：\",m)", ".error(`Error parsing ${t} model data:`,n)": ".error(`解析${t}模型数据错误：`,n)", ".error(\"API key verification failed:\",N)": ".error(\"API密钥验证失败：\",N)", ".error(`Error fetching models for ${I}:`,N)": ".error(`获取${I}的模型错误：`,N)", ".error(\"Model verification failed:\",I)": ".error(\"模型验证失败：\",I)", ".error(\"Failed to refresh word index:\",u)": ".error(\"刷新单词索引失败：\",u)", ".error(\"Error reloading plugin:\",e)": ".error(\"重新加载插件错误：\",e)", ".error(\"Chat chain not initialized\")": ".error(\"聊天链未初始化\")", "description:\"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native\"": "description:\"AWS SDK for JavaScript 的 Cognito 身份客户端，适用于 Node.js、浏览器和 React Native\"", "description:\"AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native\"": "description:\"AWS SDK for JavaScript 的 STS 客户端，适用于 Node.js、浏览器和 React Native\"", "description:\"Get the file tree as a nested structure of folders and files\"": "description:\"以嵌套结构获取文件夹和文件的树形目录\"", "description:\"Search for notes based on the time range and query\"": "description:\"根据时间范围和查询条件搜索笔记\"", "description:\"Index the vault to the Copilot index\"": "description:\"将库索引到 Copilot 索引中\"", "description:\"Search the web for information\"": "description:\"在网络上搜索信息\"", "description:\"Get the current time in various formats, including timezone information\"": "description:\"获取当前时间的多种格式，包括时区信息\"", "description:\"Get a time range in milliseconds based on a natural language time expression\"": "description:\"根据自然语言时间表达式获取毫秒级时间范围\"", "description:\"Convert a Unix timestamp (in seconds or milliseconds) to detailed time information\"": "description:\"将 Unix 时间戳（秒或毫秒）转换为详细的时间信息\"", "description:\"Start a Pomodoro timer with a customizable interval\"": "description:\"启动一个可自定义间隔的番茄钟计时器\"", "description:\"Get the transcript of a YouTube video\"": "description:\"获取 YouTube 视频的文字转录\"", "description:\"Files\"": "description:\"文件\"", "description:\"\",systemPrompt:\"": "description:\"\",systemPrompt:\"", "description:\"Briefly describe the purpose and goals of the project\"": "description:\"简要描述项目的目标和用途\"", "description:\"Custom instructions for how the AI should behave in this project context\"": "description:\"自定义 AI 在此项目上下文中的行为指令\"", "description:\"Define patterns to include specific files, folders or tags (specified in the note property) in the project context.\"": "description:\"定义模式以在项目上下文中包含特定文件、文件夹或标签（在笔记属性中指定）。\"", "description:\"Folder where custom prompts are stored\"": "description:\"存储自定义提示的文件夹\"", "description:\"Process variables like {activenote}, {foldername}, or {#tag} in prompts. Disable for raw prompts.\"": "description:\"处理提示中的变量，如 {activenote}、{foldername} 或 {#tag}。禁用则为原始提示。\"", "description:\"Sort order for slash command menu prompts\"": "description:\"斜杠命令菜单提示的排序顺序\"", "description:\"Customize the system prompt for all messages, may result in unexpected behavior!\"": "description:\"自定义所有消息的系统提示，可能导致意外行为！\"", "description:\"Enable encryption for the API keys.\"": "description:\"启用 API 密钥加密。\"", "description:\"Debug mode will log some debug message to the console.\"": "description:\"调试模式会将一些调试信息记录到控制台。\"", "description:\"Add the currently selected model to model List. After adding, please check the Model Tab.\"": "description:\"将当前选中的模型添加到模型列表。添加后，请检查模型选项卡。\"", "description:\"Select the Chat model to use\"": "description:\"选择要使用的对话模型\"", "description:\"Choose where to open the plugin\"": "description:\"选择插件打开的位置\"", "description:\"The default folder name where chat conversations will be saved. Default is 'copilot-conversations'\"": "description:\"保存对话的默认文件夹名称，默认为 'copilot-conversations'\"", "description:\"The default tag to be used when saving a conversation. Default is 'ai-conversations'\"": "description:\"保存对话时使用的默认标签，默认为 'copilot-conversations'\"", "description:\"Automatically saves the chat after every user message and AI response.\"": "description:\"在每条用户消息和 AI 响应后自动保存对话。\"", "description:\"Show suggested prompts in the chat view\"": "description:\"在对话视图中显示建议提示\"", "description:\"Show relevant notes in the chat view\"": "description:\"在对话视图中显示相关笔记\"", "description:\"Automatically include the current note in the chat context menu by default when sending messages to the AI.\"": "description:\"默认情况下，发送消息给 AI 时自动将当前笔记包含在对话上下文菜单中。\"", "description:\"Pass embedded images in markdown to the AI along with the text. Only works with multimodal models.\"": "description:\"将 Markdown 中的嵌入式图像与文本一起传递给 AI。仅适用于多模态模型。\"", "description:\"Suggest completions for partially typed words based on your vault's content. Requires at least 3 characters to trigger.\"": "description:\"根据库内容为部分输入的单词提供补全建议。至少需要输入 3 个字符才能触发。\"", "description:\"Rebuild the word index to include new words from your vault. The index is automatically built when the plugin loads.\"": "description:\"重建单词索引以包含库中的新单词。插件加载时会自动构建索引。\"", "description:\"Allow the AI to access relevant notes to provide more relevant suggestions. When off, the AI can only see the current note context.\"": "description:\"允许 AI 访问相关笔记以提供更相关的建议。关闭时，AI 只能看到当前笔记上下文。\"", "description:\"Enter OpenAI Organization ID if applicable\"": "description:\"如果适用，请输入 OpenAI 组织 ID\"", "description:\"This is your actual model, no need to pass a model name separately.\"": "description:\"这是您的实际模型，无需单独传递模型名称。\"", "description:\"Leave it blank, unless you are using a proxy.\"": "description:\"留空，除非您正在使用代理。\"", "description:\"The number of previous conversation turns to include in the context. Default is 15 turns, i.e. 30 messages.\"": "description:\"上下文中包含的先前对话轮数，默认为 15 轮，即 30 条消息。\"", "description:\"Copilot goes through your vault to find relevant blocks and passes the top N blocks to the LLM. Default for N is 3. Increase if you want more sources included in the answer generation step.\"": "description:\"Copilot 会遍历您的库以查找相关块，并将前 N 个块传递给 LLM。N 默认为 3。如果需要更多来源参与答案生成步骤，请增加此值。\"", "description:\"Default is 90. Decrease if you are rate limited by your embedding provider.\"": "description:\"默认为 90。如果您的嵌入提供者限制了速率，请减少此值。\"", "description:\"Default is 16. Increase if you are rate limited by your embedding provider.\"": "description:\"默认为 16。如果您的嵌入提供者限制了速率，请增加此值。\"", "description:\"Number of partitions for Copilot index. Default is 1. Increase if you have issues indexing large vaults. Warning: Changes require clearing and rebuilding the index!\"": "description:\"Copilot 索引的分区数，默认为 1。如果索引大型库时遇到问题，请增加此值。警告：更改需要清除并重建索引！\"", "description:\"If enabled, the index will be stored in the .obsidian folder and synced with Obsidian Sync by default. If disabled, it will be stored in .copilot-index folder at vault root.\"": "description:\"如果启用，索引将默认存储在 .obsidian 文件夹中并与 Obsidian Sync 同步。如果禁用，索引将存储在库根目录的 .copilot-index 文件夹中。\"", "description:\"When enabled, Copilot index won't be loaded on mobile devices to save resources. Only chat mode will be available. Any existing index from desktop sync will be preserved. Uncheck to enable QA modes on mobile.\"": "description:\"启用后，Copilot 索引将不会在移动设备上加载以节省资源。仅对话模式可用。桌面同步的任何现有索引将被保留。取消勾选以在移动设备上启用 QA 模式。\"", "text:\"- {} represents the selected text (not required). \"": "text:\"-{}表示所选文本（非必需）。\"", "text:\"- {[[Note Title]]} represents a note. \"": "text:\"-{[[Note Title]]}代表注释。\"", "text:\"- {activeNote} represents the active note. \"": "text:\"-{activeNote}代表活动笔记。\"", "text:\"- {FolderPath} represents a folder of notes. \"": "text:\"-{FolderPath}代表一个包含笔记的文件夹。\"", "text:\"- {#tag1, #tag2} represents ALL notes with ANY of the specified tags in their property (an OR operation). \"": "text:\"- {#tag1, #tag2} 表示包含任意指定标签（OR逻辑运算）的所有笔记（标签需定义在笔记属性中）。\"", "text:\"Debug: Search OramaDB\"": "text:\"调试：搜索OramaDB\"", "text:\"Search\"": "text:\"搜索\"", "text:\"Inspect Copilot Index by Note Paths\"": "text:\"通过笔记路径检查Copilot索引\"", "text:\"Show Index Data\"": "text:\"显示索引数据\"", "text:\"Remove Files from Copilot Index\"": "text:\"从Copilot索引中删除文件\"", "text:\"reference\"": "text:\"参考\"", "text:\"Sources\"": "text:\"来源\"", "text:\"High Relevance Sources\"": "text:\"高相关性来源\"", "text:\"Lower Relevance Sources\"": "text:\"低相关性来源\"", "search:\"This model can access the internet.\"": "search:\"这个模型可以访问互联网。\"", ".setDesc(\"Paste the markdown list of file paths to remove from the index. You can get the list by running the command `List all indexed files`.\")": ".setDesc(\"粘贴要从索引中删除的文件路径的标记列表。您可以通过运行命令“列出所有索引文件”来获取列表。\")", ".setTitle(\"Thanks for being a Copilot Plus user \\u{1F44B}\")": ".setTitle(\"感谢您成为Copilot Plus用户\\u{1F44B}\")", ".setTitle(\"Select File\")": ".setTitle(\"选择文件\")", ".setTitle(\"Copilot: Add selection to chat context\")": ".setTitle(\"Copilot：将选择添加到对话上下文中\")", ".setTitle(\"Edit Command\")": ".setTitle(\"编辑命令\")", ".setTitle(\"Welcome to Copilot Plus \\u{1F680}\")": ".setTitle(\"欢迎使用Copilot Plus{1F680}\")", ".setTitle(\"Add Extension\")": ".setTitle(\"添加扩展名\")", ".setTitle(\"Add Custom Pattern\")": ".setTitle(\"添加自定义图案\")", "title:\"string\"": "title:\"字符串\"", "title:\"Fix grammar and spelling\"": "title:\"修正语法和拼写\"", "title:\"Translate to Chinese\"": "title:\"翻译成中文\"", "title:\"Summarize\"": "title:\"总结\"", "title:\"Simplify\"": "title:\"简化\"", "title:\"Explain like I am 5\"": "title:\"像我5岁一样解释\"", "title:\"Emojify\"": "title:\"表情符号化\"", "title:\"Make shorter\"": "title:\"变短\"", "title:\"Make longer\"": "title:\"变长\"", "title:\"Generate table of contents\"": "title:\"生成目录\"", "title:\"Generate glossary\"": "title:\"生成术语表\"", "title:\"Remove URLs\"": "title:\"删除URL\"", "title:\"Rewrite as tweet\"": "title:\"重写为推特\"", "title:\"Rewrite as tweet thread\"": "title:\"重写为推特帖子\"", "title:\"New Chat\"": "title:\"新对话\"", "title:\"Save Chat as Note\"": "title:\"将对话另存为笔记\"", "title:\"Chat History\"": "title:\"对话历史\"", "title:\"Advanced Settings\"": "title:\"高级设置\"", "title:\"Remove image\"": "title:\"删除图像\"", "title:\"Copy\"": "title:\"复制\"", "title:\"Edit\"": "title:\"编辑\"", "title:\"Delete\"": "title:\"删除\"", "title:\"Show Sources\"": "title:\"显示源\"", "title:\"Insert / Replace at cursor\"": "title:\"在光标处插入/替换\"", "title:\"Regenerate\"": "title:\"重新生成\"", "title:\"Active Note Insights\"": "title:\"活动笔记见解\"", "title:\"Note Link Chat\"": "title:\"笔记链接对话\"", "title:\"Test LLM\"": "title:\"LLM测试\"", "title:\"Vault Q&A\"": "title:\"库 Q&A\"", "title:\"Copilot Plus\"": "title:\"Copilot Plus\"", "title:\"Tags\"": "title:\"标签\"", "title:\"Folders\"": "title:\"文件夹\"", "title:\"Files\"": "title:\"文件\"", "title:\"Ignore Files\"": "title:\"忽略文件\"", "title:\"Custom Prompts Folder Name\"": "title:\"自定义提示文件夹名称\"", "title:\"Custom Prompt Templating\"": "title:\"自定义提示模板\"", "title:\"Custom Prompts Sort Strategy\"": "title:\"自定义提示排序策略\"", "title:\"User System Prompt\"": "title:\"用户系统提示\"", "title:\"Enable Encryption\"": "title:\"启用加密\"", "title:\"Debug Mode\"": "title:\"调试模式\"", "title:\"API Keys\"": "title:\"API密钥\"", "title:\"Default Chat Model\"": "title:\"默认对话模式\"", "title:\"Embedding Model\"": "title:\"嵌入模型\"", "title:\"Default Mode\"": "title:\"默认模式\"", "title:\"Open Plugin In\"": "title:\"打开插件\"", "title:\"Default Conversation Folder Name\"": "title:\"默认对话文件夹名称\"", "title:\"Default Conversation Tag\"": "title:\"默认对话标签\"", "title:\"Conversation Filename Template\"": "title:\"对话文件名模板\"", "title:\"Autosave Chat\"": "title:\"自动保存对话\"", "title:\"Suggested Prompts\"": "title:\"建议提示\"", "title:\"Relevant Notes\"": "title:\"相关笔记\"", "title:\"Include Current Note in Context Menu\"": "title:\"在上下文菜单中包含当前笔记\"", "title:\"Images in Markdown\"": "title:\"Markdown中的图片\"", "title:\"Sentence Autocomplete\"": "title:\"句子自动补全\"", "title:\"Word Completion\"": "title:\"单词补全\"", "title:\"Word Index Management\"": "title:\"单词索引管理\"", "title:\"Autocomplete Accept Suggestion Key\"": "title:\"自动补全接受建议按键\"", "title:\"Allow Additional Context\"": "title:\"允许其他上下文\"", "title:\"Chat Model\"": "title:\"对话模型\"", "title:\"Conversation turns in context\"": "title:\"对话语境转折\"", "title:\"Extensions\"": "title:\"扩展\"", "title:\"Notes\"": "title:\"笔记\"", "title:\"Auto-Index Strategy\"": "title:\"自动索引策略\"", "title:\"Max Sources\"": "title:\"最大来源\"", "title:\"Requests per Minute\"": "title:\"每分钟请求数\"", "title:\"Embedding Batch Size\"": "title:\"嵌入批量大小\"", "title:\"Number of Partitions\"": "title:\"分区数\"", "title:\"Exclusions\"": "title:\"排除项\"", "title:\"Inclusions\"": "title:\"包含项\"", "title:\"Enable Obsidian Sync for Copilot index\"": "title:\"为Copilot索引启用Obsidian同步\"", "title:\"Disable index loading on mobile\"": "title:\"禁用移动设备上的索引加载\"", "placeholder:\"Enter search params JSON...\"": "placeholder:\"输入搜索参数 JSON...\"", "placeholder:\"Ask anything. [[ for notes. / for custom prompts. \"": "placeholder:\"输入任意内容。[[ 查找笔记。/ 使用自定义提示。\"", "placeholder:\"Custom search: title, #tag1, .jpg\"": "placeholder:\"自定义搜索：标题、#标签1、.jpg\"", "placeholder:\"Select a model\"": "placeholder:\"选择模型\"", "placeholder:\"Enter web URLs, one per line\"": "placeholder:\"输入网页 URL，每行一个\"", "placeholder:\"Enter YouTube URLs, one per line\"": "placeholder:\"输入 YouTube URL，每行一个\"", "placeholder:\"Search projects...\"": "placeholder:\"搜索项目...\"", "placeholder:\"Enter command name\"": "placeholder:\"输入命令名称\"", "placeholder:\"Enter command prompt\"": "placeholder:\"输入命令提示\"", "placeholder:\"copilot-custom-prompts\"": "placeholder:\"copilot-custom-prompts\"", "placeholder:\"Command name\"": "placeholder:\"命令名称\"", "placeholder:\"Enter your system prompt here...\"": "placeholder:\"在此输入系统提示...\"", "placeholder:\"Enter your license key\"": "placeholder:\"输入许可证密钥\"", "placeholder:\"Select Model\"": "placeholder:\"选择模型\"", "placeholder:\"Model\"": "placeholder:\"模型\"", "placeholder:\"copilot-conversations\"": "placeholder:\"copilot-conversations\"", "placeholder:\"ai-conversations\"": "placeholder:\"ai-conversations\"", "placeholder:\"{$date}_{$time}__{$topic}\"": "placeholder:\"{$date}_{$time}__{$topic}\"", "placeholder:\"Select key\"": "placeholder:\"选择密钥\"", "placeholder:\"Enter OpenAI Organization ID if applicable\"": "placeholder:\"如适用，请输入 OpenAI 组织 ID\"", "placeholder:\"Enter Azure OpenAI API Instance Name\"": "placeholder:\"输入 Azure OpenAI API 实例名称\"", "placeholder:\"Enter Azure OpenAI API Embedding Deployment Name\"": "placeholder:\"输入 Azure OpenAI API 嵌入部署名称\"", "placeholder:\"Enter Azure OpenAI API Deployment Name\"": "placeholder:\"输入 Azure OpenAI API 部署名称\"", "placeholder:\"Enter Azure OpenAI API Version\"": "placeholder:\"输入 Azure OpenAI API 版本\"", "placeholder:\"Custom display name (optional)\"": "placeholder:\"自定义显示名称（可选）\"", "placeholder:\"Select provider\"": "placeholder:\"选择提供商\"", "placeholder:\"Enter model name\"": "placeholder:\"输入模型名称\"", "placeholder:\"Enter the extension (e.g. txt, excalidraw.md)\"": "placeholder:\"输入扩展名（例如 txt、excalidraw.md）\"", "placeholder:\"Enter the pattern\"": "placeholder:\"输入模式\"", "placeholder:\"Strategy\"": "placeholder:\"策略\"", "placeholder:\"Enter follow-up instructions...\"": "placeholder:\"输入后续指令...\"", "Notice(`Failed to process images:\\n${e.join(`\\n`)}`)": "Notice(`处理图片失败：\\n${e.join(`\\n`)}`)", "Notice(`Pomodoro timer (${t})`)": "Notice(`番茄钟计时器（${t}）`)", "Notice(`Failed to process web URLs: ${ei(r)}`)": "Notice(`处理网页链接失败：${ei(r)}`)", "Notice(`Failed to process YouTube URL ${i}: ${ei(a)}`)": "Notice(`处理 YouTube 链接 ${i} 失败：${ei(a)}`)", "Notice(`Project \"${A.name}\" created successfully`)": "Notice(`项目\"${A.name}\"已成功创建。`)", "Notice(`Project \"${A.name}\" updated successfully`)": "Notice(`项目\"${A.name}\"已成功更新。`)", "Notice(`Project \"${A.name}\" created from scratch successfully`)": "Notice(`从头开始创建项目\"${A.name}\"成功。`)", "Notice(`Project \"${A.name}\" updated from scratch successfully`)": "Notice(`从头开始更新项目\"${A.name}\"成功。`)", "Notice(`Project \"${A.name}\" reloaded successfully`)": "Notice(`项目\"${A.name}\"已成功重新加载。`)", "Notice(`Project \"${A.name}\" rebuilt successfully`)": "Notice(`项目\"${A.name}\"已成功重建。`)", "Notice(\"API key verification failed: \"+ei(N))": "Notice(\"API 密钥验证失败：\"+ei(N))", "Notice(`Failed to load models for ${kf(I)}`)": "Notice(`加载模型失败 ${kf(I)}`)", "Notice(\"Model verification failed: \"+ei(I))": "Notice(\"模型验证失败：\"+ei(I))", "Notice(`Error: Missing required variables: ${d.join(\", \")}`)": "Notice(`错误：缺少必需变量：${d.join(\", \")}`)", "Notice('Error: Format contains illegal characters (\\\\/:*?\"<>|)')": "Notice('错误：格式包含非法字符（\\\\/:*?\"<>|）')", ".log(\"Using RetryOperation.try()\")": ".log(\"正在使用 RetryOperation.try()\")", ".log(\"Using RetryOperation.start()\")": ".log(\"正在使用 RetryOperation.start()\")", ".log(`Detected vector length: ${r.length} for model: ${Xs.getModelName(t)}`)": ".log(`检测到向量长度：${r.length}，模型：${Xs.getModelName(t)}`)", "\"Configure API keys for different AI providers\"": "\"为不同的AI提供商配置API密钥\"", "\"API key required for chat and QA features\"": "\"对话和QA功能所需的API密钥\"", "\"To enable chat and QA functionality, please provide an API key from your selected provider.\"": "\"若要启用对话和QA功能，请填写所选提供商的API密钥。\"", "\"Set Keys\"": "\"设置密钥\"", "\"AI Provider Settings\"": "\"AI提供商设置\"", "\"Verify\"": "\"验证\"", "\"Add Model\"": "\"添加模型\"", "\"Get \",A.label,\" API Key\"": "\"获取 \",A.label,\" API 密钥\"", "\"Core Feature: Powers Semantic Search & QA\"": "\"核心功能： 增强语义搜索和质量保证功能\"", "\"This model converts text into vector representations, essential for semantic search and QA functionality. Changing the embedding model will:\"": "\"该模型将文本转换为矢量表示，这对语义搜索和质量保证功能至关重要。更改嵌入模型将：\"", "\"Require rebuilding your vault's vector index\"": "\"需要重建库的矢量索引\"", "\"Affect semantic search quality\"": "\"影响语义搜索质量\"", "\"Impact QA feature performance\"": "\"影响QA功能性能\"", "\"Select the default chat mode\"": "\"选择默认对话模式\"", "\"Chat:\"),\" Regular chat mode for general conversations and tasks. \"": "\"对话：\"),\" 用于一般对话和任务的常规模式。 \"", "\"Free to use with your own API key.\"": "\"使用自己的 API 密钥即可免费使用。\"", "\"Vault QA (Basic):\"),\" Ask questions about your vault content with semantic search. \"": "\"库 QA (基本):\"),\" 通过语义搜索询问有关库内容的问题。\"", "\"Copilot Plus:\"),\" Covers all features of the 2 free modes, plus advanced paid features including chat context menu, advanced search, AI agents, and more. Check out\"": "\"Copilot Plus:\"),\"涵盖 2 种免费模式的所有功能，以及对话上下文菜单、高级搜索、AI agents等高级付费功能。查看\"", "\"for more details.\"": "\"了解更多细节。\"", "\"Customize the format of saved conversation note names.\"": "\"自定义保存的对话笔记名称的格式。\"", "\"Note: All the following variables must be included in the template.\"": "\"注意：模板中必须包含以下所有变量。\"", "\"Available variables:\"": "\"可用变量：\"", "\": Date in YYYYMMDD format\"": "\"：日期 YYYYMMDD 格式\"", "\": Time in HHMMSS format\"": "\"：时间 HHMMSS 格式\"", "\": Chat conversation topic\"": "\":对话主题\"", "\"Copilot Plus takes your Obsidian experience to the next level with cutting-edge AI capabilities. This premium tier unlocks advanced features, including chat context, PDF and image support, web search integration, exclusive chat and embedding models, and much more.\"": "\"Copilot Plus 通过最先进的人工智能功能将您的 Obsidian 体验提升到新的水平。该高级功能包括对话上下文、PDF 和图片支持、网络搜索集成、独家对话和嵌入模型等。\"", "\"Currently in beta, Copilot Plus is evolving fast, with new features and improvements rolling out regularly. Join now to secure the lowest price and get early access!\"": "\"目前处于测试阶段的 Copilot Plus 发展迅速，定期推出新功能和改进。现在加入，以确保以最低价格尽早使用！\"", "\"Apply\"": "\"应用\"", "\"Join Now \"": "\"立即加入 \"", "\"Configure your AI providers by adding their API keys.\"": "\"通过添加他们的API密钥来配置您的AI提供商。\"", "\"Get \",kf(I.provider),\" Key\"": "\"获取\",kf(I.provider),\"密钥\"", "\"Get \",u.label,\" API Key\"": "\"获取\",u.label,\" API密钥\"", "\"Refresh Built-in Models\"": "\"刷新内置模型\"", "\"Add Custom Model\"": "\"添加自定义模型\"", "\"Custom commands are preset prompts that you can trigger in the editor by right-clicking and selecting them from the context menu or by using a \",it.default.createElement(\"code\",null,\"/\"),\" command in the chat to load them into your chat input.\"": "\"自定义命令是预设的提示，您可以在编辑器中右击并从上下文菜单中选择它们，或者在聊天中使用\",it.default.createElement(\"code\",null,\"/\"),\" 命令将它们加载到聊天输入中，从而触发这些提示。\"", "\"Commands are automatically loaded from .md files in your custom prompts folder\",\" \",it.default.createElement(\"strong\",null,o.customPromptsFolder),\". Modifying the files will also update the command settings.\"": "\"命令会自动从自定义提示文件夹\" ,it.default.createElement(\"strong\",null,o.customPromptsFolder),\"中的.md文件加载。\",\"修改文件也会更新命令设置。\"", "\"Generate Default Commands\"": "\"生成默认命令\"", "\"This will add default commands to your custom prompts folder. Do you want to continue?\"": "\"这将把默认命令添加到您的自定义提示文件夹中。您想继续吗？\"", "\"Add Command\"": "\"添加命令\"", "\"Create New Command\"": "\"创建新命令\"", "\"Enter a name for your new custom command. A markdown file will be created in your custom prompts folder.\"": "\"输入新自定义命令的名称。将在自定义提示文件夹中创建一个markdown文件。\"", "\"Enable AI-powered sentence autocomplete suggestions while typing\"": "\"在打字时启用基于AI的句子自动补全建议\"", "'Select the key you want to use for accepting suggestions. Default is \"Tab\".'": "'选择用于接受建议的按键。默认值为“Tab”。'", "\"The key used to accept autocomplete suggestions\"": "\"用于接受自动补全建议的按键\"", "\"Resetting settings will clear all settings and restore the default values. You will lose any custom settings you have made including the API keys. Are you sure you want to continue?\"": "\"重置设置将清除所有设置并恢复默认值。您将丢失您所做的任何自定义设置，包括API密钥。您确定要继续吗？\"", "\"Reset Settings\"": "\"重置设置\"", "(up to date)": "(最新)", "\"Sidebar View\"": "\"侧边栏视图\"", "\"Editor\"": "\"编辑器\"", "`API key is not provided for the embedding model: ${e}`": "`没有为嵌入模型提供API密钥：${e}`", "`API key is not provided for the model: ${r}.`": "`没有为模型提供API密钥：${r}。`", "\"Decide when you want the vault to be indexed.\"": "\"决定何时对库进行索引。\"", "\"Choose when to index your vault:\"": "\"选择何时为库建立索引：\"", "\"NEVER:\"),zr.default.createElement(\"span\",null,\"Manual indexing via command or refresh only\"": "\"从不：\"),zr.default.createElement(\"span\",null,\"仅通过命令或刷新手动索引\"", "\"ON STARTUP:\"),zr.default.createElement(\"span\",null,\"Index updates when plugin loads or reloads\"": "\"启动时：\"),zr.default.createElement(\"span\",null,\"插件加载或重新加载时更新索引\"", "\"ON MODE SWITCH:\"),zr.default.createElement(\"span\",null,\"Updates when entering QA mode (Recommended)\"": "\"模式切换时：\"),zr.default.createElement(\"span\",null,\"进入 QA 模式时更新（推荐）\"", "\"Warning: Cost implications for large vaults with paid models\"": "\"警告： 对使用付费模型的大型库有成本影响\"", "\"NEVER\",\"ON STARTUP\",\"ON MODE SWITCH\"": "\"从不\",\"启动时\",\"模式切换时\"", "\"Exclude folders, tags, note titles or file extensions from being indexed. Previously indexed files will remain until a force re-index is performed.\"": "\"从索引中排除文件夹、标签、笔记标题或文件扩展名。已索引的文件将保留，直到执行强制重新索引。\"", "\"Index only the specified paths, tags, or note titles. Exclusions take precedence over inclusions. Previously indexed files will remain until a force re-index is performed.\"": "\"仅索引指定的路径、标签或笔记标题。排除规则的优先级高于包含规则。已索引的文件将保留，直至执行强制重新索引。\"", "\"Manage\"": "\"管理\"", "\"Relevant Note\"": "\"相关笔记\"", "\"Suggested Prompt\"": "\"建议提示词\"", "\"Refresh Vault Index\"": "\"刷新库索引\"", "\"Force Reindex Vault\"": "\"强制重新索引库\"", "\"Save Chat as Note\"": "\"将对话另存为笔记\"", "\"Chat History\"": "\"对话历史\"", "\"Please note that this is a retrieval-based QA. Questions should contain keywords and concepts that exist literally in your vault\"": "\"请注意，这是一个基于检索的QA。问题应包含您的库中实际存在的关键字和概念\"", "\"Reindex Current Note\"": "\"重新索引当前笔记\"", "\"Open Copilot Chat\"": "\"打开 Copilot 对话\"", "\"Refresh Word Index\"": "\"刷新词语索引\""}}