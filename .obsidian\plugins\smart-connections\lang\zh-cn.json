{"manifest": {"translationVersion": 1742968346854, "pluginVersion": "2.5.4"}, "description": {"original": "Find links to similar notes using artificial intelligence from OpenAI.", "translation": "Find links to similar notes using artificial intelligence from OpenAI."}, "dict": {"name: \"File Exclusions\"": "name: \"文件排除\"", "name: \"Folder Exclusions\"": "name: \"文件夹排除\"", "name: \"Excluded Headings\"": "name: \"排除的标题\"", "name: \"Embed Blocks\"": "name: \"嵌入块\"", "name: \"Embedding Model\"": "name: \"嵌入模型\"", "name: \"lookup\"": "name: \"查找\"", "description: \"Select an embedding model.\"": "description: \"选择一个嵌入模型。\"", "text: \"Refresh\"": "text: \"刷新\"", ".log(`getting options callback: ${elm.dataset.optionsCallback}`)": ".log(`获取选项回调：${elm.data.optionsCallback}`)", ".log(\"folder changed\", folder)": ".log(\"文件夹已更改\", folder)", ".log(\"process_embed_queue is already running, skipping concurrent call.\")": ".log(\"process_embed_queue 已经在运行，跳过并发调用。\")", ".log(`Smart Connections: No active embedding model for ${this.collection.collection_key}, skipping embedding`)": ".log(`智能连接：${this.collection.collection_key}没有活动的嵌入模型，跳过嵌入`)", ".log(`Smart Connections: No items in ${this.collection.collection_key} embed queue`)": ".log(`智能连接：${this.collection.collection_key}嵌入队列中没有项目`)", ".log(`Time spent getting embed queue: ${(/* @__PURE__ */ new Date()": ".log(`Time spent getting embed queue: ${(/* @__PURE__ */ new Date()", ".log(`Processing ${this.collection.collection_key} embed queue: ${embed_queue.length} items`)": ".log(`正在处理$｛this.collection.collection_key｝嵌入队列：$｛embed_queue.length｝个项目`)", ".log(`Saving ${this.collection.block_collection.collection_key} block collection`)": ".log(`保存 ${this.collection.block_collection.collection_key} 区块集合`)", ".log(\"Embed queue processing halted\")": ".log(\"嵌入队列处理已停止\")", ".log(\"resume_embed_queue_processing\")": ".log(\"resume_embed_queue_processing\")", ".log(`SmartEmbed not loaded for **${this.collection_key}**. Continuing without embedding capabilities.`)": ".log(`**${this.collection_key}**未加载SmartEmbed。继续而不嵌入功能。`)", ".log(`SmartEmbedModel already loading for ${this.embed_model_key}`)": ".log(`${this.embed_model_key}已经加载了SmartEmbedModel`)", ".log(`SmartEmbedModel already loaded for ${this.embed_model_key}`)": ".log(`${this.embedde_model_key}的SmartEmbedModel已加载`)", ".log(`Loading SmartEmbedModel in ${this.collection_key}, current state: ${this.embed_model.state}`)": ".log(`正在加载SmartEmbedModel在${this.collection_key}中，当前状态：${this.embed_model.state}。`)", ".log(\"model_key not set\")": ".log(\"未设置model_key\")", ".log(\"window.document not available\")": ".log(\"window.document 不可用\")", ".log(\"reload_embed_model\")": ".log(\"重新加载嵌入模型\")", ".log(results)": ".log(results)", ".log(`Found and returned ${top_k.length} ${this.collection_key}.`)": ".log(`找到并返回了 ${top_k.length} 个 ${this.collection_key}。`)", ".log(`Smart Connections: Deleting ${this.path} data because it no longer exists on disk`)": ".log(`智能连接：删除 ${this.path} 数据，因为它在磁盘上不再存在。`)", ".log(\"No source adapter found for\", this.file_type, this)": ".log(\"未找到任何来源适配器\", this.file_type, this)", ".log(source.reason)": ".log(source.reason)", ".log(`Pruned ${remove_smart_blocks.length} blocks:\n${remove_smart_blocks.map((item)": ".log(`Pruned ${remove_smart_blocks.length} blocks:\n${remove_smart_blocks.map((item)", ".log(`Time spent building links: ${end_time - start_time}ms`)": ".log(`构建链接所花费的时间：${end_Time-start_Time}ms`)", ".log(\"lookup block_collection\")": ".log(\"查找区块集合\")", ".log(\"import_queue \" + import_queue.length)": ".log(\"导入队列\" + import_queue.length)", ".log(\"skipping process_embed_queue\")": ".log(\"跳过处理嵌入队列\")", ".log(`Time spent importing: ${end_time - start_time}ms`)": ".log(`导入所花费的时间：${end_Time-start_Time}ms`)", ".log(`Smart Collections: Re-loading item ${this.item.key} because it has been updated on disk`)": ".log(`智能集合：重新加载项目 ${this.item.key}，因为它在磁盘上已更新`)", ".log(`Loading ${this.collection.collection_key}: ${load_queue.length} items`)": ".log(`正在加载$｛this.collection.collection_key｝：$｛load_queue.length｝个项目`)", ".log(`Loaded ${this.collection.collection_key} in ${this.collection.load_time_ms}ms`)": ".log(`在${this.collection.load_time_ms}ms中加载了${this.collection.collection_key}`)", ".log(`Saving ${this.collection.collection_key}: ${save_queue.length} items`)": ".log(`正在保存$｛this.collection.collection_key｝：$｛save_queue.length｝个项目`)", ".log(`Saved ${this.collection.collection_key} in ${Date.now()": ".log(`Saved ${this.collection.collection_key} in ${Date.now()", ".log(`Skipping loading ${this.collection.collection_key}...`)": ".log(`跳过加载$｛this.collection.collection_key｝。。。`)", ".log(\"pausing\")": ".log(\"暂停\")", ".log(\"get_platforms_as_options\", this.adapters)": ".log(\"获取平台作为选项\", this.adapters)", ".log(\"re_render_settings\", this.opts)": ".log(\"重新渲染设置\", this.opts)", ".log(\"reload_model\", this.opts)": ".log(\"重新加载模型\", this.opts)", ".log(result.tokens)": ".log(result.tokens)", ".log(result.vec)": ".log(result.vec)", ".log(await response.json()": ".log(await response.json()", ".log(\"Empty batch (or all items have empty embed_input)": ".log(\"Empty batch (or all items have empty embed_input)", ".log(`Retrying request (429)": ".log(`Retrying request (429)", ".log(\"Warning: prepare_embed_input received an empty string\")": ".log(\"警告：prepare_membed_input收到空字符串\")", ".log(\n        \"Warning: prepare_embed_input resulted in an empty string after trimming\"\n      )": ".log(\n        \"警告：在修剪后，prepare_embed_input 导致空字符串。\"\n      )", ".log(\"model loaded\")": ".log(\"模型已加载\")", ".log('message ignored (listener)": ".log('message ignored (listener)", ".log({ load_opts })": ".log({ load_opts })", ".log(\"[Transformers] Using GPU\")": ".log(\"[Transformers] Using GPU\")", ".log(\"[Transformers] Using CPU\")": ".log(\"[Transformers] Using CPU\")", ".log(`Processing ${filtered_inputs.length} inputs in batches of ${this.batch_size}`)": ".log(`处理 ${filtered_inputs.length} 个输入，每批 ${this.batch_size} 个。`)", ".log(\"init\")": ".log(\"初始化\")", ".log(\"load\", params)": ".log(\"加载\", params)", ".log(\"render_lookup\", query)": ".log(\"渲染查找\", query)", ".log(\"open_note\", link, this)": ".log(\"打开笔记\", link, this)", ".log(\"open_note\", link)": ".log(\"打开笔记\", link)", ".log(`Thread renamed to \"${new_name}\"`)": ".log(`线程已重命名为 \"${new_name}\"`)", ".log(\"no chunk\")": ".log(\"无分块\")", ".log(\"refresh_models\")": ".log(\"刷新模型\")", ".log(\"models_request_params\", this.models_request_params)": ".log(\"模型_请求_图表\", this.models_request_params)", ".log(\"response\", response)": ".log(\"响应\", response)", ".log(\"model_data\", model_data)": ".log(\"模型数据\", model_data)", ".log(\"request_params\", request_params)": ".log(\"请求参数\", request_params)", ".log(\"http_resp\", http_resp)": ".log(\"HTTP响应\", http_resp)", ".log(\"end of stream\")": ".log(\"流的末端\")", ".log(\"final_resp\", final_resp)": ".log(\"最终回应\", final_resp)", ".log(resp_adapter)": ".log(resp_adapter)", ".log(\"handle_chunk\", chunk)": ".log(\"处理块\", chunk)", ".log(\"get_models\", refresh)": ".log(\"获取模型\", refresh)", ".log(\"list_response\", list_resp)": ".log(\"列表响应\", list_resp)", ".log(\"model_details_response\", model_details_resp)": ".log(\"模型详情响应\", model_details_resp)", ".log(\"model_details_data\", model_details_data)": ".log(\"模型_尾部_数据\", model_details_data)", ".log(\"reload_chat_model\")": ".log(\"重新加载聊天模型\")", ".log(\"skipping embed queue processing for SmartThreads\")": ".log(\"跳过SmartThreads的嵌入队列处理\")", ".log(\"skipping load queue processing for SmartThreads\")": ".log(\"跳过对SmartThreads的加载队列处理\")", ".log(\"chat_input\", chat_input)": ".log(\"聊天输入\", chat_input)", ".log(\"validation_result\", validation_result)": ".log(\"validation_result\", validation_result)", ".log(\"Moved last user message to the end of the request for better context handling.\")": ".log(\"将最后一条用户消息移动到请求末尾，以便更好地处理上下文。\")", ".log(\"filter\", filter)": ".log(\"过滤器\", filter)", ".log(\"skipping embed queue processing for SmartMessages\")": ".log(\"跳过SmartMessages的嵌入队列处理\")", ".log(\"Message copied to clipboard\")": ".log(\"消息已复制到剪贴板\")", ".log(\"View inactive, skipping render nearest\")": ".log(\"查看非活动视图，跳过最近的渲染\")", ".log(\"Creating entity for current file\", current_file.path)": ".log(\"为当前文件创建实体\", current_file.path)", ".log(\"re_render\")": ".log(\"请求者\")", ".log(\"loading view\")": ".log(\"加载视图\")", ".log(\"displaying settings tab\")": ".log(\"显示设置选项卡\")", ".log(`ScAppConnector initialized on port ${this.port}`)": ".log(`ScAppConnector 在 ${this.port} 端口初始化完成。`)", ".log(`Port ${this.port} is already in use. Attempting to retry once.`)": ".log(`端口$｛this.Port｝已在使用中。尝试重试一次。`)", ".log(`Server running at http://localhost:${this.port}/`)": ".log(`服务器正在运行，地址为 http://localhost:${this.port}/`)", ".log(\"Dataview API not found. No dataview connection for Smart Connect.\")": ".log(\"找不到数据视图API。Smart Connect没有数据视图连接。\")", ".log(\"waiting for changes\")": ".log(\"等待更改\")", ".log(\"Server closed\")": ".log(\"服务器已关闭\")", ".log(\"Window server reference closed\")": ".log(\"窗口服务器参考已关闭\")", ".log(\"Environment no longer available. Closing server.\")": ".log(\"环境不再可用。正在关闭服务器。\")", ".log(\"Writing files to:\", adapter, baseFolder, files)": ".log(\"写入文件至:\", adapter, baseFolder, files)", ".log(\"Writing file binary:\", fullPath)": ".log(\"正在写入二进制文件：\", fullPath)", ".log(\"Writing file non-binary:\", fullPath)": ".log(\"写入非二进制文件：\", fullPath)", ".log(\"unloading plugin\")": ".log(\"卸载插件\")", ".log(\"loading env\")": ".log(\"加载环境\")", ".log(\"Smart Connections v2 loaded\")": ".log(\"已加载智能连接v2\")", ".log(\"env loaded\")": ".log(\"环境已加载\")", ".log(error)": ".log(error)", ".log(\"Added to .gitignore: \" + ignore)": ".log(\"添加到.gitignore：\" + ignore)", ".log(\"Smart Connections: Waiting for Obsidian Sync to finish\")": ".log(\"智能连接：等待 Obsidian 同步完成\")", ".log(\"render_file_counts\")": ".log(\"渲染文件_计数\")", ".log(\"Manifest written\")": ".log(\"清单已编写\")", ".error(\"Error getting component\", e)": ".error(\"获取组件时出错\", e)", ".error(`Error checking if path is excluded: ${e.message}`)": ".error(`检查路径是否被排除时出错: ${e.message}`)", ".error(`Path: `, _path)": ".error(`路径：`, _path)", ".error(\"Error during write:\", error)": ".error(\"写入过程中发生错误:\", error)", ".error({ path, elm })": ".error({ path, elm })", ".error(e)": ".error(e)", ".error(\"No callback or href found for button.\")": ".error(\"未找到按钮的回调或链接。\")", ".error(err, err.stack)": ".error(err, err.stack)", "_error(err)": "_error(err)", ".error(\"get_many called with non-array keys:\", keys)": ".error(\"get_many被非数组键调用:\", keys)", ".error(`Error processing ${this.collection.collection_key} embed queue: ` + JSON.stringify(e || {}, null, 2)": ".error(`处理$｛this.collection.collection_key｝嵌入队列时出错：` + JSON.stringify(e || {}, null, 2)", " error(error)": " error(error)", ".error(`Error loading SmartEmbedModel for ${this.embed_model.model_key}`)": ".error(`加载${this.embed_model.model_key}的SmartEmbedModel时出错`)", ".error(\"Error during update:\", error)": ".error(\"更新过程中出错：\", error)", ".error(\"Error during read:\", error)": ".error(\"读取过程中出现错误:\", error)", ".error(\"Error during remove:\", error)": ".error(\"删除过程中出错：\", error)", ".error(\"error_during_move:\", error)": ".error(\"移动过程中出错：\", error)", ".error(\"Error during merge:\", error)": ".error(\"合并过程中出错：\", error)", ".error(`Error searching item ${item.id || \"unknown\"}:`, error)": ".error(`搜索项目 ${item.id || \"未知\"} 时出错：`, error)", ".error(`Error getting embed queue:`, e)": ".error(`获取嵌入队列时出错：`, e)", ".error(e, e.stack)": ".error(e, e.stack)", ".error(`Error getting should_embed for ${this.key}: ` + JSON.stringify(e || {}, null, 2)": ".error(`获取 ${this.key} 的 should_embed 时发生错误: ` + JSON.stringify(e || {}, null, 2)", ".error(\"No response received for embedding request.\")": ".error(\"未收到嵌入请求的响应。\")", ".error(\"Failed to parse embeddings.\")": ".error(\"解析嵌入失败。\")", ".error(error)": ".error(error)", ".error(\"Invalid response format\", resp)": ".error(\"响应格式无效\", resp)", ".error(\"error_processing_batch\", err)": ".error(\"处理批量错误\", err)", ".error(\"error_processing_single_item\", single_err)": ".error(\"错误处理_单个项目\", single_err)", ".error(\"Error processing message:\", error)": ".error(\"处理消息时出现错误:\", error)", ".error(\"Error renaming thread:\", error)": ".error(\"重命名线程时出错：\", error)", ".error(\"Failed to fetch model data:\", error)": ".error(\"获取模型数据失败:\", error)", ".error(\"Error in SmartChatModelApiAdapter.complete()": ".error(\"Error in SmartChatModelApiAdapter.complete()", ".error(http_resp)": ".error(http_resp)", ".error(\"Error processing stream chunk:\", error)": ".error(\"处理流块时出错：\", error)", ".error(e.data)": ".error(e.data)", ".error(\"Stream error:\", e)": ".error(\"流错误:\", e)", ".error(\"*API Error. See console logs for details.*\")": ".error(\"*API错误。有关详细信息，请参阅控制台日志*\")", ".error(\"Failed to start stream:\", err)": ".error(\"无法启动流：\", err)", " error() {\n    return this._res.error || null;\n  }\n  /**\n   * Convert response to OpenAI format\n   * @returns {Object} Response in OpenAI format\n   */\n  to_openai()": " error() {\n    return this._res.error || null;\n  }\n  /**\n   * Convert response to OpenAI format\n   * @returns {Object} Response in OpenAI format\n   */\n  to_openai()", " error() {\n    if (!this._res.error)": " error() {\n    if (!this._res.error)", ".error(\"Failed to fetch Groq model data:\", error)": ".error(\"无法获取Groq模型数据：\", error)", ".error(\"Error in SmartHttpObsidianRequestAdapter.request()": ".error(\"Error in SmartHttpObsidianRequestAdapter.request()", ".error(JSON.stringify(request_params, null, 2)": ".error(JSON.stringify(request_params, null, 2)", ".error(response)": ".error(response)", ".error(\"Error in handle_message_from_user:\", error)": ".error(\"处理用户消息时出错：\", error)", "_error(response)": "_error(response)", ".error(\"error_handler\", response)": ".error(\"错误处理程序\", response)", "_error(response, container = this.messages_container)": "_error(response, container = this.messages_container)", "_error({ message: `No handler for tool: ${tool_call.function.name}` })": "_error({ message: `工具 ${tool_call.function.name} 没有对应的处理程序` })", ".error(`Error handling tool call ${tool_call.function.name}:`, error)": ".error(`错误处理工具调用${tool_call.function.name}：`, error)", "_error({ message: `Failed to execute tool: ${tool_call.function.name}`, error })": "_error({ message: `执行工具失败：${tool_call.function.name}`, error })", ".error(\"Failed to copy message: \", err)": ".error(\"复制邮件失败：\", err)", ".error(\"Failed to copy system message:\", err)": ".error(\"复制系统消息失败:\", err)", ".error(`Error fetching internal links content:`, error)": ".error(`获取内部链接内容时出错：`, error)", ".error(`Failed to create server after retry. Port ${this.port} is still in use.`)": ".error(`Failed to create server after retry. Port ${this.port} is still in use.`)", ".error(err)": ".error(err)", ".error(\"OAuth callback error\", err)": ".error(\"OAuth回调错误\", err)", "name: \"ejs\"": "name: \"ejs\"", "name: \"Minimum length\"": "name: \"最小长度\"", "name: \"Model Platform\"": "name: \"模型平台\"", "name: \"Embedding Model Platform\"": "name: \"嵌入式模型平台\"", "name: \"No models currently available\"": "name: \"当前\"", "name: \"Chat Model Platform\"": "name: \"聊天模型平台\"", "name: \"Chat Model\"": "name: \"聊天模块\"", "name: \"Refresh Models\"": "name: \"刷新模型\"", "name: \"API Key\"": "name: \"API密钥\"", "name: \"\",\n            arguments: \"": "好的，请告诉我插件的名称，我会开始翻译工作。name: \"\",\n            arguments: \"", "name: \"Claude 3.7 Sonnet (Latest)\"": "name: \"<PERSON> 3.7 <PERSON><PERSON> (Latest)\"", "name: \"claude-3-7-sonnet-latest\"": "name: \"claude-3-7-sonnet-latest\"", "name: \"Claude 3.5 Sonnet (Latest)\"": "name: \"<PERSON> 3.5 <PERSON><PERSON> (Latest)\"", "name: \"claude-3.5-sonnet-latest\"": "name: \"claude-3.5-sonnet-latest\"", "name: \"Claude 3.5 Haiku (Latest)\"": "name: \"<PERSON> 3.5 <PERSON><PERSON> (Latest)\"", "name: \"claude-3.5-haiku-latest\"": "name: \"claude-3.5-haiku-latest\"", "name: \"Claude 3 Opus (Latest)\"": "name: \"Claude 3 Opus (Latest)\"", "name: \"claude-3-opus-latest\"": "name: \"claude-3-opus-latest\"", "name: \"Claude 3.7 Sonnet (2025-02-19)\"": "name: \"<PERSON> 3.7 Sonnet (2025-02-19)\"", "name: \"claude-3-7-sonnet-20250219\"": "name: \"claude-3-7-sonnet-20250219\"", "name: \"Claude 3.5 Sonnet (2024-10-22)\"": "name: \"<PERSON> 3.5 <PERSON><PERSON> (2024-10-22)\"", "name: \"claude-3-5-sonnet-20241022\"": "name: \"claude-3-5-sonnet-20241022\"", "name: \"Claude 3.5 Sonnet (2024-06-20)\"": "name: \"<PERSON> 3.5 Son<PERSON> (2024-06-20)\"", "name: \"claude-3.5-sonnet-20240620\"": "name: \"claude-3.5-sonnet-20240620\"", "name: \"Claude 3.5 Haiku (2024-10-22)\"": "name: \"<PERSON> 3.5 <PERSON><PERSON> (2024-10-22)\"", "name: \"claude-3-5-haiku-20241022\"": "name: \"claude-3-5-haiku-20241022\"", "name: \"Claude 3 Opus (2024-02-29)\"": "name: \"<PERSON> 3 Opus (2024-02-29)\"", "name: \"claude-3-opus-20240229\"": "name: \"claude-3-opus-20240229\"", "name: \"Claude 3 Haiku (2024-03-07)\"": "name: \"<PERSON> 3 <PERSON>ku (2024-03-07)\"", "name: \"claude-3-haiku-20240307\"": "name: \"claude-3-haiku-20240307\"", "name: \"claude-3.5-sonnet-20241022\"": "name: \"claude-3.5-sonnet-20241022\"", "name: \"Claude 3 Sonnet (2024-02-29)\"": "name: \"<PERSON> 3 Sonnet (2024-02-29)\"", "name: \"claude-3-sonnet-20240229\"": "name: \"claude-3-sonnet-20240229\"", "name: \"Image Resolution\"": "name: \"图像分辨率\"", "name: \"\",\n    azure_deployment_name: \"": "name: \"\",\n    azure_deployment_name: \"", "name: \"Azure Resource Name\"": "name: \"Azure 资源名称\"", "name: \"Azure Deployment Name\"": "name: \"Azure部署名称\"", "name: \"Azure API Version\"": "name: \"Azure API版本\"", "name: \"API Adapter\"": "name: \"API适配器\"", "name: \"Model Name\"": "name: \"型号名称\"", "name: \"Protocol\"": "name: \"协议\"", "name: \"Hostname\"": "name: \"主机名\"", "name: \"Port\"": "name: \"端口\"", "name: \"Path\"": "name: \"路径\"", "name: \"Streaming\"": "name: \"实时传输\"", "name: \"Max Input Tokens\"": "name: \"最大输入令牌数\"", "name: \"English\"": "name: \"英文\"", "name: \"Espa\\xF1ol\"": "name: \"西班牙语\"", "name: \"Fran\\xE7ais\"": "name: \"法语\"", "name: \"Deutsch\"": "name: \"德国\"", "name: \"Italiano\"": "name: \"意大利语\"", "name: \"Portugu\\xEAs\"": "name: \"葡萄牙语\"", "name: \"Portugu\\xEAs (Brasil)\"": "name: \"葡萄牙语（巴西）\"", "name: \"\\u65E5\\u672C\\u8A9E\"": "name: \"日语\"", "name: \"\\uD55C\\uAD6D\\uC5B4\"": "name: \"韩语\"", "name: \"\\u4E2D\\u6587\\uFF08\\u7B80\\u4F53\\uFF09\"": "name: \"简体中文\"", "name: \"\\u4E2D\\u6587\\uFF08\\u7E41\\u4F53\\uFF09\"": "name: \"中文（繁体）\"", "name: \"\\u0939\\u093F\\u0928\\u094D\\u0926\\u0940\"": "name: \"印地语\"", "name: \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"": "name: \"阿拉伯语\"", "name: \"\\u09AC\\u09BE\\u0982\\u09B2\\u09BE\"": "name: \"班加拉\"", "name: \"\\u0627\\u0631\\u062F\\u0648\"": "name: \"乌尔都\"", "name: \"Kiswahili\"": "name: \"斯瓦希里语\"", "name: \"Polski\"": "name: \"波兰语\"", "name: \"\\u0423\\u043A\\u0440\\u0430\\u0457\\u043D\\u0441\\u044C\\u043A\\u0430\"": "name: \"乌克兰语\"", "name: \"Nederlands\"": "name: \"荷兰的\"", "name: \"Svenska\"": "name: \"瑞典语\"", "name: \"T\\xFCrk\\xE7e\"": "name: \"土耳其\"", "name: \"\\u0420\\u0443\\u0441\\u0441\\u043A\\u0438\\u0439\"": "name: \"俄罗斯的\"", "name: \"Language\"": "name: \"语言\"", "name: \"Review Context\"": "name: \"审查上下文\"", "name: \"Lookup Limit\"": "name: \"查找限制\"", "name: \"Send Tool Output in User Message\"": "name: \"发送工具输出至用户消息\"", "description: \"Minimum length of entity to embed (in characters).\"": "description: \"要嵌入的实体的最小长度（以字符为单位）。\"", "description: \"Select a model platform to use with Smart Model.\"": "description: \"选择要与Smart model一起使用的模型平台。\"", "description: \"Select an embedding model platform.\"": "description: \"选择一个嵌入模型平台。\"", "description: \"OpenAI (API)\"": "description: \"OpenAI（API）\"", "description: \"Transformers (Local, built-in)\"": "description: \"变压器（本地，内置）\"", "description: \"Select a chat model platform to use with Smart Chat.\"": "description: \"选择要与Smart chat一起使用的聊天模式平台。\"", "description: \"Select a chat model to use with Smart Chat.\"": "description: \"选择一个与智能聊天一起使用的聊天模型。\"", "description: \"Refresh the list of available models.\"": "description: \"刷新可用型号列表。\"", "description: \"Enter your API key for the chat model platform.\"": "description: \"输入您在聊天模型平台的API密钥。\"", "description: \"Anthropic Claude\"": "description: \"Anthropic Claude\"", "description: \"Anthropic's Claude Sonnet (Latest)\"": "description: \"Anthropic's <PERSON> (Latest)\"", "description: \"Anthropic's Claude Haiku (Latest)\"": "description: \"Anthropic's <PERSON> (Latest)\"", "description: \"Anthropic's Claude Opus (Latest)\"": "description: \"<PERSON>thropic's <PERSON> (Latest)\"", "description: \"Anthropic's Claude Sonnet (2025-02-19)\"": "description: \"Anthropic's <PERSON> (2025-02-19)\"", "description: \"Anthropic's Claude Sonnet (2024-10-22)\"": "description: \"Anthropic's <PERSON> (2024-10-22)\"", "description: \"Anthropic's Claude Sonnet (2024-06-20)\"": "description: \"Anthropic's <PERSON> (2024-06-20)\"", "description: \"Anthropic's Claude Haiku (2024-10-22)\"": "description: \"Anthropic's <PERSON> (2024-10-22)\"", "description: \"Anthropic's Claude Opus\"": "description: \"<PERSON>throp<PERSON>'s <PERSON>\"", "description: \"Anthropic's Claude Haiku (2024-03-07)\"": "description: \"Anthropic's <PERSON> (2024-03-07)\"", "description: \"Anthropic's Claude Sonnet\"": "description: \"Anthropic的<PERSON>\"", "description: \"OpenAI\"": "description: \"OpenAI 是一个人工智能研究实验室。\"", "description: \"Select the image resolution for the chat model.\"": "description: \"选择聊天模型的图像分辨率。\"", "description: \"Azure OpenAI\"": "description: \"Azure OpenAI -> Azure 开放人工智能\"", "description: \"The name of your Azure OpenAI resource (e.g. 'my-azure-openai').\"": "description: \"Azure OpenAI 资源的名称（例如 'my-azure-openai'）。\"", "description: \"The name of your specific model deployment (e.g. 'gpt35-deployment').\"": "description: \"您的特定模型部署的名称（例如“gpt35部署”）。\"", "description: \"The API version for Azure OpenAI (e.g. '2024-10-01-preview').\"": "description: \"Azure OpenAI的API版本（例如'2024-10-01-preview'）。\"", "description: `Model: ${d.model}, Status: ${d.status}`": "description: `型号：$｛d.Model｝，状态：$｛d.Status｝`", "description: \"Google Gemini\"": "description: \"Google Gemini\"", "description: \"LM Studio (OpenAI-compatible)\"": "description: \"LM工作室（兼容OpenAI）\"", "description: `LM Studio model: ${m.id}`": "description: `LM Studio型号：${m.id}`", "description: \"Ollama (Local)\"": "description: \"<PERSON><PERSON><PERSON>（本地）\"", "description: \"Custom API (Local or Remote, OpenAI format)\"": "description: \"自定义API（本地或远程，OpenAI格式）\"", "description: \"Pick a built-in or external adapter to parse request/response data.\"": "description: \"选择一个内置或外部适配器来解析请求/响应数据。\"", "description: \"Enter the model name for your endpoint if needed.\"": "description: \"如果需要，请输入端点的模型名称。\"", "description: \"e.g. http or https\"": "description: \"例如 http 或 https\"", "description: \"e.g. localhost or some.remote.host\"": "description: \"例如 localhost 或 some.remote.host\"", "description: \"Port number or leave blank\"": "description: \"端口号或留空\"", "description: \"Path portion of the URL (leading slash optional)\"": "description: \"URL的路径部分（前导斜线可选）\"", "description: \"Enable streaming if your API supports it.\"": "description: \"如果您的API支持，启用流式传输。\"", "description: \"Max number of tokens your model can handle in the prompt.\"": "description: \"模型在提示中可以处理的最大令牌数。\"", "description: \"If your service requires an API key, add it here.\"": "description: \"如果您的服务需要 API 密钥，请在此处添加。\"", "description: \"Groq\"": "description: \"Groq\"", "description: `Owned by: ${model.owned_by}, context: ${model.context_window}`": "description: `所有者：$｛model.Owned_by｝，上下文：$｛model.context_window｝`", "description: `Owned by: ${m.owned_by}, context: ${m.context_window}`": "description: `所有者: ${m.owned_by}，上下文: ${m.context_window}`", "description: \"The language to use for the chat.\"": "description: \"聊天使用的语言。\"", "description: \"Whether to review the retrieved context before the AI completes the message.\"": "description: \"在AI完成消息之前，是否查看检索到的上下文。\"", "description: \"The maximum number of context items to retrieve via lookup.\"": "description: \"通过查找检索的最大上下文项数。\"", "description: \"Whether to send tool output in the user message.\"": "description: \"Whether to send tool output in the user message.\"", "description: \"Performs a semantic search of the user's data to surface relevant content.\"": "description: \"对用户数据执行语义搜索以显示相关内容。\"", "description: \"Predicted relevant notes in markdown format. Provide at least three.\"": "description: \"预测相关笔记以Markdown格式呈现。至少提供三个。\"", "text: \"\"\n            }\n          ],\n          role: \"": "好的，请提供需要翻译的英文文本。text: \"\"\n            }\n          ],\n          role: \"", "text: `Use the \"${this.tool_choice.function.name}\" tool.`": "text: `使用“${this.tool_choice.function.name}”工具。`", "text: `Image not found: ${part.input.image_path}`": "text: `找不到图像：${part.input.Image_path}`", "text: \"Refreshing...\"": "text: \"刷新中...\"", "text: \"Loading lookup...\"": "text: \"正在加载查找。。。\"", "text: \"Copy URL\"": "text: \"复制URL\"", "text: \"Loading...\"": "text: \"加载中...\"", "page: \"https://github.com/mde/ejs\"": "page: \"https://github.com/mde/ejs\"", ".setPlaceholder(\"Select input type...\")": ".setPlaceholder(\"选择输入类型...\")", ".setPlaceholder(\"Find and select a context...\")": ".setPlaceholder(\"查找并选择上下文。。。\")", ".setPlaceholder(\"Type the name of an image...\")": ".setPlaceholder(\"键入图像的名称。。。\")", ".innerText = `${this.env.smart_sources.keys.length} (${this.env.smart_blocks.keys.length})`": ".innerText = `${this.env.smart_sources.keys.length} (${this.env.smart_blocks.keys.length})`", ".innerText = \"Loading...\"": ".innerText = \"加载。。。\"", "placeholder: \"Enter number ex. 300\"": "placeholder: \"输入数字,例如:300\"", "placeholder: \"Enter OpenAI API Key\"": "placeholder: \"输入 OpenAI API 密钥\"", "placeholder: \"Enter number ex. 10\"": "placeholder: \"输入数字,例如:10\"", "data-description='Chat with your notes in ChatGPT without uploading your notes to the cloud!'": "data-description='在 ChatGPT 中与您的笔记进行聊天,而无需将笔记上传到云端！'", "data-description='Join the supporter community chat.'": "data-description='加入支持者社区聊天。'", "data-description='Revert to the stable release of Smart Connections. Requires \"Check for Updates\" and then \"Update Plugin\" to complete the process.'": "data-description='回退到 Smart Connections 的稳定版本。需要先“检查更新”,然后“更新插件”以完成该过程。'", "data-description=\"Toggle the re-ranker\"": "data-description=\"切换重新排序器\"", "data-description=\"Show full path in view.\"": "data-description=\"在视图中显示完整路径\"", "data-description=\"Limit the number of results.\"": "data-description=\"限制结果数量\"", "data-description=\"Exclude inlinks\"": "data-description=\"排除内链\"", "data-description=\"Exclude outlinks\"": "data-description=\"排除外链\"", "data-description=\"Require that results match this value.\"": "data-description=\"要求结果匹配此值\"", "data-description=\"Exclude results that match this value.\"": "data-description=\"排除匹配此值的结果\"", "data-description=\"Default language to use for Smart Chat. Changes which self-referential pronouns will trigger lookup of your notes. <b id='self-referential-pronouns'>Current: my, I, me, mine, our, ours, us, we</b>\"": "data-description=\"智能聊天使用的默认语言。更改哪些自指代词将触发查找您的笔记。<b id='self-referential-pronouns'>当前:my, I, me, mine, our, ours, us, we</b>\"", "data-description=\"Select a model platform to use with Smart Chat.\"": "data-description=\"选择与智能聊天一起使用的模型平台\"", "data-description=\"<a href='<%= chat_platform.signup_url %>'>Get API Key</a> for <%= chat_platform.description %>.\"": "data-description=\"获取用于 <%= chat_platform.description %> 的 <a href='<%= chat_platform.signup_url %>'>API 密钥</a>\"", "data-description=\"API Key for <%= chat_platform.description %>.\"": "data-description=\"用于 <%= chat_platform.description %> 的 API 密钥\"", "data-description=\"Name of the custom model.\"": "data-description=\"自定义模型的名称\"", "data-description=\"Protocol for chat server (http or https).\"": "data-description=\"聊天服务器的协议(http 或 https)\"", "data-description=\"Host for local chat server.\"": "data-description=\"本地聊天服务器的主机\"", "data-description=\"Port for local chat server.\"": "data-description=\"本地聊天服务器的端口\"", "data-description=\"Path for local chat server.\"": "data-description=\"本地聊天服务器的路径\"", "data-description=\"Enable streaming for local chat server. Disable if you are getting CORS errors.\"": "data-description=\"启用本地聊天服务器的流媒体。如果您遇到 CORS 错误,请禁用\"", "data-description=\"Maximum number of tokens for input to the model.\"": "data-description=\"输入模型的最大令牌数\"", "data-description=\"Host for chat server.\"": "data-description=\"聊天服务器的主机\"", "data-description=\"Path for chat server.\"": "data-description=\"聊天服务器的路径\"", "data-description=\"Enable streaming for chat server. Disable if you are getting CORS errors.\"": "data-description=\"启用聊天服务器的流媒体。如果您遇到 CORS 错误,请禁用\"", "data-description=\"API Key for the custom server sent as a header (bearer token).\"": "data-description=\"作为头部发送给自定义服务器的 API 密钥(承载令牌)\"", "data-description=\"Upgrade to v2.2 (Early Access) to access new features and improvements.\"": "data-description=\"升级到 v2.2(抢先体验版),以访问新功能和改进\"", "data-description=\"Become a Supporter\"": "data-description=\"成为支持者\"", "data-description=\"Note: this is not required to use Smart Connections.\"": "data-description=\"注意:这不是使用智能连接所必需的\"", "data-description=\"Enable mobile support for Smart Connections.\"": "data-description=\"启用智能连接的移动设备支持\"", "data-btn-text=\"Upgrade to v2.2\"": "data-btn-text=\"升级到 v2.2\"", "data-btn-text=\"Open GPT\"": "data-btn-text=\"打开 GPT\"", "data-btn-text=\"Join us\"": "data-btn-text=\"加入我们\"", "data-btn-text=\"Unmute\"": "data-btn-text=\"取消静音\"", "data-btn-text=\"Revert\"": "data-btn-text=\"回退\"", "aria-label=\"Open help documentation\"": "aria-label=\"打开帮助文档\"", "data-button=\"Save\"": "data-button=\"保存\"", "data-placeholder=\"Enter a folder name\"": "data-placeholder=\"输入文件夹名称\"", "data-placeholder=\"Enter an API Key\"": "data-placeholder=\"输入 API 密钥\"", "data-placeholder=\"Enter a model name\"": "data-placeholder=\"输入模型名称\"", "data-placeholder=\"Enter a protocol\"": "data-placeholder=\"输入协议\"", "data-placeholder=\"Enter a host\"": "data-placeholder=\"输入主机\"", "data-placeholder=\"Enter a port number\"": "data-placeholder=\"输入端口号\"", "data-placeholder=\"Enter a path\"": "data-placeholder=\"输入路径\"", "data-placeholder=\"Enter a number\"": "data-placeholder=\"输入数字\"", "data-placeholder=\"Enter your license_key\"": "data-placeholder=\"输入您的许可证密钥\"", "data-name=\"Smart Chat History Folder\"": "data-name=\"智能聊天历史文件夹\"", "data-name=\"System Prompts Folder\"": "data-name=\"系统提示文件夹\"", "data-name=\"Cohere API Key\"": "data-name=\"Cohere API 密钥\"", "data-name=\"Toggle Re-Ranker\"": "data-name=\"切换重新排序器\"", "data-name=\"Show Full Path\"": "data-name=\"显示完整路径\"", "data-name=\"Results Limit\"": "data-name=\"结果限制\"", "data-name=\"Exclude Inlinks\"": "data-name=\"排除内链\"", "data-name=\"Exclude Outlinks\"": "data-name=\"排除外链\"", "data-name=\"Include Filter\"": "data-name=\"包含过滤\"", "data-name=\"Exclude Filter\"": "data-name=\"排除过滤\"", "data-name=\"Default Language\"": "data-name=\"默认语言\"", "data-name=\"Model Platform\"": "data-name=\"模型平台\"", "data-name=\"Model Name\"": "data-name=\"模型名称\"", "data-name=\"Refresh Models List\"": "data-name=\"刷新模型列表\"", "data-name=\"<%= chat_platform.description %> API Key\"": "data-name=\"<%= chat_platform.description %> API 密钥\"", "data-name=\"protocol\"": "data-name=\"协议\"", "data-name=\"hostname\"": "data-name=\"主机\"", "data-name=\"port\"": "data-name=\"端口\"", "data-name=\"path\"": "data-name=\"路径\"", "data-name=\"streaming\"": "data-name=\"流媒体\"", "data-name=\"Max input tokens\"": "data-name=\"最大输入令牌\"", "data-name=\"API Key\"": "data-name=\"API 密钥\"", "data-name=\"Upgrade to Early Access Version (v2.2)\"": "data-name=\"升级到抢先体验版 (v2.2)\"", "data-name=\"Become a Supporter\"": "data-name=\"成为支持者\"", "data-name=\"Supporter License Key\"": "data-name=\"支持者许可证密钥\"", "data-name=\"Smart Connect - Obsidian GPT\"": "data-name=\"智能连接 - Obsidian GPT\"", "data-name=\"Supporter Community Chat\"": "data-name=\"支持者社区聊天\"", "data-name=\"Enable Mobile (EXPERIMENTAL)\"": "data-name=\"启用移动设备支持(实验性)\"", "data-name=\"Revert to Stable Release\"": "data-name=\"回退到稳定版本\"", "description: \"Custom Local (OpenAI format)\"": "description: \"自定义本地(OpenAI 格式)\"", "description: \"Custom API (OpenAI format)\"": "description: \"自定义 API(OpenAI 格式)\"", "description: `Max input tokens: ${model.context_length}, Finetuned: ${model.finetuned}`": "description: `最大输入标记数: ${model.context_length}, 微调: ${model.finetuned}`", "description: \"Local, 512 tokens, 384 dim\"": "description: \"本地,512 标记,384 维\"", "description: \"Local, 4,096 tokens, 384 dim\"": "description: \"本地,4,096 标记,384 维\"", "description: \"Local, 8,192 tokens, 512 dim, Chinese/English bilingual\"": "description: \"本地,8,192 标记,512 维,中英文双语\"", "description: \"API, 8,191 tokens, 1,536 dim\"": "description: \"API,8,191 标记,1,536 维\"", "description: \"API, 8,191 tokens, 3,072 dim\"": "description: \"API,8,191 标记,3,072 维\"", "description: \"API, 8,191 tokens, 512 dim\"": "description: \"API,8,191 标记,512 维\"", "description: \"API, 8,191 tokens, 256 dim\"": "description: \"API,8,191 标记,256 维\"", "description: \"Local, 8,192 tokens, 512 dim\"": "description: \"本地,8,192 标记,512 维\"", "description: \"Local, 8,192 tokens, 256 dim\"": "description: \"本地,8,192 标记,256 维\"", "description: \"Local, 2,048 tokens, 768 dim\"": "description: \"本地,2,048 标记,768 维\"", "description: \"Minimum length of note to embed.\"": "description: \"要嵌入的笔记的最短长度。\"", "description: \"Performs a semantic search of the user's data. Required: hypothetical_1 and hypothetical_2. Optional: hypothetical_3.\"": "description: \"对用户数据执行语义搜索。必需:hypothetical_1 和 hypothetical_2。可选:hypothetical_3。\"", "description: \"Short hypothetical notes predicted to be semantically similar to the notes necessary to fulfill the user's request. At least three hypotheticals per request. The hypothetical notes may contain paragraphs, lists, or checklists in markdown format. Hypothetical notes always begin with breadcrumbs containing the anticipated folder(s), file name, and relevant headings separated by ' > ' (no slashes). Format: PARENT FOLDER NAME > CHILD FOLDER NAME > FILE NAME > HEADING 1 > HEADING 2 > HEADING 3: HYPOTHETICAL NOTE CONTENTS.\"": "description: \"预测为与满足用户请求所需的笔记语义相似的简短假设笔记。每个请求至少需要三个假设笔记。假设笔记可以包含段落、列表或检查列表,格式为 Markdown。假设笔记始终以面包屑开始,其中包含预期的文件夹、文件名和相关标题,用 ' > ' 分隔(不使用斜杠)。格式:父文件夹名称 > 子文件夹名称 > 文件名 > 标题1 > 标题2 > 标题3:假设笔记内容。\"", "description: \"Must be distinct from and not share any breadcrumbs with hypothetical_1.\"": "description: \"必须与 hypothetical_1 不同,并且不能共享任何面包屑。\"", "description: \"Must be distinct from hypothetical_1 and hypothetical_2.\"": "description: \"必须与 hypothetical_1 和 hypothetical_2 不同。\"", "\"description\": \"Enable Smart Changes (prevents accidental deletions/overwrites).\"": "\"description\": \"启用智能更改(防止意外删除/覆盖)。\"", "text: `IMPORTANT: You must use the \"${body.tools[0].function_declarations[0].name}\" function tool!`": "text: `重要提示:您必须使用 \"${body.tools[0].function_declarations[0].name}\" 函数工具！`", "text: `Image caption: ${caption}`": "text: `图片说明: ${caption}`", "text: \"Pause\"": "text: \"暂停\"", "text: \"Resume\"": "text: \"恢复\"", "text: \"Open Smart View\"": "text: \"打开智能视图\"", ".setPlaceholder(\"Type the name of a file...\")": ".setPlaceholder(\"输入文件名...\")", ".setPlaceholder(\"Type the name of a system prompt...\")": ".setPlaceholder(\"输入系统提示名...\")", "console.error(\"Invalid input for count_tokens\", input);": "console.error(\"无效的 count_tokens 输入\", input);", "console.error(\"Failed to fetch model data:\", error);": "console.error(\"获取模型数据失败:\", error);", "console.error(\"No API key provided\");": "console.error(\"未提供 API 密钥\");", "console.error(`No models found for ${this.platform_key}`, models);": "console.error(`未找到 ${this.platform_key} 的模型`, models);", "console.error(\"get_many called with non-array keys: \", keys);": "console.error(\"get_many 调用时使用了非数组类型的键: \", keys);", "console.error(`Error loading SmartEmbedModel for ${this.embed_model_key}`);": "console.error(`加载 SmartEmbedModel 时出错: ${this.embed_model_key}`);", "console.error(`Error processing ${this.collection_key} embed queue: ` + JSON.stringify(e || {}, null, 2));": "console.error(`处理 ${this.collection_key} 嵌入队列时出错: ` + JSON.stringify(e || {}, null, 2));", "console.error(\"Error during update:\", error);": "console.error(\"更新过程中出错:\", error);", "console.error(\"Error during read:\", error);": "console.error(\"读取过程中出错:\", error);", "console.error(\"Error during remove:\", error);": "console.error(\"删除过程中出错:\", error);", "console.error(\"error_during_move:\", error);": "console.error(\"移动过程中出错:\", error);", "console.error(\"Error during merge:\", error);": "console.error(\"合并过程中出错:\", error);", "console.error(`Error searching item ${item.id || \"unknown\"}:`, error);": "console.error(`搜索项目 ${item.id || \"未知\"} 时出错:`, error);", "console.error(`Error getting embed queue: ` + JSON.stringify(e || {}, null, 2));": "console.error(`获取嵌入队列时出错: ` + JSON.stringify(e || {}, null, 2));", "console.error(\"error_during_block_move:\", error);": "console.error(\"块移动过程中出错:\", error);", "console.error(`Error getting should_embed for ${this.key}: ` + JSON.stringify(e || {}, null, 2));": "console.error(`获取 ${this.key} 的 should_embed 时出错: ` + JSON.stringify(e || {}, null, 2));", "console.error(\"error_embedding_batch\", err);": "console.error(\"批量嵌入出错\", err);", "console.error(\"Error processing message:\", error);": "console.error(\"处理消息时出错:\", error);", "console.error(`Error checking if path is excluded: ${e.message}`);": "console.error(`检查路径是否被排除时出错: ${e.message}`);", "console.error(`Path: `, _path);": "console.error(`路径: `, _path);", "console.error(\"Error during write:\", error);": "console.error(\"写入过程中出错:\", error);", "console.error(\"No callback or href found for button.\");": "console.error(\"未找到按钮的回调或 href.\");", "console.error(`Options callback ${elm.dataset.optionsCallback} is not a function.`);": "console.error(`选项回调 ${elm.dataset.optionsCallback} 不是一个函数.`);", "console.error(`Failed to create server after retry. Port ${this.port} is still in use.`);": "console.error(`重试后未能创建服务器。端口 ${this.port} 仍然被占用.`);", "console.error(\"Error downloading early access update\", v2);": "console.error(\"下载早期访问更新时出错\", v2);", "Error(`Method \"${method_name}\" is not implemented for file type \"${this.item.file_type}\" in \"${this.constructor.name}\".`);": "Error(`方法 \"${method_name}\" 未为文件类型 \"${this.item.file_type}\" 在 \"${this.constructor.name}\" 中实现。`);", "Error(`BLOCK NOT FOUND: No block found with key \"${block_key}\".`);": "Error(`块未找到: 没有找到键为 \"${block_key}\" 的块。`);", "Error(`Input size (${filtered_inputs.length}) exceeds maximum batch size (${this.batch_size})`);": "Error(`输入大小 (${filtered_inputs.length}) 超过最大批量大小 (${this.batch_size})`);", "Error(`Unknown method: ${method}`);": "Error(`未知方法: ${method}`);", "Error(`Path is excluded: ${paths.find((p) => this.is_excluded(p))}`);": "Error(`路径已排除: ${paths.find((p) => this.is_excluded(p))}`);", "Error(`Method ${method} not found in adapter`);": "Error(`在适配器中未找到方法 ${method}`);", "Error(`Template '${template_name}' not found.`);": "Error(`模板 '${template_name}' 未找到。`);", "Error(`Settings template not found.`);": "Error(`未找到设置模板。`);", "Error(`Reference not found: ${path}`);": "Error(`未找到引用: ${path}`);", "Error(`Unable to start server on port ${this.port} after retry.`));\n          }\n        } else {\n          reject(error);\n        }\n      });\n      this.server.listen(this.port, () => {\n        console.log(`Server running at http://localhost:${this.port}/`);": "Error(`在重试后仍无法在端口 ${this.port} 启动服务器。`));\n          }\n        } else {\n          reject(error);\n        }\n      });\n      this.server.listen(this.port, () => {\n        console.log(`服务器正在运行于 http://localhost:${this.port}/`);", "Error('Could not find the include file \"' + options.escapeFunction(path2) + '\"');": "Error('无法找到包含文件 \"' + options.escapeFunction(path2) + '\"');", "Error('Could not find matching close tag for \"' + line + '\".');": "Error('找不到与 \"' + line + '\" 相匹配的闭合标签。');", "Error(\"Invalid string. Length must be a multiple of 4\");": "Error(\"无效的字符串。长度必须是4的倍数\");", "Error(\"Cannot find module '\" + i2 + \"'\");": "Error(\"无法找到模块 '\" + i2 + \"'\");", "Error(\"cache option requires a filename\");": "Error(\"cache 选项需要一个文件名\");", "Error(\"Internal EJS error: no file name or template provided\");": "Error(\"内部 EJS 错误: 未提供文件名或模板\");", "Error(\"Please provide a callback function\");": "Error(\"请提供一个回调函数\");", "Error(\"outputFunctionName is not a valid JS identifier.\");": "Error(\"outputFunctionName 不是有效的 JS 标识符。\");", "Error(\"localsName is not a valid JS identifier.\");": "Error(\"localsName 不是有效的 JS 标识符。\");", "Error(\"destructuredLocals[\" + i + \"] is not a valid JS identifier.\");": "Error(\"destructuredLocals[\" + i + \"] 不是有效的 JS 标识符。\");", "Error(\"This environment does not support async/await\");": "Error(\"此环境不支持 async/await\");", "Error(\"Arguments to path.resolve must be strings\");": "Error(\"path.resolve 的参数必须是字符串\");", "Error(\"Arguments to path.join must be strings\");": "Error(\"path.join 的参数必须是字符串\");", "Error(\"setTimeout has not been defined\");": "Error(\"setTimeout 未定义\");", "Error(\"clearTimeout has not been defined\");": "Error(\"clearTimeout 未定义\");", "Error(\"process.binding is not supported\");": "Error(\"process.binding 不受支持\");", "Error(\"process.chdir is not supported\");": "Error(\"process.chdir 不受支持\");", "Error(\"Network response was not ok\");": "Error(\"网络响应不正常\");", "Error(\"SmartEnv: Invalid main object provided\");": "Error(\"SmartEnv: 提供的主对象无效\");", "Error(\"Container is required\");": "Error(\"需要容器\");", "Error(\"Item must have key property\");": "Error(\"项必须具有 key 属性\");", "Error(\"No data adapter class found for \" + this.collection_key + \" or smart_collections\");": "Error(\"未找到数据适配器类 \" + this.collection_key + \" 或 smart_collections\");", "Error(\"Vectors must have the same length\");": "Error(\"向量必须具有相同的长度\");", "Error(\"Invalid entity reference for move_to operation\");": "Error(\"无效的实体引用用于 move_to 操作\");", "Error(\"merge error: markdown_to_blocks did not return an object as expected.\");": "Error(\"合并错误: markdown_to_blocks 未按预期返回对象。\");", "Error(\"SmartCollectionDataAdapter: load() not implemented\");": "Error(\"SmartCollectionDataAdapter: load() 未实现\");", "Error(\"SmartCollectionDataAdapter: save() not implemented\");": "Error(\"SmartCollectionDataAdapter: save() 未实现\");", "Error(\"Not implemented\");": "Error(\"未实现\");", "Error(\"embed_input must be a string\");": "Error(\"embed_input 必须是一个字符串\");", "Error(\"Model not loaded\");": "Error(\"模型未加载\");", "Error(\"SmartFs requires an adapter\");": "Error(\"SmartFs 需要一个适配器\");", "Error(\"setting_class() not implemented\");": "Error(\"setting_class() 未实现\");", "Error(\"get_icon_html() not implemented\");": "Error(\"get_icon_html() 未实现\");", "Error(\"render_markdown() not implemented\");": "Error(\"render_markdown() 未实现\");", "Error(\"open_url() not implemented\");": "Error(\"open_url() 未实现\");", "Error(\"handle_folder_select not implemented\");": "Error(\"handle_folder_select 未实现\");", "Error(\"handle_file_select not implemented\");": "Error(\"handle_file_select 未实现\");", "Error(\"view_type must be implemented in subclass\");": "Error(\"view_type 必须在子类中实现\");", "Error(\"display_text must be implemented in subclass\");": "Error(\"display_text 必须在子类中实现\");", "Error(\"render_view must be implemented in subclass\");": "Error(\"render_view 必须在子类中实现\");", "Error(\"You have used up your free chat limit! Please add your own API key in the Smart Chat settings to enable unlimited personal usage and prevent exhausting the shared community account limit.\");": "Error(\"您已用完免费聊天额度！请在 智能聊天 设置中添加您自己的 API 密钥,以启用无限制的个人使用并防止耗尽共享社区账户的额度。\");", "Error(\"Smart Connections: plugin disabled while waiting for obsidian sync\");": "Error(\"智能连接: 插件在等待 obsidian 同步时被禁用\");", "console.warn(\"`scope` option is deprecated and will be removed in EJS 3\")": "console.warn(\"`scope` 选项已弃用,将在 EJS 3 中移除\")", "console.warn(\"Unable to set escapeXML.toString (is the Function prototype frozen?)\")": "console.warn(\"无法设置 escapeXML.toString (Function 原型是否被冻结?)\")", "console.warn(`Invalid tool call: object is empty`)": "console.warn(`无效的工具调用: 对象为空`) ", "console.warn(`Invalid tool call: missing key ${key} in tool spec`, props)": "console.warn(`无效的工具调用: 工具规范中缺少键 ${key}`, props)", "console.warn(`Invalid tool call: array items are not of the same type`)": "console.warn(`无效的工具调用: 数组项类型不一致`) ", "console.warn(`Invalid tool call: array items are not of the same type as the spec`)": "console.warn(`无效的工具调用: 数组项类型与规范不符`) ", "console.warn(`Invalid tool call: value ${value} is not a valid number`)": "console.warn(`无效的工具调用: 值 ${value} 不是有效的数字`) ", "console.warn(`Invalid tool call: value ${value} is not of type ${props[key].type}`)": "console.warn(`无效的工具调用: 值 ${value} 不是类型 ${props[key].type}`)", "console.warn(`Invalid tool call: value ${value} is not in enum ${props[key].enum}`)": "console.warn(`无效的工具调用: 值 ${value} 不在枚举 ${props[key].enum} 中`) ", "console.warn(`Invalid tool call: missing required key ${key}`)": "console.warn(`无效的工具调用: 缺少必需的键 ${key}`) ", "console.warn(`Empty value for required key ${key}`)": "console.warn(`必需键 ${key} 的值为空`) ", "console.warn(`Tool ${tool_name} not found or invalid, returning tool_call_content`)": "console.warn(`工具 ${tool_name} 未找到或无效,返回 tool_call_content`) ", "console.warn(`No API key found for ${this.platform_key}. Cannot retrieve models.`)": "console.warn(`未找到 ${this.platform_key} 的 API 密钥。无法获取模型。`) ", "console.warn(`SmartEnv: module ${module_key} not found`)": "console.warn(`SmartEnv: 模块 ${module_key} 未找到`) ", "console.warn(`deep_merge_no_overwrite error (${key}): ${e.message}`)": "console.warn(`deep_merge_no_overwrite 错误 (${key}): ${e.message}`)", "console.warn(\"Invalid item, skipping adding to collection: \", item)": "console.warn(\"无效项,跳过添加到集合: \", item) ", "console.warn(\"Entity.search: keywords not set or is not an array\")": "console.warn(\"Entity.search: 关键词未设置或不是数组\")", "console.warn(\"error getting meta changed for \", this.path, \": \", e)": "console.warn(\"获取元数据变化错误 \", this.path, \": \", e)", "console.warn(\"search_filter.keywords not set\")": "console.warn(\"search_filter.keywords 未设置\")", "console.warn(\"smart_change disabled by settings\")": "console.warn(\"smart_change 被设置禁用\")", "console.warn(\"No content to import for \" + this.file_path)": "console.warn(\"没有内容可导入: \" + this.file_path)", "console.warn(\"merge error: Expected an object from markdown_to_blocks, but received:\", blocks_obj)": "console.warn(\"合并错误: 期望从 markdown_to_blocks 返回对象,但收到:\", blocks_obj)", "console.warn(\"Error reading block:\", error.message)": "console.warn(\"读取块时出错:\", error.message)", "console.warn(\"Error updating block:\", error.message)": "console.warn(\"更新块时出错:\", error.message)", "console.warn(\"Error removing block:\", error.message)": "console.warn(\"移除块时出错:\", error.message)", "console.warn(\"error removing block: \", e)": "console.warn(\"移除块时出错: \", e)", "console.warn(\"error moving block: \", e)": "console.warn(\"移动块时出错: \", e)", "console.warn(\"Error parsing line: \", line)": "console.warn(\"解析行时出错: \", line)", "console.warn(`Collection class not found: ${class_name}`)": "console.warn(`未找到集合类: ${class_name}`)", "console.warn(\"Error saving collection item: \", item.key)": "console.warn(\"保存集合项时出错: \", item.key)", "console.warn(\"SmartEmbedModel adapter not set\")": "console.warn(\"SmartEmbedModel 适配器未设置\")", "console.warn(`SmartEmbedModel adapter ${this.opts.adapter} not found`)": "console.warn(`未找到 SmartEmbedModel 适配器 ${this.opts.adapter}`)", "console.warn(\"Error during read: \" + error.message)": "console.warn(\"读取期间错误: \" + error.message)", "console.warn(\"get_link_target_path: file_paths not found\")": "console.warn(\"get_link_target_path: 未找到 file_paths\")", "console.warn(`Error removing file: ${rel_path2}`, error)": "console.warn(`移除文件时出错: ${rel_path2}`, error)", "console.warn(\"pre_change() not implemented\")": "console.warn(\"pre_change() 尚未实现\")", "console.warn(\"post_change() not implemented\")": "console.warn(\"post_change() 尚未实现\")", "console.warn(\"revert_setting() not implemented\")": "console.warn(\"revert_setting() 尚未实现\")", "console.warn(`Unsupported setting type: ${elm.dataset.type}`)": "console.warn(`不支持的设置类型: ${elm.dataset.type}`)", "console.warn(\"Scope required for rendering markdown in Obsidian adapter\")": "console.warn(\"Obsidian 适配器渲染 markdown 需要 scope\")", "console.warn(\"Error rendering markdown in Obsidian adapter\", e)": "console.warn(\"Obsidian 适配器渲染 markdown 出错\", e)", "console.warn(\"missing Obsidian Setting component\")": "console.warn(\"缺少 Obsidian 设置组件\")", "console.warn(`Error registering code block: ${name}`, error)": "console.warn(`注册代码块时出错: ${name}`, error)", "console.warn(\"No active file\", curr_file)": "console.warn(\"没有激活文件\", curr_file)", "console.warn(\"Error getting folders\", error)": "console.warn(\"获取文件夹时出错\", error)", "console.log(\"Using fetch\")": "console.log(\"使用 fetch\")", "console.log(\"Using request adapter\")": "console.log(\"使用请求适配器\")", "console.log(\"has chat model adapter\")": "console.log(\"有聊天模型适配器\")", "console.log(\"Streaming Request: \")": "console.log(\"流请求:\")", "console.log(\"ReadyState: \" + e.readyState)": "console.log(\"就绪状态: \" + e.readyState)", "console.log(\"init SmartChatRenderer\")": "console.log(\"初始化 SmartChatRenderer\")", "console.log(\"tool call\")": "console.log(\"工具调用\")", "console.log(\"Creating new conversation\")": "console.log(\"创建新会话\")", "console.log(\"get_conversation_ids_and_file_types\")": "console.log(\"获取会话ID和文件类型\")", "console.log(\"Already saving\")": "console.log(\"正在保存\")", "console.log(\"Saving \" + this.collection_key + \": \", save_queue.length + \" items\")": "console.log(\"保存 \" + this.collection_key + \": \", save_queue.length + \" 项\")", "console.log(\"Saved \" + this.collection_key + \" in \" + (Date.now()": "console.log(\"已保存 \" + this.collection_key + \" 于 \" + (Date.now()", "console.log(\"Already loading\")": "console.log(\"正在加载\")", "console.log(\"Loading \" + this.collection_key + \": \", load_queue.length + \" items\")": "console.log(\"加载 \" + this.collection_key + \": \", load_queue.length + \" 项\")", "console.log(\"Loaded \" + this.collection_key + \" in \" + this.load_time_ms + \"ms\")": "console.log(\"已加载 \" + this.collection_key + \" 用时 \" + this.load_time_ms + \" 毫秒\")", "console.log(`SmartEmbed not loaded for ${this.collection_key}. Continuing without embedding capabilities.`)": "console.log(`SmartEmbed 未加载 ${this.collection_key}。继续没有嵌入功能。`)", "console.log(`SmartEmbedModel already loading for ${this.embed_model_key}`)": "console.log(`SmartEmbedModel 已经在加载 ${this.embed_model_key}`)", "console.log(`SmartEmbedModel already loaded for ${this.embed_model_key}`)": "console.log(`SmartEmbedModel 已经加载 ${this.embed_model_key}`)", "console.log(\"Switching to legacy transformers\")": "console.log(\"切换到旧版转换器\")", "console.log(\"model_key not set\")": "console.log(\"model_key未设置\")", "console.log(\"window.document not available\")": "console.log(\"window.document不可用\")", "console.log(\"no vec\")": "console.log(\"没有向量\")", "console.log(`Found and returned ${top_k.length} ${this.collection_key}.`)": "console.log(`找到并返回了 ${top_k.length} ${this.collection_key}.`)", "console.log(`Smart Connections: No active embedding model for ${this.collection_key}, skipping embedding`)": "console.log(`智能连接:没有活动的嵌入模型,跳过嵌入 ${this.collection_key}`)", "console.log(`Smart Connections: Embed queue processing already in progress for ${this.collection_key}`)": "console.log(`智能连接:正在处理 ${this.collection_key} 的嵌入队列`)", "console.log(`Time spent getting embed queue: ${datetime_end.getTime() - datetime_start.getTime()}ms`)": "console.log(`获取嵌入队列所花时间:${datetime_end.getTime() - datetime_start.getTime()}毫秒`)", "console.log(`Smart Connections: No items in ${this.collection_key} embed queue`)": "console.log(`智能连接:${this.collection_key} 的嵌入队列没有任何项目`)", "console.log(`Processing ${this.collection_key} embed queue: ${this.queue_total} items`)": "console.log(`正在处理 ${this.collection_key} 的嵌入队列:${this.queue_total} 项`)", "console.log(\"Embed queue processing halted\")": "console.log(\"嵌入队列处理已停止\")", "console.log(\"resume_embed_queue_processing\")": "console.log(\"恢复嵌入队列处理\")", "console.log(`Smart Connections: Skipping large file: ${this.path}`)": "console.log(`智能连接:跳过大文件:${this.path}`)", "console.log(`Smart Connections: Re-loading data source for ${this.path} because it has been updated on disk`)": "console.log(`智能连接:由于磁盘上更新,重新加载数据源:${this.path}`)", "console.log(`Pruned ${remove_smart_blocks.length} blocks:\n${remove_smart_blocks.map((item) => `${item.reason} - ${item.key}`).join(\"\\n\")}`)": "console.log(`修剪了 ${remove_smart_blocks.length} 块:\n${remove_smart_blocks.map((item) => `${item.reason} - ${item.key}`).join(\"\\n\")}`)", "console.log(\"import_queue \" + import_queue.length)": "console.log(\"导入队列 \" + import_queue.length)", "console.log(`Time spent building links: ${end_time - start_time}ms`)": "console.log(`构建链接耗时:${end_time - start_time}毫秒`)", "console.log(`Time spent importing: ${end_time - start_time}ms`)": "console.log(`导入耗时:${end_time - start_time}毫秒`)", "console.log(\"File stats changed, but content is the same. Skipping import.\")": "console.log(\"文件统计信息已更改,但内容相同。跳过导入。\")", "console.log(`Data file not found: ${item.data_path}`)": "console.log(`未找到数据文件:${item.data_path}`)", "console.log(\"Error loading collection item: \" + item.key)": "console.log(\"加载集合项出错:\" + item.key)", "console.log(`Smart Embed Model: No active embedding model for ${this.collection_key}, skipping embedding`)": "console.log(`智能嵌入模型:${this.collection_key} 无活动嵌入模型,跳过嵌入`)", "console.log(`Original inputs length: ${inputs.length}`)": "console.log(`原始输入长度:${inputs.length}`)", "console.log(`Filtered inputs length: ${inputs.length}`)": "console.log(`过滤后输入长度:${inputs.length}`)", "console.log(\"empty batch (or all items have empty embed_input)\")": "console.log(\"空批次(或所有项的 embed_input 为空)\")", "console.log(`Prepared embed_inputs length: ${embed_inputs.length}`)": "console.log(`准备好的 embed_inputs 长度:${embed_inputs.length}`)", "console.log(\"Warning: prepare_embed_input received an empty string\")": "console.log(\"警告:prepare_embed_input 收到了一个空字符串\")", "console.log(\"Warning: prepare_embed_input resulted in an empty string after trimming\")": "console.log(\"警告:prepare_embed_input 在修剪后结果为空字符串\")", "console.log(\"embed_input is empty after filtering null and empty strings\")": "console.log(\"过滤空值和空字符串后,embed_input 为空\")", "console.log(`Retrying request (429) in ${backoff} seconds...`)": "console.log(`正在重试请求 (429)，将在 ${backoff} 秒后重试...`)", "console.log('message ignored (listener)'": "console.log('消息被忽略(监听器)'", "console.log(\"model loaded\")": "console.log(\"模型已加载\")", "console.log(\"[Transformers] Using GPU\")": "console.log(\"[Transformers] 正在使用 GPU\")", "console.log(\"[Transformers] Using CPU\")": "console.log(\"[Transformers] 正在使用 CPU\")", "console.log(\"init\")": "console.log(\"初始化\")", "console.log(\"load\", params)": "console.log(\"加载\", params)", "console.log(\"View inactive, skipping render nearest\")": "console.log(\"视图未激活,跳过最近的渲染\")", "console.log(\"refresh_smart_view\")": "console.log(\"刷新智能视图\")", "console.log(\"displaying settings tab\")": "console.log(\"显示设置标签页\")", "console.log(\"Accepted\")": "console.log(\"已接受\")", "console.log(\"File does not exist, creating it\")": "console.log(\"文件不存在,正在创建\")", "console.log(\"loading view\")": "console.log(\"加载视图\")", "console.log(\"copy message to clipboard\")": "console.log(\"复制消息到剪贴板\")", "console.log(`Error resolving ref: ${refPath}`, e)": "console.log(`解析引用错误:${refPath}`, e)", "console.log(\"should trigger retrieval\")": "console.log(\"应触发检索\")", "console.log(\"get_context_hyde\")": "console.log(\"获取上下文 Hyde\")", "console.log(`ScAppConnector initialized on port ${this.port}`)": "console.log(`ScAppConnector 已初始化于端口 ${this.port}`)", "console.log(`Port ${this.port} is already in use. Attempting to retry once.`)": "console.log(`端口 ${this.port} 已被占用。尝试重新连接一次。`)", "console.log(`Server running at http://localhost:${this.port}/`)": "console.log(`服务器正在运行于 http://localhost:${this.port}/`)", "console.log(\"Dataview API not found. No dataview connection for Smart Connect.\")": "console.log(\"未找到 Dataview API。智能连接无法建立 Dataview 连接。\")", "console.log(\"waiting for changes\")": "console.log(\"等待更改\")", "console.log(\"Server closed\")": "console.log(\"服务器已关闭\")", "console.log(\"Window server reference closed\")": "console.log(\"窗口服务器引用已关闭\")", "console.log(\"Environment no longer available. Closing server.\")": "console.log(\"环境不再可用。正在关闭服务器.\")", "console.log(\"unloading plugin\")": "console.log(\"卸载插件\")", "console.log(\"loading env\")": "console.log(\"加载环境\")", "console.log(\"Smart Connections v2 loaded\")": "console.log(\"智能连接 v2 已加载\")", "console.log(\"env loaded\")": "console.log(\"环境已加载\")", "console.log(\"Added to .gitignore: \" + ignore)": "console.log(\"已添加到 .gitignore: \" + ignore)", "console.log(\"old showing notice\")": "console.log(\"旧的显示通知\")", "console.log(\"Smart Connections: Waiting for Obsidian Sync to finish\")": "console.log(\"智能连接: 等待 Obsidian Sync 完成\")", "console.log(\"render_file_counts\")": "console.log(\"渲染文件计数\")", "console.log(\"Manifest written\")": "console.log(\"Manifest 已写入\")", "console.log(\"open_note\", link)": "console.log(\"打开笔记\", link)", "\"Saving \"": "\"正在保存 \"", "\"Loading \"": "\"正在加载 \"", "`Loading ${this.collection_key}...`": "`正在加载 ${this.collection_key}...`", "`${this.collection_key} loaded`": "`${this.collection_key} 加载完成`", "`Making Smart Connections...`": "`正在建立智能连接...`", "`Embedding progress: ${this.embedded_total} / ${this.queue_total}`": "`嵌入进度: ${this.embedded_total} / ${this.queue_total}`", "`${this._calculate_embed_tokens_per_second()} tokens/sec using ${this.smart_embed.opts.model_key}`": "`${this._calculate_embed_tokens_per_second()} tokens/秒,使用模型 ${this.smart_embed.opts.model_key}`", "`Embedding complete.`": "`嵌入完成.`", "`${this.embedded_total} entities embedded.`": "`${this.embedded_total} 实体已嵌入.`", "`Embedding paused.`": "`嵌入已暂停.`", "`Progress: ${this.embedded_total} / ${this.queue_total}`": "`进度: ${this.embedded_total} / ${this.queue_total}`", "\"Starting initial scan...\"": "\"开始初始扫描...\"", "\"Initial scan complete\"": "\"初始扫描完成\"", "\"Pruning sources...\"": "\"修剪数据源...\"", "`Pruned ${remove_sources.length} sources`": "`修剪了 ${remove_sources.length} 个数据源`", "\"Pruning blocks...\"": "\"修剪块...\"", "`Pruned ${remove_smart_blocks.length} blocks`": "`修剪了 ${remove_smart_blocks.length} 个块`", "`Importing...`, `Progress: ${i} / ${import_queue.length} files`": "`正在导入...`, `进度: ${i} / ${import_queue.length} 个文件`", "`Processed import queue in ${Date.now() - time_start}ms`": "`处理导入队列花费了 ${Date.now() - time_start} 毫秒`", "\"No items in import queue\"": "\"导入队列中没有项目\"", "\"Clearing all data...\"": "\"正在清除所有数据...\"", "\"All data cleared and reimported\"": "\"所有数据已清除并重新导入\"", ", \"Notice muted\"": ", \"通知已静音\"", "'Smart View must be open to utilize all Smart Chat features. For example, asking things like \"Based on my notes...\" requires Smart View to be open.'": "'智能视图 必须开启才能使用所有 智能聊天 功能。例如,提问类似 \"根据我的笔记...\" 的问题需要 智能视图 开启.'", "\"No entity found for key: \"": "\"没有找到与键相关的实体: \"", "\"Embed model not loaded. Please wait for the model to load and try again.\"": "\"嵌入模型未加载,请等待模型加载完成后重试。\"", "\"Failed to embed search text.\"": "\"搜索文本嵌入失败。\"", "\"Error in embedding search. See console for details.\"": "\"嵌入搜索时出错。请查看控制台获取详细信息。\"", "\"Success! API key is valid\"": "\"成功!API 密钥有效\"", "\"Error: API key is invalid!\"": "\"错误:API 密钥无效！\"", "\"Chat input is empty.\"": "\"聊天输入为空。\"", "\"Your chats are currently using a community account with very limited usage. Please add your own API key in the Smart Chat settings to enable unlimited personal usage and prevent exhausting the shared account limit.\"": "\"您的聊天当前使用的是社区账户,使用量非常有限。请在 智能聊天 设置中添加您的 API 密钥,以启用无限制个人使用,避免消耗共享账户配额。\"", "\"Waiting for Obsidian Sync to finish...\"": "\"等待 Obsidian 同步完成...\"", "\"Loading Smart Connections...\"": "\"正在加载智能连接...\"", "*API Error. See console logs for details.*": "*API 错误。请查看控制台日志以获取详细信息。*", "*An error occurred. See console logs for details.*": "*发生错误。请查看控制台日志以获取详细信息。*", "**********": "***********", "name: \"OpenAI Text-3 Small\"": "name: \"OpenAI Text-3 小型\"", "name: \"OpenAI Text-3 Large\"": "name: \"OpenAI Text-3 大型\"", "name: \"OpenAI Text-3 Small - 512\"": "name: \"OpenAI Text-3 小型 - 512\"", "name: \"OpenAI Text-3 Large - 256\"": "name: \"OpenAI Text-3 大型 - 256\"", "name: \"Minimum Embedding Length\"": "name: \"最小嵌入长度\"", "name: \"smart_settings\"": "name: \"智能设置\"", "name: \"Open: View Smart Connections\"": "name: \"打开: 查看智能连接\"", "name: \"Open: Smart Chat Conversation\"": "name: \"打开: 智能聊天会话\"", "name: \"Open: Smart ChatGPT\"": "name: \"打开: 智能 ChatGPT\"", "name: \"Open: Smart Connections Supporter Private Chat\"": "name: \"打开: 智能连接支持者私人聊天\"", "name: \"Not found: \"": "name: \"未找到: \"", "\"name\": \"Smart Change (change safety)\"": "\"name\": \"智能更改(更改安全)\"", "return \"Smart Search\"": "return \"智能搜索\"", "return \"Smart ChatGPT\"": "return \"智能ChatGPT\"", "return \"Smart Connections Supporter Private Chat\"": "return \"智能连接支持者专属聊天\"", "return `<span>${item_count} sources (embeddings not currently loaded)</span>`": "return `<span>${item_count} 源(嵌入尚未加载)</span>`", "return `<span>${scope.loaded}/${included_count} sources (partially loaded, should refresh/reload)</span>`": "return `<span>${scope.loaded}/${included_count} 源(部分加载,应刷新/重新加载)</span>`", "return `<span>${item_count} blocks (embeddings not currently loaded)</span>`": "return `<span>${item_count} 块(嵌入尚未加载)</span>`", "return `<span>${scope.loaded}/${item_count} blocks (partially loaded, should refresh/reload)</span>`": "return `<span>${scope.loaded}/${item_count} 块(部分加载,应刷新/重新加载)</span>`", "return `Anticipate the type of answer desired by the user. Imagine the following${params.ct ? \" \" + params.ct : \"\"} notes were written by the user and contain all the necessary information to answer the user's question. Begin responses with \"${ScTranslations_default[this.env.smart_connections_plugin.settings.language].prompt}...\"`": "return `预判用户希望得到的答案类型。假设以下${params.ct ? \" \" + params.ct : \"\"} 笔记是用户写的,并包含回答用户问题所需的所有信息。以 \"${ScTranslations_default[this.env.smart_connections_plugin.settings.language].prompt}...\" 开始回答`", "e.message += \" in \" + opts.filename": "e.message += \" 在 \" + opts.filename", "e.message += \" while compiling ejs\\n\\n\"": "e.message += \" 在编译ejs时\\n\\n\"", "e.message += \"If the above error is not helpful, you may want to try EJS-Lint:\\n\"": "e.message += \"如果上述错误信息没有帮助,您可以尝试使用EJS-Lint:\\n\"", "e.message += \"Or, if you meant to create an async function, pass `async: true` as an option.\"": "e.message += \"或者,如果您打算创建异步函数,请传递 `async: true` 作为选项。\"", "`<span>Load time: ${scope.load_time_ms}ms</span>`": "`<span>加载时间: ${scope.load_time_ms}ms</span>`", "<span>${embedded_percentage}% embedded</span>": "<span>${embedded_percentage}% 已嵌入</span>", "<span>${included_count} sources included (${total_count} total)</span>": "<span>${included_count} 源已包含(共 ${total_count} 源)</span>", "<span>${embedded_percentage}% embedded (${items_w_vec})</span>": "<span>${embedded_percentage}% 已嵌入(${items_w_vec} 个项)</span>", "<span>Loaded: ${item_count} blocks (expected ${scope.expected_blocks_ct})</span>": "<span>已加载: ${item_count} 块(预期 ${scope.expected_blocks_ct} 块)</span>", "User Agreement:</strong> By using Smart Connections you agree to share how it helps you with at least one other person \\u{1F60A}\\u{1F334}": "用户协议:</strong> 使用智能连接即表示您同意与至少一个其他人分享它是如何帮助您的 \\u{1F60A}\\u{1F334}", "The success of Smart Connections is a direct result of our community of supporters who generously fund and evaluate new features. Their unwavering commitment to our privacy-focused, open-source software benefits all. Together, we can continue to innovate and make a positive impact on the world.": "智能连接的成功直接得益于我们支持者社区,他们慷慨资助并评估新功能。他们对我们注重隐私的开源软件的坚定承诺造福了所有人。我们可以携手创新,积极影响世界。", "Smart Connections Supporter Community": "智能连接支持者社区", "Join the next <a href=\"https://lu.ma/calendar/cal-ZJtdnzAdURyouM7\">Lean Coffee session</a> to discuss future features & improvements.": "加入下一个<a href=\"https://lu.ma/calendar/cal-ZJtdnzAdURyouM7\">Lean Coffee 会议</a>,讨论未来的功能和改进。", "Supporter benefits include:": "支持者福利包括:", "Early access to new &amp; experimental features:": "提前体验新功能和实验性功能:", "Early access to new versions enables supporters to help ensure new features are ready for the broader community.": "提前体验新版本使支持者能够帮助确保新功能为更广泛的社区准备好。", "Current Early Access Features:": "当前早期访问功能:", "\\u{1F5BC}\\uFE0F Add images to Smart Chat (multimodal chat)": "\\u{1F5BC}\\uFE0F 将图像添加到智能聊天(多模态聊天)", "Re-ranking model in the Smart Connections View": "在智能连接视图中重新排名模型", "Smart Chat History in canvas format": "智能聊天历史记录(画布格式)", "Coming soon to Early Access:": "即将上线至早期访问:", "PDF Support in Smart Connections view": "智能连接视图中的 PDF 支持", "Edit notes in Smart Chat": "在智能聊天中编辑笔记", "New retrieval methods in Smart Chat": "智能聊天中的新检索方法", "Review retrieved context before sending in Smart Chat": "在智能聊天中发送前审查检索到的上下文", "Audio files in Smart Connections view": "智能连接视图中的音频文件", "Past Early Access Features:": "过去的早期访问功能:", "ChatGPT integration with your Obsidian Vault": "ChatGPT 与你的 Obsidian 库集成", "Mobile support for Smart Connections": "智能连接的移动支持", "Access to the supporter-only <a href=\"https://chat.smartconnections.app\">private chat": "访问仅限支持者的 <a href=\"https://chat.smartconnections.app\">私人聊天</a>", "Community:": "社区:", "Ask questions and share insights with other supporters.": "向其他支持者提问并分享见解。", "Help &amp; Support (priority):": "帮助与支持(优先):", "Swift, top-priority support in the <a href=\"https://chat.smartconnections.app\">Supporter Chat": "在 <a href=\"https://chat.smartconnections.app\">支持者聊天</a> 中获得快速的优先支持", "Feature Requests (priority):": "功能请求(优先):", "Influence the future of Smart Connections with priority feature requests in the <a href=\"https://chat.smartconnections.app\">Supporter Chat": "在 <a href=\"https://chat.smartconnections.app\">支持者聊天</a> 中通过优先功能请求影响智能连接的未来", "Insider Updates:": "内部更新:", "Learn about the latest features &amp; improvements before they are announced.": "在功能发布前,了解最新的功能与改进。", "For a very limited time:</b> Early access to Smart Connect: Use ChatGPT with your notes <i>without</i> uploading your notes to the cloud using <a href=\"https://chat.openai.com/g/g-9Xb1mRJYl-smart-connect-obsidian\">Smart Connect - Obsidian": "仅限极短时间:</b>智能连接早期访问:使用 ChatGPT 处理你的笔记 <i>无需</i> 将笔记上传到云端,使用 <a href=\"https://chat.openai.com/g/g-9Xb1mRJYl-smart-connect-obsidian\">智能连接 - Obsidian</a>", "<h3>Smart Chat</h3>": "<h3>智能聊天</h3>", "<h3>System Prompts</h3>": "<h3>系统提示</h3>", "<h3>Custom Local Model</h3>": "<h3>自定义本地模型</h3>", "<h3>Custom Server</h3>": "<h3>自定义服务器</h3>", "Additional settings available in the Smart Chat settings tab (ex. chat model and api key).": "在智能聊天设置选项卡中可以找到更多设置(例如聊天模型和 API 密钥)。", "<h2>Blocks</h2>": "<h2>区块</h2>", "<h2>Smart Environment</h2>": "<h2>智能环境</h2>", "Notes about embedding models:": "关于嵌入模型的说明:", "IMPORTANT: make sure local <code>BGE-micro-v2</code> embedding model works before trying other local models.": "重要提示:在尝试其他本地模型之前,请确保本地 <code>BGE-micro-v2</code> 嵌入模型正常工作。", "Local model compatibility depends on available CPU and RAM. Try reducing the max tokens (context) if a local model if failing.": "本地模型的兼容性取决于可用的 CPU 和内存。如果本地模型失败,尝试减少最大 token(上下文)数量。", "API models are not dependent on local compute, but they require an API key and send your notes to third-party servers for processing.": "API 模型不依赖于本地计算,但需要 API 密钥,并将你的笔记发送到第三方服务器进行处理。", "<h1>Muted Notices</h1>": "<h1>静音通知</h1>", "No muted notices.": "没有静音通知。", "\"Re-load\"": "\"重新加载\"", "\"Load\"": "\"加载\"", " Sources": " 源", ">Import<": ">导入<", ">Prune<": ">修剪<", ">Clear All &amp; Re-import<": ">清除所有并重新导入<", ">Smart Connections<": ">智能连接<", "Smart Chat Links": "智能聊天链接", "show_notice(\"Wait until current response is finished.\")": "show_notice(\"等待当前响应完成。\")", "show_notice(\"Message copied to clipboard\")": "show_notice(\"消息已复制到剪贴板\")", "show_notice(\"Supporter license key required for early access update\")": "show_notice(\"需要支持者许可证密钥才能进行早期访问更新\")", "Are you sure you want to clear all data and re-import? This action cannot be undone.": "你确定要清除所有数据并重新导入吗？此操作无法撤销。", "--------------------------------": "--------------------------------------", "Open: View Smart Connections": "打开:查看智能连接", "\"Smart Connections\"": "\"智能连接\"", "Open: Smart Chat Conversation": "打开:智能聊天对话", "\"Smart Chat Conversation View\"": "\"智能聊天对话视图\"", "Waiting for Obsidian Sync to finish...": "等待 Obsidian 同步完成...", "Loading Smart Connections...": "加载智能连接...", "\"UNTITLED CHAT\"": "\"未命名聊天\"", "\"UNTITLED CHAT \"": "\"未命名聊天 \"", "\" view\"": "\"视图\"", "`Use the \"${tool_choice.function.name}\" tool!`": "`使用 \"${tool_choice.function.name}\" 工具！`", "`Required: use the \"${tool_choice.function.name}\" tool!`": "`必需：使用 \"${tool_choice.function.name}\" 工具！`", "`IMPORTANT: You must use the \"${body.tools[0].function_declarations[0].name}\" function tool!`": "`重要：您必须使用 \"${body.tools[0].function_declarations[0].name}\" 函数工具！`", "`context: ${m[1].context}, output: ${m[1].max_out}`": "`上下文: ${m[1].context}, 输出: ${m[1].max_out}`", "'<div class=\"sc-loading\">Loading ' + this.collection_key + \" settings...</div>\"": "'<div class=\"sc-loading\">加载 ' + this.collection_key + \" 设置中...</div>\"", "<p>Entity not found.</p>": "<p>实体未找到。</p>", "Loading search...": "加载搜索中...", "'<div class=\"sc-loading\">Loading main settings...</div>'": "'<div class=\"sc-loading\">加载主要设置中...</div>'", "Entity not found: ": "实体未找到：", "hypotheticals is required": "hypotheticals 是必需的", "Embedding search is not enabled.": "嵌入搜索未启用。", "`The text contains a special token that is not allowed: ${specialMatch[0]}`": "`文本中包含不允许的特殊标记：${specialMatch[0]}`", "Based on your notes": "基于您的笔记", "Hi, I'm ChatGPT with access to your notes via Smart Connections. Ask me a question about your notes and I'll try to answer it.": "您好，我是通过智能连接访问您笔记的 ChatGPT。关于您的笔记,您可以问我任何问题,我会尽力回答。", "`Anticipate what the user is seeking. Respond in the form of a hypothetical note written by the user. The note may contain statements as paragraphs, lists, or checklists in markdown format with no headings. Please respond with one hypothetical note and abstain from any other commentary. Use the format: PARENT FOLDER NAME > CHILD FOLDER NAME > FILE NAME > HEADING 1 > HEADING 2 > HEADING 3: HYPOTHETICAL NOTE CONTENTS.`": "`尝试预测用户的需求。以用户编写的一份假设笔记的形式回复。笔记可以包含段落、列表或复选框，但不包含标题。请仅回复一份假设笔记，避免其他评论。使用格式：父文件夹名称 > 子文件夹名称 > 文件名称 > 标题1 > 标题2 > 标题3: 假设笔记内容。`", "Obsidian HTTP server is running": "Obsidian HTTP 服务器正在运行", "\\n\\n# Ignore Smart Environment folder\\n.smart-env": "\\n\\n# 忽略智能环境文件夹\\n.smart-env", "`[Smart Connections] A new version is available! (v${latest_release})`": "`[智能连接] 新版本可用！(v${latest_release})`", "------------------------------------": "-------------------------------------", "----------智能聊天对话-----------": "----------智能聊天对话----------", "Open help documentation": "打开帮助文档", "Open Conversation Note": "打开对话笔记", "Chat History": "聊天历史", "Chat Options": "聊天选项", "Chat Settings": "聊天设置", "New Chat": "新建聊天", "Copy message to clipboard": "复制消息到剪贴板", "Hi there, welcome to the Smart Chat.&nbsp;Ask me a question about your notes and I'll try to answer it.": "你好,欢迎使用智能聊天。请问你有什么关于笔记的问题,我会尽力为你解答。", "Try &quot;Based on my notes&quot; or &quot;Summarize [[this note]]&quot; or &quot;Important tasks in /folder/&quot;": "试试&quot;基于我的笔记&quot;,或者&quot;总结[[此笔记]]&quot;,或者&quot;/folder/中的重要任务&quot;", "-----------智能聊天对话-----------": "-----------智能聊天对话----------", "----------智能连接搜索-----------": "----------智能连接搜索----------", "Search Smart Connections": "搜索智能连接", "\"Describe what you're looking for (e.g., 'PKM strategies', 'story elements', 'personal AI alignment')\"": "\"描述你要查找的内容(例如:'PKM 策略','故事元素','个人 AI 对齐')\"", "Use semantic (embeddings) search to surface relevant notes. Results are sorted by similarity to your query. Note: returns different results than lexical (keyword) search.": "使用语义(嵌入)搜索来展示相关笔记。结果按与查询的相似度排序。注意:与词汇(关键词)搜索返回的结果不同。", "-----------智能连接搜索------------": "-----------智能连接搜索-----------", "data-description=\"Folder to store Smart Chat history. Use a preceeding <code>.</code> to hide it (ex. <code>.smart-chats</code>).\"": "data-description=\"用于存储智能聊天历史的文件夹。使用前置 <code>.</code> 隐藏文件夹(例如:<code>.smart-chats</code>).\"", "data-description=\"Folder to store system prompts. Available in chat by typing '@'\"": "data-description=\"用于存储系统提示的文件夹。在聊天中输入 '@' 即可使用。\"", "data-description=\"API Key required to use Cohere re-ranker.\"": "data-description=\"使用 Cohere 重新排序器所需的 API 密钥。\""}}