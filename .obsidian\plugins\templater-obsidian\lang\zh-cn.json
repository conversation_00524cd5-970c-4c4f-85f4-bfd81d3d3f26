{"manifest": {"translationVersion": 1742966871310, "pluginVersion": "2.9.2"}, "description": {"original": "Create and use templates", "translation": "创建和使用模板"}, "dict": {"description:\"This module exposes the app instance. Prefer to use this over the global app instance.\"": "description:\"此模块公开应用实例。比起全局应用实例，更喜欢使用它。\"", "description:`This module exposes Templater's running configuration.\n\nThis is mostly useful when writing scripts requiring some context information.\n`": "description:`配置： 该模块公开了 Templater 的运行配置。这在编写需要一些上下文信息的脚本时非常有用。`", "description:\"The `TFile` object representing the template file.\"": "description: \"代表模板文件的`TFile`对象\"", "description:\"The `TFile` object representing the target file where the template will be inserted.\"": "description: \"代表目标文件的`TFile`对象。即模板将要插入的目标文件\"", "description:\"The `RunMode`, representing the way Templater was launched (Create new from template, Append to active file, ...).\"": "description:\"`RunMode`代表 Templater 的启动方式（从模板创建新文件、追加到活动文件, ...).\"", "description:\"The active file (if existing) when launching Templater.\"": "description:\"启动模板时的活动文件（如果存在）.\"", "description:\"This module contains every internal function related to dates.\"": "description:\"日期： 该模块包含与日期相关的所有内部功能.\"", "description:\"Retrieves the date.\"": "description:\"当前： 获取当前日期.\"", "description:'The format for the date. Defaults to `\"YYYY-MM-DD\"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'": "description:'日期的格式。默认为 `\"YYYY-MM-DD\"`。请参阅[格式参考](https://momentjs.com/docs/#/displaying/format/).'", "description:\"Duration to offset the date from. If a number is provided, duration will be added to the date in days. You can also specify the offset as a string using the ISO 8601 format.\"": "description:\"抵消日期的持续时间。如果提供的是数字，持续时间将以天为单位加到日期上。您也可以使用 ISO 8601 格式将偏移量指定为字符串.\"", "description:\"The date referential, e.g. set this to the note's title.\"": "description:\"日期参照，例如将其设置为注释的标题.\"", "description:\"The format for the reference date. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).\"": "description:\"参考日期的格式。请参阅 [参考格式](https://momentjs.com/docs/#/displaying/format/).\"", "description:\"Retrieves tomorrow's date.\"": "description:\"明天： 获取明天的日期.\"", "description:\"Retrieves yesterday's date.\"": "description:\"昨天： 获取昨天的日期.\"", "description:\"Week day number. If the locale assigns Monday as the first day of the week, `0` will be Monday, `-7` will be last week's day.\"": "description:\"星期编号。如果本地指定星期一为一周的第一天，`0 `将是星期一，`-7 `将是上周的一天.\"", "description:\"This module contains every internal function related to files.\"": "description:\"该模块包含与文件相关的所有内部功能.\"", "description:\"The string contents of the file at the time that Templater was executed. Manipulating this string will *not* update the current file.\"": "description:\"Templater 执行时文件的字符串内容。操作此字符串*不*会更新当前文件.\"", "description:\"Creates a new file using a specified template or with a specified content.\"": "description:\"使用指定模板或指定内容创建新文件.\"", "description:\"Either the template used for the new file content, or the file content as a string. If it is the template to use, you retrieve it with `tp.file.find_tfile(TEMPLATENAME)`.\"": "description:\"新文件内容使用的模板，或文件内容字符串。如果是要使用的模板，则可使用 `tp.file.find_tfile(TEMPLATENAME)` 获取.\"", "description:'The filename of the new file, defaults to \"Untitled\".'": "description:'文件名： 新文件的文件名，默认为 \"Untitled\".'", "description:\"Whether to open or not the newly created file. Warning: if you use this option, since commands are executed asynchronously, the file can be opened first and then other commands are appended to that new file and not the previous file.\"": "description:\"是否打开新创建的文件。警告：如果使用该选项，由于命令是异步执行的，文件可能会先被打开，然后其他命令会附加到新文件，而不是之前的文件。.\"", "description:'The folder to put the new file in, defaults to Obsidian\\'": "description:'要放入新文件的文件夹，默认为 Obsidian\\'", "description:\"Retrieves the file's creation date.\"": "description:\"创建日期： 读取文件的创建日期.\"", "description:'The format for the date. Defaults to `\"YYYY-MM-DD HH:mm\"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'": "description:'日期的格式。默认为 `\"YYYY-MM-DD HH:mm\"`。请参阅[格式参考](https://momentjs.com/docs/#/displaying/format/).'", "description:`Sets the cursor to this location after the template has been inserted. \n\nYou can navigate between the different cursors using the configured hotkey in Obsidian settings.\n`": "description:`插入模板后，将光标设置到该位置。您可以使用 Obsidian 设置中配置的热键在不同光标之间导航。`", "description:`The order of the different cursors jump, e.g. it will jump from 1 to 2 to 3, and so on.\nIf you specify multiple tp.file.cursor with the same order, the editor will switch to multi-cursor.\n`": "description:`不同光标的跳转顺序，例如从 1 跳转到 2 再跳转到 3，以此类推。如果指定了多个具有相同顺序的 tp.file.cursor，编辑器将切换到多光标模式。`", "description:\"Appends some content after the active cursor in the file.\"": "description:\"在文件中的活动光标后添加一些内容.\"", "description:\"The content to append after the active cursor.\"": "description:\"要追加到活动光标后的内容.\"", "description:\"Check to see if a file exists by it's file path. The full path to the file, relative to the Vault and containing the extension, must be provided.\"": "description:\"根据文件路径检查文件是否存在。必须提供文件的完整路径（相对于 Vault 并包含扩展名）。.\"", "description:\"The full file path of the file we want to check existence for.\"": "description:\"我们要检查是否存在的文件的完整文件路径.\"", "description:\"Search for a file and returns its `TFile` instance.\"": "description:\"搜索文件并返回其 `TFile` 实例.\"", "description:\"The filename we want to search and resolve as a `TFile`.\"": "description:\"我们要搜索并解析为 `TFile` 的文件名。\"", "description:\"Retrieves the file's folder name.\"": "description:\"检索文件的文件夹名称.\"", "description:\"If set to `true`, returns the vault-absolute path of the folder. If `false`, only returns the basename of the folder (the last part). Defaults to `false`.\"": "description:\"如果设置为 `true`，将返回文件夹的绝对路径。如果设置为 `false`，则只返回文件夹的基名（最后一部分）。默认设置为`false`.\"", "description:\"Includes the file's link content. Templates in the included content will be resolved.\"": "description:\"包含文件的链接内容。包含内容中的模板将得到解决.\"", "description:'The link to the file to include, e.g. `\"[[MyFile]]\"`, or a TFile object. Also supports sections or blocks inclusions.'": "description:'要包含的文件的链接，例如`\"[[MyFile]]\"或 TFile 对象。也支持包含部分或块.'", "description:\"Retrieves the file's last modification date.\"": "description:\"读取文件的最后修改日期.\"", "description:\"Moves the file to the desired vault location.\"": "description:\"将文件移动到所需的存储库位置.\"", "description:'The new vault relative path of the file, without the file extension. Note: the new path needs to include the folder and the filename, e.g. `\"/Notes/MyNote\"`.'": "description:'文件的新存储库相对路径，不含文件扩展名。注意：新路径必须包括文件夹和文件名，例如`\"/Notes/MyNote\"`.'", "description:\"The file to move, defaults to the current file.\"": "description:\"要移动的文件，默认为当前文件.\"", "description:\"Retrieves the file's absolute path on the system.\"": "description:\"读取文件在系统中的绝对路径.\"", "description:\"If set to `true`, only retrieves the vault's relative path.\"": "description:\"如果设置为 `true`，则只检索保险库的相对路径.\"", "description:\"Renames the file (keeps the same file extension).\"": "description:\"重命名文件（保持文件扩展名不变）.\"", "description:\"The new file title.\"": "description:\"新文件标题.\"", "description:\"Retrieves the active file's text selection.\"": "description:\"读取活动文件的文本选区.\"", "description:\"Retrieves the file's tags (array of string).\"": "description:\"读取文件的标记（字符串数组）.\"", "description:\"Retrieves the file's title.\"": "description:\"读取文件标题.\"", "description:\"This modules exposes all the frontmatter variables of a file as variables.\"": "description:\"该模块将文件的所有前置变量作为变量公开.\"", "description:\"This module exposes hooks that allow you to execute code when a Templater event occurs.\"": "description:\"该模块公开了钩子，允许您在 Templater 事件发生时执行代码.\"", "description:\"Hooks into when all actively running templates have finished executing. Most of the time this will be a single template, unless you are using `tp.file.include` or `tp.file.create_new`.\\n\\nMultiple invokations of this method will have their callback functions run in parallel.\"": "description:\"当所有正在运行的模板都已执行完毕时挂钩。大多数情况下，这将是单个模板，除非你使用了 `tp.file.include` 或 `tp.file.create_new`.\"", "description:\"Callback function that will be executed when all actively running templates have finished executing.\"": "description:\"当所有正在运行的模板执行完毕后执行的回调函数.\"", "description:\"This module exposes all the functions and classes from the Obsidian API.\"": "description:\"该模块公开 Obsidian API 中的所有函数和类.\"", "description:\"This module contains system related functions.\"": "description:\"该模块包含与系统相关的功能.\"", "description:\"Retrieves the clipboard's content.\"": "description:\"读取剪贴板内容.\"", "description:\"Spawns a prompt modal and returns the user's input.\"": "description:\"生成提示模块，并返回用户输入的内容.\"", "description:\"Text placed above the input field.\"": "description:\"放置在输入框上方的文本.\"", "description:\"A default value for the input field.\"": "description:\"输入字段的默认值.\"", "description:\"Throws an error if the prompt is canceled, instead of returning a `null` value.\"": "description:\"如果取消提示，则抛出错误，而不是返回一个 `null` 值。\"", "description:\"If set to `true`, the input field will be a multiline textarea. Defaults to `false`.\"": "description:\"如果设置为 `true`，输入框将是一个多行文本区。默认为 `false`.\"", "description:\"Spawns a suggester prompt and returns the user's chosen item.\"": "description:\"生成建议器提示，并返回用户选择的项目.\"", "description:\"Array of strings representing the text that will be displayed for each item in the suggester prompt. This can also be a function that maps an item to its text representation.\"": "description:\"数组字符串，代表建议程序提示符中每个项目将显示的文本。这也可以是一个将项目映射到其文本表示的函数.\"", "description:\"Array containing the values of each item in the correct order.\"": "description:\"包含按正确顺序排列的各项目值的数组.\"", "description:\"Placeholder string of the prompt.\"": "description:\"提示字符串的占位符.\"", "description:\"Limit the number of items rendered at once (useful to improve performance when displaying large lists).\"": "description:\"限制一次呈现的条目的数量（在显示大型列表时有助于提高性能）.\"", "description:\"This modules contains every internal function related to the web (making web requests).\"": "description:\"该模块包含与网络有关的所有内部功能（进行网络请求）.\"", "description:\"Retrieves and parses the daily quote from `https://github.com/Zachatoo/quotes-database` as a callout.\"": "description:\"从 `https://github.com/Zachatoo/quotes-database` 获取并解析每日名言作为 callout\"", "description:\"Gets a random image from `https://unsplash.com/`.\"": "description:\"从此获取一张随机图片 `https://unsplash.com/`获取一张随机图片.\"", "description:\"Image size in the format `<width>x<height>`.\"": "description:\"格式中的图像大小 `<width>x<height>`.\"", "description:\"Limits selection to photos matching a search term. Multiple search terms can be passed separated by a comma.\"": "description:\"仅限选择与搜索条件匹配的照片。可用逗号分隔多个搜索词.\"", "description:\"Optional argument to include the specified size in the image link markdown. Defaults to false.\"": "description:\"可选参数，用于在图片链接标记中包含指定尺寸。默认为 false.\"", "description:\"Makes a HTTP request to the specified URL. Optionally, you can specify a path to extract specific data from the response.\"": "description:\"向指定的 URL 发送 HTTP 请求。可选择指定路径，以便从响应中提取特定数据.\"", "description:\"The URL to which the HTTP request will be made.\"": "description:\"发出 HTTP 请求的 URL.\"", "description:\"A path within the response JSON to extract specific data.\"": "description:\"在响应 JSON 中提取特定数据的路径.\"", "description:\"Retrieves and parses the daily quote from the API `https://api.quotable.io` as a callout.\"": "description:\"从API `https://api.quotable.io`获取并解析每日名言作为 callout。\"", "name:\"Open insert template modal\"": "name:\"打开插入模板模式\"", "name:\"Replace templates in the active file\"": "name:\"替换活动文件中的模板\"", "name:\"Jump to next cursor location\"": "name:\"跳转到下一个光标位置\"", "name:\"Create new note from template\"": "name:\"从模板创建新笔记\"", "text:\"User function n\\xB0\"": "text:\"用户函数 n\\xB0\"", ".setButtonText(\"Add new hotkey for template\")": ".setButtonText(\"为模板添加新快捷键\")", ".setButtonText(\"Add new folder template\")": ".setButtonText(\"添加新文件夹模板\")", ".setButtonText(\"Add new file regex\")": ".setButtonText(\"添加新的文件正则表达式\")", ".setButtonText(\"Add new startup template\")": ".setButtonText(\"添加新的启动模板\")", ".setButtonText(\"Add new user function\")": ".setButtonText(\"添加新用户功能\")", ".setButtonText(\"Submit\")": ".setButtonText(\"提交\")", ".setName(\"Template folder location\")": ".setName(\"模板文件夹位置\")", ".setName(\"Internal variables and functions\")": ".set<PERSON>ame(\"内部变量和函数\")", ".setName(\"Syntax highlighting on desktop\")": ".setName(\"桌面语法高亮\")", ".setName(\"Syntax highlighting on mobile\")": ".setName(\"移动设备上的语法高亮显示\")", ".setName(\"Automatic jump to cursor\")": ".setName(\"自动跳转到光标\")", ".setName(\"Trigger Templater on new file creation\")": ".setName(\"创建新文件时触发模板程序\")", ".setName(\"Template hotkeys\")": ".setName(\"模板热键\")", ".setName(\"Folder templates\")": ".set<PERSON>ame(\"文件夹模板\")", ".setName(\"Enable folder templates\")": ".setName(\"启用文件夹模板\")", ".setName(\"File regex templates\")": ".set<PERSON><PERSON>(\"文件正则表达式模板\")", ".setName(\"Enable file regex templates\")": ".setName(\"启用文件正则表达式模板\")", ".setName(\"Startup templates\")": ".setName(\"启动模板\")", ".setName(\"User script functions\")": ".setName(\"用户脚本功能\")", ".setName(\"Script files folder location\")": ".setName(\"脚本文件文件夹位置\")", ".setName(\"User system command functions\")": ".setName(\"用户系统指令功能\")", ".setName(\"Enable user system command functions\")": ".setName(\"启用用户系统指令功能\")", ".setName(\"Timeout\")": ".setName(\"超时\")", ".setName(\"Shell binary location\")": ".setName(\"外壳二进制位置\")", ".setName(\"Donate\")": ".setName(\"捐赠\")", ".setName(\"File Regex Templates\")": ".set<PERSON><PERSON>(\"文件正则表达式模板\")", ".setName(\"Enable File Regex Templates\")": ".setName(\"启用文件正则表达式模板\")", ".setDesc(\"Files in this folder will be available as templates.\")": ".setDesc(\"该文件夹中的文件可作为模板使用.\")", ".setDesc(\"Maximum timeout in seconds for a system command.\")": ".setDesc(\"系统命令的最长超时时间（以秒为单位）\")", ".setDesc(\"If you like this Plugin, consider donating to support continued development.\")": ".setDesc(\"如果您喜欢此插件，请考虑捐赠以支持持续开发.\")", ".setPlaceholder(\"Folder\")": ".setPlaceholder(\"文件夹\")", ".setPlaceholder(\"Template\")": ".setPlaceholder(\"模板\")", ".setPlaceholder(\"File regex\")": ".setPlaceholder(\"文件正则表达式\")", ".setPlaceholder(\"Timeout\")": ".setPlaceholder(\"超时\")", ".setPlaceholder(\"Example: /bin/bash, ...\")": ".setPlaceholder(\"示例：/bin/bash,...\")", ".setPlaceholder(\"Function name\")": ".setPlaceholder(\"功能名称\")", ".setPlaceholder(\"System command\")": ".setPlaceholder(\"系统指令\")", ".setPlaceholder(\"Type name of a template...\")": ".setPlaceholder(\"模板类型名称...\")", ".setPlaceholder(\"Type text here\")": ".setPlaceholder(\"在此输入文字\")", ".setTooltip(\"Configure Hotkey\")": ".setTooltip(\"配置快捷键\")", ".setTooltip(\"Move up\")": ".setTooltip(\"上移\")", ".setTooltip(\"Move down\")": ".setTooltip(\"下移\")", ".setTooltip(\"Delete\")": ".setTooltip(\"删除\")", ".setTooltip(\"Add additional folder template\")": ".setTooltip(\"添加额外的文件夹模板\")", ".setTooltip(\"Add additional file regex\")": ".setTooltip(\"添加额外的文件 regex\")", ".setTooltip(\"Refresh\")": ".setTooltip(\"刷新\")", ".setTitle(\"Create new note from template\")": ".setTitle(\"从模板创建新笔记\")", ".setPlaceholder(\"File Regex\")": ".setPlaceholder(\"文件正则表达式\")", ".append(\"Templater provides multiples predefined variables / functions that you can use.\",e.createEl(\"br\"),\"Check the \",e.createEl(\"a\",{href:\"https://silentvoid13.github.io/Templater/\",text:\"documentation\"}),\" to get a list of all the available internal variables / functions.\")": ".append(\"Templater 提供了多个预定义的变量/函数供你使用。\",e.createEl(\"br\"),\"请查看 \",e.createEl(\"a\",{href:\"https://silentvoid13.github.io/Templater/\",text:\"文档\"}),\" 获取所有可用的内部变量/函数列表。\")", ".append(\"Adds syntax highlighting for Templater commands in edit mode.\")": ".append(\"在编辑模式下为 Templater 命令添加语法高亮。\")", ".append(\"Adds syntax highlighting for Templater commands in edit mode on mobile. Use with caution: this may break live preview on mobile platforms.\")": ".append(\"在移动端编辑模式下为 Templater 命令添加语法高亮。请谨慎使用：这可能会导致移动端的实时预览功能出现问题。\")", ".append(\"Automatically triggers \",e.createEl(\"code\",{text:\"tp.file.cursor\"}),\" after inserting a template.\",e.createEl(\"br\"),\"You can also set a hotkey to manually trigger \",e.createEl(\"code\",{text:\"tp.file.cursor\"}),\".\")": ".append(\"在插入模板后会自动触发 \",e.createEl(\"code\",{text:\"tp.file.cursor\"}),\"。\",e.createEl(\"br\"),\"你也可以设置一个快捷键来手动触发 \",e.createEl(\"code\",{text:\"tp.file.cursor\"}),\"。\")", ".append(\"Template hotkeys allows you to bind a template to a hotkey.\")": ".append(\"模板快捷键允许你将某个模板绑定到一个快捷键上。\")", ".append(\"Startup templates are templates that will get executed once when Templater starts.\",e.createEl(\"br\"),\"These templates won't output anything.\",e.createEl(\"br\"),\"This can be useful to set up templates adding hooks to Obsidian events for example.\")": ".append(\"启动模板是在 Templater 启动时执行一次的模板。\",e.createEl(\"br\"),\"这些模板不会输出任何东西。\",e.createEl(\"br\"),\"例如，这对于设置向 Obsidian 事件添加钩子函数（hooks）的模板很有用。\",e.createEl(\"br\"),\"（这段的意思是：在 Obsidian 的 Templater 插件中，“启动模板” 的功能可以用于在 Obsidian 启动时自动执行一些预定义的操作，而这些操作不会有任何直接输出结果。比如，你可以使用这些模板来为 Obsidian 的特定事件（如文件打开、保存等）设置钩子函数（hooks）。钩子函数是一种在特定事件发生时触发的代码。通过这种方式，你可以在 Obsidian 运行时自动完成一些自定义操作。简单来说，就是利用启动模板，在 Obsidian 启动时自动执行特定任务或设置，而无需手动操作。）\")", ".append(\"All JavaScript files in this folder will be loaded as CommonJS modules, to import custom user functions.\",e.createEl(\"br\"),\"The folder needs to be accessible from the vault.\",e.createEl(\"br\"),\"Check the \",e.createEl(\"a\",{href:\"https://silentvoid13.github.io/Templater/\",text:\"documentation\"}),\" for more information.\")": ".append(\"此文件夹中的所有 JavaScript 文件将作为 CommonJS 模块加载，以导入自定义用户函数。\",e.createEl(\"br\"),\"这个文件夹需要能够从保管库中访问。\",e.createEl(\"br\"),\"请查看 \",e.createEl(\"a\",{href:\"https://silentvoid13.github.io/Templater/\",text:\"文档\"}),\" 以获取更多信息。\")", ".append(\"Allows you to create user functions linked to system commands.\",e.createEl(\"br\"),e.createEl(\"b\",{text:\"Warning: \"}),\"It can be dangerous to execute arbitrary system commands from untrusted sources. Only run system commands that you understand, from trusted sources.\")": ".append(\"允许您创建链接到系统命令的用户函数。\",e.createEl(\"br\"),e.createEl(\"b\",{text:\"警告： \"}),\"从不可信的来源执行任意的系统命令可能是危险的。只能从可信来源运行您理解的系统命令。\")", ".append(\"Templater will listen for the new file creation event, and, if it matches a rule you've set, replace every command it finds in the new file's content. \",\"This makes Templater compatible with other plugins like the Daily note core plugin, Calendar plugin, Review plugin, Note refactor plugin, etc. \",e.createEl(\"br\"),e.createEl(\"br\"),\"Make sure to set up rules under either Folder Templates or File Regex Template below.\",e.createEl(\"br\"),e.createEl(\"br\"),e.createEl(\"b\",{text:\"Warning: \"}),\"This can be dangerous if you create new files with unknown / unsafe content on creation. Make sure that every new file's content is safe on creation.\")": ".append(\"Templater 会监听新文件创建事件，如果新文件符合你设置的规则，就会替换其内容中的所有命令。\",\"这使得 Templater 能与其他插件兼容，例如 Daily Note 核心插件、Calendar 插件、Review 插件、Note Refactor 插件等。 \",e.createEl(\"br\"),e.createEl(\"br\"),\"确保在下面的文件夹模板或文件正则表达式模板下设置规则。\",e.createEl(\"br\"),e.createEl(\"br\"),e.createEl(\"b\",{text:\"警告：\"}),\"如果创建的新文件中包含未知或不安全的内容，这可能会带来风险。请确保每个新文件的内容在创建时是安全的。\")", ".append(\"Full path to the shell binary to execute the command with.\",e.createEl(\"br\"),\"This setting is optional and will default to the system's default shell if not specified.\",e.createEl(\"br\"),\"You can use forward slashes ('/') as path separators on all platforms if in doubt.\")": ".append(\"用于执行命令的shell二进制文件的完整路径。\",e.createEl(\"br\"),\"此设置是可选的，如果未指定，将默认为系统的默认shell。\",e.createEl(\"br\"),\"如有疑问，您可以在所有平台上使用正斜杠（'/'）作为路径分隔符。\")", ".append(\"Templater will listen for the new file creation event, and, if it matches a rule you've set, replace every command it finds in the new file's content. \",\"This makes Templater compatible with other plugins like the Daily note core plugin, Calendar plugin, Review plugin, Note refactor plugin, etc. \",e.createEl(\"br\"),e.createEl(\"br\"),\"Make sure to set up rules under either folder templates or file regex template below.\",e.createEl(\"br\"),e.createEl(\"br\"),e.createEl(\"b\",{text:\"Warning: \"}),\"This can be dangerous if you create new files with unknown / unsafe content on creation. Make sure that every new file's content is safe on creation.\")": ".append(\"Templater将监听新文件创建事件，如果它与您设置的规则匹配，则替换它在新文件内容中找到的每个命令。\",\"这使得Templater与其他插件兼容，如每日笔记核心插件、日历插件、评论插件、笔记重构插件等。\",e.createEl(\"br\"),e.createEl(\"br\"),\"请确保在下面的文件夹模板或文件正则表达式模板下设置规则。\",e.createEl(\"br\"),e.createEl(\"br\"),e.createEl(\"b\",{text:\"警告: \"}),\"如果您在创建时创建了包含未知/不安全内容的新文件，这可能会很危险。确保每个新文件的内容在创建时都是安全的。\")", ".append(\"Folder templates are triggered when a new \",e.createEl(\"strong\",{text:\"empty \"}),\"file is created in a given folder.\",e.createEl(\"br\"),\"Templater will fill the empty file with the specified template.\",e.createEl(\"br\"),\"The deepest match is used. A global default template would be defined on the root \",e.createEl(\"code\",{text:\"/\"}),\".\")": ".append(\"当在给定文件夹中创建新的\",e.createEl(\"strong\",{text:\"空\"}),\"文件时，会触发文件夹模板。\",e.createEl(\"br\"),\"Templater将用指定的模板填充空文件。\",e.createEl(\"br\"),\"使用最深匹配。全局默认模板将在根\",e.createEl(\"code\",{text:\"/\"}),\"上定义。\")", ".append(\"When enabled, Templater will make use of the folder templates defined below. This option is mutually exclusive with file regex templates below, so enabling one will disable the other.\")": ".append(\"启用后，Templater将使用下面定义的文件夹模板。此选项与下面的文件正则表达式模板互斥，因此启用一个将禁用另一个。\")", ".append(\"File regex templates are triggered when a new \",e.createEl(\"strong\",{text:\"empty\"}),\" file is created that matches one of them. Templater will fill the empty file with the specified template.\",e.createEl(\"br\"),\"The first match from the top is used, so the order of the rules is important.\",e.createEl(\"br\"),\"Use \",e.createEl(\"code\",{text:\".*\"}),\" as a final catch-all, if you need it.\")": ".append(\"当创建与其中一个匹配的新\",e.createEl(\"strong\",{text:\"空\"}),\"文件时，会触发文件正则表达式模板。Templater将用指定的模板填充空文件。\",e.createEl(\"br\"),\"使用从顶部开始的第一个匹配，因此规则的顺序很重要。\",e.createEl(\"br\"),\"使用\",e.createEl(\"code\",{text:\".*\"}),\"作为最终的总括，如果你需要的话。\")", ".append(\"When enabled, Templater will make use of the file regex templates defined below. This option is mutually exclusive with folder templates above, so enabling one will disable the other.\")": ".append(\"启用后，Templater将使用下面定义的文件正则表达式模板。此选项与上述文件夹模板互斥，因此启用一个将禁用另一个。\")", ".log(`Templater skipped parsing ${t.path} because file size exceeds 10000`)": ".log(`Templater跳过解析${t. path}，因为文件大小超过10000`)", "w(`Folder \"${e}\" doesn't exist`)": "w(`文件夹\"${e}\"不存在`)", "w(`${e} is a file, not a folder`)": "w(`${e}是一个文件，而不是文件夹`)", "w(`File \"${e}\" doesn't exist`)": "w(`文件\"${e}\"不存在`)", "w(`${e} is a folder, not a file`)": "w(`${e}是一个文件夹，而不是文件`)", "w(\"This template is already bound to a hotkey\")": "w(\"此模板已绑定到热键\")", "w(\"This folder already has a template associated with it\")": "w(\"此文件夹已关联了一个模板\")", "w(\"This startup template already exist\")": "w(\"此启动模板已存在\")", "w(\"Timeout must be a number\")": "w(\"超时必须是数字\")", "w(\"Invalid reference date format, try specifying one with the argument 'reference_format'\")": "w(\"引用日期格式无效，请尝试使用参数'reference_format'指定一种格式\")", "w(\"Reached create_new depth limit (max = 10)": "w(\"已达到创建新文件的深度限制 (最大值 = 10)", "w(\"No active editor, can't append to cursor.\")": "w(\"没有活动编辑器，无法追加到光标。\")", "w(\"Reached inclusion depth limit (max = 10)": "w(\"已达到包含深度限制 (最大值 = 10)", "w(\"Invalid file format, provide an obsidian link between quotes.\")": "w(\"文件格式无效，请在引号之间提供黑曜石链接。\")", "w(`File ${e} doesn't exist`)": "w(`文件${e}不存在`)", "w(\"app.vault is not a FileSystemAdapter instance\")": "w(\"app.vault不是FileSystemAdapter实例\")", "w(\"File name cannot contain any of these characters: \\\\ / :\")": "w(\"文件名不能包含以下任何字符：\\\\/：\")", "w(\"Active editor is null, can't read selection.\")": "w(\"活动编辑器为空，无法读取所选内容。\")", "w(\"Error performing GET request\")": "w(\"执行GET请求时出错\")", "w(\"Error generating daily quote\")": "w(\"生成每日名言时出错\")", "w(\"Error generating random picture\")": "w(\"生成随机图片时出错\")", "w(\"Error fetching and extracting value\")": "w(\"获取和提取值时出错\")", "w(\"Cancelled prompt\")": "w(\"取消提示\")", "w(`Error with User Template ${s}`,f)": "w(`用户模板 ${s}出错`,f)", "w(`Failed to load user script at \"${e.path}\".`,c.message)": "w(`未能在“${e.path}”处加载用户脚本。`,c.message)", "w(`Failed to load user script at \"${e.path}\". No exports detected.`)": "w(`未能在“${e.path}”处加载用户脚本。未检测到出口。`)", "w(`Failed to load user script at \"${e.path}\". Default export is not a function.`)": "w(`未能在“${e.path}”处加载用户脚本。默认导出不是函数。`)", "w(\"No active leaf\")": "w(\"无活动子节点\")", "w(\"No active editor, can't append templates.\")": "w(\"没有活动编辑器，无法附加模板。\")", "w(\"Active editor is null, can't overwrite content\")": "w(\"活动编辑器为空，无法覆盖内容\")", "w(\"Unable to enable syntax highlighting. Could not define language.\")": "w(\"无法启用语法突出显示。无法定义语言。\")", "w(\"Javascript syntax mode couldn't be found, can't enable syntax highlighting.\")": "w(\"找不到Javascript语法模式，无法启用语法突出显示。\")", "w(\"Couldn't find customOverlayMode, can't enable syntax highlighting.\")": "w(\"找不到customOverlayMode，无法启用语法突出显示。\")", "`<b>Templater Error</b>:<br/>${r.message}`": "`<b>Templater 错误</b>:<br/>${r.message}`", "`Path ${t} not found in the JSON response`": "`JSON响应中找不到路径${t}`", "`expected instance of ${e.name}`": "`${e.name}的预期实例`", "\"out of js stack\"": "\"脱离js堆栈\"", "`<b>Templater Error</b>:<br/>${r.message}<br/>Check console for more information`": "`<b>Templater 错误</b>:<br/>${r.message}<br/>有关详细信息，请检查控制台`", "`Detected ${o} User Script(s)`": "`检测到${o}个用户脚本`", ".log(`Templater skipped parsing ${n.path} because file size exceeds 10000`)": ".log(`Templater 跳过了对 ${n.path} 的解析，因为文件大小超过了 10000。`)", ".error(\"Templater Error:\",r.message,`\n`,r.console_msg)": ".error(\"模板错误：\",r.message,`\n`,r.console_msg)", "description:\"\",example:\"": "description:\"\",例:\"", "text:\"reference\"": "text:\"参考\"", "text:\"documentation\"": "text:\"文档\"", "text:\"Warning: \"": "text:\"警告: \"", "text:\"empty \"": "text:\"空\"", "text:\"empty\"": "text:\"空\"", "\"No user scripts folder set\"": "\"未设置用户脚本文件夹\""}}