{"main": {"id": "80376c92ccbd048c", "type": "split", "children": [{"id": "4fa98184c0cab90d", "type": "tabs", "children": [{"id": "cdd2555befa34ec8", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/自研提示词集合/知乎悬疑脑洞创作专家.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "知乎悬疑脑洞创作专家"}}, {"id": "c21b6c89c481ce64", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/古典雅致叙事风格模仿提示词.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "古典雅致叙事风格模仿提示词"}}, {"id": "e9f5f37ca2ab15cd", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/自研提示词集合/悬疑小说大纲GPT5.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "悬疑小说大纲GPT5"}}, {"id": "8c2775361068efe6", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/chatgpt修改文风2.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "chatgpt修改文风2"}}, {"id": "06c2145f9f17b7ae", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0 .md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "文章风格分析器 v1.0"}}, {"id": "0298c6a42c22cac6", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0(小七姐原版）.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "文章风格分析器 v1.0(小七姐原版）"}}, {"id": "cf83d17023f007dd", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/暂存.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "暂存"}}, {"id": "bb6bb355330ab9f5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/李继刚优化版.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "李继刚优化版"}}, {"id": "2cc7646cfffbbff5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/赘婿风格分析一.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "赘婿风格分析一"}}, {"id": "9275075334639032", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/赘婿风格提示词测试（这个是最新完成版本）.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "赘婿风格提示词测试（这个是最新完成版本）"}}, {"id": "ccbf3520afc23af4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/李白风格提示词.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "李白风格提示词"}}, {"id": "3ef68c06e77cdb09", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/悬疑小说文风.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "悬疑小说文风"}}, {"id": "93f6f392971ba149", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/文风相关（攻克难题）/诡秘之主设定.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "诡秘之主设定"}}, {"id": "67ce471446364ecc", "type": "leaf", "state": {"type": "markdown", "state": {"file": "悬疑小说/大纲20章.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "大纲20章"}}, {"id": "685dae6e7f3dacf1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/自研提示词集合/悬疑小说大纲claude.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "悬疑小说大纲claude"}}, {"id": "6f10c4a8b80d7f62", "type": "leaf", "state": {"type": "markdown", "state": {"file": "春宵载酒小说设定/玄幻小说写作专家pormpt.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "玄幻小说写作专家pormpt"}}, {"id": "478e1030065b61b0", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说prompt分类/英雄之旅小七姐.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "英雄之旅小七姐"}}], "currentTab": 11}], "direction": "vertical"}, "left": {"id": "5e3297abbba77ac9", "type": "split", "children": [{"id": "5be7eee1e420ef44", "type": "tabs", "children": [{"id": "ac481d11147963cc", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "byModifiedTime", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "98d81302ec7789ed", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "f48b45ea543b2a34", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "bc7c8914872dc2e6", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}, {"id": "e16f276bbbf50f0f", "type": "leaf", "state": {"type": "mk-path-view", "state": {}, "icon": "layout-grid", "title": "Navigator"}}], "currentTab": 4}], "direction": "horizontal", "width": 319.5106964111328}, "right": {"id": "e694e4579af8287a", "type": "split", "children": [{"id": "77c75b0027771faa", "type": "tabs", "children": [{"id": "5a9ae2d0fb92e514", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": false, "showSearch": true, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "f3ffaa0e9633fbe9", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "66d236c4aa75c554", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "春宵载酒小说设定/小说设定自己修改更新/玄幻小说设定精简版.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "玄幻小说设定精简版 的出链列表"}}, {"id": "5b90581dc31dda70", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "添加笔记属性"}}, {"id": "022aa76b876d8742", "type": "leaf", "state": {"type": "outline", "state": {"file": "悬疑小说/claude大纲13章.md", "followCursor": true, "showSearch": true, "searchQuery": ""}, "icon": "lucide-list", "title": "claude大纲13章 的大纲"}}, {"id": "ccb6ef233e2b31e5", "type": "leaf", "state": {"type": "smtcmp-chat-view", "state": {}, "icon": "wand-sparkles", "title": "Smart composer chat"}}], "currentTab": 4}], "direction": "horizontal", "width": 297.5, "collapsed": true}, "left-ribbon": {"hiddenItems": {"bases:创建新数据库": false, "i18n:I18N": false, "remotely-save:Remotely Save": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "templater-obsidian:Templater": false, "workspaces:管理工作区布局": false, "zk-prefixer:创建时间戳笔记": false, "obsidian-kanban:创建新看板": false, "obsidian42-brat:BRAT": false, "smart-composer:Open smart composer": false, "outliner-md:New outliner file": false, "obsidian-memos:Thino": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "pkmer:Open Pkmer Market": false}}, "active": "3ef68c06e77cdb09", "lastOpenFiles": ["小说prompt分类/文风相关（攻克难题）/古典雅致叙事风格模仿提示词.md", "小说prompt分类/文风相关（攻克难题）/悬疑小说文风.md", "小说prompt分类/文风相关（攻克难题）/赘婿风格提示词测试（这个是最新完成版本）.md", "小说prompt分类/文风相关（攻克难题）/chatgpt修改文风2.md", "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0(小七姐原版）.md", "小说prompt分类/自研提示词集合/知乎悬疑脑洞创作专家.md", "小说prompt分类/自研提示词集合/GPT5优化.md", "小说prompt分类/文风相关（攻克难题）/李白风格提示词.md", "小说prompt分类/文风相关（攻克难题）/赘婿风格分析一.md", "小说prompt分类/文风相关（攻克难题）/李继刚优化版.md", "小说prompt分类/文风相关（攻克难题）/暂存.md", "小说prompt分类/文风相关（攻克难题）/文章风格分析器 v1.0 .md", "小说prompt分类/文风相关（攻克难题）/Lyra优化后的风格提示词.md", "小说prompt分类/文风相关（攻克难题）/诡秘之主设定.md", "小说prompt分类/文风相关（攻克难题）/chatgpt修改文风提示词.md", "小说prompt分类/自研提示词集合/悬疑小说大纲GPT5.md", "悬疑小说/claude大纲13章.md", "小说prompt分类/文风相关（攻克难题）/未命名 1.md", "悬疑小说/大纲12章.md", "小说prompt分类/悬疑小说文风.md", "Vault.md", "小说prompt分类/英雄之旅小七姐.md", "小说prompt分类/自研提示词集合/claude优化.md", "小说prompt分类/自研提示词集合/deepseekV3悬疑小说大纲.md", "小说prompt分类/救猫咪猫叔.md", "小说prompt分类/自研提示词集合/悬疑小说大纲claude.md", "未命名 6.base", "未命名 4.base", "未命名 3.base", "未命名 2.base", "未命名 1.base", "未命名.base", "悬疑小说", "未命名 5.base", "小说prompt分类/自研提示词集合", "lyra_system/backend/main.py", "杂项/小说软件创作流程一.png", "杂项/小说软件创作流程二.png", "杂项/故事卡系统.png", "杂项/未命名.canvas", "未命名.canvas", "未命名 1.canvas"]}