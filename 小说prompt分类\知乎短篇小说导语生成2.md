---
date created: 星期五, 八月 15日 2025, 6:17:22 晚上
date modified: 星期六, 八月 16日 2025, 9:55:51 晚上
---
# 知乎短篇小说导语生成提示词

## 核心目标
生成一个不超过100字的小说导语，让读者产生强烈的阅读欲望和情绪冲击。

## 必备要素结构

### 【开局设定】(15-25字)
- 建立一个看似普通但暗含异常的初始情境
- 使用具体的身份、时间、地点或事件作为锚点
- 示例模式："我是/我在/发生了…"

### 【第一层冲突】(20-30字)  
- 引入违背常识或预期的元素
- 制造认知落差，让读者产生"这不对劲"的感觉
- 可使用对比、反转、矛盾等手法

### 【第二层冲突/反转】(25-35字)
- 在第一层冲突基础上再次颠覆认知
- 揭示更深层的矛盾或更大的危机
- 引入新的角色、时间线或超自然元素

### 【悬念钩子】(10-20字)
- 以钩子进行结尾
- 暗示更大的秘密或危险即将揭晓
- 让读者必须继续阅读才能获得答案
- 可参考下面的【钩子类型库】进行设定

#### 【钩子类型库】

1. **结尾钩子（Chapter-End Cliffhangers）**
- **悬念中断**：在关键信息即将揭露时戛然而止
- **危险降临**：主角面临新的威胁或困境
- **意外转折**：情节突然朝意想不到的方向发展
- **身份揭露**：暗示某个角色的真实身份即将曝光

 2. **信息钩子（Information Hooks）**

- **线索投放**：给出部分线索但不完整
- **红鲱鱼**：故意误导读者的假线索
- **伏笔埋设**：看似无关但后续重要的细节
- **谜题设置**：提出需要解答的问题

3. **角色钩子（Character Hooks）**

- **可疑行为**：角色做出令人费解的举动
- **秘密暗示**：暗示角色隐藏重要秘密
- **动机模糊**：角色的真实目的不明
- **身份疑云**：对角色真实身份的质疑

4. **情感钩子（Emotional Hooks）**

- **恐惧制造**：营造紧张恐怖的氛围
- **同情引发**：让读者关心角色命运
- **愤怒激发**：通过不公正事件激起读者情绪
- **好奇驱动**：激发读者的探知欲

 5. **时间钩子（Time-Based Hooks）**

- **倒计时**：设定时间限制增加紧迫感
- **时间跳跃**：突然的时间转换
- **截止期限**：必须在特定时间内完成某事
- **时间悖论**：时间线上的矛盾或疑点

 6. **环境钩子（Setting Hooks）**

- **诡异场所**：选择令人不安的地点
- **封闭空间**：困在特定环境中无法逃脱
- **熟悉变陌生**：日常环境突然变得危险
- **象征性场景**：具有特殊意义的地点

## 情绪调动技巧
1. **恐惧系**：死亡、失踪、诡异现象、时空错乱
2. **愤怒系**：背叛、不公、复仇、身份颠覆  
3. **好奇系**：神秘声音、预言、警告、隐藏真相
4. **震惊系**：身份反转、道德颠覆、极端选择
5. 紧张系：
6. 欢乐系
7. 悲伤系

## 语言要求
- 句式简短有力，多用短句
- 善用具体细节（金簪、楼梯、电话等）
- 避免形容词堆砌，用事实说话
- 营造紧迫感和临场感

## 反常识元素库
- 时间：重生、预知、时空错乱
- 身份：敌友互换、真假难辨、道德颠覆
- 生死：死而复生、预知死亡、灵魂出窍
- 感知：失明复明、听到不该听的、看到不该看的
- 关系：亲人背叛、敌人拯救、爱恨逆转

## 生成步骤
1. 确定主角身份和基础处境
2. 设计核心的反常识冲突点
3. 安排两次认知颠覆的时机
4. 选择最能勾起好奇心的结尾悬念
5. 精炼语言，确保每个字都有价值
6. 检查是否符合100字限制和情绪冲击力要求

请根据以上结构，为[具体题材/主题]生成一个导语。