{"manifest": {"translationVersion": 1741566731816, "pluginVersion": "7.14.0"}, "description": {"original": "Track tasks across your vault. Supports due dates, recurring tasks, done dates, sub-set of checklist items, and filtering.", "translation": "(搜索.format(\"ddd Do MMM\")翻译可更改右键菜单日期格式)  跟踪你库中的任务.支持截止日期、重复任务、完成日期、子任务列表项和过滤功能."}, "dict": {"Notice(\"Cannot postpone a null date\")": "Notice(\"不能推迟无效日期\")", "Notice(r+`\n\nThis message has been written to the console.\n`,1e4)": "Notice(r+`\n\n此信息已写入控制台.\n`,1e4)", "name:\"Create or edit task\"": "name:\"创建或编辑任务\"", "name:\"Toggle task done\"": "name:\"切换任务完成状态\"", "description:\"Description\"": "description:\"说明\"", "text:\"Core Statuses\"": "text:\"核心状态\"", "text:\"Custom Statuses\"": "text:\"自定义状态\"", "text:\"Changing any settings requires a restart of obsidian.\"": "text:\"更改任何设置都需要重新启动Obsidian.\"", ".setText(\"Create or edit Task\")": ".setText(\"创建或编辑任务\")", ".setText(\"Loading Tasks ...\")": ".setText(\"加载任务...\")", ".setButtonText(\"Add New Task Status\")": ".setButtonText(\"添加新任务状态\")", ".setButtonText(\"Add All Unknown Status Types\")": ".setButtonText(\"添加所有未知状态类型\")", ".setButtonText(\"Reset Custom Status Types to Defaults\")": ".setButtonText(\"重置自定义状态类型为默认值\")", ".setName(\"Task Status Symbol\")": ".setName(\"任务状态符号\")", ".setName(\"Task Status Name\")": ".setName(\"任务状态名称\")", ".setName(\"Task Next Status Symbol\")": ".setName(\"任务下一个状态符号\")", ".setName(\"Task Status Type\")": ".setName(\"任务状态类型\")", ".setName(\"Available as command\")": ".setName(\"可用作命令\")", ".setName(\"Task Format\")": ".setName(\"任务格式\")", ".setName(\"Global task filter\")": ".setName(\"全局任务筛选器\")", ".setName(\"Global filter\")": ".setName(\"全局筛选器\")", ".setName(\"Remove global filter from description\")": ".setName(\"从描述中删除全局筛选器\")", ".setName(\"Global Query\")": ".setName(\"全局查询\")", ".setName(\"Task Statuses\")": ".setName(\"任务状态\")", ".setName(\"Dates\")": ".setName(\"日期\")", ".setName(\"Set created date on every added task\")": ".setName(\"设置每个添加任务的创建日期\")", ".setName(\"Set done date on every completed task\")": ".setName(\"设置每个已完成任务的完成日期\")", ".setName(\"Set cancelled date on every cancelled task\")": ".setName(\"为每个已取消的任务设置已取消的日期\")", ".setName(\"Dates from file names\")": ".setName(\"从文件名中提取日期\")", ".setName(\"Use filename as Scheduled date for undated tasks\")": ".setName(\"将文件名用作未注明日期的任务的计划日期\")", ".setName(\"Additional filename date format as Scheduled date for undated tasks\")": ".setName(\"为无日期任务添加文件名日期格式作为计划日期\")", ".setName(\"Folders with default Scheduled dates\")": ".setName(\"具有默认计划日期的文件夹\")", ".setName(\"Recurring tasks\")": ".setName(\"重复性任务\")", ".setName(\"Next recurrence appears on the line below\")": ".set<PERSON>ame(\"下一个重复出现在下一行\")", ".setName(\"Auto-suggest\")": ".setName(\"自动建议\")", ".setName(\"Auto-suggest task content\")": ".setName(\"自动建议任务内容\")", ".setName(\"Minimum match length for auto-suggest\")": ".setName(\"自动建议的最小匹配长度\")", ".setName(\"Maximum number of auto-suggestions to show\")": ".setName(\"要显示的最大自动建议数\")", ".setName(\"Dialogs\")": ".setName(\"对话框\")", ".setName(\"Provide access keys in dialogs\")": ".setName(\"在对话框中提供访问密钥\")", ".setDesc(\"This is the character between the square braces. (It can only be edited for Custom statuses, and not Core statuses.)\")": ".setDesc(\"这是方括号中的字符.(仅可编辑自定义状态,不可编辑核心状态.)\")", ".setDesc(\"This is the friendly name of the task status.\")": ".setDesc(\"这是任务状态的友好名称.\")", ".setDesc(\"When clicked on this is the symbol that should be used next.\")": ".setDesc(\"单击时,这是下一步应该使用的符号.\")", ".setDesc(\"Control how the status behaves for searching and toggling.\")": ".setDesc(\"控制搜索和切换状态的行为.\")", ".setDesc(\"If enabled this status will be available as a command so you can assign a hotkey and toggle the status using it.\")": ".setDesc(\"如果启用,此状态将作为命令可用,因此您可以指定热键并使用它切换状态.\")", ".setDesc(\"Enabling this removes the string that you set as global filter from the task description when displaying a task.\")": ".setDesc(\"启用此选项将在显示任务时从任务描述中删除设置为全局筛选器的字符串.\")", ".setDesc(\"Leave empty if you want to use default Scheduled dates everywhere, or enter a comma-separated list of folders.\")": ".setDesc(\"如果要处处使用默认的“计划日期”,请保留为空,或者输入以逗号分隔的文件夹列表\")", ".setDesc(\"If higher than 0, auto-suggest will be triggered only when the beginning of any supported keywords is recognized.\")": ".setDesc(\"如果高于0,则只有在识别出任何支持的关键字的开头时才会触发自动建议.\")", ".setDesc('How many suggestions should be shown when an auto-suggest menu pops up (including the \"\\u23CE\" option).')": ".setDesc('当自动建议菜单弹出时,应显示多少条建议 (包含 \"\\u23CE\" 选项).')", ".setPlaceholder(\"e.g. #task or TODO\")": ".setPlaceholder(\"例如:#task 或 TODO\")", ".setPlaceholder(`# For example...\npath does not include _templates/\nlimit 300\nshow urgency`)": ".setPlaceholder(`# 例如...\npath does not include _templates/\nlimit 300\nshow urgency`)", ".setPlaceholder(\"example: MMM DD YYYY\")": ".setPlaceholder(\"示例:MMM DD YYYY\")", ".setTooltip(\"Save\")": ".setTooltip(\"保存\")", ".setTooltip(\"Cancel\")": ".setTooltip(\"取消\")", ".setTooltip(\"Create a new file in the root of the vault, containing a Mermaid diagram of the current status settings.\")": ".setTooltip(\"在库的根目录下创建一个新文件,包含当前状态设置的 Mermaid 图表.\")", ".setTooltip(\"Delete\")": ".setTooltip(\"删除\")", ".setTooltip(\"Edit\")": ".setTooltip(\"编辑\")", ".createEl(\"h3\",{text:\"Tasks Settings\"})": ".createEl(\"h3\",{text:\"任务设置(曲淡歌 & <PERSON><PERSON> 汉化)\"})", ".createEl(\"h4\",{text:\"Task Format Settings\"})": ".createEl(\"h4\",{text:\"任务格式设置\"})", ".createEl(\"h4\",{text:\"Global filter Settings\"})": ".createEl(\"h4\",{text:\"全局过滤器设置\"})", ".createEl(\"h4\",{text:\"Global Query\"})": ".createEl(\"h4\",{text:\"全局查询\"})", ".createEl(\"h4\",{text:\"Task Statuses\"})": ".createEl(\"h4\",{text:\"任务状态\"})", ".createEl(\"h4\",{text:\"Date Settings\"})": ".createEl(\"h4\",{text:\"日期设置\"})", ".createEl(\"h4\",{text:\"Recurring task Settings\"})": ".createEl(\"h4\",{text:\"常规任务设置\"})", ".createEl(\"h4\",{text:\"Auto-suggest Settings\"})": ".createEl(\"h4\",{text:\"自动建议设置\"})", ".createEl(\"h4\",{text:\"Dialog Settings\"})": ".createEl(\"h4\",{text:\"对话设置\"})", "setText(\"Create or edit Task\"": "setText(\"创建或编辑任务\"", "setText(\"Loading Tasks ...\"": "setText(\"加载任务 ...\"", "Name:\"Dataview\"": "Name:\"Dataview 格式\"", ".setDesc(\"Enabling this will open an intelligent suggest menu while typing inside a recognized task line.\")": ".setDesc(\"启用此选项将打开一个智能建议菜单,同时在已识别的任务行中键入.\")", "text:\"Global filter Settings\"": "text:\"全局筛选器设置\"", "Recommended: Leave empty if you want all checklist items in your vault to be tasks managed by this plugin.": "建议:如果您希望仓库中的所有检查表项目都是由此插件管理的任务,请保留为空", "Use a global filter if you want Tasks to only act on a subset of your \"<code>- [ ]</code>\" checklist items, so that a checklist item must include the specified string in its description in order to be considered a task.": "如果您希望“任务”仅作用于 \"<code>- [ ]</code>\" 检查表项的子集,请使用全局筛选器,以便检查表项必须在其描述中包含指定的字符串才能被视为任务.", "For example, if you set the global filter to <code>#task</code>, the Tasks plugin will only handle checklist items tagged with <code>#task</code>.": "例如,如果将全局筛选器设置为 <code>#task</code>,则Tasks插件将仅处理标记为 <code>#task</code> 的检查表项.", "Other checklist items will remain normal checklist items and not appear in queries or get a done date set.": "其他检查表项目将保持正常的检查表项目,不会出现在查询中或设置完成日期.", "See the <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/global-filter/\">documentation</a>.": "请参阅 <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/global-filter/\">文档</a>.", "text:\"Task Statuses\"": "text:\"任务状态\"", "These are the core statuses that Tasks supports natively, with no need for custom CSS styling or theming.": "这些是Tasks本机支持的核心状态,不需要自定义CSS样式或主题.", "You can add edit and add your own custom statuses in the section below.": "您可以在下面的部分中添加编辑和添加自己的自定义状态.", "You should first <b>select and install a CSS Snippet or Theme</b> to style custom checkboxes": "您应该首先<b>选择并安装CSS代码段或主题<b>以设置自定义复选框的样式", "Then, use the buttons below to set up your custom statuses, to match your chosen CSS checkboxes.": "然后,使用下面的按钮设置您的自定义状态,以匹配您选择的CSS复选框.", "<b>Note</b> Any statuses with the same symbol as any earlier statuses will be ignored. You can confirm the actually loaded statuses by running the 'Create or edit task' command and looking at the Status drop-down.": "<b>注意</b>任何与任何早期状态具有相同符号的状态都将被忽略.您可以通过运行“创建或编辑任务”命令并查看“状态”下拉列表来确认实际加载的状态.", "See the <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/statuses/\">documentation</a> to get started!": "请参阅 <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/statuses/\">documentation</a> 开始!", "text:\"Date Settings\"": "text:\"日期设置\"", "Save time entering Scheduled (\\u23F3) dates": "输入计划 (\\u23F3) 日期节省时间", "If this option is enabled, any undated tasks will be given a default Scheduled date extracted from their file name.": "如果启用此选项,则任何未注明日期的任务都将获得从其文件名中提取的默认“计划日期”.", "The date in the file name must be in one of <code>YYYY-MM-DD</code> or <code>YYYYMMDD</code> formats.": "文件名中的日期必须是 <code>YYYY-MM-DD</code> or <code>YYYYMMDD</code> 格式之一.", "See the <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/use-filename-as-default-date/\">documentation": "请参阅 <a href=\"https://obsidian-tasks-group.github.io/obsidian-tasks/getting-started/use-filename-as-default-date/\">文档", "text:\"Auto-suggest Settings\"": "text:\"自动建议设置\"", "How many suggestions should be shown when an auto-suggest menu pops up (including the \"\\u23CE\" option).": "弹出自动建议菜单(包括 \"\\u23CE\" 选项)时应显示多少建议.", "If the access keys (keyboard shortcuts) for various controls in dialog boxes conflict with system keyboard shortcuts or assistive technology functionality that is important for you, you may want to deactivate them here.": "如果对话框中各种控件的访问键(键盘快捷键)与系统键盘快捷键或对您很重要的辅助技术功能相冲突,您可能需要在此处停用它们.", "Add New Task Status": "添加新任务状态", "Add All Unknown Status Types": "添加所有未知状态类型", "Reset Custom Status Types to Defaults": "将自定义状态类型重置为默认值", "AnuPpuccin Theme": "AnuPpuccin 主题", "Ebullientworks Theme": "Ebullientworks 主题", "ITS Theme & SlRvb Checkboxes": "ITS主题 和 SlRvb复选框", "Minimal Theme": "Minimal 主题", "Things Theme": "Things 主题", "Aura Theme (Dark mode only)": "Aura 主题 (仅限黑暗模式)", "text:\"Dialog Settings\"": "text:\"对话框设置\"", "text:\"Task Format Settings\"": "text:\"任务格式设置\"", "The format that Tasks uses to read and write tasks.": "任务用于读取和写入任务的格式.", "Important:</b> Tasks currently only supports one format at a time. Selecting Dataview will currently <b>stop Tasks reading its own emoji signifiers": "重点:</b> 任务当前一次只支持一种格式.选择数据视图当前将 <b>停止Tasks读取自己的表情符号", "See the <a href=\"https://publish.obsidian.md/tasks/Reference/Task+Formats/About+Task+Formats\">documentation": "请参阅 <a href=\"https://publish.obsidian.md/tasks/Reference/Task+Formats/About+Task+Formats\">文档", "text:\"Global Query\"": "text:\"全局查询\"", "A query that is automatically included at the start of every Tasks block in the vault. Useful for adding default filters, or layout options.": "自动包含在库中每个“任务”块开头的查询.用于添加默认过滤器或布局选项.", "Enabling this will make the next recurrence of a task appear on the line below the completed task. Otherwise the next recurrence will appear before the completed one.": "启用此选项将使任务的下一个重复出现在已完成任务下方的行中.否则,下一次重复将出现在已完成的重复之前.", "Enabling this will add a timestamp \\u2795 YYYY-MM-DD before other date values, when a task is created with 'Create or edit task', or by completing a recurring task.": "启用此功能将添加时间戳 \\u2795 YYYY-MM-DD 在其他日期值之前,当使用“创建或编辑任务”创建任务时,或通过完成定期任务创建任务时.", "Enabling this will add a timestamp \\u2705 YYYY-MM-DD at the end when a task is toggled to done.": "启用此功能将添加时间戳 \\u2705 YYYY-MM-DD 在任务切换为已完成时结束.", "text:\"Recurring task Settings\"": "text:\"定期任务设置\"", "Not Recurring": "不重复", "Apply": "应用", "textContent=\"Cancel\"": "textContent=\"取消\"", "textContent=\"Recurs\"": "textContent=\"重复\"", "textContent=\"Due\"": "textContent=\"截止\"", "textContent=\"Scheduled\"": "textContent=\"预约\"", "textContent=\"Completed:\"": "textContent=\"完成:\"", "textContent=\"Created on:\"": "textContent=\"创建时间:\"", "textContent=\"Done on:\"": "textContent=\"完成时间:\"", "(\"At least one of\"": "(\"满足其中一个条件即可\"", "(\"All of\"": "(\"满足所有条件\"", "t,\"using regex: \"": "t,\"匹配正则: \"", "At most ${n} task`": "最多展示 ${n} 个任务`", "Edit task": "编辑任务", "Try 'Monday' or 'tomorrow', or [td|tm|yd|tw|nw|we] then space.": "尝试输入 'Monday' or 'tomorrow', 或 [td|tm|yd|tw|nw|we] 然后空格.", "Try 'every 2 weeks on Thursday'.": "尝试输入 'every 2 weeks on Thursday'.", "Search for tasks that depend on this task being done...": "搜索依赖于正在执行的此任务的任务...", "Search for tasks that the task being edited depends on...": "搜索正在编辑的任务所依赖的任务...", "text:\"Tasks Settings\"": "text:\"Tasks 设置\"", "do not understand query": "无法理解查询", "Problem line: \"${t}\"`}get": "问题行: \"${t}\"`}get", " with `;switch(this.regex.flags.length){case 0:t+=\"no flags\"": " with `;switch(this.regex.flags.length){case 0:t+=\"no flags\"", "Explanation of this Tasks code block query:": "此任务代码块查询的说明如下:", ".setPlaceholder(`# For example...": ".setPlaceholder(`# 例如...", "setName(\"Set cancelled date on every cancelled task\"": "setName(\"设置每个已取消任务的取消日期\"", "setDesc($t.createFragmentWithHTML('Enabling this will add a timestamp \\u274C YYYY-MM-DD at the end when a task is toggled to cancelled.": "setDesc($t.createFragmentWithHTML('启用此功能将添加时间戳 \\u274C YYYY-MM-DD 在结尾,当任务切换为取消时.", "createFragmentWithHTML('Enabling this will open an intelligent suggest menu while typing inside a recognized task line.": "createFragmentWithHTML('启用此选项将打开智能建议菜单,同时在已识别的任务行中键入.", "displayText:`${n.startDateSymbol} start date`": "displayText:`${n.startDateSymbol} 开始日期`", "N(a,\"placeholder\",\"Take out the trash\"": "N(a,\"placeholder\",\"尽情倾述吧\"", "malformed boolean query": "格式错误的布尔查询", "(check the documentation for guidelines)": "(查看文档中的指导原则)", "TypeError(\"Invalid token\"": "TypeError(\"无效令牌\"", "navActionButton.ariaLabel = \"open settings\"": "navActionButton.ariaLabel = \"打开设置\"", "navActionButton.ariaLabel = \"home\"": "navActionButton.ariaLabel = \"主页\"", "navActionButton.ariaLabel = \"extract\"": "navActionButton.ariaLabel = \"提取\"", "navActionButton.ariaLabel = \"unextract\"": "navActionButton.ariaLabel = \"取消提取\"", "navActionButton.ariaLabel = \"expand\"": "navActionButton.ariaLabel = \"扩展\"", "navActionButton.ariaLabel = \"collapse all\"": "navActionButton.ariaLabel = \"折叠所有\"", "setButtonText(\"Extract\"": "setButtonText(\"提取\"", "setButtonText(\"Cancel\"": "setButtonText(\"取消\"", "setButtonText(\"Jump\"": "setButtonText(\"跳转\"", ".setTitle(\"Open in new tab\"": ".setTitle(\"在新标签页打开\"", ".setTitle(\"Open to the right\"": ".setTitle(\"在右侧打开\"", ".setTitle(\"Open in new window\"": ".setTitle(\"在新窗口中打开\"", ".error(\"Daily Note Outline: Could not create \"": ".error(\"每日笔记大纲:无法创建 \"", ".error(\"Daily Note Outline: Could not create\"": ".error(\"每日笔记大纲:无法创建\"", ".setMessage(`Daily Note Outline: created ${createdDN} daily notes for unresolved links`)": ".setMessage(`每日笔记大纲: 创建 ${createdDN} 未解析链接的每日笔记`)", ".Notice(\"Daily Note Outline: created all daily notes for unresolved links\"": ".Notice(\"每日笔记大纲: 为未解析的链接创建了所有每日笔记\"", "return \"Daily Note Outline\"": "return \"每日笔记大纲\"", "navActionButton.ariaLabel = \"previous notes\"": "navActionButton.ariaLabel = \"上一个笔记\"", "navActionButton.ariaLabel = \"next notes\"": "navActionButton.ariaLabel = \"下一个笔记\"", "let labelForToday = \"create/open \"": "let labelForToday = \"创建/打开 \"", "let labelForNext = \"create/open \"": "let labelForNext = \"创建/打开 \"", "labelForToday = labelForToday + \"today's \";": "labelForToday = labelForToday + \"今天的 \";", "labelForNext = labelForNext + \"tomorrow's \"": "labelForNext = labelForNext + \"明天的 \"", "labelForToday = labelForToday + \"present \"": "labelForToday = labelForToday + \"上一个 \"", "labelForNext = labelForNext + \"next \"": "labelForNext = labelForNext + \"下一个 \"", "labelForToday = labelForToday + GRANULARITY_TO_PERIODICITY[granularity] + \" note\"": "labelForToday = labelForToday + GRANULARITY_TO_PERIODICITY[granularity] + \" 笔记\"", "labelForNext = labelForNext + GRANULARITY_TO_PERIODICITY[granularity] + \" note\"": "labelForNext = labelForNext + GRANULARITY_TO_PERIODICITY[granularity] + \" 笔记\"", "console.info(\"No custom daily note settings found!\"": "console.info(\"找不到自定义的每日笔记设置!\"", "day: \"daily\",": "day: \"每日\",", "week: \"weekly\"": "week: \"每周\"", "month: \"monthly\"": "month: \"每月\"", "quarter: \"quarterly\"": "quarter: \"每季\"", "year: \"yearly\"": "year: \"每年\"", "none: \"none\"": "none: \"无\"", "lines: \"num of lines\"": "lines: \"总行数\"", "days: \"distance\"": "days: \"距今天数\"", "tag: \"first tag\"": "tag: \"首标签\"", "dow: \"day of week\"": "dow: \"星期几\"", "dowshort: \"day of week(short)\"": "dowshort: \"周几\"", "weeknumber: \"week number\"": "weeknumber: \"第几周\"", "item.setTitle(\"refresh view\"": "item.setTitle(\"刷新视图\"", ".setTitle(`show backlink files`": ".setTitle(`显示反向链接文件`", ".setTitle(\"show tooltip preview\"": ".setTitle(\"显示工具提示预览\"", "item.setTitle(\"extract tasks\")": "item.setTitle(\"提取任务\")", "item.setTitle(\"extract list callouts\")": "item.setTitle(\"提取列表标注\")", "item.setTitle(\"extract time lists\")": "item.setTitle(\"提取时间列表\")", "item.setTitle(\"Extract\"": "item.setTitle(\"提取\"", "<p>See the <a": "<p>请参阅 <a", ">documentation</a>.</p>": ">文档</a>.</p>", ">documentation</a> to get started!</p>": ">文档</a> 开始吧!</p>", "('Save time entering Scheduled (\\u23F3) dates.": "('节省输入 计划(Scheduled) (\\u23F3) 日期.", "By default, Tasks plugin will match both": "默认情况下,Tasks插件将兼容", "<code>YYYY-MM-DD</code> and <code>YYYYMMDD</code> date formats.": "<code>YYYY-MM-DD</code> 和 <code>YYYYMMDD</code> 日期格式.", "</br>Undated tasks have none of": "</br>未注明日期的任务没有", "Due (\\u{1F4C5} ), Scheduled (\\u23F3) and Start (\\u{1F6EB}) dates.": "到期(Due) (\\u{1F4C5} ), 计划(Scheduled) (\\u23F3) 和 开始(Start) (\\u{1F6EB}) 日期.", "('An additional date format that Tasks plugin will recogize when using the file name as the Scheduled date for undated tasks.</br><p><a href=\"https://momentjs.com/docs/#/displaying/format/\">Syntax Reference</a></p>')": "('当使用文件名作为未注明日期的任务的计划日期时,任务插件将识别的另一种日期格式.</br><p><a href=\"https://momentjs.com/docs/#/displaying/format/\">语法参考</a></p>')", "Enabling this will add a timestamp \\u274C YYYY-MM-DD at the end when a task is toggled to cancelled": "启用此功能将添加时间戳 \\u274C YYYY-MM-DD 当任务切换为取消时", "{return`Change status to: [${this.newStatus.symbol}": "{return`将状态更改为: [${this.newStatus.symbol}", "\"Review and check your Statuses\"": "\"查看并检查您的状态\"", "\"Recurs\"": "\"重复\"", "\"Status\"": "\"状态\"", "labelText:\"Before this\"": "labelText:\"父依赖\"", "labelText:\"After this\"": "labelText:\"子任务\"", "\"Only future dates:\"": "\"仅限未来日期:\"", "{parsedRecurrence:\"<i>not recurring</>\",isRecurrenceValid:!0}": "{parsedRecurrence:\"<i>不重复</>\",isRecurrenceValid:!0}", "{parsedRecurrence:\"<i>due, scheduled or start date required</i>\",isRecurrenceValid:!1}": "{parsedRecurrence:\"<i>需要截止日期、计划日期或开始日期</i>\",isRecurrenceValid:!1}", "{parsedRecurrence:\"<i>invalid recurrence rule</i>\",isRecurrenceValid:!1}": "{parsedRecurrence:\"<i>重复规则无效</i>\",isRecurrenceValid:!1}", "Problem line: \"${e.rawInstruction}\"": "问题行: \"${e.rawInstruction}\"", "<pre>Tasks query: $": "<pre>任务查询: $", ".indent(`No filters supplied. All tasks will match the query.": ".indent(`未提供筛选条件.所有任务都将与查询匹配.", ".indent(`No grouping instructions supplied.": ".indent(`未提供分组说明.", ".indent(`No sorting instructions supplied.": ".indent(`未提供排序说明.", "t=`sort by ${this.fieldNameSingular()}`": "t=`排序方式: ${this.fieldNameSingular()}`", "t=`group by ${this.fieldNameSingular()}`": "t=`分组方式: ${this.fieldNameSingular()}`", "(t+=\" reverse\")": "(t+=\" 倒序\")", "c=`${t} date is between:`": "c=`${t} 日期在此之间:`", "$i(\"Search failed\"": "$i(\"搜索失败\"", "$i(\"Parsing regular expression\"": "$i(\"解析正则表达式\"", "The error message was:": "错误消息为:", "`Error: Failed calculating expression \"${e}\". The error message was: `": "`错误: 计算表达式失败 \"${e}\". 错误消息为: `", "`Error: Cannot evaluate an expression which is not valid: \"${this.line}\" gave error: \"${this.parseError}\"`": "`错误: 无法计算无效的表达式: \"${this.line}\" 给出错误: \"${this.parseError}\"`", "Error(\"Unknown unit \"": "Error(\"未知单位\"", "Error(\"Class extends value \"": "Error(\"类扩展值\"", "Error(\"Generator is already executing.\"": "Error(\"生成器已在执行.\"", "Error(\"Symbol.asyncIterator is not defined.\"": "Error(\"Symbol.asyncIterator 未定义.\"", "Error(\"Private accessor was defined without a getter\"": "Error(\"定义了没有 getter 的私有访问器\"", "Error(\"Cannot read private member from an object whose class did not declare it\"": "Error(\"无法从类未声明私有成员的对象中读取私有成员\"", "Error(\"Private method is not writable\"": "Error(\"私有方法不可写\"", "Error(\"Private accessor was defined without a setter\"": "Error(\"定义私有访问器时没有设置器\"", "Error(\"Cannot write private member to an object whose class did not declare it\"": "Error(\"无法将私有成员写入类未声明它的对象\"", "Error(\"Cannot use 'in' operator on non-object\"": "Error(\"不能在非对象上使用 'in' 运算符\"", "Error(\"target does not implement any known event API\"": "Error(\"目标未实现任何已知事件 API\"", "Error(\"on method must be a function\"": "Error(\"on 方法必须是函数\"", "Error(\"off method must be a function\"": "Error(\"off 方法必须是函数\"", "Error(\"Event '\"": "Error(\"事件'\"", "Error(\"event must be a string\"": "Error(\"事件必须是字符串\"", "Error(\"options must be an object\"": "Error(\"选项必须是一个对象\"", "Error('Invalid \"'": "Error('无效\"'", "Error('Unknown \"'": "Error('未知\"'", "Error(\"Unable to subscribe on cancel event asynchronously\"": "Error(\"无法异步订阅取消事件\"", "Error(\"onCancel callback must be a function\"": "Error(\"onCancel 回调必须是函数\"", "Error(\"options should be an object or true\"": "Error(\"选项应该是 object 或 true\"", "Error(\"process.nextTick is not supported\"": "Error(\"process.nextTick 不受支持\"", "Error(\"target musts be an object\"": "Error(\"目标必须是一个对象\"", "Error(\"events must be an object\"": "Error(\"事件必须是一个对象\"", "Error(\"target should be an object\"": "Error(\"目标应该是一个对象\"", "Error(\"many only accepts instances of Function\"": "Error(\"许多只接受 Function 的实例\"", "Error(\"Uncaught, unspecified 'error' event.\"": "Error(\"未捕获, 未指定的 '错误' 事件.\"", "Error(\"onAny only accepts instances of Function\"": "Error(\"onAny 只接受 Function 的实例\"", "Error(\"on only accepts instances of Function\"": "Error(\"on 只接受 Function 的实例\"", "Error(\"removeListener only takes instances of Function\"": "Error(\"removeListener 只接受 Function 的实例\"", "Error(\"event name required for wildcard emitter\"": "Error(\"通配符发射器需要事件名称\"", "Error(\"n must be a non-negative number\"": "Error(\"n 必须是非负数\"", "Error(\"Unexpected character: \"": "Error(\"意外字符:\"", "Error(\"Unexpected end of expression: expected \"": "Error(\"表达式意外结束:预期\"", "Error(\"Unexpected end of expression\"": "Error(\"表达式意外结束\"", "Error(\"Invalid token\"": "Error(\"无效令牌\"", "Error(\"Expected string but received \"": "Error(\"预期的字符串已收到\"", "Error(\"Invalid postfix expression: \"": "Error(\"后缀表达式无效:\"", "Error(\"Invalid token: \"": "Error(\"无效令牌:\"", "Error(\"timeout while waiting for mutex to become available\"": "Error(\"等待互斥体可用时超时\"", "Error(\"mutex already locked\"": "Error(\"互斥体已锁定\"", "Error(\"request for lock canceled\"": "Error(\"锁定请求已取消\"", "Error(`invalid weight ${e}: must be positive`": "Error(`无效权重 ${e}: 必须为正`", "Error(\"Can't create weekday with n == 0\"": "Error(\"无法创建 n == 0 的工作日\"", "Error(\"Invalid UNTIL value: \"": "Error(\"UNTIL 值无效:\"", "Error(\"expected \"": "Error(\"预期\"", "Error(\"Unexpected end\"": "Error(\"意外结局\"", "Error(\"Unexpected symbol \"": "Error(\"意外符号\"", "Error(\"Unknown symbol\"": "Error(\"未知符号\"", "Error(\"Nth out of range: \"": "Error(\"第 N 个超出范围:\"", "Error(\"Cannot parse until date:\"": "Error(\"无法解析,直到日期:\"", "Error(\"Invalid options: \"": "Error(\"无效选项:\"", "Error(\"Invalid frequency: \"": "Error(\"无效频率:\"", "Error(\"bysetpos must be between 1 and 366, or between -366 and -1\"": "Error(\"bysetpos 必须介于 1 和 366 之间,或介于 -366 和 -1 之间\"", "Error(\"Unsupported RFC prop \"": "Error(\"不支持的 RFC 道具\"", "Error(\"Unknown RRULE property '\"": "Error(\"未知 RRULE 属性'\"", "Error(\"Invalid weekday string: \"": "Error(\"工作日字符串无效:\"", "Error(\"Invalid date passed to DateWithZone\"": "Error(\"传递给 DateWithZone 的日期无效\"", "Error(\"Invalid date passed in to RRule.between\"": "Error(\"传递给 RRule.between 的日期无效\"", "Error(\"Invalid date passed in to RRule.before\"": "Error(\"传递给 RRule.before 的日期无效\"", "Error(\"Invalid date passed in to RRule.after\"": "Error(\"传递给 RRule.after 的日期无效\"", "Error(\"unsupported RRULE parm: \"": "Error(\"不支持的 RRULE 参数:\"", "Error(\"unsupported EXRULE parm: \"": "Error(\"不支持的 EXRULE 参数:\"", "Error(\"unsupported property: \"": "Error(\"不支持的属性:\"", "Error(\"empty property name\"": "Error(\"空属性名称\"", "Error(\"Invalid empty string\"": "Error(\"无效的空字符串\"", "Error(\"unsupported RDATE/EXDATE parm: \"": "Error(\"不支持的 RDATE/EXDATE 参数:\"", "Error(`Don't know how to render task component of type '${n}'`": "Error(`不知道如何呈现类型为 '${n}' 的任务组件`", "Error(`Illegal argument passed to fromString(): ${e} does not correspond to any available Feature ${this.prototype.constructor.name}`": "Error(`传递给 fromString()的非法参数: ${e}与任何可用的 Feature ${this.prototype.constructor.name} 都不对应`", "Error(\"Function called outside component initialization\"": "Error(\"调用外部组件初始化的函数\"", "error(\"Tasks: Cannot create task on line:\"": "error(\"任务:无法在线创建任务:\"", "Error(`sorterRegExp() unimplemented for ${this.fieldNameSingular()}`": "Error(`${this.fieldNameSingular()} 的 sorterRegExp() 未实现`", "Error(`comparator() unimplemented for ${this.fieldNameSingular()}`": "Error(`${this.fieldNameSingular()} 的 comparator()未实现`", "Error(`grouper() unimplemented for ${this.fieldNameSingular()}`": "Error(`${this.fieldNameSingular()} 的 grouper() 未实现`", "Error(`filterRegExp() unimplemented for ${this.fieldName()}`": "Error(`${this.fieldName()} 的 filterRegExp() 未实现`", "Error(\"null token value\"": "Error(\"空令牌值\"", "Error(\"Unsupported operator: \"": "Error(\"不支持的操作员:\"", "Error(\"Unsupported token type: \"": "Error(\"不支持的令牌类型:\"", "Error(`filtering function must return true or false. This returned \"${n}\".`": "Error(`过滤函数必须返回 true 或 false.这返回 \"${n}\".`", "`Error:": "`错误:", "Error(\"removeListener 只接受 Function 的实例\"": "Error(\"移除监听器 只接受 Function 的实例\"", "Error(`Missing Mustache data property: ${s.join(\" > \")}`": "Error(`缺少 Mustache 数据属性: ${s.join(\" > \")}`", "Error(`Tasks: Does not support files with the ${t.extension} file extension.`": "Error(`任务:不支持文件扩展名为${t.extension}的文件.`", "Error(\"Invalid tags: \"": "Error(\"标签无效: \"", "Error(\"Unclosed tag at \"": "Error(\"未闭合标签位于 \"", "Error('Unopened section \"'": "Error('未打开的部分 \"'", "Error('Unclosed section \"'": "Error('未封闭部分 \"'", "Error(\"Cannot use higher-order sections without the original template\"": "Error(\"没有原始模板,无法使用高阶部分\"", "Error('Invalid template! Template should be a \"string\" but \"'": "Error('模板无效!模板应该是\"string\" 但是 \"'", "Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`": "Error(`grouperRegExp() 未实施 ${this.fieldNameSingular()}`", "Error(`\"${n}\" is not a valid sort key`": "Error(`\"${n}\" 不是有效的排序键`", "Error(`Unable to compare two different sort key types '${n}' and '${i}' order`": "Error(`无法比较两种不同排序键类型\"${n}\"和\"${i}\"的顺序`", "Error(`Unable to determine sort order for sort key types '${n}' and '${i}'`": "Error(`无法确定排序键类型的排序顺序 '${n}' 和 '${i}'`", "Error(\"grouper() function not valid for FunctionField. Use createGrouperFromLine() instead.\"": "Error(\"grouper()函数对 FunctionField 无效.请改用 createGrouperFromLine().\"", "Error(\"do not understand hide/show option\"": "Error(\"不能理解隐藏/显示选项\"", "Error(\"MenuDividerInstruction.apply(): Method not implemented.\"": "Error(\"MenuDividerInstruction.apply(): 方法未实现.\"", "Error(\"Developer note: CSS class cannot be an empty string, please specify one.\"": "Error(\"开发者注意:CSS 类不能为空字符串,请指定一个.\"", "$i(`Failed calculating expression \"${t}\"`": "$i(`计算表达式失败 \"${t}\"`", "$i(`Failed parsing expression \"${e}\"`": "$i(`解析表达式失败 \"${e}\"`", "The error message is:": "错误消息为:", "return`Could not interpret the following instruction as a Boolean combination:": "return`无法将以下指令解释为布尔组合:", "## About this file": "## 关于此文件", "This file was created by the Obsidian Tasks plugin (version ${n}) to help visualise the task statuses in this vault.": "此文件由黑曜石任务插件(版本 ${n})创建,以帮助可视化此保管库中的任务状态.", "If you change the Tasks status settings, you can get an updated report by:": "如果更改“任务”状态设置,则可以通过以下方式获取更新的报告:", "Going to \\`Settings\\` -> \\`Tasks\\`.": "去 \\`设置\\` -> \\`Tasks\\`.", "Clicking on \\`Review and check your Statuses\\`.": "点击 \\`查看并检查您的状态\\`.", "You can delete this file any time.": "您可以随时删除此文件.", "## Status Settings": "## 状态设置", "Switch to Live Preview or Reading Mode to see the table.": "切换到实时预览或阅读模式以查看表格.", "If there are any Markdown formatting characters in status names, such as '*' or '_',": "如果状态名称中有任何Markdown格式字符,如 '*' 或 '_',", "Obsidian may only render the table correctly in Reading Mode.": "黑曜石只能在阅读模式下正确渲染表格.", "These are the status values in the Core and Custom statuses sections.": "这些是核心和自定义状态部分中的状态值.", "## Loaded Settings": "## 已加载设置", "<!-- Switch to Live Preview or Reading Mode to see the diagram. -->": "<!-- 切换到实时预览或阅读模式以查看图表. -->", "These are the settings actually used by Tasks.": "这些是任务实际使用的设置.", "return`Instruction contains unexpanded template text: \"${e}\" - and cannot be interpreted.": "return`指令包含未展开的模板文本: \"${e}\" - 并且不能被解释.", "Possible causes:": "可能原因:", "The query is an a template file, and is not intended to be searched.": "该查询是一个模板文件,不用于搜索.", "A command such as \"Replace templates in the active file\" needs to be run.": "需要运行 \"替换活动文件中的模板\" 等命令.", "The core \"Daily notes\" plugin is in use, and the template contained": "核心插件\"Daily notes\"正在使用中,模板包含", "date calculations that it does not support.": "它不支持的日期计算.", "Some sample template text was accidentally pasted in to a tasks query,": "一些示例模板文本意外粘贴到任务查询中,", "instead of in to a template file.": "而不是放入模板文件中.", "See: https:": "请查阅: https:", "s=\"All filters in a Boolean instruction must be inside one of these pairs of delimiter characters: \"": "s=\"布尔指令中的所有筛选器都必须位于这些分隔符对之一内: \"", "\". Combinations of those delimiters are no longer supported.\"": "\".不再支持这些分隔符的组合.\"", "\"+Uh.map(([a,o])=>a+\"...\"+o).join(\" or \")+\"": "\"+Uh.map(([a,o])=>a+\"...\"+o).join(\" 或 \")+\"", "Problem statement:": "问题陈述:", ".log(`${this.constructor.name} merged ${u} and ${l} into ${c}`)": ".log(`${this.constructor.name} 并入到 ${u} 和 ${l} into ${c}`)", ".log(`Extracting timezone: '${c}' into: ${d} for: ${s.start}`)": ".log(`提取时区: '${c}' into: ${d} for: ${s.start}`)", ".log(`Extracting timezone: '${s[0]}' into : ${n}`)": ".log(`提取时区: '${s[0]}' into : ${n}`)", ".log(`Forward yearly adjusted for ${n} (${n.start})": ".log(`未来年度调整到 ${n} (${n.start})", ".log(`Forward yearly adjusted for ${n} (${n.end})": ".log(`未来年度调整到 ${n} (${n.end})", ".log(`Forward weekly adjusted for ${n} (${n.start})": ".log(`未来周调整到 ${n} (${n.start})", ".log(`Forward weekly adjusted for ${n} (${n.end})": ".log(`未来周调整到 ${n} (${n.end})", ".log(`Removing unlikely result '${t.text}'`)": ".log(`删除不太可能的结果 '${t.text}'`)", ".log(`Removing invalid result: ${t} (${t.end})": ".log(`删除无效结果: ${t} (${t.end})", ".log(`Removing invalid result: ${t} (${t.start})": ".log(`删除无效结果: ${t} (${t.start})", ".log(`${t.constructor.name} extracted result ${c}`)": ".log(`${t.constructor.name} 提取结果 ${c}`)", ".log(`OnCompletion action ${s} not yet implemented.`)": ".log(`OnCompletion action ${s} 尚未实施.`)", ".log(`Error in EditTask: cannot find status with symbol ${o}`)": ".log(`编辑任务中出错:找不到带  ${o} 符号的状态`)", ".log(`Measurement for ${this.label} not found`)": ".log(`未找到 ${this.label} `)", ".error(\"Tasks: Cannot create task on line:\",r)": ".error(\"任务:无法在行创建任务:\",r)", "Name:\"Tasks Emoji Format\"": "Name:\"Tasks 表情格式\"", ",\"Try 'every day when done'\"": ",\"尝试 'every day when done'\"", "dI=\"Try 'Mon' or 'tm' then space\"": "dI=\"尝试 'Mon' 或 'tm' 然后按空格\"", "return`Task's ${e} removed`": "return`任务 ${e} 被移除`", "return`${this.label} - start`": "return`${this.label} - 开始`", "return`${this.label} - end`": "return`${this.label} - 结束`", "return`<i>no ${r} date</i>`": "return`<i>无 ${r} 日期</i>`", ".replace(\" 1 day ago\",\" yesterday\")": ".replace(\" 1 day ago\",\" 昨天\")", ".replace(\" in 0 days\",\" today\")": ".replace(\" in 0 days\",\" 在今天\")", ".replace(\"in a day\",\"tomorrow\")": ".replace(\"in a day\",\"在明天\")", "var fI=\"Try 'Mon' or 'tm' then space\"": "var fI=\"尝试 'Mon' 或 'tm' 然后按空格\"", "\"priority:: highest\"": "\"优先级:: 最高\"", "\"priority:: high\"": "\"优先级:: 高\"", "\"priority:: medium\"": "\"优先级:: 中\"", "\"priority:: low\"": "\"优先级:: 低\"", "\"priority:: lowest\"": "\"优先级:: 最低\"", "\"start::\"": "\"开始::\"", "\"created::\"": "\"创建::\"", "\"scheduled::\"": "\"计划::\"", "\"due::\"": "\"截止::\"", "\"completion::\"": "\"完成::\"", "\"cancelled::\"": "\"已取消::\"", "\"repeat::\"": "\"重复::\"", "\"onCompletion::\"": "\"完成时::\"", "\"dependsOn::\"": "\"依赖于::\"", "start:: ": "开始:: ", "created:: ": "创建:: ", "scheduled:: ": "计划:: ", "due:: ": "截止:: ", "completion:: ": "完成:: ", "cancelled:: ": "已取消:: ", "repeat:: ": "重复:: ", "onCompletion:: ": "完成时:: ", "id:\"due\"": "id:\"截止\"", "id:\"scheduled\"": "id:\"计划\"", "id:\"start\"": "id:\"开始\"", "id:\"done\"": "id:\"完成\"", "id:\"cancelled\"": "id:\"取消\"", "Symbol,\"due date\"": "Symbol,\"截止日期\"", "Symbol,\"start date\"": "Symbol,\"开始日期\"", "Symbol,\"scheduled date\"": "Symbol,\"计划日期\"", "Symbol,\"recurring (repeat)\"": "Symbol,\"重复\"", "`<i>invalid ${r} date</i>`": "`无效的 ${r} 日期`", "Pr(new RegExp(\"dependsOn:: ": "Pr(new RegExp(\"依赖于:: ", "displayText:\"generate unique id\"": "displayText:\"生成唯一id\"", "Pi(n,i,r.dependsOnSymbol,\"depends on id\")": "Pi(n,i,r.dependsOnSymbol,\"取决于ID\")", "Pi(n,i,r.onCompletionSymbol,\"on completion\"": "Pi(n,i,r.onCompletionSymbol,\"结束\"", "displayText:`${e.createdDateSymbol} created today (${i})`": "displayText:`${e.createdDateSymbol} 今日创建 (${i})`", "Symbol,\"Priority\"": "Symbol,\"优先级\"", "Symbol,\"Description\"": "Symbol,\"描述\"", ":`Remove ${cc(r)}`": ":`移除 ${cc(r).replace(\"due date\",\"截止日期\").replace(\"scheduled date\",\"计划日期\").replace(\"start date\",\"开始日期\").replace(\"done date\",\"完成日期\").replace(\"created date\",\"创建日期\")}`", ".format(\"DD MMM YYYY\")": ".format(\"YYYY  MMM  DD\")", "\"Lowest\"": "\"最低\"", "\"Low\"": "\"低\"", "\"Normal\"": "\"一般\"", "\"Medium\"": "\"中\"", "\"High\"": "\"高\"", "\"Highest\"": "\"最高\"", "Highest:": "最高:", "High:": "高:", "Medium:": "中:", "Low:": "低:", "Lowest:": "最低:", ".Lowest": ".最低", ".Low": ".低", "id:\"created\"": "id:\"创建\"", "`${o} priority`": "`${o} 优先级`", "`${e.createdDateSymbol} created`": "`${e.createdDateSymbol} 创建`", "`${o} ${a.toLowerCase()} priority`": "`${o} ${a.toLowerCase()} 优先级`", "[\"today\",\"tomorrow\",\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"next week\",\"next month\",\"next year\"]": "[\"今天\",\"明天\",\"星期日\",\"星期一\",\"星期二\",\"星期三\",\"星期四\",\"星期五\",\"星期六\",\"下周\",\"下个月\",\"明年\"]", "sunday:0,sun:0,\"sun.\":0,monday:1,mon:1,\"mon.\":1,tuesday:2,tue:2,\"tue.\":2,wednesday:3,wed:3,\"wed.\":3,thursday:4,thurs:4,\"thurs.\":4,thur:4,\"thur.\":4,thu:4,\"thu.\":4,friday:5,fri:5,\"fri.\":5,saturday:6,sat:6,\"sat.\":6": "星期日:0,星期一:1,星期二:2,星期三:3,星期四:4,星期五:5,星期六:6,sunday:0,sun:0,\"sun.\":0,monday:1,mon:1,\"mon.\":1,tuesday:2,tue:2,\"tue.\":2,wednesday:3,wed:3,\"wed.\":3,thursday:4,thurs:4,\"thurs.\":4,thur:4,\"thur.\":4,thu:4,\"thu.\":4,friday:5,fri:5,\"fri.\":5,saturday:6,sat:6,\"sat.\":6", "UNIT_DICTIONARY={": "UNIT_DICTIONARY={秒:\"second\",分:\"minute\",时:\"hour\",天:\"d\",周:\"week\",月:\"month\",年:\"year\",", "MONTH_DICTIONARY={": "MONTH_DICTIONARY={十一月:11,十二月:12,一月:1,二月:2,三月:3,四月:4,五月:5,六月:6,七月:7,八月:8,九月:9,十月:10,", ".format(\"ddd Do MMM\")": ".format(\"MMMDo  ddd\")", "t:\"a\"": "t:\"1\"", ".setAttribute(\"title\",`Right-click to edit ${cc(c)}`": ".setAttribute(\"title\",`右键编辑 ${cc(c).replace(\"created\",\"创建\").replace(\"due\",\"截止\").replace(\"scheduled\",\"计划\").replace(\"start\",\"开始\").replace(\"done\",\"完成\").replace(\" date\",\"日期\").replace(\"day\",\"天\")}`", "return t>=0?`Postpone ${o} by ${a} ${n}, to ${s}`:`Backdate ${o} by ${-a} ${n}, to ${s}`": "return (t>=0?`推迟 ${o}于 ${a} ${n}后, 即: ${s}`:`回溯 ${o}于 ${-a} ${n}前, 即: ${s}`).replace(\"days\",\"天\").replace(\"weeks\",\"周\").replace(\"week\",\"周\").replace(\"month\",\"月\").replace(\"created\",\"创建\").replace(\"due\",\"截止\").replace(\"scheduled\",\"计划\").replace(\"start\",\"开始\").replace(\"done\",\"完成\").replace(\" date\",\"日期\").replace(\"day\",\"天\")", "return`Task's ${e} changed to ${t}`": "return`任务 ${e.replace(\"created\",\"创建\").replace(\"due\",\"截止\").replace(\"scheduled\",\"计划\").replace(\"start\",\"开始\").replace(\"done\",\"完成\").replace(\"Date\",\"日期\")} 变更为 ${t}`", ".Highest": ".最高", ".High": ".高", ".Medium": ".中", "(right-click for more options)": "(右键显示更多选项)", "return(t>=0?`${u} in ${a} ${n}, on ${s}`:`${u} ${-a} ${n} ago, on ${s}`)": "return(t>=0?`${u} ${a} ${n} 后, 即: ${s}`:`${u}于 ${-a} ${n}前, 即: ${s}`).replace(\"days\",\"天\").replace(\"day\",\"天\").replace(\"weeks\",\"周\").replace(\"week\",\"周\").replace(\"month\",\"月\").replace(\"created\",\"创建\").replace(\"Created\",\"创建\").replace(\"Due\",\"截止\").replace(\"Scheduled\",\"计划\").replace(\"Start\",\"开始\").replace(\"Done\",\"完成\").replace(\" 0 天 后\",\"于今天\").replace(\" 1 天 后\",\"于明天\").replace(\" 0 天前\",\"于今天\").replace(\" 1 天前\",\"昨天\")", "Right-click for options": "右键显示选项", "(highest|high|medium|low|lowest)": "(highest|high|medium|low|lowest最高|高|中|低|最低)", "[15](\"b\")": "[15](\"\")", "[15](\"e\")": "[15](\"\")", "[15](\"t\")": "[15](\"\")", "[15](\"f\")": "[15](\"\")", "[15](\"r\")": "[15](\"\")", "[15](\"d\")": "[15](\"\")", "[15](\"s\")": "[15](\"\")", "[15](\"a\")": "[15](\"\")", "[15](\"u\")": "[15](\"\")", "[15](\"c\")": "[15](\"\")", "[15](\"x\")": "[15](\"\")", "[15](\"-\")": "[15](\"\")", "case\"lowest\":": "case\"最低\":case\"lowest\":", "case\"low\":": "case\"低\":case\"low\":", "case\"medium\":": "case\"中\":case\"medium\":", "case\"high\":": "case\"高\":case\"high\":", "case\"highest\":": "case\"最高\":case\"highest\":", "case\"above\":": "case\"高于\":case\"above\":", "case\"below\":": "case\"低于\":case\"below\":", "case\"not\":o=u=>u.priority!==s;break;": "case\"不是\":case\"not\":o=u=>u.priority!==s;break;case\"是\":case\"is\":o=u=>u.priority==s;break;", "Yi.priorityRegexp=/^priority(\\s+is)?(\\s+(above|below|not))?(\\s+(lowest|low|none|medium|high|highest))$/i;": "Yi.priorityRegexp=/(^priority|^优先级)(\\s+(above|below|not|is|高于|低于|不是|是))?(\\s+(lowest|low|none|medium|high|highest|最低|低|中|高|最高))$/i;", "this.filterInstructions.add(`has ${this.fieldName()} date`,n=>this.date(n)!==null)": "this.filterInstructions.add(`有 ${this.fieldName()} 日期`,n=>this.date(n)!==null),this.filterInstructions.add(`has ${this.fieldName()} date`,n=>this.date(n)!==null)", "this.filterInstructions.add(`no ${this.fieldName()} date`,n=>this.date(n)===null)": "this.filterInstructions.add(`无 ${this.fieldName()} 日期`,n=>this.date(n)===null),this.filterInstructions.add(`no ${this.fieldName()} date`,n=>this.date(n)===null)", "this.filterInstructions.add(`${this.fieldName()} date is invalid`,n=>{let i=this.date(n);return i!==null&&!i.isValid()})": "this.filterInstructions.add(`${this.fieldName()} 日期无效`,n=>{let i=this.date(n);return i!==null&&!i.isValid()}),this.filterInstructions.add(`${this.fieldName()} date is invalid`,n=>{let i=this.date(n);return i!==null&&!i.isValid()})", "e.add(\"has happens date\",t=>this.dates(t).some(n=>n!==null))": "e.add(\"有日期\",t=>this.dates(t).some(n=>n!==null)),e.add(\"has happens date\",t=>this.dates(t).some(n=>n!==null))", "e.add(\"no happens date\",t=>!this.dates(t).some(n=>n!==null))": "e.add(\"无日期\",t=>!this.dates(t).some(n=>n!==null)),e.add(\"no happens date\",t=>!this.dates(t).some(n=>n!==null))", "this._filters.add(\"is recurring\",e=>e.recurrence!==null)": "this._filters.add(\"重复\",e=>e.recurrence!==null),this._filters.add(\"is recurring\",e=>e.recurrence!==null)", "this._filters.add(\"is not recurring\",e=>e.recurrence===null)": "this._filters.add(\"不重复\",e=>e.recurrence===null),this._filters.add(\"is not recurring\",e=>e.recurrence===null)", "this._filters.add(\"done\",e=>e.isDone)": "this._filters.add(\"完成\",e=>e.isDone),this._filters.add(\"done\",e=>e.isDone)", "this._filters.add(\"not done\",e=>!e.isDone)": "this._filters.add(\"未完成\",e=>!e.isDone),this._filters.add(\"not done\",e=>!e.isDone)", "this.filterInstructions.add(`has ${this.fieldNameSingular()}`,t=>this.values(t).length>0)": "this.filterInstructions.add(`有 ${this.fieldNameSingular()}`,t=>this.values(t).length>0),this.filterInstructions.add(`has ${this.fieldNameSingular()}`,t=>this.values(t).length>0)", "this.filterInstructions.add(`has ${this.fieldNamePlural()}`,t=>this.values(t).length>0)": "this.filterInstructions.add(`有 ${this.fieldNamePlural()}`,t=>this.values(t).length>0),this.filterInstructions.add(`has ${this.fieldNamePlural()}`,t=>this.values(t).length>0)", "this.filterInstructions.add(`no ${this.fieldNameSingular()}`,t=>this.values(t).length===0)": "this.filterInstructions.add(`无 ${this.fieldNameSingular()}`,t=>this.values(t).length===0),this.filterInstructions.add(`no ${this.fieldNameSingular()}`,t=>this.values(t).length===0)", "this.filterInstructions.add(`no ${this.fieldNamePlural()}`,t=>this.values(t).length===0)": "this.filterInstructions.add(`无 ${this.fieldNamePlural()}`,t=>this.values(t).length===0),this.filterInstructions.add(`no ${this.fieldNamePlural()}`,t=>this.values(t).length===0)", "this.filterInstructions.add(\"has id\",t=>t.id.length>0)": "this.filterInstructions.add(\"有 id\",t=>t.id.length>0),this.filterInstructions.add(\"has id\",t=>t.id.length>0)", "this.filterInstructions.add(\"no id\",t=>t.id.length===0)": "this.filterInstructions.add(\"无 id\",t=>t.id.length===0),this.filterInstructions.add(\"no id\",t=>t.id.length===0)", "`couldn't parse sub-expression '${f}'`": "`无法解析子表达式 '${f}'`", "`couldn't parse sub-expression '${f}': ${m.error}`": "`无法解析子表达式 '${f}': ${m.error}`", "\"empty operator in boolean query\"": "\"布尔查询中的操作符为空\"", "`unknown boolean operator '${c.value}'`": "`未知的布尔操作符 '${c.value}'`", "\"Filtering by urgency is not yet supported\"": "\"按紧急程度过滤尚不支持\"", "\"backlink field does not support filtering\"": "\"反向链接字段不支持过滤\"", "`For information, the conventional type for status symbol ${$n(r.symbol)} is ${$n(t)}: you may wish to review this type.`": "`供参考,状态符号 ${$n(r.symbol)} 的常规类型是 ${$n(t)}:您可能希望查看此类型.`", "`Next symbol ${$n(e.nextStatusSymbol)} is unknown: create a status with symbol ${$n(e.nextStatusSymbol)}.`": "`下一个符号 ${$n(e.nextStatusSymbol)} 未知:请创建符号为 ${$n(e.nextStatusSymbol)} 的状态.`", "`This \\`DONE\\` status is followed by ${$n(i.type)}, not \\`TODO\\` or \\`IN_PROGRESS\\`.`,\"If used to complete a recurring task, it will instead be followed by `TODO` or `IN_PROGRESS`, to ensure the next task matches the `not done` filter.\",`See [Recurring Tasks and Custom Statuses](${s}).`": "`此 \\`DONE\\` 状态后跟 ${$n(i.type)},而不是 \\`TODO\\` 或 \\`IN_PROGRESS\\`.`,\"如果用于完成一个重复任务,它将被 \\`TODO\\` 或 \\`IN_PROGRESS\\` 后续,以确保下一个任务匹配 `not done` 过滤器.\",`查看 [重复任务和自定义状态](${s}).`", "\"Unexpected failure to find the next status.\"": "\"未能意外找到下一个状态.\"", "\"Empty symbol: this status will be ignored.\"": "\"空符号:此状态将被忽略.\"", "`Duplicate symbol '${$n(e.symbol)}': this status will be ignored.`": "`重复的符号 '${$n(e.symbol)}':此状态将被忽略.`", "`Status symbol '${t}' toggles to itself`": "`状态符号 '${t}' 切换到自身`", "\"Task Status Name cannot be empty.\"": "\"任务状态名称不能为空.\"", "`Status Type \"${e}\" is not a valid type`": "`状态类型 \"${e}\" 不是有效类型`", "'Status Type \"EMPTY\" is not permitted in user data'": "'状态类型 \"EMPTY\" 不允许在用户数据中出现'", "`Next Status Symbol for symbol '${n}': '${e.nextStatusSymbol}' is inconsistent with convention '${a.nextStatusSymbol}'`": "`符号 '${n}' 的下一个状态符号 '${e.nextStatusSymbol}' 与约定 '${a.nextStatusSymbol}' 不一致`", "`Status Type for symbol '${n}': '${e.type}' is inconsistent with convention '${a.type}'`": "`符号 '${n}' 的状态类型 '${e.type}' 与约定 '${a.type}' 不一致`", "`${t} cannot be empty.`": "`${t} 不能为空.`", "`${t} (\"${e}\") must be a single character.`": "`${t} (\"${e}\") 必须是一个单一字符.`", "`See https://publish.obsidian.md/tasks/Queries/Regular+Expressions\n\nRegular expressions must look like this:\n    /pattern/\nor this:\n    /pattern/flags\n\nWhere:\n- pattern: The 'regular expression' pattern to search for.\n- flags:   Optional characters that modify the search.\n           i => make the search case-insensitive\n           u => add Unicode support\n\nExamples:  /^Log/\n           /^Log/i\n           /File Name\\.md/\n           /waiting|waits|waited/i\n           /\\d\\d:\\d\\d/\n\nThe following characters have special meaning in the pattern:\nto find them literally, you must add a \\ before them:\n    [\\^$.|?*+()\n\nCAUTION! Regular expression (or 'regex') searching is a powerful\nbut advanced feature that requires thorough knowledge in order to\nuse successfully, and not miss intended search results.\n`": "`请参阅 https://publish.obsidian.md/tasks/Queries/Regular+Expressions\n\n正则表达式必须如下所示:\n    /pattern/\n或者:\n    /pattern/flags\n\n其中:\n- pattern: 要搜索的 '正则表达式' 模式.\n- flags:   修改搜索的可选字符.\n           i => 使搜索不区分大小写\n           u => 添加对 Unicode 的支持\n\n示例:  /^Log/\n           /^Log/i\n           /File Name\\.md/\n           /waiting|waits|waited/i\n           /\\d\\d:\\d\\d/\n\n以下字符在模式中具有特殊意义:\n要按字面意思查找它们,必须在它们前面加上 \\:\n    [\\^$.|?*+()\n\n警告!正则表达式(或 'regex')搜索是一个强大的\n但高级的功能,需要充分的知识来成功使用,\n否则可能会错过预期的搜索结果.\n`", "`There was an error reading one of the tasks in this vault.\nThe following task has been ignored, to prevent Tasks queries getting stuck with 'Loading Tasks ...'\nError: ${e}\nFile: ${t}\nLine number: ${n.position.start.line}\nTask line: ${i}\n\nPlease create a bug report for this message at\nhttps://github.com/obsidian-tasks-group/obsidian-tasks/issues/new/choose\nto help us find and fix the underlying issue.\n\nInclude:\n- either a screenshot of the error popup, or copy the text from the console, if on a desktop machine.\n- the output from running the Obsidian command 'Show debug info'\n\nThe error popup will only be shown when Tasks is starting up, but if the error persists,\nit will be shown in the console every time this file is edited during the Obsidian\nsession.\n`": "`读取此库中一个任务时出错.\n以下任务已被忽略,以防止任务查询卡住显示 '加载任务...'\n错误: ${e}\n文件: ${t}\n行号: ${n.position.start.line}\n任务行: ${i}\n\n请在以下网址为此消息创建一个错误报告\nhttps://github.com/obsidian-tasks-group/obsidian-tasks/issues/new/choose\n以帮助我们找到并修复根本问题.\n\n包括:\n- 错误弹窗的截图,或如果是在桌面机器上,复制控制台中的文本.\n- 执行 Obsidian 命令 '显示调试信息' 的输出.\n\n错误弹窗只会在任务启动时显示,但如果错误持续存在,\n每次编辑此文件时,错误会在 Obsidian 会话中显示在控制台中.\n`", "`${this.helpMessageFromSimpleError(t,n)}\n\nThe instruction was converted to the following simplified line:\n    ${i.simplifiedLine}\n\nWhere the sub-expressions in the simplified line are:\n${a}\n\nFor help, see:\n    https://publish.obsidian.md/tasks/Queries/Combining+Filters\n`": "`${this.helpMessageFromSimpleError(t,n)}\n\n该指令已被转换为以下简化行:\n    ${i.simplifiedLine}\n\n简化行中的子表达式如下:\n${a}\n\n欲了解更多帮助,请参阅:\n    https://publish.obsidian.md/tasks/Queries/Combining+Filters\n`", "`ERROR:\n           do not understand query`": "`ERROR:\n           无法理解查询`", "`Could not interpret the following instruction as a Boolean combination:\n    ${t}\n\nThe error message is:\n    ${n}`": "`无法将以下指令解析为布尔组合:\n    ${t}\n\n错误信息如下:\n    ${n}`", "`Invalid ${this.fieldNameSingular()} instruction: '${e}'.\n    Allowed options: 'is' and 'is not' (without quotes).\n    Allowed values:  ${t}\n                     Note: values are case-insensitive,\n                           so 'in_progress' works too, for example.\n    Example:         ${this.fieldNameSingular()} is not NON_TASK`": "`无效的 ${this.fieldNameSingular()} 指令:'${e}'.\n    允许的选项:'is' 和 'is not'(不加引号).\n    允许的值:  ${t}\n                     注意:值不区分大小写,\n                           例如 'in_progress' 也适用.\n    示例:         ${this.fieldNameSingular()} is not NON_TASK`", "`# ${t}\n\n## About this file\n\nThis file was created by the Obsidian Tasks plugin (version ${n}) to help visualise the task statuses in this vault.\n\nIf you change the Tasks status settings, you can get an updated report by:\n\n- Going to \\`Settings\\` -> \\`Tasks\\`.\n- Clicking on \\`Review and check your Statuses\\`.\n\nYou can delete this file any time.\n\n## Status Settings\n\n<!--\nSwitch to Live Preview or Reading Mode to see the table.\nIf there are any Markdown formatting characters in status names, such as '*' or '_',\nObsidian may only render the table correctly in Reading Mode.\n-->\n\nThese are the status values in the Core and Custom statuses sections.\n\n${s}\n## Loaded Settings\n\n<!-- Switch to Live Preview or Reading Mode to see the diagram. -->\n\nThese are the settings actually used by Tasks.\n${a}`": "`# ${t}\n\n## 关于此文件\n\n该文件由 Obsidian Tasks 插件(版本 ${n})创建,用于帮助可视化此库中的任务状态.\n\n如果您更改了任务状态设置,可以通过以下方式获得更新的报告:\n\n- 进入 \\`设置\\` -> \\`任务\\`.\n- 点击 \\`检查并审查您的状态\\`.\n\n您可以随时删除此文件.\n\n## 状态设置\n\n<!--\n切换到实时预览或阅读模式以查看表格.\n如果状态名称中包含任何 Markdown 格式字符(如 '*' 或 '_'),\nObsidian 可能只有在阅读模式下才能正确渲染表格.\n-->\n\n这些是核心和自定义状态部分中的状态值.\n\n${s}\n## 加载的设置\n\n<!-- 切换到实时预览或阅读模式以查看图表. -->\n\n这些是 Tasks 实际使用的设置.\n${a}`", "`The query looks like it contains a placeholder, with \"{{\" and \"}}\"\nbut no file path has been supplied, so cannot expand placeholder values.\nThe query is:\n${n}`": "`查询看起来包含占位符，带有 \"{{\" 和 \"}}\"\n但没有提供文件路径，因此无法展开占位符值。\n查询为：\n${n}`", "case\"tree\":": "case\"树状结构\":case\"树\":case\"tree\":", "case\"task count\":": "case\"任务计数\":case\"任务数\":case\"task count\":", "case\"backlink\":": "case\"反向链接\":case\"反链\":case\"backlink\":", "case\"postpone button\":": "case\"推迟按钮\":case\"推迟\":case\"postpone button\":", "case\"priority\":": "case\"优先级\":case\"priority\":", "case\"cancelled date\":": "case\"取消日期\":case\"取消\":case\"cancelled date\":", "case\"created date\":": "case\"创建日期\":case\"创建\":case\"created date\":", "case\"start date\":": "case\"开始日期\":case\"start date\":case\"开始\":", "case\"scheduled date\":": "case\"计划日期\":case\"计划\":case\"scheduled date\":", "case\"due date\":": "case\"截止日期\":case\"截止\":case\"due date\":", "case\"done date\":": "case\"完成日期\":case\"完成\":case\"done date\":", "case\"recurrence rule\":": "case\"重复规则\":case\"重复\":case\"recurrence rule\":", "case\"edit button\":": "case\"编辑按钮\":case\"编辑\":case\"edit button\":", "case\"urgency\":": "case\"紧急程度\":case\"紧急\":case\"urgency\":", "case\"tags\":": "case\"标签\":case\"tags\":", "case\"depends on\":": "case\"依赖于\":case\"depends on\":case\"depends on\":", "case\"on completion\":": "case\"完成时\":case\"完成\":case\"on completion\":", "(hide|show) (task count": "(hide|show|隐藏|显示) (任务计数|任务数|反链|反向链接|优先级|取消(日期)?|创建(日期)?|开始(日期)?|计划(日期)?|完成(日期)?|到期(日期)?|重复(规则)?|编辑(按钮)?|推迟(按钮)?|紧急(程度)|标签|依赖(于)?|ID|完成(时)?|树(状结构)?|task count", "(now|today|tonight|tomorrow|tmr|tmrw|yesterday|last\\s*night)": "(now|today|tonight|tomorrow|tmr|tmrw|yesterday|last\\s*night|现在|今天|今晚|明天|昨天|昨晚|下午|晚上|夜晚|午夜|早晨|中午)", "toLowerCase()===\"hide\"": "toLowerCase()===\"hide\"|| t[1].toLowerCase()===\"隐藏\"", "Me(\"Priority\")": "Me(\"优先级\")", "gt(\"Description\"": "gt(\"描述\"", "case\"description\":": "case\"描述\":case\"description\":", "case\"now\":": "case\"现在\":case\"now\":case\"now\":", "case\"today\":": "case\"今天\":case\"today\":case\"today\":", "case\"yesterday\":": "case\"昨天\":case\"yesterday\":case\"yesterday\":", "case\"tomorrow\":": "case\"明天\":case\"tomorrow\":case\"tomorrow\":", "case\"tonight\":": "case\"今晚\":case\"tonight\":case\"tonight\":", "case\"afternoon\":": "case\"下午\":case\"afternoon\":case\"afternoon\":", "case\"evening\":": "case\"晚上\":case\"evening\":case\"evening\":", "case\"night\":": "case\"夜晚\":case\"night\":case\"night\":", "case\"midnight\":": "case\"午夜\":case\"midnight\":case\"midnight\":", "case\"morning\":": "case\"早晨\":case\"morning\":case\"morning\":", "case\"noon\":": "case\"中午\":case\"noon\":case\"noon\":", "case\"this\":": "case\"这\":case\"this\":case\"this\":", "case\"next\":": "case\"下\":case\"next\":case\"next\":", "case\"last\":": "case\"上\":case\"last\":case\"last\":", "case\"past\":": "case\"past\":case\"past\":case\"past\":", "${this.totalTasksCount()} tasks": "${this.totalTasksCount()} 个任务", "\"Add all Query File Defaults properties\"": "\"添加所有查询文件默认值属性\""}}