var __create=Object.create,__defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__commonJS=(t,e)=>(function(){return e||(0,t[__getOwnPropNames(t)[0]])((e={exports:{}}).exports,e),e.exports}),__export=(t,e)=>{for(var s in e)__defProp(t,s,{get:e[s],enumerable:!0})},__copyProps=(t,e,s,i)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let n of __getOwnPropNames(e))__hasOwnProp.call(t,n)||n===s||__defProp(t,n,{get:()=>e[n],enumerable:!(i=__getOwnPropDesc(e,n))||i.enumerable});return t},__toESM=(t,e,s)=>(s=null!=t?__create(__getProtoOf(t)):{},__copyProps(!e&&t&&t.__esModule?s:__defProp(s,"default",{value:t,enumerable:!0}),t)),__toCommonJS=t=>__copyProps(__defProp({},"__esModule",{value:!0}),t),require_universalify=__commonJS({"node_modules/universalify/index.js"(t){"use strict";t.fromCallback=function(t){return Object.defineProperty(function(...e){if("function"!=typeof e[e.length-1])return new Promise((s,i)=>{e.push((t,e)=>null!=t?i(t):s(e)),t.apply(this,e)});t.apply(this,e)},"name",{value:t.name})},t.fromPromise=function(t){return Object.defineProperty(function(...e){const s=e[e.length-1];if("function"!=typeof s)return t.apply(this,e);e.pop(),t.apply(this,e).then(t=>s(null,t),s)},"name",{value:t.name})}}}),require_polyfills=__commonJS({"node_modules/graceful-fs/polyfills.js"(t,e){var s,i=require("constants"),n=process.cwd,a=null,o=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return a||(a=n.call(process)),a};try{process.cwd()}catch(t){}"function"==typeof process.chdir&&(s=process.chdir,process.chdir=function(t){a=null,s.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,s)),e.exports=function(t){i.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&((e=t).lchmod=function(t,s,n){e.open(t,i.O_WRONLY|i.O_SYMLINK,s,function(t,i){t?n&&n(t):e.fchmod(i,s,function(t){e.close(i,function(e){n&&n(t||e)})})})},e.lchmodSync=function(t,s){var n,a=e.openSync(t,i.O_WRONLY|i.O_SYMLINK,s),o=!0;try{n=e.fchmodSync(a,s),o=!1}finally{if(o)try{e.closeSync(a)}catch(t){}else e.closeSync(a)}return n});var e;t.lutimes||function(t){i.hasOwnProperty("O_SYMLINK")&&t.futimes?(t.lutimes=function(e,s,n,a){t.open(e,i.O_SYMLINK,function(e,i){e?a&&a(e):t.futimes(i,s,n,function(e){t.close(i,function(t){a&&a(e||t)})})})},t.lutimesSync=function(e,s,n){var a,o=t.openSync(e,i.O_SYMLINK),r=!0;try{a=t.futimesSync(o,s,n),r=!1}finally{if(r)try{t.closeSync(o)}catch(t){}else t.closeSync(o)}return a}):t.futimes&&(t.lutimes=function(t,e,s,i){i&&process.nextTick(i)},t.lutimesSync=function(){})}(t);t.chown=a(t.chown),t.fchown=a(t.fchown),t.lchown=a(t.lchown),t.chmod=s(t.chmod),t.fchmod=s(t.fchmod),t.lchmod=s(t.lchmod),t.chownSync=r(t.chownSync),t.fchownSync=r(t.fchownSync),t.lchownSync=r(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=l(t.stat),t.fstat=l(t.fstat),t.lstat=l(t.lstat),t.statSync=c(t.statSync),t.fstatSync=c(t.fstatSync),t.lstatSync=c(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(t,e,s){s&&process.nextTick(s)},t.lchmodSync=function(){});t.chown&&!t.lchown&&(t.lchown=function(t,e,s,i){i&&process.nextTick(i)},t.lchownSync=function(){});"win32"===o&&(t.rename="function"!=typeof t.rename?t.rename:function(e){function s(s,i,n){var a=Date.now(),o=0;e(s,i,function r(l){if(l&&("EACCES"===l.code||"EPERM"===l.code||"EBUSY"===l.code)&&Date.now()-a<6e4)return setTimeout(function(){t.stat(i,function(t,a){t&&"ENOENT"===t.code?e(s,i,r):n(l)})},o),void(o<100&&(o+=10));n&&n(l)})}return Object.setPrototypeOf&&Object.setPrototypeOf(s,e),s}(t.rename));function s(e){return e?function(s,i,n){return e.call(t,s,i,function(t){_(t)&&(t=null),n&&n.apply(this,arguments)})}:e}function n(e){return e?function(s,i){try{return e.call(t,s,i)}catch(t){if(!_(t))throw t}}:e}function a(e){return e?function(s,i,n,a){return e.call(t,s,i,n,function(t){_(t)&&(t=null),a&&a.apply(this,arguments)})}:e}function r(e){return e?function(s,i,n){try{return e.call(t,s,i,n)}catch(t){if(!_(t))throw t}}:e}function l(e){return e?function(s,i,n){function a(t,e){e&&(e.uid<0&&(e.uid+=4294967296),e.gid<0&&(e.gid+=4294967296)),n&&n.apply(this,arguments)}return"function"==typeof i&&(n=i,i=null),i?e.call(t,s,i,a):e.call(t,s,a)}:e}function c(e){return e?function(s,i){var n=i?e.call(t,s,i):e.call(t,s);return n&&(n.uid<0&&(n.uid+=4294967296),n.gid<0&&(n.gid+=4294967296)),n}:e}function _(t){if(!t)return!0;if("ENOSYS"===t.code)return!0;var e=!process.getuid||0!==process.getuid();return!(!e||"EINVAL"!==t.code&&"EPERM"!==t.code)}t.read="function"!=typeof t.read?t.read:function(e){function s(s,i,n,a,o,r){var l;if(r&&"function"==typeof r){var c=0;l=function(_,d,u){if(_&&"EAGAIN"===_.code&&c<10)return c++,e.call(t,s,i,n,a,o,l);r.apply(this,arguments)}}return e.call(t,s,i,n,a,o,l)}return Object.setPrototypeOf&&Object.setPrototypeOf(s,e),s}(t.read),t.readSync="function"!=typeof t.readSync?t.readSync:(d=t.readSync,function(e,s,i,n,a){for(var o=0;;)try{return d.call(t,e,s,i,n,a)}catch(t){if("EAGAIN"===t.code&&o<10){o++;continue}throw t}});var d}}}),require_legacy_streams=__commonJS({"node_modules/graceful-fs/legacy-streams.js"(t,e){var s=require("stream").Stream;e.exports=function(t){return{ReadStream:function e(i,n){if(!(this instanceof e))return new e(i,n);s.call(this);var a=this;this.path=i;this.fd=null;this.readable=!0;this.paused=!1;this.flags="r";this.mode=438;this.bufferSize=65536;n=n||{};var o=Object.keys(n);for(var r=0,l=o.length;r<l;r++){var c=o[r];this[c]=n[c]}this.encoding&&this.setEncoding(this.encoding);if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(null!==this.fd)return void process.nextTick(function(){a._read()});t.open(this.path,this.flags,this.mode,function(t,e){if(t)return a.emit("error",t),void(a.readable=!1);a.fd=e,a.emit("open",e),a._read()})},WriteStream:function e(i,n){if(!(this instanceof e))return new e(i,n);s.call(this);this.path=i;this.fd=null;this.writable=!0;this.flags="w";this.encoding="binary";this.mode=438;this.bytesWritten=0;n=n||{};var a=Object.keys(n);for(var o=0,r=a.length;o<r;o++){var l=a[o];this[l]=n[l]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1;this._queue=[];null===this.fd&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}}}}),require_clone=__commonJS({"node_modules/graceful-fs/clone.js"(t,e){"use strict";e.exports=function(t){if(null===t||"object"!=typeof t)return t;if(t instanceof Object)var e={__proto__:s(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))}),e};var s=Object.getPrototypeOf||function(t){return t.__proto__}}}),require_graceful_fs=__commonJS({"node_modules/graceful-fs/graceful-fs.js"(t,e){var s,i,n=require("fs"),a=require_polyfills(),o=require_legacy_streams(),r=require_clone(),l=require("util");function c(t,e){Object.defineProperty(t,s,{get:function(){return e}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(s=Symbol.for("graceful-fs.queue"),i=Symbol.for("graceful-fs.previous")):(s="___graceful-fs.queue",i="___graceful-fs.previous");var _,d,u=function(){};function h(t){a(t),t.gracefulify=h,t.createReadStream=function(e,s){return new t.ReadStream(e,s)},t.createWriteStream=function(e,s){return new t.WriteStream(e,s)};var e=t.readFile;t.readFile=function(t,s,i){"function"==typeof s&&(i=s,s=null);return function t(s,i,n,a){return e(s,i,function(e){!e||"EMFILE"!==e.code&&"ENFILE"!==e.code?"function"==typeof n&&n.apply(this,arguments):p([t,[s,i,n],e,a||Date.now(),Date.now()])})}(t,s,i)};var s=t.writeFile;t.writeFile=function(t,e,i,n){"function"==typeof i&&(n=i,i=null);return function t(e,i,n,a,o){return s(e,i,n,function(s){!s||"EMFILE"!==s.code&&"ENFILE"!==s.code?"function"==typeof a&&a.apply(this,arguments):p([t,[e,i,n,a],s,o||Date.now(),Date.now()])})}(t,e,i,n)};var i=t.appendFile;i&&(t.appendFile=function(t,e,s,n){"function"==typeof s&&(n=s,s=null);return function t(e,s,n,a,o){return i(e,s,n,function(i){!i||"EMFILE"!==i.code&&"ENFILE"!==i.code?"function"==typeof a&&a.apply(this,arguments):p([t,[e,s,n,a],i,o||Date.now(),Date.now()])})}(t,e,s,n)});var n=t.copyFile;n&&(t.copyFile=function(t,e,s,i){"function"==typeof s&&(i=s,s=0);return function t(e,s,i,a,o){return n(e,s,i,function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?"function"==typeof a&&a.apply(this,arguments):p([t,[e,s,i,a],n,o||Date.now(),Date.now()])})}(t,e,s,i)});var r=t.readdir;t.readdir=function(t,e,s){"function"==typeof e&&(s=e,e=null);var i=l.test(process.version)?function(t,e,s,i){return r(t,n(t,e,s,i))}:function(t,e,s,i){return r(t,e,n(t,e,s,i))};return i(t,e,s);function n(t,e,s,n){return function(a,o){!a||"EMFILE"!==a.code&&"ENFILE"!==a.code?(o&&o.sort&&o.sort(),"function"==typeof s&&s.call(this,a,o)):p([i,[t,e,s],a,n||Date.now(),Date.now()])}}};var l=/^v[0-5]\./;if("v0.8"===process.version.substr(0,4)){var c=o(t);g=c.ReadStream,f=c.WriteStream}var _=t.ReadStream;_&&(g.prototype=Object.create(_.prototype),g.prototype.open=function(){var t=this;T(t.path,t.flags,t.mode,function(e,s){e?(t.autoClose&&t.destroy(),t.emit("error",e)):(t.fd=s,t.emit("open",s),t.read())})});var d=t.WriteStream;d&&(f.prototype=Object.create(d.prototype),f.prototype.open=function(){var t=this;T(t.path,t.flags,t.mode,function(e,s){e?(t.destroy(),t.emit("error",e)):(t.fd=s,t.emit("open",s))})}),Object.defineProperty(t,"ReadStream",{get:function(){return g},set:function(t){g=t},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return f},set:function(t){f=t},enumerable:!0,configurable:!0});var u=g;Object.defineProperty(t,"FileReadStream",{get:function(){return u},set:function(t){u=t},enumerable:!0,configurable:!0});var m=f;function g(t,e){return this instanceof g?(_.apply(this,arguments),this):g.apply(Object.create(g.prototype),arguments)}function f(t,e){return this instanceof f?(d.apply(this,arguments),this):f.apply(Object.create(f.prototype),arguments)}Object.defineProperty(t,"FileWriteStream",{get:function(){return m},set:function(t){m=t},enumerable:!0,configurable:!0});var E=t.open;function T(t,e,s,i){return"function"==typeof s&&(i=s,s=null),function t(e,s,i,n,a){return E(e,s,i,function(o,r){!o||"EMFILE"!==o.code&&"ENFILE"!==o.code?"function"==typeof n&&n.apply(this,arguments):p([t,[e,s,i,n],o,a||Date.now(),Date.now()])})}(t,e,s,i)}return t.open=T,t}function p(t){u("ENQUEUE",t[0].name,t[1]),n[s].push(t),g()}function m(){for(var t=Date.now(),e=0;e<n[s].length;++e)n[s][e].length>2&&(n[s][e][3]=t,n[s][e][4]=t);g()}function g(){if(clearTimeout(d),d=void 0,0!==n[s].length){var t=n[s].shift(),e=t[0],i=t[1],a=t[2],o=t[3],r=t[4];if(void 0===o)u("RETRY",e.name,i),e.apply(null,i);else if(Date.now()-o>=6e4){u("TIMEOUT",e.name,i);var l=i.pop();"function"==typeof l&&l.call(null,a)}else{var c=Date.now()-r,_=Math.max(r-o,1);c>=Math.min(1.2*_,100)?(u("RETRY",e.name,i),e.apply(null,i.concat([o]))):n[s].push(t)}void 0===d&&(d=setTimeout(g,0))}}l.debuglog?u=l.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(u=function(){var t=l.format.apply(l,arguments);t="GFS4: "+t.split(/\n/).join("\nGFS4: "),console.error(t)}),n[s]||(_=global[s]||[],c(n,_),n.close=function(t){function e(e,s){return t.call(n,e,function(t){t||m(),"function"==typeof s&&s.apply(this,arguments)})}return Object.defineProperty(e,i,{value:t}),e}(n.close),n.closeSync=function(t){function e(e){t.apply(n,arguments),m()}return Object.defineProperty(e,i,{value:t}),e}(n.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){u(n[s]),require("assert").equal(n[s].length,0)})),global[s]||c(global,n[s]),e.exports=h(r(n)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!n.__patched&&(e.exports=h(n),n.__patched=!0)}}),require_fs=__commonJS({"node_modules/fs-extra/lib/fs/index.js"(t){"use strict";var e=require_universalify().fromCallback,s=require_graceful_fs(),i=["access","appendFile","chmod","chown","close","copyFile","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","link","lstat","mkdir","mkdtemp","open","opendir","readdir","readFile","readlink","realpath","rename","rm","rmdir","stat","symlink","truncate","unlink","utimes","writeFile"].filter(t=>"function"==typeof s[t]);Object.assign(t,s),i.forEach(i=>{t[i]=e(s[i])}),t.exists=function(t,e){return"function"==typeof e?s.exists(t,e):new Promise(e=>s.exists(t,e))},t.read=function(t,e,i,n,a,o){return"function"==typeof o?s.read(t,e,i,n,a,o):new Promise((o,r)=>{s.read(t,e,i,n,a,(t,e,s)=>{if(t)return r(t);o({bytesRead:e,buffer:s})})})},t.write=function(t,e,...i){return"function"==typeof i[i.length-1]?s.write(t,e,...i):new Promise((n,a)=>{s.write(t,e,...i,(t,e,s)=>{if(t)return a(t);n({bytesWritten:e,buffer:s})})})},t.readv=function(t,e,...i){return"function"==typeof i[i.length-1]?s.readv(t,e,...i):new Promise((n,a)=>{s.readv(t,e,...i,(t,e,s)=>{if(t)return a(t);n({bytesRead:e,buffers:s})})})},t.writev=function(t,e,...i){return"function"==typeof i[i.length-1]?s.writev(t,e,...i):new Promise((n,a)=>{s.writev(t,e,...i,(t,e,s)=>{if(t)return a(t);n({bytesWritten:e,buffers:s})})})},"function"==typeof s.realpath.native?t.realpath.native=e(s.realpath.native):process.emitWarning("fs.realpath.native is not a function. Is fs being monkey-patched?","Warning","fs-extra-WARN0003")}}),require_utils=__commonJS({"node_modules/fs-extra/lib/mkdirs/utils.js"(t,e){"use strict";var s=require("path");e.exports.checkPath=function(t){if("win32"===process.platform){if(/[<>:"|?*]/.test(t.replace(s.parse(t).root,""))){const e=new Error(`Path contains invalid characters: ${t}`);throw e.code="EINVAL",e}}}}}),require_make_dir=__commonJS({"node_modules/fs-extra/lib/mkdirs/make-dir.js"(t,e){"use strict";var s=require_fs(),{checkPath:i}=require_utils(),n=t=>{return"number"==typeof t?t:{...{mode:511},...t}.mode};e.exports.makeDir=(async(t,e)=>(i(t),s.mkdir(t,{mode:n(e),recursive:!0}))),e.exports.makeDirSync=((t,e)=>(i(t),s.mkdirSync(t,{mode:n(e),recursive:!0})))}}),require_mkdirs=__commonJS({"node_modules/fs-extra/lib/mkdirs/index.js"(t,e){"use strict";var s=require_universalify().fromPromise,{makeDir:i,makeDirSync:n}=require_make_dir(),a=s(i);e.exports={mkdirs:a,mkdirsSync:n,mkdirp:a,mkdirpSync:n,ensureDir:a,ensureDirSync:n}}}),require_path_exists=__commonJS({"node_modules/fs-extra/lib/path-exists/index.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require_fs();e.exports={pathExists:s(function(t){return i.access(t).then(()=>!0).catch(()=>!1)}),pathExistsSync:i.existsSync}}}),require_utimes=__commonJS({"node_modules/fs-extra/lib/util/utimes.js"(t,e){"use strict";var s=require_fs(),i=require_universalify().fromPromise;e.exports={utimesMillis:i(async function(t,e,i){const n=await s.open(t,"r+");let a=null;try{await s.futimes(n,e,i)}finally{try{await s.close(n)}catch(t){a=t}}if(a)throw a}),utimesMillisSync:function(t,e,i){const n=s.openSync(t,"r+");return s.futimesSync(n,e,i),s.closeSync(n)}}}}),require_stat=__commonJS({"node_modules/fs-extra/lib/util/stat.js"(t,e){"use strict";var s=require_fs(),i=require("path"),n=require_universalify().fromPromise;function a(t,e){return e.ino&&e.dev&&e.ino===t.ino&&e.dev===t.dev}function o(t,e){const s=i.resolve(t).split(i.sep).filter(t=>t),n=i.resolve(e).split(i.sep).filter(t=>t);return s.every((t,e)=>n[e]===t)}function r(t,e,s){return`Cannot ${s} '${t}' to a subdirectory of itself, '${e}'.`}e.exports={checkPaths:n(async function(t,e,n,l){const{srcStat:c,destStat:_}=await function(t,e,i){const n=i.dereference?t=>s.stat(t,{bigint:!0}):t=>s.lstat(t,{bigint:!0});return Promise.all([n(t),n(e).catch(t=>{if("ENOENT"===t.code)return null;throw t})]).then(([t,e])=>({srcStat:t,destStat:e}))}(t,e,l);if(_){if(a(c,_)){const s=i.basename(t),a=i.basename(e);if("move"===n&&s!==a&&s.toLowerCase()===a.toLowerCase())return{srcStat:c,destStat:_,isChangingCase:!0};throw new Error("Source and destination must not be the same.")}if(c.isDirectory()&&!_.isDirectory())throw new Error(`Cannot overwrite non-directory '${e}' with directory '${t}'.`);if(!c.isDirectory()&&_.isDirectory())throw new Error(`Cannot overwrite directory '${e}' with non-directory '${t}'.`)}if(c.isDirectory()&&o(t,e))throw new Error(r(t,e,n));return{srcStat:c,destStat:_}}),checkPathsSync:function(t,e,n,l){const{srcStat:c,destStat:_}=function(t,e,i){let n;const a=i.dereference?t=>s.statSync(t,{bigint:!0}):t=>s.lstatSync(t,{bigint:!0}),o=a(t);try{n=a(e)}catch(t){if("ENOENT"===t.code)return{srcStat:o,destStat:null};throw t}return{srcStat:o,destStat:n}}(t,e,l);if(_){if(a(c,_)){const s=i.basename(t),a=i.basename(e);if("move"===n&&s!==a&&s.toLowerCase()===a.toLowerCase())return{srcStat:c,destStat:_,isChangingCase:!0};throw new Error("Source and destination must not be the same.")}if(c.isDirectory()&&!_.isDirectory())throw new Error(`Cannot overwrite non-directory '${e}' with directory '${t}'.`);if(!c.isDirectory()&&_.isDirectory())throw new Error(`Cannot overwrite directory '${e}' with non-directory '${t}'.`)}if(c.isDirectory()&&o(t,e))throw new Error(r(t,e,n));return{srcStat:c,destStat:_}},checkParentPaths:n(async function t(e,n,o,l){const c=i.resolve(i.dirname(e)),_=i.resolve(i.dirname(o));if(_===c||_===i.parse(_).root)return;let d;try{d=await s.stat(_,{bigint:!0})}catch(t){if("ENOENT"===t.code)return;throw t}if(a(n,d))throw new Error(r(e,o,l));return t(e,n,_,l)}),checkParentPathsSync:function t(e,n,o,l){const c=i.resolve(i.dirname(e)),_=i.resolve(i.dirname(o));if(_===c||_===i.parse(_).root)return;let d;try{d=s.statSync(_,{bigint:!0})}catch(t){if("ENOENT"===t.code)return;throw t}if(a(n,d))throw new Error(r(e,o,l));return t(e,n,_,l)},isSrcSubdir:o,areIdentical:a}}}),require_copy=__commonJS({"node_modules/fs-extra/lib/copy/copy.js"(t,e){"use strict";var s=require_fs(),i=require("path"),{mkdirs:n}=require_mkdirs(),{pathExists:a}=require_path_exists(),{utimesMillis:o}=require_utimes(),r=require_stat();async function l(t,e,s){return!s.filter||s.filter(t,e)}async function c(t,e,n,a){const o=a.dereference?s.stat:s.lstat,d=await o(e);if(d.isDirectory())return async function(t,e,n,a,o){e||await s.mkdir(a);const _=await s.readdir(n);await Promise.all(_.map(async t=>{const e=i.join(n,t),s=i.join(a,t);if(!await l(e,s,o))return;const{destStat:_}=await r.checkPaths(e,s,"copy",o);return c(_,e,s,o)})),e||await s.chmod(a,t.mode)}(d,t,e,n,a);if(d.isFile()||d.isCharacterDevice()||d.isBlockDevice())return async function(t,e,i,n,a){if(!e)return _(t,i,n,a);if(a.overwrite)return await s.unlink(n),_(t,i,n,a);if(a.errorOnExist)throw new Error(`'${n}' already exists`)}(d,t,e,n,a);if(d.isSymbolicLink())return async function(t,e,n,a){let o=await s.readlink(e);a.dereference&&(o=i.resolve(process.cwd(),o));if(!t)return s.symlink(o,n);let l=null;try{l=await s.readlink(n)}catch(t){if("EINVAL"===t.code||"UNKNOWN"===t.code)return s.symlink(o,n);throw t}a.dereference&&(l=i.resolve(process.cwd(),l));if(r.isSrcSubdir(o,l))throw new Error(`Cannot copy '${o}' to a subdirectory of itself, '${l}'.`);if(r.isSrcSubdir(l,o))throw new Error(`Cannot overwrite '${l}' with '${o}'.`);return await s.unlink(n),s.symlink(o,n)}(t,e,n,a);if(d.isSocket())throw new Error(`Cannot copy a socket file: ${e}`);if(d.isFIFO())throw new Error(`Cannot copy a FIFO pipe: ${e}`);throw new Error(`Unknown file: ${e}`)}async function _(t,e,i,n){if(await s.copyFile(e,i),n.preserveTimestamps){0==(128&t.mode)&&await function(t,e){return s.chmod(t,128|e)}(i,t.mode);const n=await s.stat(e);await o(i,n.atime,n.mtime)}return s.chmod(i,t.mode)}e.exports=async function(t,e,s={}){"function"==typeof s&&(s={filter:s}),s.clobber=!("clobber"in s&&!s.clobber),s.overwrite="overwrite"in s?!!s.overwrite:s.clobber,s.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0001");const{srcStat:o,destStat:_}=await r.checkPaths(t,e,"copy",s);if(await r.checkParentPaths(t,o,e,"copy"),!await l(t,e,s))return;const d=i.dirname(e);await a(d)||await n(d),await c(_,t,e,s)}}}),require_copy_sync=__commonJS({"node_modules/fs-extra/lib/copy/copy-sync.js"(t,e){"use strict";var s=require_graceful_fs(),i=require("path"),n=require_mkdirs().mkdirsSync,a=require_utimes().utimesMillisSync,o=require_stat();function r(t,e,n,a){const r=(a.dereference?s.statSync:s.lstatSync)(e);if(r.isDirectory())return function(t,e,i,n,a){return e?_(i,n,a):function(t,e,i,n){return s.mkdirSync(i),_(e,i,n),c(i,t)}(t.mode,i,n,a)}(r,t,e,n,a);if(r.isFile()||r.isCharacterDevice()||r.isBlockDevice())return function(t,e,i,n,a){return e?function(t,e,i,n){if(n.overwrite)return s.unlinkSync(i),l(t,e,i,n);if(n.errorOnExist)throw new Error(`'${i}' already exists`)}(t,i,n,a):l(t,i,n,a)}(r,t,e,n,a);if(r.isSymbolicLink())return function(t,e,n,a){let r=s.readlinkSync(e);a.dereference&&(r=i.resolve(process.cwd(),r));if(t){let t;try{t=s.readlinkSync(n)}catch(t){if("EINVAL"===t.code||"UNKNOWN"===t.code)return s.symlinkSync(r,n);throw t}if(a.dereference&&(t=i.resolve(process.cwd(),t)),o.isSrcSubdir(r,t))throw new Error(`Cannot copy '${r}' to a subdirectory of itself, '${t}'.`);if(o.isSrcSubdir(t,r))throw new Error(`Cannot overwrite '${t}' with '${r}'.`);return function(t,e){return s.unlinkSync(e),s.symlinkSync(t,e)}(r,n)}return s.symlinkSync(r,n)}(t,e,n,a);if(r.isSocket())throw new Error(`Cannot copy a socket file: ${e}`);if(r.isFIFO())throw new Error(`Cannot copy a FIFO pipe: ${e}`);throw new Error(`Unknown file: ${e}`)}function l(t,e,i,n){return s.copyFileSync(e,i),n.preserveTimestamps&&function(t,e,i){(function(t){return 0==(128&t)})(t)&&function(t,e){c(t,128|e)}(i,t);(function(t,e){const i=s.statSync(t);a(e,i.atime,i.mtime)})(e,i)}(t.mode,e,i),c(i,t.mode)}function c(t,e){return s.chmodSync(t,e)}function _(t,e,n){s.readdirSync(t).forEach(s=>(function(t,e,s,n){const a=i.join(e,t),l=i.join(s,t);if(n.filter&&!n.filter(a,l))return;const{destStat:c}=o.checkPathsSync(a,l,"copy",n);return r(c,a,l,n)})(s,t,e,n))}e.exports=function(t,e,a){"function"==typeof a&&(a={filter:a}),(a=a||{}).clobber=!("clobber"in a&&!a.clobber),a.overwrite="overwrite"in a?!!a.overwrite:a.clobber,a.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0002");const{srcStat:l,destStat:c}=o.checkPathsSync(t,e,"copy",a);if(o.checkParentPathsSync(t,l,e,"copy"),a.filter&&!a.filter(t,e))return;const _=i.dirname(e);return s.existsSync(_)||n(_),r(c,t,e,a)}}}),require_copy2=__commonJS({"node_modules/fs-extra/lib/copy/index.js"(t,e){"use strict";var s=require_universalify().fromPromise;e.exports={copy:s(require_copy()),copySync:require_copy_sync()}}}),require_remove=__commonJS({"node_modules/fs-extra/lib/remove/index.js"(t,e){"use strict";var s=require_graceful_fs(),i=require_universalify().fromCallback;e.exports={remove:i(function(t,e){s.rm(t,{recursive:!0,force:!0},e)}),removeSync:function(t){s.rmSync(t,{recursive:!0,force:!0})}}}}),require_empty=__commonJS({"node_modules/fs-extra/lib/empty/index.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require_fs(),n=require("path"),a=require_mkdirs(),o=require_remove(),r=s(async function(t){let e;try{e=await i.readdir(t)}catch(e){return a.mkdirs(t)}return Promise.all(e.map(e=>o.remove(n.join(t,e))))});function l(t){let e;try{e=i.readdirSync(t)}catch(e){return a.mkdirsSync(t)}e.forEach(e=>{e=n.join(t,e),o.removeSync(e)})}e.exports={emptyDirSync:l,emptydirSync:l,emptyDir:r,emptydir:r}}}),require_file=__commonJS({"node_modules/fs-extra/lib/ensure/file.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require("path"),n=require_fs(),a=require_mkdirs();e.exports={createFile:s(async function(t){let e;try{e=await n.stat(t)}catch(t){}if(e&&e.isFile())return;const s=i.dirname(t);let o=null;try{o=await n.stat(s)}catch(e){if("ENOENT"===e.code)return await a.mkdirs(s),void await n.writeFile(t,"");throw e}o.isDirectory()?await n.writeFile(t,""):await n.readdir(s)}),createFileSync:function(t){let e;try{e=n.statSync(t)}catch(t){}if(e&&e.isFile())return;const s=i.dirname(t);try{n.statSync(s).isDirectory()||n.readdirSync(s)}catch(t){if(!t||"ENOENT"!==t.code)throw t;a.mkdirsSync(s)}n.writeFileSync(t,"")}}}}),require_link=__commonJS({"node_modules/fs-extra/lib/ensure/link.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require("path"),n=require_fs(),a=require_mkdirs(),{pathExists:o}=require_path_exists(),{areIdentical:r}=require_stat();e.exports={createLink:s(async function(t,e){let s,l;try{s=await n.lstat(e)}catch(t){}try{l=await n.lstat(t)}catch(t){throw t.message=t.message.replace("lstat","ensureLink"),t}if(s&&r(l,s))return;const c=i.dirname(e);await o(c)||await a.mkdirs(c),await n.link(t,e)}),createLinkSync:function(t,e){let s;try{s=n.lstatSync(e)}catch(t){}try{const e=n.lstatSync(t);if(s&&r(e,s))return}catch(t){throw t.message=t.message.replace("lstat","ensureLink"),t}const o=i.dirname(e);return n.existsSync(o)?n.linkSync(t,e):(a.mkdirsSync(o),n.linkSync(t,e))}}}}),require_symlink_paths=__commonJS({"node_modules/fs-extra/lib/ensure/symlink-paths.js"(t,e){"use strict";var s=require("path"),i=require_fs(),{pathExists:n}=require_path_exists(),a=require_universalify().fromPromise;e.exports={symlinkPaths:a(async function(t,e){if(s.isAbsolute(t)){try{await i.lstat(t)}catch(t){throw t.message=t.message.replace("lstat","ensureSymlink"),t}return{toCwd:t,toDst:t}}const a=s.dirname(e),o=s.join(a,t);if(await n(o))return{toCwd:o,toDst:t};try{await i.lstat(t)}catch(t){throw t.message=t.message.replace("lstat","ensureSymlink"),t}return{toCwd:t,toDst:s.relative(a,t)}}),symlinkPathsSync:function(t,e){if(s.isAbsolute(t)){if(!i.existsSync(t))throw new Error("absolute srcpath does not exist");return{toCwd:t,toDst:t}}const n=s.dirname(e),a=s.join(n,t);if(i.existsSync(a))return{toCwd:a,toDst:t};if(!i.existsSync(t))throw new Error("relative srcpath does not exist");return{toCwd:t,toDst:s.relative(n,t)}}}}}),require_symlink_type=__commonJS({"node_modules/fs-extra/lib/ensure/symlink-type.js"(t,e){"use strict";var s=require_fs(),i=require_universalify().fromPromise;e.exports={symlinkType:i(async function(t,e){if(e)return e;let i;try{i=await s.lstat(t)}catch(t){return"file"}return i&&i.isDirectory()?"dir":"file"}),symlinkTypeSync:function(t,e){if(e)return e;let i;try{i=s.lstatSync(t)}catch(t){return"file"}return i&&i.isDirectory()?"dir":"file"}}}}),require_symlink=__commonJS({"node_modules/fs-extra/lib/ensure/symlink.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require("path"),n=require_fs(),{mkdirs:a,mkdirsSync:o}=require_mkdirs(),{symlinkPaths:r,symlinkPathsSync:l}=require_symlink_paths(),{symlinkType:c,symlinkTypeSync:_}=require_symlink_type(),{pathExists:d}=require_path_exists(),{areIdentical:u}=require_stat();e.exports={createSymlink:s(async function(t,e,s){let o;try{o=await n.lstat(e)}catch(t){}if(o&&o.isSymbolicLink()){const[s,i]=await Promise.all([n.stat(t),n.stat(e)]);if(u(s,i))return}const l=await r(t,e);t=l.toDst;const _=await c(l.toCwd,s),h=i.dirname(e);return await d(h)||await a(h),n.symlink(t,e,_)}),createSymlinkSync:function(t,e,s){let a;try{a=n.lstatSync(e)}catch(t){}if(a&&a.isSymbolicLink()){const s=n.statSync(t),i=n.statSync(e);if(u(s,i))return}const r=l(t,e);t=r.toDst,s=_(r.toCwd,s);const c=i.dirname(e);return n.existsSync(c)?n.symlinkSync(t,e,s):(o(c),n.symlinkSync(t,e,s))}}}}),require_ensure=__commonJS({"node_modules/fs-extra/lib/ensure/index.js"(t,e){"use strict";var{createFile:s,createFileSync:i}=require_file(),{createLink:n,createLinkSync:a}=require_link(),{createSymlink:o,createSymlinkSync:r}=require_symlink();e.exports={createFile:s,createFileSync:i,ensureFile:s,ensureFileSync:i,createLink:n,createLinkSync:a,ensureLink:n,ensureLinkSync:a,createSymlink:o,createSymlinkSync:r,ensureSymlink:o,ensureSymlinkSync:r}}}),require_utils2=__commonJS({"node_modules/jsonfile/utils.js"(t,e){e.exports={stringify:function(t,{EOL:e="\n",finalEOL:s=!0,replacer:i=null,spaces:n}={}){const a=s?e:"";return JSON.stringify(t,i,n).replace(/\n/g,e)+a},stripBom:function(t){return Buffer.isBuffer(t)&&(t=t.toString("utf8")),t.replace(/^\uFEFF/,"")}}}}),require_jsonfile=__commonJS({"node_modules/jsonfile/index.js"(t,e){var s;try{s=require_graceful_fs()}catch(t){s=require("fs")}var i=require_universalify(),{stringify:n,stripBom:a}=require_utils2();var o={readFile:i.fromPromise(async function(t,e={}){"string"==typeof e&&(e={encoding:e});const n=e.fs||s,o=!("throws"in e)||e.throws;let r,l=await i.fromCallback(n.readFile)(t,e);l=a(l);try{r=JSON.parse(l,e?e.reviver:null)}catch(e){if(o)throw e.message=`${t}: ${e.message}`,e;return null}return r}),readFileSync:function(t,e={}){"string"==typeof e&&(e={encoding:e});const i=e.fs||s,n=!("throws"in e)||e.throws;try{let s=i.readFileSync(t,e);return s=a(s),JSON.parse(s,e.reviver)}catch(e){if(n)throw e.message=`${t}: ${e.message}`,e;return null}},writeFile:i.fromPromise(async function(t,e,a={}){const o=a.fs||s,r=n(e,a);await i.fromCallback(o.writeFile)(t,r,a)}),writeFileSync:function(t,e,i={}){const a=i.fs||s,o=n(e,i);return a.writeFileSync(t,o,i)}};e.exports=o}}),require_jsonfile2=__commonJS({"node_modules/fs-extra/lib/json/jsonfile.js"(t,e){"use strict";var s=require_jsonfile();e.exports={readJson:s.readFile,readJsonSync:s.readFileSync,writeJson:s.writeFile,writeJsonSync:s.writeFileSync}}}),require_output_file=__commonJS({"node_modules/fs-extra/lib/output-file/index.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require_fs(),n=require("path"),a=require_mkdirs(),o=require_path_exists().pathExists;e.exports={outputFile:s(async function(t,e,s="utf-8"){const r=n.dirname(t);return await o(r)||await a.mkdirs(r),i.writeFile(t,e,s)}),outputFileSync:function(t,...e){const s=n.dirname(t);i.existsSync(s)||a.mkdirsSync(s),i.writeFileSync(t,...e)}}}}),require_output_json=__commonJS({"node_modules/fs-extra/lib/json/output-json.js"(t,e){"use strict";var{stringify:s}=require_utils2(),{outputFile:i}=require_output_file();e.exports=async function(t,e,n={}){const a=s(e,n);await i(t,a,n)}}}),require_output_json_sync=__commonJS({"node_modules/fs-extra/lib/json/output-json-sync.js"(t,e){"use strict";var{stringify:s}=require_utils2(),{outputFileSync:i}=require_output_file();e.exports=function(t,e,n){const a=s(e,n);i(t,a,n)}}}),require_json=__commonJS({"node_modules/fs-extra/lib/json/index.js"(t,e){"use strict";var s=require_universalify().fromPromise,i=require_jsonfile2();i.outputJson=s(require_output_json()),i.outputJsonSync=require_output_json_sync(),i.outputJSON=i.outputJson,i.outputJSONSync=i.outputJsonSync,i.writeJSON=i.writeJson,i.writeJSONSync=i.writeJsonSync,i.readJSON=i.readJson,i.readJSONSync=i.readJsonSync,e.exports=i}}),require_move=__commonJS({"node_modules/fs-extra/lib/move/move.js"(t,e){"use strict";var s=require_fs(),i=require("path"),{copy:n}=require_copy2(),{remove:a}=require_remove(),{mkdirp:o}=require_mkdirs(),{pathExists:r}=require_path_exists(),l=require_stat();e.exports=async function(t,e,c={}){const _=c.overwrite||c.clobber||!1,{srcStat:d,isChangingCase:u=!1}=await l.checkPaths(t,e,"move",c);await l.checkParentPaths(t,d,e,"move");const h=i.dirname(e);return i.parse(h).root!==h&&await o(h),async function(t,e,i,o){if(!o)if(i)await a(e);else if(await r(e))throw new Error("dest already exists.");try{await s.rename(t,e)}catch(s){if("EXDEV"!==s.code)throw s;await async function(t,e,s){const i={overwrite:s,errorOnExist:!0,preserveTimestamps:!0};return await n(t,e,i),a(t)}(t,e,i)}}(t,e,_,u)}}}),require_move_sync=__commonJS({"node_modules/fs-extra/lib/move/move-sync.js"(t,e){"use strict";var s=require_graceful_fs(),i=require("path"),n=require_copy2().copySync,a=require_remove().removeSync,o=require_mkdirs().mkdirpSync,r=require_stat();function l(t,e,i){try{s.renameSync(t,e)}catch(s){if("EXDEV"!==s.code)throw s;return function(t,e,s){return n(t,e,{overwrite:s,errorOnExist:!0,preserveTimestamps:!0}),a(t)}(t,e,i)}}e.exports=function(t,e,n){const c=(n=n||{}).overwrite||n.clobber||!1,{srcStat:_,isChangingCase:d=!1}=r.checkPathsSync(t,e,"move",n);return r.checkParentPathsSync(t,_,e,"move"),function(t){const e=i.dirname(t);return i.parse(e).root===e}(e)||o(i.dirname(e)),function(t,e,i,n){if(n)return l(t,e,i);if(i)return a(e),l(t,e,i);if(s.existsSync(e))throw new Error("dest already exists.");return l(t,e,i)}(t,e,c,d)}}}),require_move2=__commonJS({"node_modules/fs-extra/lib/move/index.js"(t,e){"use strict";var s=require_universalify().fromPromise;e.exports={move:s(require_move()),moveSync:require_move_sync()}}}),require_lib=__commonJS({"node_modules/fs-extra/lib/index.js"(t,e){"use strict";e.exports={...require_fs(),...require_copy2(),...require_empty(),...require_ensure(),...require_json(),...require_mkdirs(),...require_move2(),...require_output_file(),...require_path_exists(),...require_remove()}}}),main_exports={};__export(main_exports,{default:()=>main_default}),module.exports=__toCommonJS(main_exports);var path7=__toESM(require("path")),fs10=__toESM(require_lib()),import_obsidian28=require("obsidian"),DEFAULT_SETTINGS={I18N_AGREEMENT:!1,I18N_WIZARD:!0,I18N_UUID:"",I18N_LANGUAGE:"zh-cn",I18N_COLOR:"#409EFF",I18N_AUTHOR:"",I18N_EDIT_MODE:!0,I18N_OPEN_SETTINGS:!0,I18N_CHECK_UPDATES:!0,I18N_SEARCH_TEXT:"",I18N_SORT:"0",I18N_TYPE:"0",I18N_MODE:0,I18N_NOTICE:!0,I18N_START_TIME:!0,I18N_MODE_LDT:!0,I18N_AUTOMATIC_UPDATE:!1,I18N_INCREMENTAL_EXTRACTION:!1,I18N_NAME_TRANSLATION:!1,I18N_NAME_TRANSLATION_PREFIX:"[",I18N_NAME_TRANSLATION_SUFFIX:"]",I18N_STYLE_SETTINGS:"obsidian-style-settings",I18N_MODE_NDT:!0,I18N_IGNORE:!1,I18N_NDT_URL:"gitee",I18N_MODE_NIT:!1,I18N_NIT_API:"BAIDU",I18N_NIT_API_INTERVAL:500,I18N_NIT_APIS:{BAIDU:{FROM:"auto",TO:"zh",APP_ID:"",KEY:""}},I18N_NIT_OPENAI_URL:"https://api.openai.com",I18N_NIT_OPENAI_KEY:"",I18N_NIT_OPENAI_MODEL:"gpt-3.5-turbo",I18N_NIT_OPENAI_TIPS:"你是一个翻译工作者，你将进行obsidian笔记软件的插件翻译，本次翻译的插件名称为: ${plugin}，请结合插件名称以及软件翻译的标准进行后续工作，因为大多数文本长度较短，请以符合中文习惯的方式翻译。接下来我会提交给你很多英文文本，请将其翻译为简体中文，并且只返回给我翻译后的内容",I18N_MODE_IMT:!1,I18N_IMT_CONFIG:{selectors:["*"],excludeSelectors:[".modal .i18n__container"],excludeTags:[],additionalSelectors:[],additionalExcludeSelectors:[],additionalExcludeTags:[],stayOriginalSelectors:[],stayOriginalTags:[],atomicBlockSelectors:[],atomicBlockTags:[]},I18N_SHARE_MODE:!0,I18N_SHARE_TOKEN:"",I18N_ADMIN_MODE:!1,I18N_ADMIN_VERIFY:!1,I18N_ADMIN_TOKEN:"",I18N_RE_TEMP_MODE:!0,I18N_RE_TEMP:"",I18N_RE_MODE:"默认",I18N_RE_FLAGS:"gs",I18N_RE_LENGTH:300,I18N_RE_MODE_EDIT:!1,I18N_RE_MODE_DISPLAY:!1,I18N_RE_DATAS_DISPLAY:!1,I18N_RE_MODES:["默认"],I18N_RE_DATAS:{"默认":["Notice\\(\\s*(.+?)\\s*\\)",".log\\(\\s*(.+?)\\s*\\)",".error\\(\\s*(.+?)\\s*\\)","t\\s*=\\s*:\\s*(['\"`])(.+?)\\1",".textContent\\s*=\\s*:\\s*(['\"`])(.+?)\\1","name\\s*:\\s*(['\"`])(.+?)\\1","description\\s*:\\s*(['\"`])(.+?)\\1","selection\\s*:\\s*(['\"`])(.+?)\\1","annotation\\s*:\\s*(['\"`])(.+?)\\1","link\\s*:\\s*(['\"`])(.+?)\\1","text\\s*:\\s*(['\"`])(.+?)\\1","search\\s*:\\s*(['\"`])(.+?)\\1","speech\\s*:\\s*(['\"`])(.+?)\\1","page\\s*:\\s*(['\"`])(.+?)\\1","settings\\s*:\\s*(['\"`])(.+?)\\1",".setText\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setButtonText\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setName\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setDesc\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setPlaceholder\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setTooltip\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".appendText\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".setTitle\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".addHeading\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".renderMarkdown\\(\\s*(['\"`])(.+?)\\1\\s*\\)",".innerText\\s*=\\s*(['\"`]).*?\\1"]},I18N_TAG_TYPE:"light",I18N_TAG_SHAPE:"square",I18N_BUTTON_TYPE:"default",I18N_BUTTON_SHAPE:"square"},import_obsidian15=require("obsidian"),fs=__toESM(require_lib()),import_path=require("path"),BaseSetting=class{constructor(t){this.settingTab=t,this.i18n=t.i18n,this.settings=t.i18n.settings,this.a=t.containerEl,this.containerEl=t.contentEl,this.app=t.app}display(){this.main()}},import_obsidian2=require("obsidian"),LANGUAGES={"zh-cn":"简体中文"},DOWNLOAD={gitee:"gitee(国内)",github:"github(国外)"},API_TYPES={BAIDU:"百度",OPENAI:"OpenAI"},BAIDU_ERROR_CODE={52000:"成功",52001:"请求超时",52002:"系统错误",52003:"未授权用户",54000:"必填参数为空",54001:"签名错误",54003:"访问频率受限",54004:"账户余额不足",54005:"长query请求频繁",58000:"客户端IP非法",58001:"译文语言方向不支持",58002:"服务当前已关闭",58003:"此IP已被封禁",90107:"认证未通过或未生效",20003:"请求内容存在安全风险 "},I18N_SORT={0:"正序",1:"倒序"},I18N_TYPE={0:"全部",1:"提取",2:"翻译",3:"还原"},TAG_TYPE={light:"样式一",dark:"样式二",plain:"样式三"},TAG_SHAPE={square:"方形",round:"圆形"},BUTTON_TYPE={plain:"样式一",default:"样式二"},BUTTON_SHAPE={square:"方形",round:"圆形"},import_obsidian=require("obsidian"),zh_cn_default={"通用_I18N_文本":"I18N","通用_标题_文本":"遇到难题？速加Q群咨询！","通用_成功_文本":"成功","通用_失败_文本":"失败","通用_搜索_标题":"搜索","通用_QQ_描述":"一键直达，加入Q群共享精彩！","通用_贡献榜_描述":"贡献者荣誉榜","通用_名称翻译_描述":"插件名称翻译列表","通用_设置_描述":"进入I18N设置界面","通用_帮助_描述":"查看I18N帮助文档","通用_插件_描述":"I18N插件管理菜单","通用_主题_描述":"I18N主题管理菜单","通用_状态文件_前缀":"状态文件","通用_插件更新_前缀":"插件更新","标签_无译文_文本":"无译文","标签_无译文_描述":"您还未提取译文","标签_已翻译_文本":"已翻译","标签_未翻译_文本":"未翻译","标签_已过时_文本":"已过时","标签_译文有误_文本":"译文有误","标签_译文有误_描述":"格式格式可能存在错误 请仔细检查后重试","标签_自带翻译_文本":"自带翻译","标签_自带翻译_描述":"根据反馈 此插件可能自带中文","标签_无_文本":"无","命令_打开翻译面板":"打开翻译面板","命令_打开帮助面板":"打开帮助面板","功能_正则_占位符":"临时正则表达式(使用|分割)","功能_删除_前缀":"删除译文","功能_删除_描述":"删除译文目录","功能_打开_前缀":"打开目录","功能_打开_描述":"打开插件目录","功能_编辑_描述":"编辑译文","功能_共享_描述":"共享译文","功能_提取_文本":"提取","功能_提取_前缀":"提取译文","功能_提取_通知一":"请还原插件后再进行提取","功能_下载_文本":"下载","功能_更新_文本":"更新","功能_下载更新_通知一":"未获取到文件","功能_替换_文本":"替换","功能_替换_前缀":"插件翻译","功能_替换_通知一":"译文未翻译，请先翻译译文","功能_替换_通知二":"如遇翻译后插件失效\n说明译文可能出现问题\n点击还原即可恢复原状","功能_还原_文本":"还原","功能_还原_前缀":"插件还原","功能_AI_文本":"AI","功能_检查更新_前缀":"检查更新","功能_检查更新_通知一":"发现新版本","编辑器_通用_名称":"译文编辑器","编辑器_通知_前缀":"译文编辑器","编辑器_通用_AI_按钮":"AI","编辑器_通用_还原_按钮":"还原","编辑器_通用_删除_按钮":"删除","编辑器_AI_通知一":"翻译成功","编辑器_还原_通知一":"还原成功","编辑器_删除_通知一":"删除成功","编辑器_新增_通知一":"新增成功","编辑器_新增_通知二":"新增失败\n译文中已存在此内容","编辑器_删除_按钮":"删除","编辑器_删除_按钮_提示":"一键清空，删除所有未翻译项","编辑器_新增_按钮":"新增","编辑器_新增_按钮_提示":"点击添加，创造新的条目","编辑器_源码_按钮":"源码","编辑器_源码_按钮_提示":"深入代码，打开main.js文件","编辑器_译文_按钮":"译文","编辑器_译文_按钮_提示":"翻译世界，打开译文文件","编辑器_保存_按钮":"保存","编辑器_保存_按钮_提示":"锁定成果，保存当前的更改","编辑器_大小_按钮_提示":"切换大小模式","编辑器_上个_按钮_提示":"切换至上一个数据项","编辑器_下个_按钮_提示":"切换至下一个数据项","编辑器_数量_按钮_提示":"显示匹配的数据总数","编辑器_行_插件版本":"插件版本","编辑器_行_主题版本":"主题版本","编辑器_行_修改日期":"修改日期","编辑器_行_文件大小":"文件大小","编辑器_行_源代码":"源代码","编辑器_行_新描述":"新描述","向导_通用_版本":"版本","向导_通用_浏览":"浏览","向导_通用_加入":"加入","向导_视频_标题":"官方视频教程","向导_视频_描述":"详尽演示Obsidian i18n操作，助力快速掌握","向导_文档_标题":"官方文档教程","向导_文档_描述":"Obsidian i18n的全面探索之旅指南","向导_QQ_标题":"官方Q群","向导_QQ_描述":"在官方群，您可发布需求、提交BUG、分享译文，并与其他用户就插件使用、翻译等话题交流互助。","设置_通用_开启_文本":"开启","设置_通用_关闭_文本":"关闭","设置_通用_添加_文本":"添加","设置_通用_显示_文本":"显示","设置_通用_隐藏_文本":"隐藏","设置_通用_测试_文本":"测试","设置_主题_标题_缩写":"主题","设置_主题_标签_标题":"标签样式","设置_主题_标签_描述":"更改标签的外观，包括颜色、形状。","设置_主题_按钮_标题":"按钮样式","设置_主题_按钮_描述":"更改按钮的外观，包括颜色、形状。","设置_基础_标题_缩写":"基础","设置_基础_鸣谢列表_标题":"鸣谢列表","设置_基础_鸣谢列表_描述":"感谢所有对本项目提供支持和帮助的个人和组织。","设置_基础_鸣谢列表_按钮":"鸣谢","设置_基础_翻译语言_标题":"翻译语言","设置_基础_翻译语言_描述":"设置您希望翻译的目标语言，以便插件能够自动翻译成所需语言。(重启后生效)","设置_基础_检查更新_标题":"检查更新","设置_基础_检查更新_描述":"启用此功能，插件将自动检测可用更新，并在新版本发布时提醒您，确保插件功能始终处于最新状态。","设置_基础_检查更新_按钮":"更新","设置_基础_跳转设置_标题":"跳转设置","设置_基础_跳转设置_描述":"启用此功能，插件页面将提供快速跳转按钮，方便您直接访问相关设置。","设置_基础_译文编辑_标题":"译文编辑","设置_基础_译文编辑_描述":"启用此功能，插件将允许您直接编辑和调整译文，以优化翻译内容。","设置_基础_通知提示_标题":"通知提示","设置_基础_通知提示_描述":"启用此功能，系统将在您成功执行特定操作时显示通知，以便您及时获得反馈。","设置_基础_启动耗时_标题":"启动耗时","设置_基础_启动耗时_描述":"启用此功能，插件将在启动时显示具体耗时（秒）。","设置_本地_标题_缩写":"本地模式","设置_本地_标题":"本地译文模式","设置_本地_描述":"启用此模式，插件将优先使用本地存储的译文，以提供更个性化的翻译体验。","设置_本地_智能更新_标题":"智能更新","设置_本地_智能更新_描述":"启用此功能，当检测到插件版本变动时，自动应用本地已有译文。请注意，由于插件更新，部分译文可能需要适配，如遇错误请及时修正。","设置_本地_智能更新_通知一":"开始检查更新","设置_本地_智能更新_通知二":"没有需要更新的插件","设置_本地_智能更新_通知三":"更新","设置_本地_智能更新_通知四":"个插件","设置_本地_扩展提取_标题":"扩展提取","设置_本地_扩展提取_描述":"启用此功能，插件将在本地已有译文的基础上继续提取新译文，并自动合并，以实现译文的持续更新与累积。","设置_本地_名称翻译_标题":"名称翻译","设置_本地_名称翻译_描述":"启用此功能，将在插件后添加[]以展示其翻译名称(插件设置界面刷新操作会导致当前翻译失效，需重启ob重新加载翻译)","设置_云端_标题_缩写":"云端模式","设置_云端_标题":"云端译文模式","设置_云端_描述":"启用此模式，插件将从云端获取最新的译文，确保翻译的时效性和准确性。","设置_云端_通知一":"网络异常","设置_云端_通知二":"主题目录获取成功","设置_云端_通知三":"插件目录获取成功","设置_云端_标记汉化_标题":"标记汉化","设置_云端_标记汉化_描述":"启用此功能，自动识别并标记已内置中文的插件，该功能依赖于社区反馈和网络查询。","设置_云端_云端接口_标题":"云端接口","设置_云端_云端接口_描述":"设置云端接口地址，用于插件从指定的云端服务获取译文数据。","设置_云端_服务配置_标题":"服务配置","设置_AI_标题_缩写":"机器翻译","设置_AI_标题":"人工智能模式","设置_AI_描述":"启用此模式，利用人工智能技术进行翻译，提高翻译的准确性和流畅性。","设置_AI_接口服务_标题":"接口服务","设置_AI_接口服务_描述":"选择用于AI翻译的接口服务，以满足不同的翻译需求和偏好。","设置_AI_请求间隔_标题":"请求间隔","设置_AI_请求间隔_描述":"设置AI翻译请求之间的时间间隔，以避免过快的请求频率导致服务限制。","设置_AI_百度_原始语言_标题":"原始语言","设置_AI_百度_原始语言_描述":"选择百度翻译服务的原始语言。","设置_AI_百度_目标语言_标题":"目标语言","设置_AI_百度_目标语言_描述":"选择百度翻译服务的目标语言，即您希望将源语言翻译成的语言。","设置_AI_百度_标识_标题":"标识","设置_AI_百度_标识_描述":"输入您的百度翻译服务APPID，用于身份验证和访问控制。","设置_AI_百度_密钥_标题":"密钥","设置_AI_百度_密钥_描述":"输入与您的百度APPID关联的密钥，以确保翻译请求的安全。","设置_AI_OPENAI_接口_标题":"接口","设置_AI_OPENAI_接口_描述":"选择OPENAI提供的API接口，以利用其先进的AI模型进行翻译。","设置_AI_OPENAI_KEY_标题":"密钥","设置_AI_OPENAI_KEY_描述":"输入您的OPENAI服务密钥，用于访问OPENAI的AI模型和接口。","设置_AI_OPENAI_模型_标题":"模型","设置_AI_OPENAI_模型_描述":"选择OPENAI提供的AI模型，不同的模型可能适用于不同的翻译任务和场景。","设置_AI_OPENAI_提示_标题":"提示","设置_AI_OPENAI_提示_描述":"配置OPENAI模型的提示信息，以优化翻译结果或指导模型生成特定风格的文本。","设置_AI_OPENAI_提示_占位符":"提示词","设置_沉浸_标题_缩写":"沉浸翻译","设置_沉浸_标题":"沉浸翻译模式","设置_沉浸_描述":"启用此模式，为您提供一个专注于翻译的环境，减少干扰，提高翻译效率和质量。","设置_共享_标题_缩写":"共建云端","设置_共享_标题":"共建云端","设置_共享_描述":"启用此模式，共享和协作翻译资源，提升整个社区的翻译质量和效率。","设置_共享_贡献者_标题":"贡献者模式","设置_共享_贡献者_描述":"启用此功能，作为贡献者参与云端翻译资源的共建，提交和分享您的翻译成果。","设置_共享_贡献者_提示":"Token","设置_共享_管理员_标题":"管理员模式","设置_共享_管理员_描述":"启用此功能，管理云端翻译资源，审核发布贡献者的翻译，维护翻译质量。","设置_共享_管理员_提示":"Token","设置_正则_标题_缩写":"正则配置","设置_正则_通知_前缀":"正则配置","设置_正则_临时正则_标题":"临时正则","设置_正则_临时正则_描述":"启用此功能，设置临时使用的正则表达式，用于快速匹配。","设置_正则_正则模式_标题":"正则模式","设置_正则_正则模式_描述":"正则表达式的模式，这将决定匹配文本的基本规则。","设置_正则_正则标志_标题":"正则标志","设置_正则_正则标志_描述":"正则表达式的修饰符，如全局匹配、忽略大小写等，以控制匹配行为。","设置_正则_正则标志_占位符":"修饰符","设置_正则_匹配长度_标题":"匹配长度","设置_正则_匹配长度_描述":"指定匹配时考虑的文本长度，以优化匹配效率和结果。","设置_正则_模式编辑_标题":"模式编辑","设置_正则_模式编辑_描述":"编辑和调整正则表达式的模式，以适应复杂的匹配场景。","设置_正则_模式编辑_占位符":"请输入模式","设置_正则_模式编辑_通知一":"添加失败，请确保输入了有效的模式字符串，并且它尚未被添加。","设置_正则_数据编辑_标题":"数据编辑","设置_正则_数据编辑_描述":"编辑模式中的正则数据，对匹配到的文本进行精细化调整和处理。","设置_正则_数据编辑_占位符":"请输入正则表达式","设置_正则_数据编辑_通知一":"不能删除最后一个正则表达式模式。"},localeMap={"zh-cn":zh_cn_default},locale=localeMap[import_obsidian.moment.locale()];function t(t){return locale&&locale[t]||zh_cn_default[t]}var I18nBasis=class extends BaseSetting{main(){new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_鸣谢列表_标题")).setDesc(t("设置_基础_鸣谢列表_描述")).addButton(e=>e.setButtonText(t("设置_基础_鸣谢列表_按钮")).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).onClick(async()=>{this.i18n.notice.warning("Git","\n曲淡歌",1e4),this.i18n.notice.success("审核人员","\n曲淡歌\nFENDI\n宇桐非\n孤猫",1e4),this.i18n.notice.error("贡献人员","详情查看贡献榜单",1e4)})),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_翻译语言_标题")).setDesc(t("设置_基础_翻译语言_描述")).addDropdown(t=>t.addOptions(LANGUAGES).setValue(this.settings.I18N_LANGUAGE).onChange(async t=>{this.settings.I18N_LANGUAGE=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select")),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_检查更新_标题")).setDesc(t("设置_基础_检查更新_描述")).addButton(e=>e.setButtonText(t("设置_基础_检查更新_按钮")).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).onClick(async()=>{const t=await this.i18n.api.giteeGetReleasesLatest();if(t.data.tag_name===this.i18n.updatesVersion){const e=t.data.assets.find(t=>"manifest.json"===t.name),s=t.data.assets.find(t=>"styles.css"===t.name),i=t.data.assets.find(t=>"main.js"===t.name);let n="",a="",o="";if(void 0===e)return void this.i18n.notice.result("检查更新",!1,"未找到manifest.json文件");{const t=await this.i18n.api.giteeDownload(e.browser_download_url);if(!t.state)return void this.i18n.notice.result("检查更新",!1,`请求manifest.json失败\n${t.data}`);o=t.data}if(void 0===s)return void this.i18n.notice.result("检查更新",!1,"未找到styles.css文件");{const t=await this.i18n.api.giteeDownload(s.browser_download_url);if(!t.state)return void this.i18n.notice.result("检查更新",!1,`请求styles.css失败\n${t.data}`);n=t.data}if(void 0===i)return void this.i18n.notice.result("检查更新",!1,"未找到main.js文件");{const t=await this.i18n.api.giteeDownload(i.browser_download_url);if(!t.state)return void this.i18n.notice.result("检查更新",!1,`请求main.js失败\n${t.data}`);a=t.data}try{const t=(0,import_path.join)((0,import_path.normalize)(this.app.vault.adapter.getBasePath()),this.i18n.manifest.dir);console.log(t),fs.ensureDirSync(t),await fs.writeFile((0,import_path.join)(t,"styles.css"),n),await fs.writeFile((0,import_path.join)(t,"main.js"),a),await fs.writeFile((0,import_path.join)(t,"manifest.json"),o),this.i18n.notice.result("检查更新",!0,"更新成功"),document.location.reload()}catch(t){this.i18n.notice.result("检查更新",!1,`写入文件失败${t}`)}}else this.i18n.notice.result("检查更新",!1,"未找到文件")}).setClass(this.i18n.updatesMark?"1":"i18n--hidden")).addToggle(t=>t.setValue(this.settings.I18N_CHECK_UPDATES).onChange(async()=>{this.settings.I18N_CHECK_UPDATES=!this.settings.I18N_CHECK_UPDATES,this.i18n.saveSettings(),this.settings.I18N_CHECK_UPDATES?await this.i18n.checkUpdates():(this.i18n.updatesMark=!1,this.i18n.updatesVersion=""),this.settingTab.basisDisplay()}).toggleEl.addClass("i18n-checkbox")),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_跳转设置_标题")).setDesc(t("设置_基础_跳转设置_描述")).addToggle(t=>t.setValue(this.settings.I18N_OPEN_SETTINGS).onChange(()=>{this.settings.I18N_OPEN_SETTINGS=!this.settings.I18N_OPEN_SETTINGS,this.i18n.saveSettings(),this.settingTab.basisDisplay()}).toggleEl.addClass("i18n-checkbox")),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_译文编辑_标题")).setDesc(t("设置_基础_译文编辑_描述")).addToggle(t=>t.setValue(this.settings.I18N_EDIT_MODE).onChange(()=>{this.settings.I18N_EDIT_MODE=!this.settings.I18N_EDIT_MODE,this.i18n.saveSettings(),this.settingTab.basisDisplay()}).toggleEl.addClass("i18n-checkbox")),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_通知提示_标题")).setDesc(t("设置_基础_通知提示_描述")).addToggle(t=>t.setValue(this.settings.I18N_NOTICE).onChange(()=>{this.settings.I18N_NOTICE=!this.settings.I18N_NOTICE,this.i18n.saveSettings()}).toggleEl.addClass("i18n-checkbox")),new import_obsidian2.Setting(this.containerEl).setName(t("设置_基础_启动耗时_标题")).setDesc(t("设置_基础_启动耗时_描述")).addToggle(t=>t.setValue(this.settings.I18N_START_TIME).onChange(()=>{this.settings.I18N_START_TIME=!this.settings.I18N_START_TIME,this.i18n.saveSettings()}).toggleEl.addClass("i18n-checkbox"))}},import_obsidian3=require("obsidian"),I18nModLDT=class extends BaseSetting{main(){const e=new import_obsidian3.Setting(this.containerEl);e.setName(`${t("设置_本地_标题")} ${this.settings.I18N_MODE_LDT?"🟢":"🔴"}`),e.setDesc(t("设置_本地_描述")),e.addButton(e=>{e.setButtonText(this.settings.I18N_MODE_LDT?t("设置_通用_关闭_文本"):t("设置_通用_开启_文本")),e.onClick(()=>{this.settings.I18N_MODE_LDT=!this.settings.I18N_MODE_LDT,this.i18n.saveSettings(),this.settingTab.ldtDisplay()}),e.setClass("i18n-button"),e.setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`),this.settings.I18N_MODE_LDT?e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`):e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`)});const s=new import_obsidian3.Setting(this.containerEl);s.setName(t("设置_本地_智能更新_标题")),s.setDesc(t("设置_本地_智能更新_描述")),s.addToggle(t=>t.setValue(this.settings.I18N_AUTOMATIC_UPDATE).onChange(()=>{this.settings.I18N_AUTOMATIC_UPDATE=!this.settings.I18N_AUTOMATIC_UPDATE,this.i18n.saveSettings(),this.settingTab.ldtDisplay()}).toggleEl.addClass("i18n-checkbox"));const i=new import_obsidian3.Setting(this.containerEl);i.setName(t("设置_本地_扩展提取_标题")),i.setDesc(t("设置_本地_扩展提取_描述")),i.addToggle(t=>t.setValue(this.settings.I18N_INCREMENTAL_EXTRACTION).onChange(()=>{this.settings.I18N_INCREMENTAL_EXTRACTION=!this.settings.I18N_INCREMENTAL_EXTRACTION,this.i18n.saveSettings(),this.settingTab.ldtDisplay()}).toggleEl.addClass("i18n-checkbox"));const n=new import_obsidian3.Setting(this.containerEl);n.setName(t("设置_本地_名称翻译_标题")),n.setDesc(t("设置_本地_名称翻译_描述")),n.addText(t=>t.setValue(this.settings.I18N_NAME_TRANSLATION_PREFIX).onChange(t=>{this.settings.I18N_NAME_TRANSLATION_PREFIX=t,this.i18n.reloadPluginsName(),this.i18n.saveSettings()}).inputEl.addClass("i18n-name__input","i18n-input")),n.addText(t=>t.setValue(this.settings.I18N_NAME_TRANSLATION_SUFFIX).onChange(t=>{this.settings.I18N_NAME_TRANSLATION_SUFFIX=t,this.i18n.reloadPluginsName(),this.i18n.saveSettings()}).inputEl.addClass("i18n-name__input","i18n-input")),n.addToggle(t=>t.setValue(this.settings.I18N_NAME_TRANSLATION).onChange(()=>{this.settings.I18N_NAME_TRANSLATION=!this.settings.I18N_NAME_TRANSLATION,this.settings.I18N_NAME_TRANSLATION?this.i18n.trenslatorPluginsName():this.i18n.restorePluginsName(),this.i18n.saveSettings(),this.settingTab.ldtDisplay()}).toggleEl.addClass("i18n-checkbox"))}},import_obsidian4=require("obsidian"),I18nModNDT=class extends BaseSetting{main(){const e=new import_obsidian4.Setting(this.containerEl);e.setName(`${t("设置_云端_标题")} ${this.settings.I18N_MODE_NDT?"🟢":"🔴"}`),e.setDesc(t("设置_云端_描述")),e.addButton(e=>{e.setButtonText(this.settings.I18N_MODE_NDT?t("设置_通用_关闭_文本"):t("设置_通用_开启_文本")),e.onClick(async()=>{this.settings.I18N_MODE_NDT=!this.settings.I18N_MODE_NDT,this.settingTab.ndtDisplay(),await this.i18n.saveSettings(),await this.i18n.ignoreCache(),await this.i18n.themeDirectoryCache(),await this.i18n.pliginDirectoryCache()}),e.setClass("i18n-button"),e.setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`),this.settings.I18N_MODE_NDT?e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`):e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`)});const s=new import_obsidian4.Setting(this.containerEl);s.setName(t("设置_云端_标记汉化_标题")),s.setDesc(t("设置_云端_标记汉化_描述")),s.addToggle(t=>t.setValue(this.settings.I18N_IGNORE).onChange(async()=>{this.settings.I18N_IGNORE=!this.settings.I18N_IGNORE,this.i18n.saveSettings(),await this.i18n.ignoreCache(),this.settingTab.ndtDisplay()}).toggleEl.addClass("i18n-checkbox"));const i=new import_obsidian4.Setting(this.containerEl);i.setName(t("设置_云端_云端接口_标题")),i.setDesc(t("设置_云端_云端接口_描述")),i.addDropdown(t=>t.addOptions(DOWNLOAD).setValue(this.settings.I18N_NDT_URL).onChange(async t=>{this.settings.I18N_NDT_URL=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select"))}},import_obsidian6=require("obsidian"),import_crypto=require("crypto"),import_obsidian5=require("obsidian"),API=class{constructor(t){this.geteeOwner="zero--two",this.geteeRepo="obsidian-i18n-translation",this.i18n=t,this.settings=this.i18n.settings}async version(){const t={url:"https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/version.json",method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return{state:!0,data:t}}}async getMark(){const t={url:"https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/translation/mark/zh-cn.json",method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return{state:!1,data:[]}}}async baiduAPI(t){const e=this.i18n.settings.I18N_NIT_APIS.BAIDU,s=(0,import_crypto.createHash)("md5"),i=e.FROM,n=e.TO,a=e.APP_ID,o=e.KEY,r=Math.round(10*Math.random()),l={url:`https://fanyi-api.baidu.com/api/trans/vip/translate?q=${t}&from=${i}&to=${n}&appid=${a}&salt=${r}&sign=${s.update(`${a}${t}${r}${o}`).digest("hex")}`,method:"GET"};try{const t=await(0,import_obsidian5.requestUrl)(l);if(t.json.hasOwnProperty("error_code")){const e=t.json.error_code;return this.i18n.notice.error("百度",`${e}\n${BAIDU_ERROR_CODE[e]}`),{state:!1,data:""}}return{state:!0,data:t.json.trans_result[0].dst}}catch(t){return{state:!1,data:""}}}async openAI(t,e){try{const t={url:`${this.settings.I18N_NIT_OPENAI_URL}/v1/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.settings.I18N_NIT_OPENAI_KEY}`},body:JSON.stringify({model:this.settings.I18N_NIT_OPENAI_MODEL,messages:[{role:"user",content:this.settings.I18N_NIT_OPENAI_TIPS},{role:"user",content:e}],temperature:.7})},s=await(0,import_obsidian5.requestUrl)(t);return s.json&&s.json.choices&&s.json.choices.length>0?s.json.choices[0].message:null}catch(t){return this.i18n.notice.error("错误",t),null}}openAITest(){const t={url:`${this.settings.I18N_NIT_OPENAI_URL}/v1/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.settings.I18N_NIT_OPENAI_KEY}`},body:JSON.stringify({model:this.settings.I18N_NIT_OPENAI_MODEL,messages:[{role:"user",content:"i18n"}],temperature:.7})};(0,import_obsidian5.requestUrl)(t).then(()=>{this.i18n.notice.result("OpenAI",!0)}).catch(t=>{this.i18n.notice.result("OpenAI",!1,t)})}async githubGetTranslation(t,e,s){try{const i={url:`https://raw.githubusercontent.com/0011000000110010/obsidian-i18n/refs/heads/master/${t}/dict/${e}/zh-cn/${s}.json`,method:"GET"};return console.log(i),{state:!0,data:(await(0,import_obsidian5.requestUrl)(i)).json}}catch(t){return{state:!1,data:""}}}async giteeGetTranslation(t,e,s){try{const i={url:`https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/${t}/dict/${e}/zh-cn/${s}.json`,method:"GET"};return{state:!0,data:(await(0,import_obsidian5.requestUrl)(i)).json}}catch(t){return{state:!1,data:""}}}async giteeGetToken(){const t={url:"https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/version.json",method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json.token}}catch(t){return this.i18n.notice.error("I18N",`token获取失败(如果没有自定义token则无法使用提交功能)\n${t}`),{state:!1,data:t}}}async giteeGetDirectory(t){const e={url:`https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/${t}/directory/${this.i18n.settings.I18N_LANGUAGE}.json`,method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).json}}catch(t){return{state:!1,data:t}}}async giteeGetDirectoryAdmin(t,e){const s={url:`https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/${t}/directory/${e}.json`,method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(s)).json}}catch(t){return{state:!1,data:t}}}async giteeGetSha(t){try{const e={url:`https://gitee.com/api/v5/repos/zero--two/obsidian-i18n-translation/contents/${t}`,method:"GET",body:JSON.stringify({access_token:this.i18n.settings.I18N_ADMIN_TOKEN,owner:"zero--two",repo:"obsidian-i18n-translation",path:t})};return console.log(e),{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).json}}catch(t){return{state:!1,data:t}}}async giteeGetContents(t){try{const e={url:`https://gitee.com/api/v5/repos/zero--two/obsidian-i18n-translation/contents/${t}`,method:"GET",body:JSON.stringify({access_token:this.i18n.settings.I18N_ADMIN_TOKEN,owner:"zero--two",repo:"obsidian-i18n-translation",path:t})};return{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).json}}catch(t){return{state:!1,data:t}}}async giteeGetContributor(){try{const t={url:"https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/translation/contributor/zh-cn.json",method:"GET"};return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return{state:!1,data:t}}}async giteeGetReleasesLatest(){try{const t={url:"https://gitee.com/api/v5/repos/zero--two/obsidian-i18n-translation/releases/latest",method:"GET"};return console.log(t),{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return console.log(t),{state:!1,data:""}}}async giteeGetAllIssue(){const t={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/issues`,method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return{state:!1,data:t}}}async giteeGetIssue(t){const e={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/issues/${t}`,method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).json}}catch(t){return{state:!1,data:t}}}async giteePostIssue(t,e,s){try{let i;if(""!==this.settings.I18N_SHARE_TOKEN)i=this.settings.I18N_SHARE_TOKEN;else{const t=await this.giteeGetToken();console.log(t),i=t.state?atob(t.data):""}if(""===i)return;const n={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/issues`,method:"POST",headers:{"Content-Type":"application/json",Charset:"UTF-8"},body:JSON.stringify({access_token:i,repo:this.geteeRepo,title:t,body:e,labels:s})};return{state:!0,data:(await(0,import_obsidian5.requestUrl)(n)).json}}catch(t){return this.i18n.notice.result("提交操作",!1,`${t}`),{state:!1,data:t}}}async giteePatchIssue(t,e){const s={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/issues/${t}`,method:"PATCH",headers:{"Content-Type":"application/json",Charset:"UTF-8"},body:JSON.stringify({access_token:this.i18n.settings.I18N_ADMIN_TOKEN,owner:this.geteeOwner,repo:this.geteeRepo,number:t,state:e})};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(s)).json}}catch(t){return{state:!1,data:t}}}async giteePostIssueComments(t,e){try{let s;if(""!==this.settings.I18N_SHARE_TOKEN)s=this.settings.I18N_SHARE_TOKEN;else{const t=await this.giteeGetToken();console.log(t),s=t.state?atob(t.data):""}if(""===s)return;const i={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/issues/${t}/comments`,method:"POST",headers:{"Content-Type":"application/json",Charset:"UTF-8"},body:JSON.stringify({access_token:s,owner:this.geteeOwner,repo:this.geteeRepo,number:t,body:e})};return{state:!0,data:(await(0,import_obsidian5.requestUrl)(i)).json}}catch(t){return{state:!1,data:t}}}async giteeDownload(t){try{const e={url:t,method:"GET"};return console.log(e),{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).text}}catch(t){return console.log(t),{state:!1,data:""}}}async giteeGetFile(t){const e={url:`https://gitee.com/${this.geteeOwner}/${this.geteeRepo}/raw/master/${t}`,method:"GET"};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(e)).json}}catch(t){return{state:!1,data:t}}}async giteeCreateFileContent(t,e,s){const i={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/contents/${t}`,method:"POST",headers:{"Content-Type":"application/json",charset:"UTF-8"},body:JSON.stringify({access_token:this.settings.I18N_ADMIN_TOKEN,owner:this.geteeOwner,repo:this.geteeRepo,path:t,content:e,message:s})};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(i)).json}}catch(t){return{state:!1,data:t}}}async giteeUpdateFileContent(t,e,s,i){const n={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/contents/${t}`,method:"PUT",headers:{"Content-Type":"application/json",Charset:"UTF-8"},body:JSON.stringify({access_token:this.i18n.settings.I18N_ADMIN_TOKEN,owner:this.geteeOwner,repo:this.geteeRepo,content:e,sha:s,message:i})};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(n)).json}}catch(t){return{state:!1,data:t}}}async giteeUser(){const t={url:"https://gitee.com/api/v5/user",method:"GET",headers:{Authorization:`token ${this.i18n.settings.I18N_ADMIN_TOKEN}`}};try{return{state:!0,data:(await(0,import_obsidian5.requestUrl)(t)).json}}catch(t){return{state:!1,data:t}}}async checkUser(t){const e={url:`https://gitee.com/api/v5/repos/${this.geteeOwner}/${this.geteeRepo}/collaborators/${t}`,method:"GET",headers:{Authorization:`token ${this.i18n.settings.I18N_ADMIN_TOKEN}`},body:JSON.stringify({owner:this.geteeOwner,repo:this.geteeRepo,username:t})};try{return 204===(await(0,import_obsidian5.requestUrl)(e)).status?{state:!0,data:!0}:{state:!1,data:!1}}catch(t){return{state:!1,data:t}}}},TranslationAPI=class{constructor(t){this.geteeOwner="zero--two",this.geteeRepo="obsidian-i18n-translation",this.i18n=t,this.settings=this.i18n.settings}async baiduAPI(t){const e=this.i18n.settings.I18N_NIT_APIS.BAIDU,s=(0,import_crypto.createHash)("md5"),i=e.FROM,n=e.TO,a=e.APP_ID,o=e.KEY,r=Math.round(10*Math.random()),l={url:`https://fanyi-api.baidu.com/api/trans/vip/translate?q=${t}&from=${i}&to=${n}&appid=${a}&salt=${r}&sign=${s.update(`${a}${t}${r}${o}`).digest("hex")}`,method:"GET"};try{const t=await(0,import_obsidian5.requestUrl)(l);if(t.json.hasOwnProperty("error_code")){const e=t.json.error_code;return{state:!1,data:BAIDU_ERROR_CODE[e]}}return{state:!0,data:t.json.trans_result[0].dst}}catch(t){return{state:!1,data:""}}}async openAI(t){try{const e={url:`${this.settings.I18N_NIT_OPENAI_URL}/v1/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.settings.I18N_NIT_OPENAI_KEY}`},body:JSON.stringify({model:this.settings.I18N_NIT_OPENAI_MODEL,messages:[{role:"user",content:this.settings.I18N_NIT_OPENAI_TIPS},{role:"user",content:t}],temperature:.7})},s=await(0,import_obsidian5.requestUrl)(e);if(s.json&&s.json.choices&&s.json.choices.length>0)return{state:!0,data:s.json.choices[0].message}}catch(t){return this.i18n.notice.error("错误",t),{state:!0,data:t}}}openAITest(){const t={url:`${this.settings.I18N_NIT_OPENAI_URL}/v1/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.settings.I18N_NIT_OPENAI_KEY}`},body:JSON.stringify({model:this.settings.I18N_NIT_OPENAI_MODEL,messages:[{role:"user",content:"i18n"}],temperature:.7})};(0,import_obsidian5.requestUrl)(t).then(()=>{this.i18n.notice.result("OpenAI",!0)}).catch(t=>{this.i18n.notice.result("OpenAI",!1,t)})}},I18nModeNIT=class extends BaseSetting{main(){const e=new API(this.i18n),s=new import_obsidian6.Setting(this.containerEl);s.setName(`${t("设置_AI_标题")} ${this.settings.I18N_MODE_NIT?"🟢":"🔴"}`),s.setDesc(t("设置_AI_描述")),s.addButton(e=>{e.setButtonText(this.settings.I18N_MODE_NIT?t("设置_通用_关闭_文本"):t("设置_通用_开启_文本")),e.onClick(async()=>{this.settings.I18N_MODE_NIT=!this.settings.I18N_MODE_NIT,this.i18n.saveSettings(),this.settingTab.nitDisplay()}),e.setClass("i18n-button"),e.setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`),this.settings.I18N_MODE_NIT?e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`):e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`)});const i=new import_obsidian6.Setting(this.containerEl);i.setName(t("设置_AI_接口服务_标题")),i.setDesc(t("设置_AI_接口服务_描述")),i.addDropdown(t=>t.addOptions(API_TYPES).setValue(this.settings.I18N_NIT_API).onChange(t=>{this.settings.I18N_NIT_API=t,this.i18n.saveSettings(),this.settingTab.nitDisplay()}).selectEl.addClass("i18n-select")),i.addButton(s=>s.setButtonText(t("设置_通用_测试_文本")).onClick(async()=>{switch(this.settings.I18N_NIT_API){case"BAIDU":(await e.baiduAPI("i18n")).state&&this.i18n.notice.result("百度",!0);break;case"OPENAI":e.openAITest()}}).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`));const n=new import_obsidian6.Setting(this.containerEl);n.setName(t("设置_AI_请求间隔_标题")),n.setDesc(t("设置_AI_请求间隔_描述")),n.addSlider(t=>t.setDynamicTooltip().setLimits(0,1e3,50).setValue(this.settings.I18N_NIT_API_INTERVAL).onChange(t=>{this.settings.I18N_NIT_API_INTERVAL=t,this.i18n.saveSettings()})),new import_obsidian6.Setting(this.containerEl).setName(t("设置_云端_服务配置_标题")).setHeading()}},import_obsidian7=require("obsidian"),I18nModIMT=class extends BaseSetting{main(){const e=new import_obsidian7.Setting(this.containerEl);e.setName(`${t("设置_沉浸_标题")} ${this.settings.I18N_MODE_IMT?"🟢":"🔴"}`),e.setDesc(t("设置_沉浸_描述")),e.addButton(t=>t.setButtonText("重启").onClick(()=>{document.location.reload()}).buttonEl.addClasses(["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-warning`,`is-${this.settings.I18N_BUTTON_SHAPE}`])),e.addButton(e=>{e.setButtonText(this.settings.I18N_MODE_IMT?t("设置_通用_关闭_文本"):t("设置_通用_开启_文本")),e.onClick(()=>{this.settings.I18N_MODE_IMT=!this.settings.I18N_MODE_IMT,this.settings.I18N_MODE_IMT||document.location.reload(),this.i18n.saveSettings(),this.settingTab.imtDisplay(),this.settings.I18N_MODE_IMT?this.i18n.activateIMT():this.i18n.enableIMT()}),e.setClass("i18n-button"),e.setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`),this.settings.I18N_MODE_IMT?e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`):e.setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`)}),new import_obsidian7.Setting(this.containerEl).setName("指定翻译范围").setHeading();const s=new import_obsidian7.Setting(this.containerEl);s.setName("匹配元素"),s.setDesc("修改后请重启沉浸式翻译功能"),s.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.selectors?this.settings.I18N_IMT_CONFIG.selectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.selectors=t.split(","),await this.i18n.saveSettings(),window.immersiveTranslateConfig={pageRule:this.settings.I18N_IMT_CONFIG},console.log(window.immersiveTranslateConfig)}));const i=new import_obsidian7.Setting(this.containerEl);i.setName("排除元素"),i.setDesc("仅翻译匹配到的元素"),i.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.excludeSelectors?this.settings.I18N_IMT_CONFIG.excludeSelectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.excludeSelectors=t.split(","),await this.i18n.saveSettings(),window.immersiveTranslateConfig={pageRule:this.settings.I18N_IMT_CONFIG},console.log(window.immersiveTranslateConfig)}));const n=new import_obsidian7.Setting(this.containerEl);n.setName("排除Tags"),n.setDesc("排除Tags，不翻译匹配的Tag"),n.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.excludeTags?this.settings.I18N_IMT_CONFIG.excludeTags.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.excludeTags=t.split(","),await this.i18n.saveSettings()})),new import_obsidian7.Setting(this.containerEl).setName("追加翻译范围").setHeading();const a=new import_obsidian7.Setting(this.containerEl);a.setName("追加翻译范围"),a.setDesc("追加翻译范围。在智能翻译的区域，追加翻译位置。"),a.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.additionalSelectors?this.settings.I18N_IMT_CONFIG.additionalSelectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.additionalSelectors=t.split(","),await this.i18n.saveSettings()}));const o=new import_obsidian7.Setting(this.containerEl);o.setName("追加排除元素"),o.setDesc("追加排除元素，让智能翻译不翻译特定位置。"),o.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.additionalExcludeSelectors?this.settings.I18N_IMT_CONFIG.additionalExcludeSelectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.additionalExcludeSelectors=t.split(","),await this.i18n.saveSettings()}));const r=new import_obsidian7.Setting(this.containerEl);r.setName("追加排除Tags"),r.setDesc("追加排除Tags"),r.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.additionalExcludeTags?this.settings.I18N_IMT_CONFIG.additionalExcludeTags.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.additionalExcludeTags=t.split(","),await this.i18n.saveSettings()})),new import_obsidian7.Setting(this.containerEl).setName("保持原样").setHeading();const l=new import_obsidian7.Setting(this.containerEl);l.setName("匹配的元素将保持原样"),l.setDesc("匹配的元素将保持原样。常用于论坛网站的标签。"),l.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.stayOriginalSelectors?this.settings.I18N_IMT_CONFIG.stayOriginalSelectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.stayOriginalSelectors=t.split(","),await this.i18n.saveSettings()}));const c=new import_obsidian7.Setting(this.containerEl);c.setName("匹配到的Tag将保持原样"),c.setDesc("匹配到的Tag将保持原样，比如 `code`"),c.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.stayOriginalTags?this.settings.I18N_IMT_CONFIG.stayOriginalTags.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.stayOriginalTags=t.split(","),await this.i18n.saveSettings()})),new import_obsidian7.Setting(this.containerEl).setName("区域翻译").setHeading();const _=new import_obsidian7.Setting(this.containerEl);_.setName("区域选择器"),_.setDesc("匹配的元素将被视为一个整体, 不会分段翻译"),_.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.atomicBlockSelectors?this.settings.I18N_IMT_CONFIG.atomicBlockSelectors.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.atomicBlockSelectors=t.split(","),await this.i18n.saveSettings()}));const d=new import_obsidian7.Setting(this.containerEl);d.setName("区域Tag选择器"),d.setDesc("匹配的元素将被视为一个整体, 不会分段翻译"),d.addTextArea(t=>t.setValue(this.settings.I18N_IMT_CONFIG.atomicBlockTags?this.settings.I18N_IMT_CONFIG.atomicBlockTags.toString():"").onChange(async t=>{this.settings.I18N_IMT_CONFIG.atomicBlockTags=t.split(","),await this.i18n.saveSettings()}))}},import_obsidian8=require("obsidian"),from_lang={auto:"自动检测",zh:"中文",cht:"繁体中文",yue:"粤语",wyw:"文言文",en:"英语",jp:"日语",kor:"韩语",fra:"法语",spa:"西班牙语",th:"泰语",ara:"阿拉伯语",ru:"俄语",pt:"葡萄牙语",de:"德语",it:"意大利语",el:"希腊语",nl:"荷兰语",pl:"波兰语",bul:"保加利亚语",est:"爱沙尼亚语",dan:"丹麦语",fin:"芬兰语",cs:"捷克语",rom:"罗马尼亚语",slo:"斯洛文尼亚语",swe:"瑞典语",hu:"匈牙利语",vie:"越南语"},to_lang=JSON.parse(JSON.stringify(from_lang));delete to_lang.auto;var I18nNitBaiDu=class extends BaseSetting{main(){const e=new import_obsidian8.Setting(this.containerEl);"BAIDU"!=this.settings.I18N_NIT_API&&e.setClass("i18n--hidden"),e.setName(t("设置_AI_百度_原始语言_标题")),e.setDesc(t("设置_AI_百度_原始语言_描述")),e.addDropdown(t=>t.addOptions(from_lang).setValue(this.settings.I18N_NIT_APIS.BAIDU.FROM).onChange(t=>{this.settings.I18N_NIT_APIS.BAIDU.FROM=t,this.i18n.saveSettings()}).selectEl.addClass("i18n-select"));const s=new import_obsidian8.Setting(this.containerEl);"BAIDU"!=this.settings.I18N_NIT_API&&s.setClass("i18n--hidden"),s.setName(t("设置_AI_百度_目标语言_标题")),s.setDesc(t("设置_AI_百度_目标语言_描述")),s.addDropdown(t=>t.addOptions(to_lang).setValue(this.settings.I18N_NIT_APIS.BAIDU.TO).onChange(t=>{this.settings.I18N_NIT_APIS.BAIDU.TO=t,this.i18n.saveSettings()}).selectEl.addClass("i18n-select"));const i=new import_obsidian8.Setting(this.containerEl);"BAIDU"!=this.settings.I18N_NIT_API&&i.setClass("i18n--hidden"),i.setName(t("设置_AI_百度_标识_标题")),i.setDesc(t("设置_AI_百度_标识_描述")),i.addText(t=>t.setValue(this.settings.I18N_NIT_APIS.BAIDU.APP_ID).setPlaceholder("APPID").onChange(t=>{this.settings.I18N_NIT_APIS.BAIDU.APP_ID=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"));const n=new import_obsidian8.Setting(this.containerEl);"BAIDU"!=this.settings.I18N_NIT_API&&n.setClass("i18n--hidden"),n.setName(t("设置_AI_百度_密钥_标题")),n.setDesc(t("设置_AI_百度_密钥_描述")),n.addText(t=>t.setValue(this.settings.I18N_NIT_APIS.BAIDU.KEY).setPlaceholder("KEY").onChange(t=>{this.settings.I18N_NIT_APIS.BAIDU.KEY=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"))}},import_obsidian9=require("obsidian"),I18nNITOpenAI=class extends BaseSetting{main(){const e=new import_obsidian9.Setting(this.containerEl);e.setName(t("设置_AI_OPENAI_接口_标题")),e.setDesc(t("设置_AI_OPENAI_接口_描述")),"OPENAI"!=this.settings.I18N_NIT_API&&e.setClass("i18n--hidden"),e.addText(t=>t.setValue(this.settings.I18N_NIT_OPENAI_URL).setPlaceholder("https://api.openai.com").onChange(t=>{this.settings.I18N_NIT_OPENAI_URL=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"));const s=new import_obsidian9.Setting(this.containerEl);s.setName(t("设置_AI_OPENAI_KEY_标题")),s.setDesc(t("设置_AI_OPENAI_KEY_描述")),"OPENAI"!=this.settings.I18N_NIT_API&&s.setClass("i18n--hidden"),s.addText(t=>t.setValue(this.settings.I18N_NIT_OPENAI_KEY).setPlaceholder("KEY").onChange(t=>{this.settings.I18N_NIT_OPENAI_KEY=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"));const i=new import_obsidian9.Setting(this.containerEl);i.setName(t("设置_AI_OPENAI_模型_标题")),i.setDesc(t("设置_AI_OPENAI_模型_描述")),"OPENAI"!=this.settings.I18N_NIT_API&&i.setClass("i18n--hidden"),i.addText(t=>t.setValue(this.settings.I18N_NIT_OPENAI_MODEL).setPlaceholder("Model").onChange(t=>{this.settings.I18N_NIT_OPENAI_MODEL=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"));const n=new import_obsidian9.Setting(this.containerEl);"OPENAI"!=this.settings.I18N_NIT_API&&n.setClass("i18n--hidden"),n.setName(t("设置_AI_OPENAI_提示_标题")),n.setDesc(t("设置_AI_OPENAI_提示_描述")),n.addTextArea(e=>e.setValue(this.settings.I18N_NIT_OPENAI_TIPS).setPlaceholder(t("设置_AI_OPENAI_提示_占位符")).onChange(t=>{this.settings.I18N_NIT_OPENAI_TIPS=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"))}},import_obsidian10=require("obsidian"),I18nRE=class extends BaseSetting{main(){const e=new import_obsidian10.Setting(this.containerEl);e.setName(t("设置_正则_临时正则_标题")),e.setDesc(t("设置_正则_临时正则_描述")),e.addToggle(t=>t.setValue(this.settings.I18N_RE_TEMP_MODE).onChange(async()=>{this.settings.I18N_RE_TEMP_MODE=!this.settings.I18N_RE_TEMP_MODE,this.i18n.saveSettings()}).toggleEl.addClass("i18n-checkbox"));const s=new import_obsidian10.Setting(this.containerEl);s.setName(t("设置_正则_正则模式_标题")),s.setDesc(t("设置_正则_正则模式_描述")),s.addText(t=>t.setValue(this.settings.I18N_RE_MODE).setDisabled(!0).inputEl.addClass("i18n-input"));const i=new import_obsidian10.Setting(this.containerEl);i.setName(t("设置_正则_正则标志_标题")),i.setDesc(t("设置_正则_正则标志_描述")),i.addText(e=>e.setValue(this.settings.I18N_RE_FLAGS).setPlaceholder(t("设置_正则_正则标志_占位符")).onChange(t=>{this.settings.I18N_RE_FLAGS=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input"));const n=new import_obsidian10.Setting(this.containerEl);n.setName(t("设置_正则_匹配长度_标题")),n.setDesc(t("设置_正则_匹配长度_描述")),n.addSlider(t=>t.setDynamicTooltip().setLimits(0,3e3,100).setValue(this.settings.I18N_RE_LENGTH).onChange(t=>{this.settings.I18N_RE_LENGTH=t,this.i18n.saveSettings()}));let a="";const o=new import_obsidian10.Setting(this.containerEl);if(o.setName(t("设置_正则_模式编辑_标题")),o.setDesc(t("设置_正则_模式编辑_描述")),o.addText(e=>e.setPlaceholder(t("设置_正则_模式编辑_占位符")).onChange(t=>{a=t}).inputEl.addClass("i18n-input")),o.addButton(e=>e.setButtonText(t("设置_通用_添加_文本")).onClick(()=>{""==a||this.settings.I18N_RE_MODES.includes(a)?this.i18n.notice.error(t("设置_正则_通知_前缀"),t("设置_正则_模式编辑_通知一")):(this.settings.I18N_RE_MODES.push(a),this.settings.I18N_RE_DATAS.hasOwnProperty(a)||(this.settings.I18N_RE_DATAS[a]=[]),this.i18n.saveSettings(),this.settingTab.reDisplay())}).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`)),o.addButton(e=>e.setButtonText(this.settings.I18N_RE_MODE_DISPLAY?t("设置_通用_隐藏_文本"):t("设置_通用_显示_文本")).onClick(()=>{this.settings.I18N_RE_MODE_DISPLAY=!this.settings.I18N_RE_MODE_DISPLAY,this.i18n.saveSettings(),this.settingTab.reDisplay()}).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`)),this.settings.I18N_RE_MODE_DISPLAY)for(let e=0;e<this.settings.I18N_RE_MODES.length;e++){const s=new import_obsidian10.Setting(this.containerEl);s.setName(this.settings.I18N_RE_MODES[e]),this.settings.I18N_RE_MODE!=this.settings.I18N_RE_MODES[e]&&s.addButton(t=>t.setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("check").onClick(()=>{this.settings.I18N_RE_MODE=this.settings.I18N_RE_MODES[e],this.i18n.saveSettings(),this.settingTab.reDisplay()})),s.addButton(s=>s.setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("trash").onClick(()=>{if(this.settings.I18N_RE_MODES.length>1){delete this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODES[e]];const s=this.settings.I18N_RE_MODES[e],i=this.settings.I18N_RE_MODE;console.log(s==i),this.settings.I18N_RE_MODES[e]==this.settings.I18N_RE_MODE&&(this.settings.I18N_RE_MODE=this.settings.I18N_RE_MODES[0]),this.settings.I18N_RE_MODES.splice(e,1),this.i18n.notice.result(t("设置_正则_通知_前缀"),!0)}else this.i18n.notice.result(t("设置_正则_通知_前缀"),!1,t("设置_正则_数据编辑_通知一"));this.i18n.saveSettings(),this.settingTab.reDisplay()}))}let r="";const l=new import_obsidian10.Setting(this.containerEl);if(l.setName(t("设置_正则_数据编辑_标题")),l.setDesc(t("设置_正则_数据编辑_描述")),l.addText(e=>e.setPlaceholder(t("设置_正则_数据编辑_占位符")).onChange(t=>{r=t}).inputEl.addClass("i18n-input")),l.addButton(e=>e.setButtonText(t("设置_通用_添加_文本")).onClick(()=>{""!=r&&(this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE].push(r),this.i18n.saveSettings(),this.settingTab.reDisplay())}).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`)),l.addButton(e=>e.setButtonText(this.settings.I18N_RE_DATAS_DISPLAY?t("设置_通用_隐藏_文本"):t("设置_通用_显示_文本")).onClick(()=>{this.settings.I18N_RE_DATAS_DISPLAY=!this.settings.I18N_RE_DATAS_DISPLAY,this.i18n.saveSettings(),this.settingTab.reDisplay()}).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`)),this.settings.I18N_RE_DATAS_DISPLAY)for(let t=0;t<this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE].length;t++){const e=new import_obsidian10.Setting(this.containerEl);e.setName(this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE][t]),e.addButton(e=>e.setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("trash").onClick(()=>{this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE].splice(t,1),this.i18n.saveSettings(),this.settingTab.reDisplay()}))}}},import_obsidian13=require("obsidian"),import_obsidian12=require("obsidian"),fs2=__toESM(require_lib()),import_obsidian11=require("obsidian"),import_zlib=require("zlib"),import_child_process=require("child_process");function Diff(){}function buildValues(t,e,s,i,n){for(var a,o=[];e;)o.push(e),a=e.previousComponent,delete e.previousComponent,e=a;o.reverse();for(var r=0,l=o.length,c=0,_=0;r<l;r++){var d=o[r];if(d.removed)d.value=t.join(i.slice(_,_+d.count)),_+=d.count;else{if(!d.added&&n){var u=s.slice(c,c+d.count);u=u.map(function(t,e){var s=i[_+e];return s.length>t.length?s:t}),d.value=t.join(u)}else d.value=t.join(s.slice(c,c+d.count));c+=d.count,d.added||(_+=d.count)}}return o}Diff.prototype={diff:function(t,e){var s,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=i.callback;"function"==typeof i&&(n=i,i={});var a=this;function o(t){return t=a.postProcess(t,i),n?(setTimeout(function(){n(t)},0),!0):t}t=this.castInput(t,i),e=this.castInput(e,i),t=this.removeEmpty(this.tokenize(t,i));var r=(e=this.removeEmpty(this.tokenize(e,i))).length,l=t.length,c=1,_=r+l;null!=i.maxEditLength&&(_=Math.min(_,i.maxEditLength));var d=null!==(s=i.timeout)&&void 0!==s?s:1/0,u=Date.now()+d,h=[{oldPos:-1,lastComponent:void 0}],p=this.extractCommon(h[0],e,t,0,i);if(h[0].oldPos+1>=l&&p+1>=r)return o(buildValues(a,h[0].lastComponent,e,t,a.useLongestToken));var m=-1/0,g=1/0;function f(){for(var s=Math.max(m,-c);s<=Math.min(g,c);s+=2){var n=void 0,_=h[s-1],d=h[s+1];_&&(h[s-1]=void 0);var u=!1;if(d){var f=d.oldPos-s;u=d&&0<=f&&f<r}var E=_&&_.oldPos+1<l;if(u||E){if(n=!E||u&&_.oldPos<d.oldPos?a.addToPath(d,!0,!1,0,i):a.addToPath(_,!1,!0,1,i),p=a.extractCommon(n,e,t,s,i),n.oldPos+1>=l&&p+1>=r)return o(buildValues(a,n.lastComponent,e,t,a.useLongestToken));h[s]=n,n.oldPos+1>=l&&(g=Math.min(g,s-1)),p+1>=r&&(m=Math.max(m,s+1))}else h[s]=void 0}c++}if(n)!function t(){setTimeout(function(){if(c>_||Date.now()>u)return n();f()||t()},0)}();else for(;c<=_&&Date.now()<=u;){var E=f();if(E)return E}},addToPath:function(t,e,s,i,n){var a=t.lastComponent;return a&&!n.oneChangePerToken&&a.added===e&&a.removed===s?{oldPos:t.oldPos+i,lastComponent:{count:a.count+1,added:e,removed:s,previousComponent:a.previousComponent}}:{oldPos:t.oldPos+i,lastComponent:{count:1,added:e,removed:s,previousComponent:a}}},extractCommon:function(t,e,s,i,n){for(var a=e.length,o=s.length,r=t.oldPos,l=r-i,c=0;l+1<a&&r+1<o&&this.equals(s[r+1],e[l+1],n);)l++,r++,c++,n.oneChangePerToken&&(t.lastComponent={count:1,previousComponent:t.lastComponent,added:!1,removed:!1});return c&&!n.oneChangePerToken&&(t.lastComponent={count:c,previousComponent:t.lastComponent,added:!1,removed:!1}),t.oldPos=r,l},equals:function(t,e,s){return s.comparator?s.comparator(t,e):t===e||s.ignoreCase&&t.toLowerCase()===e.toLowerCase()},removeEmpty:function(t){for(var e=[],s=0;s<t.length;s++)t[s]&&e.push(t[s]);return e},castInput:function(t){return t},tokenize:function(t){return Array.from(t)},join:function(t){return t.join("")},postProcess:function(t){return t}};var characterDiff=new Diff;function longestCommonPrefix(t,e){var s;for(s=0;s<t.length&&s<e.length;s++)if(t[s]!=e[s])return t.slice(0,s);return t.slice(0,s)}function longestCommonSuffix(t,e){var s;if(!t||!e||t[t.length-1]!=e[e.length-1])return"";for(s=0;s<t.length&&s<e.length;s++)if(t[t.length-(s+1)]!=e[e.length-(s+1)])return t.slice(-s);return t.slice(-s)}function replacePrefix(t,e,s){if(t.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return s+t.slice(e.length)}function replaceSuffix(t,e,s){if(!e)return t+s;if(t.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return t.slice(0,-e.length)+s}function removePrefix(t,e){return replacePrefix(t,e,"")}function removeSuffix(t,e){return replaceSuffix(t,e,"")}function maximumOverlap(t,e){return e.slice(0,overlapCount(t,e))}function overlapCount(t,e){var s=0;t.length>e.length&&(s=t.length-e.length);var i=e.length;t.length<e.length&&(i=t.length);var n=Array(i),a=0;n[0]=0;for(var o=1;o<i;o++){for(e[o]==e[a]?n[o]=n[a]:n[o]=a;a>0&&e[o]!=e[a];)a=n[a];e[o]==e[a]&&a++}a=0;for(var r=s;r<t.length;r++){for(;a>0&&t[r]!=e[a];)a=n[a];t[r]==e[a]&&a++}return a}var extendedWordChars="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",tokenizeIncludingWhitespace=new RegExp("[".concat(extendedWordChars,"]+|\\s+|[^").concat(extendedWordChars,"]"),"ug"),wordDiff=new Diff;function diffWords(t,e,s){return null==(null==s?void 0:s.ignoreWhitespace)||s.ignoreWhitespace?wordDiff.diff(t,e,s):diffWordsWithSpace(t,e,s)}function dedupeWhitespaceInChangeObjects(t,e,s,i){if(e&&s){var n=e.value.match(/^\s*/)[0],a=e.value.match(/\s*$/)[0],o=s.value.match(/^\s*/)[0],r=s.value.match(/\s*$/)[0];if(t){var l=longestCommonPrefix(n,o);t.value=replaceSuffix(t.value,o,l),e.value=removePrefix(e.value,l),s.value=removePrefix(s.value,l)}if(i){var c=longestCommonSuffix(a,r);i.value=replacePrefix(i.value,r,c),e.value=removeSuffix(e.value,c),s.value=removeSuffix(s.value,c)}}else if(s)t&&(s.value=s.value.replace(/^\s*/,"")),i&&(i.value=i.value.replace(/^\s*/,""));else if(t&&i){var _=i.value.match(/^\s*/)[0],d=e.value.match(/^\s*/)[0],u=e.value.match(/\s*$/)[0],h=longestCommonPrefix(_,d);e.value=removePrefix(e.value,h);var p=longestCommonSuffix(removePrefix(_,h),u);e.value=removeSuffix(e.value,p),i.value=replacePrefix(i.value,_,p),t.value=replaceSuffix(t.value,_,_.slice(0,_.length-p.length))}else if(i){var m=i.value.match(/^\s*/)[0],g=maximumOverlap(e.value.match(/\s*$/)[0],m);e.value=removeSuffix(e.value,g)}else if(t){var f=maximumOverlap(t.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=removePrefix(e.value,f)}}wordDiff.equals=function(t,e,s){return s.ignoreCase&&(t=t.toLowerCase(),e=e.toLowerCase()),t.trim()===e.trim()},wordDiff.tokenize=function(t){var e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s.intlSegmenter){if("word"!=s.intlSegmenter.resolvedOptions().granularity)throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(s.intlSegmenter.segment(t),function(t){return t.segment})}else e=t.match(tokenizeIncludingWhitespace)||[];var i=[],n=null;return e.forEach(function(t){/\s/.test(t)?null==n?i.push(t):i.push(i.pop()+t):/\s/.test(n)?i[i.length-1]==n?i.push(i.pop()+t):i.push(n+t):i.push(t),n=t}),i},wordDiff.join=function(t){return t.map(function(t,e){return 0==e?t:t.replace(/^\s+/,"")}).join("")},wordDiff.postProcess=function(t,e){if(!t||e.oneChangePerToken)return t;var s=null,i=null,n=null;return t.forEach(function(t){t.added?i=t:t.removed?n=t:((i||n)&&dedupeWhitespaceInChangeObjects(s,n,i,t),s=t,i=null,n=null)}),(i||n)&&dedupeWhitespaceInChangeObjects(s,n,i,null),t};var wordWithSpaceDiff=new Diff;function diffWordsWithSpace(t,e,s){return wordWithSpaceDiff.diff(t,e,s)}wordWithSpaceDiff.tokenize=function(t){var e=new RegExp("(\\r?\\n)|[".concat(extendedWordChars,"]+|[^\\S\\n\\r]+|[^").concat(extendedWordChars,"]"),"ug");return t.match(e)||[]};var lineDiff=new Diff;lineDiff.tokenize=function(t,e){e.stripTrailingCr&&(t=t.replace(/\r\n/g,"\n"));var s=[],i=t.split(/(\n|\r\n)/);i[i.length-1]||i.pop();for(var n=0;n<i.length;n++){var a=i[n];n%2&&!e.newlineIsToken?s[s.length-1]+=a:s.push(a)}return s},lineDiff.equals=function(t,e,s){return s.ignoreWhitespace?(s.newlineIsToken&&t.includes("\n")||(t=t.trim()),s.newlineIsToken&&e.includes("\n")||(e=e.trim())):s.ignoreNewlineAtEof&&!s.newlineIsToken&&(t.endsWith("\n")&&(t=t.slice(0,-1)),e.endsWith("\n")&&(e=e.slice(0,-1))),Diff.prototype.equals.call(this,t,e,s)};var sentenceDiff=new Diff;sentenceDiff.tokenize=function(t){return t.split(/(\S.+?[.!?])(?=\s+|$)/)};var cssDiff=new Diff;function _typeof(t){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}cssDiff.tokenize=function(t){return t.split(/([{}:;,]|\s+)/)};var jsonDiff=new Diff;function canonicalize(t,e,s,i,n){var a,o;for(e=e||[],s=s||[],i&&(t=i(n,t)),a=0;a<e.length;a+=1)if(e[a]===t)return s[a];if("[object Array]"===Object.prototype.toString.call(t)){for(e.push(t),o=new Array(t.length),s.push(o),a=0;a<t.length;a+=1)o[a]=canonicalize(t[a],e,s,i,n);return e.pop(),s.pop(),o}if(t&&t.toJSON&&(t=t.toJSON()),"object"===_typeof(t)&&null!==t){e.push(t),o={},s.push(o);var r,l=[];for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&l.push(r);for(l.sort(),a=0;a<l.length;a+=1)o[r=l[a]]=canonicalize(t[r],e,s,i,r);e.pop(),s.pop()}else o=t;return o}jsonDiff.useLongestToken=!0,jsonDiff.tokenize=lineDiff.tokenize,jsonDiff.castInput=function(t,e){var s=e.undefinedReplacement,i=e.stringifyReplacer,n=void 0===i?function(t,e){return void 0===e?s:e}:i;return"string"==typeof t?t:JSON.stringify(canonicalize(t,null,null,n),n,"  ")},jsonDiff.equals=function(t,e,s){return Diff.prototype.equals.call(jsonDiff,t.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),s)};var arrayDiff=new Diff;arrayDiff.tokenize=function(t){return t.slice()},arrayDiff.join=arrayDiff.removeEmpty=function(t){return t};var State=class{constructor(t,e){this.stateJson={type:"0",state:!1,pluginVersion:"",translationVersion:0},this.path=e,this.isStateDoc=fs2.pathExistsSync(this.path),this.stateObj=this.isStateDoc?fs2.readJsonSync(this.path):void 0}getType(){return this.stateObj.type}getState(){return this.stateObj.state}getPluginVersion(){return this.stateObj.pluginVersion}getTranslationVersion(){return this.stateObj.translationVersion}setType(t){this.stateObj.type=t,fs2.outputJsonSync(this.path,this.stateObj)}setState(t){this.stateObj.state=t,fs2.outputJsonSync(this.path,this.stateObj)}setPluginVersion(t){this.stateObj.pluginVersion=t,fs2.outputJsonSync(this.path,this.stateObj)}setTranslationVersion(t){this.stateObj.translationVersion=t,fs2.outputJsonSync(this.path,this.stateObj)}insert(){try{this.stateObj=this.stateJson,this.isStateDoc=!0,fs2.outputJsonSync(this.path,this.stateJson)}catch(t){this.i18n.notice.result("新增状态文件",!1,t)}}delete(){try{this.isStateDoc=!1,fs2.removeSync(this.path)}catch(t){this.i18n.notice.result("删除状态文件",!1,t)}}update(t,e,s,i){const n={type:t,state:e,pluginVersion:s,translationVersion:i};this.stateObj=n;try{fs2.outputJsonSync(this.path,n)}catch(t){this.i18n.notice.result("修改状态文件",!1,t)}}reset(){try{fs2.outputJsonSync(this.path,this.stateJson),console.log(this.stateJson)}catch(t){this.i18n.notice.result("重置状态文件",!1,t)}}},Notification=class{constructor(t,e){this.notices=[],this.app=t,this.i18n=e}primary(t,e,s=4e3){const i=!!document.body&&document.body.classList.contains("theme-dark"),n=new import_obsidian11.Notice(`[${t}] ${e}`,s);n.noticeEl.addClass("notice__container",`notice__${i?"dark":"light"}--primary`),this.addNotice(n)}success(t,e,s=4e3){const i=!!document.body&&document.body.classList.contains("theme-dark"),n=new import_obsidian11.Notice(`[${t}] ${e}`,s);n.noticeEl.addClass("notice__container",`notice__${i?"dark":"light"}--success`),this.addNotice(n)}info(t,e,s=4e3){const i=!!document.body&&document.body.classList.contains("theme-dark"),n=new import_obsidian11.Notice(`[${t}] ${e}`,s);n.noticeEl.addClass("notice__container",`notice__${i?"dark":"light"}--info`),this.addNotice(n)}warning(t,e,s=4e3){const i=!!document.body&&document.body.classList.contains("theme-dark"),n=new import_obsidian11.Notice(`[${t}] ${e}`,s);n.noticeEl.addClass("notice__container",`notice__${i?"dark":"light"}--warning`),this.addNotice(n)}error(t,e,s=1e4){const i=!!document.body&&document.body.classList.contains("theme-dark"),n=new import_obsidian11.Notice(`[${t}] ${e}`,s);n.noticeEl.addClass("notice__container",`notice__${i?"dark":"light"}--error`),this.addNotice(n)}result(e,s,i="",n=4e3){const a=!!document.body&&document.body.classList.contains("theme-dark");if(s)if(""!=i){const s=new import_obsidian11.Notice(`[${e}] ${t("通用_成功_文本")}\n${i}`,n);s.noticeEl.addClass(`notice__${a?"dark":"light"}--success`),this.addNotice(s)}else{const s=new import_obsidian11.Notice(`[${e}] ${t("通用_成功_文本")}`,n);s.noticeEl.addClass("notice__container",`notice__${a?"dark":"light"}--success`),this.addNotice(s)}else{const s=new import_obsidian11.Notice(`[${e}] ${t("通用_失败_文本")}\n${i}`,1e4);s.noticeEl.addClass("notice__container",`notice__${a?"dark":"light"}--error`),this.addNotice(s)}}reload(){this.notices.forEach(t=>t.noticeEl.remove()),this.notices.length=0}addNotice(t){this.notices.length>=100&&(this.notices[0].noticeEl.remove(),this.notices.shift()),this.notices.push(t)}};function generatePlugin(t,e,s,i,n,a){const o=e.description,r={manifest:{translationVersion:Date.now(),pluginVersion:t},description:{original:o,translation:o},dict:{}};for(let t=0;t<n.length;t++){const e=s.match(new RegExp(n[t],a));if(null!=e)for(const t in e)e[t].length<=i&&(r.dict[e[t]]=e[t])}return r}function generateTheme(t,e){const s={manifest:{translationVersion:0,pluginVersion:t.version},dict:{}},i=e.match(/(?:\/\* @settings)([\s\S]*?)(?:\*\/)/);if(i){const t=/title:\s*([^\n]*)|description:\s*([^\n]*)/gm,e=i[0].match(t);e&&e.forEach(t=>{s.dict[t]=t})}else console.log("没有找到匹配的内容");return s}function compareVersions(t,e){const s=t.split(".").map(Number),i=e.split(".").map(Number),n=Math.max(s.length,i.length);for(let t=0;t<n;t++){const e=s[t]||0,n=i[t]||0;if(e>n)return 1;if(e<n)return-1}return 0}var formatTimestamp=t=>{const e=new Date(t);return`${String(e.getMonth()+1).padStart(2,"0")}月${String(e.getDate()).padStart(2,"0")}日 ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},formatTimestamp_concise=t=>{const e=new Date(t),[s,i,n,a,o]=[e.getFullYear(),String(e.getMonth()+1).padStart(2,"0"),String(e.getDate()).padStart(2,"0"),String(e.getHours()).padStart(2,"0"),String(e.getMinutes()).padStart(2,"0")];return`${s}/${i}/${n} ${a}:${o}`},comparePlugin=(t,e)=>{const s={},i={},n={},a={};for(const[n,o]of Object.entries(e.dict))n in t.dict?t.dict[n]!==o?a[n]={oldValue:t.dict[n],newValue:o}:s[n]=o:i[n]=o;for(const[s,i]of Object.entries(t.dict))s in e.dict||(n[s]=i);return{unchanged:s,added:i,modified:a,removed:n}},compareTheme=(t,e)=>{const s={},i={},n={},a={};for(const[n,o]of Object.entries(e.dict))n in t.dict?t.dict[n]!==o?a[n]={oldValue:t.dict[n],newValue:o}:s[n]=o:i[n]=o;for(const[s,i]of Object.entries(t.dict))s in e.dict||(n[s]=i);return{unchanged:s,added:i,modified:a,removed:n}},isValidpluginTranslationFormat=t=>{if(!t)return!1;const e="manifest"in t,s="dict"in t,i="description"in t,n=e&&"translationVersion"in t.manifest,a=e&&"pluginVersion"in t.manifest,o=i&&"original"in t.description,r=i&&"translation"in t.description;return e&&n&&a&&s&&i&&o&&r},isValidThemeTranslationFormat=t=>{if(!t)return!1;const e="manifest"in t,s=e&&"translationVersion"in t.manifest,i=e&&"pluginVersion"in t.manifest;return e&&s&&i&&"dict"in t},deflate=t=>(0,import_zlib.deflateSync)(t).toString("base64"),inflate=t=>(0,import_zlib.inflateSync)(Buffer.from(t,"base64")).toString(),i18nOpen=(e,s)=>{navigator.userAgent.match(/Win/i)&&(0,import_child_process.exec)(`start "" "${s}"`,s=>{s?e.notice.result(t("功能_打开_前缀"),!1,s):e.notice.result(t("功能_打开_前缀"),!0)}),navigator.userAgent.match(/Mac/i)&&(0,import_child_process.exec)(`open ${s}`,s=>{s?e.notice.result(t("功能_打开_前缀"),!1,s):e.notice.result(t("功能_打开_前缀"),!0)})},diff2=(t,e)=>{const s=diffWords(t,e);let i="",n="";return s.forEach(t=>{t.added?n+=`<span class='color__text--success'>${t.value}</span>`:t.removed?i+=`<span class='color__text--danger'>${t.value}</span>`:(i+=t.value,n+=t.value)}),{s1:i,s2:n}},parseIssueTitle=t=>{const e=t.match(/\[(.*?)\]\s*\[(.*?)\]\s*\[(.*?)\]/);return e?[e[1],e[2],e[3]]:["","",""]},isPlugin=t=>void 0!==t.description,isTheme=t=>void 0!==t.dict&&!t.description,restoreTranslate=()=>{const t=new KeyboardEvent("keydown",{key:"a",keyCode:65,which:65,code:"KeyA",altKey:!0,bubbles:!0});document.dispatchEvent(t)},clearStorage=async()=>{Object.keys(window.localStorage).filter(t=>t.startsWith("immersiveTranslate")).forEach(t=>{delete window.localStorage[t]});await window.indexedDB.databases().then(t=>{var e;null==(e=null==t?void 0:t.filter(t=>{var e;return null==(e=t.name)?void 0:e.startsWith("immersive-translate")}))||e.forEach(t=>{t.name&&window.indexedDB.deleteDatabase(t.name)})}).catch(()=>{});Object.keys(window).filter(t=>-1!==t.indexOf("mmersiveTranslate")).forEach(t=>{void 0!==window[t]&&delete window[t]})},ai=async(t,e)=>{switch(t.settings.I18N_NIT_API){case"BAIDU":return await t.tapi.baiduAPI(e);case"OPENAI":return await t.tapi.openAI(e)}},AdminModal=class extends import_obsidian12.Modal{constructor(t,e){super(t),this.issue=(t=>{const e=t.title,[s,i,n]=parseIssueTitle(e),a=new import_obsidian12.Setting(this.contentEl);a.setClass("i18n__item"),a.nameEl.addClass("i18n__item-title");let o="";"0"===s[0]&&(o="primary"),"1"===s[0]&&(o="danger");let r="";"0"===s[0]&&(r="插件"),"1"===s[0]&&(r="主题");let l="";"0"===s[1]&&(l="primary"),"1"===s[1]&&(l="success"),"2"===s[1]&&(l="warning");let c="";"0"===s[1]&&(c="标记汉化"),"1"===s[1]&&(c=`提交${"0"===s[0]?"插件":"主题"}`),"2"===s[1]&&(c=`更新${"0"===s[0]?"插件":"主题"}`),a.nameEl.innerHTML=`<span class="i18n-tag i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-${o} is-${this.i18n.settings.I18N_TAG_SHAPE}">${r}</span>\n        <span class="i18n-tag i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-${l} is-${this.i18n.settings.I18N_TAG_SHAPE}">${c}</span>\n        <span class="i18n__item-title"> ${n}[${i}](${t.user.name})</span>`,new import_obsidian12.ButtonComponent(a.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setButtonText("审核").onClick(()=>{this.i18n.issue=t,this.i18n.activateAdminView()}),new import_obsidian12.ButtonComponent(a.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setButtonText(`查看[${t.comments}]`).onClick(()=>{window.open(`https://gitee.com/zero--two/obsidian-i18n-translation/issues/${t.number}`)})}),this.isRandomContentFormat=(t=>{return/^\[.*?\]\s*\[.*?\]\s*\[.*?\]$/.test(t)}),this.i18n=e,this.a()}async a(){const t=await this.i18n.api.giteeGetAllIssue();t.state?t.data.length>0?(this.i18n.issues=t.data,this.i18n.notice.result("获取",!0,`${this.i18n.issues.length}条待审核内容`)):this.i18n.notice.result("获取",!0,"暂时没有可审核任务"):this.i18n.notice.result("获取",!1,"获取失败,请检查网络后重试")}async showHead(){const t=this.contentEl.parentElement;t.addClass("i18n-share-history__container"),t.removeChild(t.getElementsByClassName("modal-close-button")[0]),this.titleEl.addClass("i18n-share-history__title-box");const e=new import_obsidian12.Setting(this.titleEl);e.setName("I18N审核").setClass("i18n-share-history__title"),new import_obsidian12.ButtonComponent(e.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setIcon("refresh-ccw").onClick(async()=>{const t=await this.i18n.api.giteeGetAllIssue();t.state?t.data.length>0?this.i18n.issues=t.data:this.i18n.notice.result("获取",!0,"暂时没有可审核任务"):this.i18n.notice.result("获取",!1,"获取失败,请检查网络后重试"),this.reloadShowData()})}async showMain(){if(this.i18n.issues)for(const t of this.i18n.issues)this.isRandomContentFormat(t.title)&&this.issue(t)}async onOpen(){await this.showHead(),await this.showMain()}async onClose(){this.contentEl.empty()}async reloadShowData(){let t=0;const e=this.contentEl;t=e.scrollTop,e.empty(),await this.showMain(),e.scrollTo(0,t)}},I18nShare=class extends BaseSetting{main(){new import_obsidian13.Setting(this.containerEl).setName(t("设置_共享_标题")).setDesc(t("设置_共享_描述")),new import_obsidian13.Setting(this.containerEl).setName(t("设置_共享_贡献者_标题")).setDesc(t("设置_共享_贡献者_描述")).addText(e=>e.setValue(this.settings.I18N_SHARE_TOKEN).setPlaceholder(t("设置_共享_贡献者_提示")).onChange(t=>{this.settings.I18N_SHARE_TOKEN=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input")).addToggle(t=>t.setValue(this.settings.I18N_SHARE_MODE).onChange(()=>{this.settings.I18N_SHARE_MODE=!this.settings.I18N_SHARE_MODE,this.i18n.saveSettings(),this.settingTab.shareDisplay()}).toggleEl.addClass("i18n-checkbox")),new import_obsidian13.Setting(this.containerEl).setName(`${t("设置_共享_管理员_标题")} (${this.settings.I18N_ADMIN_VERIFY?"已验证":"未验证"})`).setDesc(t("设置_共享_管理员_描述")).addButton(t=>t.setButtonText("验证").setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).onClick(async()=>{if(this.settings.I18N_ADMIN_VERIFY)this.i18n.notice.result("共建云端",!0,"您已验证,无需重复验证");else{const t=await this.i18n.api.giteeUser();(await this.i18n.api.checkUser(t.data.login)).state?(this.settings.I18N_ADMIN_VERIFY=!this.settings.I18N_ADMIN_VERIFY,this.i18n.saveSettings(),this.settingTab.shareDisplay(),this.i18n.notice.result("共建云端",!0,"验证成功")):this.i18n.notice.result("共建云端",!1,"验证失败")}}).setClass(this.i18n.settings.I18N_ADMIN_VERIFY?"i18n--hidden":"i18n--none")).addText(e=>e.setValue(this.settings.I18N_ADMIN_TOKEN).setPlaceholder(t("设置_共享_管理员_提示")).onChange(t=>{this.settings.I18N_ADMIN_TOKEN=t,this.i18n.saveSettings()}).inputEl.addClass("i18n-input")).addToggle(t=>t.setValue(this.settings.I18N_ADMIN_MODE).onChange(()=>{this.settings.I18N_ADMIN_VERIFY?(this.settings.I18N_ADMIN_MODE=!this.settings.I18N_ADMIN_MODE,this.i18n.saveSettings(),this.settings.I18N_ADMIN_MODE?this.i18n.i18nReviewEl=this.i18n.addRibbonIcon("i18n-review","I18N审核",t=>{new AdminModal(this.app,this.i18n).open()}):this.i18n.i18nReviewEl.remove()):(this.i18n.notice.result("共建云端",!1,"您尚未验证,请先验证"),this.settingTab.shareDisplay())}).toggleEl.addClass("i18n-checkbox"))}},import_obsidian14=require("obsidian"),I18nStyle=class extends BaseSetting{main(){new import_obsidian14.Setting(this.containerEl).setName(t("设置_主题_标签_标题")).setDesc(t("设置_主题_标签_描述")).addDropdown(t=>t.addOptions(TAG_TYPE).setValue(this.settings.I18N_TAG_TYPE).onChange(async t=>{this.settings.I18N_TAG_TYPE=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select")).addDropdown(t=>t.addOptions(TAG_SHAPE).setValue(this.settings.I18N_TAG_SHAPE).onChange(async t=>{this.settings.I18N_TAG_SHAPE=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select")),new import_obsidian14.Setting(this.containerEl).setName(t("设置_主题_按钮_标题")).setDesc(t("设置_主题_按钮_描述")).addDropdown(t=>t.addOptions(BUTTON_TYPE).setValue(this.settings.I18N_BUTTON_TYPE).onChange(async t=>{this.settings.I18N_BUTTON_TYPE=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select")).addDropdown(t=>t.addOptions(BUTTON_SHAPE).setValue(this.settings.I18N_BUTTON_SHAPE).onChange(async t=>{this.settings.I18N_BUTTON_SHAPE=t,await this.i18n.saveSettings()}).selectEl.addClass("i18n-select"))}},I18nSettingTab=class extends import_obsidian15.PluginSettingTab{constructor(t,e){super(t,e),this.i18n=e,this.app=t}display(){const{containerEl:e}=this;e.empty(),e.addClass("i18n-setting__container");const s=this.containerEl.createEl("div");s.addClass("i18n-setting__tabs"),this.contentEl=this.containerEl.createEl("div"),this.contentEl.addClass("i18n-setting__content");const i=[{text:t("设置_主题_标题_缩写"),content:()=>this.styleDisplay()},{text:t("设置_基础_标题_缩写"),content:()=>this.basisDisplay()},{text:t("设置_本地_标题_缩写"),content:()=>this.ldtDisplay()},{text:t("设置_云端_标题_缩写"),content:()=>this.ndtDisplay()},{text:t("设置_AI_标题_缩写"),content:()=>this.nitDisplay()},{text:t("设置_沉浸_标题_缩写"),content:()=>this.imtDisplay()},{text:t("设置_共享_标题_缩写"),content:()=>this.shareDisplay()},{text:t("设置_正则_标题_缩写"),content:()=>this.reDisplay()}],n=[];i.forEach((t,e)=>{const i=s.createEl("div");i.addClass("i18n-setting__tabs-item"),i.textContent=t.text,n.push(i),0===e&&(i.addClass("i18n-setting__tabs-item_is-active"),t.content()),i.addEventListener("click",()=>{n.forEach(t=>{t.removeClass("i18n-setting__tabs-item_is-active")}),i.addClass("i18n-setting__tabs-item_is-active"),t.content()})})}styleDisplay(){this.contentEl.empty(),new I18nStyle(this).display()}basisDisplay(){this.contentEl.empty(),new I18nBasis(this).display()}ldtDisplay(){this.contentEl.empty(),new I18nModLDT(this).display()}ndtDisplay(){this.contentEl.empty(),new I18nModNDT(this).display()}nitDisplay(){this.contentEl.empty(),new I18nModeNIT(this).display(),new I18nNitBaiDu(this).display(),new I18nNITOpenAI(this).display()}imtDisplay(){this.contentEl.empty(),new I18nModIMT(this).display()}shareDisplay(){this.contentEl.empty(),new I18nShare(this).display()}reDisplay(){this.contentEl.empty(),new I18nRE(this).display()}},path3=__toESM(require("path")),fs5=__toESM(require_lib()),import_obsidian20=require("obsidian"),import_obsidian16=require("obsidian"),Url=class{};Url.I18N_ICON="https://gitee.com/zero--two/obsidian-i18n-translation/raw/master/Obsidian.png",Url.QQ_GROUP="https://qm.qq.com/cgi-bin/qm/qr?k=kHTS0iC1FC5igTXbdbKzff6_tc54mOF5&jump_from=webapi&authKey=AoSkriW+nDeDzBPqBl9jcpbAYkPXN2QRbrMh0hFbvMrGbqZyRAbJwaD6JKbOy4Nx",Url.VIDEO_TUTORIAL=" https://www.bilibili.com/video/BV1VcxJeNExx/",Url.DOCUMENTATION_TUTORIAL="https://gitee.com/zero--two/obsidian-i18n-translation",Url.TASK="https://gitee.com/zero--two/obsidian-i18n-translation/issues?assignee_id=&author_id=&branch=&collaborator_ids=&issue_search=&label_ids=266056763&label_text=&milestone_id=&priority=&private_issue=&program_id=&project_id=zero--two%2Fobsidian-i18n-translation&project_type=&scope=&single_label_id=&single_label_text=&sort=&state=open&target_project=",Url.SDK_URL="https://download.immersivetranslate.com/immersive-translate-sdk-latest.js";var i,getRandomValues,WizardModal=class extends import_obsidian16.Modal{constructor(t,e){super(t),this.imgDate="data:image/png;base64,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",this.i18n=e}async Main(){const{contentEl:e}=this;this.contentEl.parentElement.addClass("i18n-wizard__container"),this.contentEl.addClass("i18n-wizard__box"),this.img=this.contentEl.doc.createElement("img"),this.img.addClass("i18n-wizard__img"),this.img.src=this.imgDate,this.contentEl.appendChild(this.img),this.title=this.contentEl.doc.createElement("p"),this.title.addClass("i18n-wizard__title"),this.title.innerHTML="Obsidian-I18N",this.contentEl.appendChild(this.title),this.version=this.contentEl.doc.createElement("p"),this.version.addClass("i18n-wizard__version"),this.version.innerHTML=`${t("向导_通用_版本")} ${this.i18n.manifest.version}`,this.contentEl.appendChild(this.version);const s=new import_obsidian16.Setting(e);s.setName(t("向导_视频_标题")),s.setDesc(t("向导_视频_描述")),s.controlEl.createEl("button",{text:t("向导_通用_浏览"),cls:["i18n-button",`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",async()=>{window.open(Url.VIDEO_TUTORIAL)})});const i=new import_obsidian16.Setting(e);i.setName(t("向导_文档_标题")),i.setDesc(t("向导_文档_描述")),i.controlEl.createEl("button",{text:t("向导_通用_浏览"),cls:["i18n-button",`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`,`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",async()=>{window.open(Url.DOCUMENTATION_TUTORIAL)})});const n=new import_obsidian16.Setting(e);n.setName(t("向导_QQ_标题")),n.setDesc(t("向导_QQ_描述")),n.controlEl.createEl("button",{text:t("向导_通用_加入"),cls:["i18n-button",`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`,`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",async()=>{window.open(Url.QQ_GROUP)})})}async onOpen(){await this.Main()}async onClose(){this.contentEl.empty()}},path=__toESM(require("path")),fs3=__toESM(require_lib()),import_obsidian17=require("obsidian"),NameTranslationModal=class extends import_obsidian17.Modal{constructor(t,e){super(t),this.i18n=e,this.i18n.originalPluginsManifests.forEach(t=>{!t.name||t.name in this.i18n.nameTranslationJSON||"i18n"==t.id||(this.i18n.nameTranslationJSON[t.name]="")}),fs3.writeJSONSync(path.join(path.normalize(this.app.vault.adapter.getBasePath()),path.join(this.i18n.manifest.dir,"name.json")),this.i18n.nameTranslationJSON,{spaces:4})}async showHead(){this.contentEl.addClass("i18n__item-box");const t=this.contentEl.parentElement;t.addClass("i18n-name__container"),t.removeChild(t.getElementsByClassName("modal-close-button")[0]),this.titleEl.addClass("i18n-share-history__title-box");const e=new import_obsidian17.Setting(this.titleEl);e.setClass("i18n-share-history__title"),e.setName("插件列表");const s=new import_obsidian17.ButtonComponent(e.controlEl);s.setClass("i18n-button"),s.setClass("i18n-button--primary"),s.setButtonText("退出"),s.onClick(()=>{this.close()})}async showMain(){for(const t in this.i18n.nameTranslationJSON){const e=new import_obsidian17.Setting(this.contentEl);e.setClass("i18n__item"),e.nameEl.addClass("i18n__item-title"),e.infoEl.remove();const s=new import_obsidian17.TextComponent(e.controlEl);s.inputEl.addClass("i18n-name__input-auto"),s.setValue(t),s.setDisabled(!0);const i=new import_obsidian17.TextComponent(e.controlEl);i.inputEl.addClass("i18n-name__input-auto"),i.setValue(this.i18n.nameTranslationJSON[t]),i.onChange(e=>{this.i18n.nameTranslationJSON[t]=e,fs3.writeJSONSync(path.join(this.app.vault.adapter.getBasePath(),path.join(this.i18n.manifest.dir,"name.json")),this.i18n.nameTranslationJSON,{spaces:4}),this.i18n.reloadPluginsName()})}}async reloadShowData(){let t=0;const e=this.contentEl;t=e.scrollTop,e.empty(),await this.showMain(),e.scrollTo(0,t)}async onOpen(){await this.showHead(),await this.showMain(),console.log("进入了")}async onClose(){this.contentEl.empty(),console.log("推出了")}},import_obsidian18=require("obsidian"),ContributorModal=class extends import_obsidian18.Modal{constructor(t,e){super(t),this.i18n=e}async showHead(){var t;const e=this.contentEl.parentElement;e.addClass("i18n-share-history__container"),e.removeChild(e.getElementsByClassName("modal-close-button")[0]),null==(t=this.titleEl.parentElement)||t.addClass("i18n-contributor__title-box"),this.contentEl.addClass("i18n-contributor__content");const s=new import_obsidian18.Setting(this.titleEl).setClass("i18n-contributor__title").setName("译文贡献榜单");new import_obsidian18.ButtonComponent(s.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setIcon("refresh-ccw").onClick(()=>{this.reloadShowData()})}async showMain(){if(void 0===this.i18n.contributorCache&&(this.i18n.contributorCache=(await this.i18n.api.giteeGetContributor()).data),void 0!==this.i18n.contributorCache){this.i18n.contributorCache.sort((t,e)=>{const s=t.translation+t.modification+t.erasure;return e.translation+e.modification+e.erasure-s});const t=this.contentEl.createEl("table");t.addClass("i18n-contributor__table");let e=1;for(const{name:s,url:i,translation:n,modification:a,erasure:o}of this.i18n.contributorCache){const r=t.createEl("tr",{cls:["i18n-contributor__row"]});r.addEventListener("click",()=>{window.open(i)});const l=e<=3?`i18n-contributor__rank-${e}`:"i18n-contributor__rank-4";r.createEl("td",{text:e.toString(),cls:["i18n-contributor__rank",l]}),e++,r.createEl("td",{text:s,cls:["i18n-contributor__name"]}),r.createEl("td",{text:(n+a+o).toString(),cls:["i18n-contributor__translation"]})}}}async reloadShowData(){let t=0;const e=this.contentEl;t=e.scrollTop,e.empty(),await this.showMain(),e.scrollTo(0,t)}async onOpen(){await this.showHead(),await this.showMain()}async onClose(){this.contentEl.empty()}},path2=__toESM(require("path")),fs4=__toESM(require_lib()),import_obsidian19=require("obsidian"),I18NThemeModal=class extends import_obsidian19.Modal{constructor(t,e){super(t),this.themes=[],this.showThemes=[],this.developerMode=!1,this.i18n=e,this.basePath=path2.normalize(this.app.vault.adapter.getBasePath()),this.settings=e.settings,this.settingPlugins=this.app.setting}async showHead(){var e;const s=this.contentEl.parentElement;s.addClass("i18n__container"),s.removeChild(s.getElementsByClassName("modal-close-button")[0]),null==(e=this.titleEl.parentElement)||e.addClass("i18n__header"),this.contentEl.addClass("i18n__item-box"),this.detailsEl=document.createElement("div"),this.detailsEl.addClass("i18n__item-details"),this.detailsEl.innerText="无",s.appendChild(this.detailsEl);const i=new import_obsidian19.Setting(this.titleEl).setClass("i18n__help").setName(t("通用_标题_文本"));new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("i18n_qq").setTooltip(t("通用_QQ_描述")).onClick(()=>{window.open(Url.QQ_GROUP)}),this.settings.I18N_MODE_NDT&&new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("i18n-contributor").setTooltip(t("通用_贡献榜_描述")).onClick(()=>{new ContributorModal(this.app,this.i18n).open()}),this.settings.I18N_MODE_LDT&&this.settings.I18N_NAME_TRANSLATION&&new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("name-setting").setTooltip(t("通用_名称翻译_描述")).onClick(()=>{this.close(),new NameTranslationModal(this.app,this.i18n).open()}),new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("settings").setTooltip(t("通用_设置_描述")).onClick(()=>{this.enabledPlugins.has(this.i18n.settings.I18N_STYLE_SETTINGS)?(this.settingPlugins.open(),this.settingPlugins.openTabById(this.i18n.settings.I18N_STYLE_SETTINGS),this.close()):this.i18n.notice.result("设置",!1,"插件未开启")}),new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("circle-help").setTooltip(t("通用_帮助_描述")).onClick(()=>{new WizardModal(this.app,this.i18n).open()}),new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("chevrons-left-right-ellipsis").setTooltip(t("通用_插件_描述")).onClick(()=>{this.i18n.settings.I18N_MODE=0,this.i18n.saveSettings(),this.i18n.themeModal.close(),this.i18n.pluginModal=new I18NPluginModal(this.app,this.i18n),this.i18n.pluginModal.open()}),this.developerMode&&new import_obsidian19.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("refresh-ccw").setTooltip("刷新插件").onClick(async()=>{this.close(),await this.reloadPlugin(this.i18n.manifest.id)}),new import_obsidian19.Setting(this.titleEl).setClass("i18n__search").setName(t("通用_搜索_标题")).addSearch(t=>t.setValue(this.settings.I18N_SEARCH_TEXT).onChange(t=>{this.settings.I18N_SEARCH_TEXT=t,this.i18n.saveSettings(),this.reloadShowData()}).inputEl.addClass("i18n-input")),this.i18n.settings.I18N_RE_TEMP_MODE&&new import_obsidian19.Setting(this.titleEl).setClass("i18n__search").addText(e=>e.setValue(this.settings.I18N_RE_TEMP).setPlaceholder(t("功能_正则_占位符")).onChange(t=>{this.settings.I18N_RE_TEMP=t,this.i18n.saveSettings(),this.regexps=[...this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE],...this.i18n.settings.I18N_RE_TEMP.split("|")].filter(t=>""!==t)}).inputEl.addClass("i18n__re-input","i18n-input")).infoEl.remove()}async showData(){this.themes=Object.values(this.app.customCss.themes),this.enabledPlugins=this.app.plugins.enabledPlugins,""==this.settings.I18N_SEARCH_TEXT?this.showThemes=this.themes:this.showThemes=this.themes.filter(t=>-1!=t.name.toLowerCase().indexOf(this.settings.I18N_SEARCH_TEXT.toLowerCase()));for(const e of this.showThemes){const s=path2.join(this.basePath,e.dir),i=path2.join(s,"lang"),n=path2.join(s,"lang",`${this.settings.I18N_LANGUAGE}.json`),a=path2.join(s,"lang","state.json"),o=fs4.pathExistsSync(i),r=fs4.pathExistsSync(n),l=path2.join(s,"manifest.json"),c=path2.join(s,"theme.css"),_=path2.join(s,"duplicate.css"),d=new State(this.i18n,a);if(o&&!d.isStateDoc&&d.insert(),d.isStateDoc&&d.getState()&&e.version!=d.getPluginVersion())try{fs4.removeSync(_),d.reset(),this.i18n.notice.primary(t("通用_插件更新_前缀"),e.name)}catch(e){this.i18n.notice.error(t("通用_插件更新_前缀"),e)}let u,h,p,m,g=!0,f=!1;if(r)try{u=fs4.readJsonSync(n),g=isValidThemeTranslationFormat(u)}catch(t){g=!1}this.i18n.themeDirectoryMark&&(h=this.i18n.themeDirectory.find(t=>t.id===e.name)),this.i18n.themeDirectoryMark&&h&&g&&(p=e.version in h.translations?e.version:Object.keys(h.translations).slice(-1)[0],u&&u.manifest.translationVersion<h.translations[p]&&(f=!0)),m=!d.isStateDoc||(!["0","1"].includes(d.getType())||"0"===d.getType());const E=new import_obsidian19.Setting(this.contentEl);E.setClass("i18n__item"),E.nameEl.addClass("i18n__item-title");const T={mark:1,label:{color:"",text:""},text:""};d.isStateDoc?u&&g?(1===compareVersions(e.version,u.manifest.pluginVersion)?(T.label.color="warning",T.label.text=t("标签_已过时_文本")):d.getState()?(T.label.color="success",T.label.text=t("标签_已翻译_文本")):(T.label.color="danger",T.label.text=t("标签_未翻译_文本")),T.text=`<span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-primary is-${this.settings.I18N_TAG_SHAPE}">修改日期</span> ${formatTimestamp(fs4.statSync(n).mtimeMs)} <span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-primary is-${this.settings.I18N_TAG_SHAPE}">支持版本</span> ${null==u?void 0:u.manifest.pluginVersion}`):(T.label.color="danger",T.label.text=t("标签_译文有误_文本"),T.text=t("标签_译文有误_描述")):(T.label.color="info",T.label.text=t("标签_无译文_文本"),T.text=t("标签_无译文_描述")),this.i18n.ignoreMark&&this.i18n.ignorePlugins.includes("这里需要修改")&&(T.label.color="primary",T.label.text=t("标签_自带翻译_文本"),T.text=t("标签_自带翻译_描述")),E.nameEl.innerHTML=`<span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-${T.label.color} is-${this.settings.I18N_TAG_SHAPE}">${T.label.text}</span><span class="i18n__item-name">${e.name}</span> <span class="i18n__item-version">[${e.version}]</span> `,E.settingEl.onmouseover=(t=>{this.detailsEl.innerHTML=T.text}),E.settingEl.onmouseout=(e=>{this.detailsEl.innerHTML=t("标签_无_文本")});const b=new import_obsidian19.ExtraButtonComponent(E.controlEl).setIcon("folder-open").setTooltip(t("功能_打开_描述")).onClick(()=>{b.setDisabled(!0),i18nOpen(this.i18n,s),b.setDisabled(!1)});if(o){const e=new import_obsidian19.ExtraButtonComponent(E.controlEl).setIcon("trash").setTooltip(t("功能_删除_描述")).onClick(()=>{e.setDisabled(!0);try{d.stateObj.state&&(fs4.removeSync(c),fs4.renameSync(_,c)),fs4.removeSync(i),this.i18n.notice.result(t("功能_删除_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_删除_前缀"),!1,e)}this.reloadShowData()})}if(g&&r&&this.settings.I18N_EDIT_MODE&&new import_obsidian19.ExtraButtonComponent(E.controlEl).setIcon("pencil").setTooltip(t("功能_编辑_描述")).onClick(()=>{this.i18n.editorLoad("theme",n,d)}),this.settings.I18N_SHARE_MODE&&g&&r){const s=new import_obsidian19.ExtraButtonComponent(E.controlEl);s.setIcon("cloud-upload"),s.setTooltip(t("功能_共享_描述")),s.onClick(()=>{this.i18n.shareLoad(1,n,e)})}this.settings.I18N_MODE_LDT&&!r&&E.controlEl.createEl("button",{text:t("功能_提取_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},e=>{e.addEventListener("click",()=>{try{const e=fs4.readFileSync(c).toString(),s=generateTheme(fs4.readJsonSync(l),e);fs4.ensureDirSync(i),fs4.writeJsonSync(n,s,{spaces:4}),d.insert(),d.setType("0"),this.i18n.notice.result(t("功能_提取_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_提取_前缀"),!1,`${e}`)}this.reloadShowData()})}),this.settings.I18N_MODE_LDT&&this.settings.I18N_INCREMENTAL_EXTRACTION&&r&&g&&E.controlEl.createEl("button",{text:t("功能_提取_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{if(d.isStateDoc&&!d.getState())try{const s=fs4.readJsonSync(n),a=generatePlugin(e.version,fs4.readJsonSync(l),fs4.readFileSync(c).toString(),this.settings.I18N_RE_LENGTH,this.regexps,this.settings.I18N_RE_FLAGS);a.manifest=s.manifest,a.description=s.description,a.dict={...a.dict,...s.dict},fs4.ensureDirSync(i),fs4.writeJsonSync(n,a,{spaces:4}),this.i18n.notice.result(t("功能_提取_前缀"),!0,`提取译文${Object.keys(a.dict).length-Object.keys(s.dict).length}条`)}catch(e){this.i18n.notice.result(t("功能_提取_前缀"),!1,`${e}`)}else this.i18n.notice.result(t("功能_提取_前缀"),!1,t("功能_提取_通知一"))})}),this.settings.I18N_MODE_NDT&&this.i18n.themeDirectory&&g&&null!=h&&m&&(r&&1!=f||E.controlEl.createEl("button",{text:t(r?"功能_更新_文本":"功能_下载_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",async()=>{let s;"gitee"===this.i18n.settings.I18N_NDT_URL?s=await this.i18n.api.giteeGetTranslation("theme",e.name,p):"github"===this.i18n.settings.I18N_NDT_URL&&(s=await this.i18n.api.githubGetTranslation("theme",e.name,p)),void 0!==s&&null==u&&(u=s.data),void 0!==u&&void 0!==s?(this.i18n.downloadType="1",this.i18n.downloadPath=i,this.i18n.downloadCloudJson=s.data,void 0!==u&&(this.i18n.downloadLocalJson=u),this.i18n.downloadView=this,this.i18n.activateDownloadView(),await this.reloadShowData()):this.i18n.notice.result(t(r?"功能_更新_文本":"功能_下载_文本"),!1,t("功能_下载更新_通知一"))})})),this.settings.I18N_MODE_NIT&&!r&&E.controlEl.createEl("button",{text:"AI",cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},e=>{e.addEventListener("click",async()=>{try{const e=fs4.readFileSync(c).toString(),s=generateTheme(fs4.readJsonSync(l),e);let a=0;for(const e in s.dict){this.i18n.notice.info(t("功能_AI_文本"),`${a+=1}/${Object.keys(s.dict).length}`,this.settings.I18N_NIT_API_INTERVAL);const i=e.match(/title:(.*)|description:(.*)/),n=i?i[1]||i[2]:null;if(n&&i){const t=await ai(this.i18n,n);t&&(i[1]?s.dict[e]=e.replace(i[1],` ${t.data}`):i[2]&&(s.dict[e]=e.replace(i[2],` ${t.data}`))),await sleep(this.settings.I18N_NIT_API_INTERVAL)}}fs4.ensureDirSync(i),fs4.writeJsonSync(n,s,{spaces:4}),d.insert(),d.setType("0"),this.reloadShowData()}catch(e){this.i18n.notice.result(t("功能_AI_文本"),!1,e)}})}),r&&d.isStateDoc&&g&&(0==d.getState()&&E.controlEl.createEl("button",{text:t("功能_替换_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{try{const s=fs4.readJsonSync(n);if(s&&Object.keys(s.dict).every(t=>s.dict[t]===t))return void this.i18n.notice.result(t("功能_替换_前缀"),!1,t("功能_替换_通知一"));fs4.copySync(c,_);let i=fs4.readFileSync(c).toString();i=this.translationMain(s,i),fs4.writeFileSync(c,i),d.setState(!0),d.setPluginVersion(e.version),d.setTranslationVersion(s.manifest.translationVersion),this.enabledPlugins.has(this.i18n.settings.I18N_STYLE_SETTINGS)&&this.reloadPlugin(this.i18n.settings.I18N_STYLE_SETTINGS)}catch(e){this.i18n.notice.result(t("功能_替换_前缀"),!1,e)}this.reloadShowData()})}),1==d.getState()&&E.controlEl.createEl("button",{text:t("功能_还原_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},e=>{e.addEventListener("click",()=>{try{fs4.unlinkSync(c),fs4.renameSync(_,c),d.reset(),this.enabledPlugins.has(this.i18n.settings.I18N_STYLE_SETTINGS)&&this.reloadPlugin(this.i18n.settings.I18N_STYLE_SETTINGS),this.i18n.notice.result(t("功能_还原_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_还原_前缀"),!1,e)}this.reloadShowData()})})),this.developerMode&&E.controlEl.createEl("button",{text:"测试",cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",async()=>{})})}}async reloadShowData(){let t=0;const e=this.contentEl;t=e.scrollTop,e.empty(),await this.showData(),e.scrollTo(0,t)}async onOpen(){await this.showHead(),await this.showData()}async onClose(){this.contentEl.empty()}async reloadPlugin(t){this.enabledPlugins.has(t)&&(await this.app.plugins.disablePlugin(t),await this.app.plugins.enablePlugin(t))}translationMain(e,s){for(const t in e.dict)s=s.replaceAll(t,e.dict[t]);return this.i18n.notice.result(t("功能_替换_前缀"),!0,t("功能_替换_通知二")),s}},I18NPluginModal=class extends import_obsidian20.Modal{constructor(t,e){super(t),this.plugins=[],this.showPlugins=[],this.developerMode=!1,this.i18n=e,this.basePath=path3.normalize(this.app.vault.adapter.getBasePath()),this.settings=e.settings,this.settingPlugins=this.app.setting,this.regexps=[...this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE],...this.i18n.settings.I18N_RE_TEMP.split("|")].filter(t=>""!==t)}async showHead(){var e;const s=this.contentEl.parentElement;s.addClass("i18n__container"),s.removeChild(s.getElementsByClassName("modal-close-button")[0]),null==(e=this.titleEl.parentElement)||e.addClass("i18n__header"),this.contentEl.addClass("i18n__item-box"),this.detailsEl=document.createElement("div"),this.detailsEl.addClass("i18n__item-details"),this.detailsEl.innerText="无",s.appendChild(this.detailsEl);const i=new import_obsidian20.Setting(this.titleEl).setClass("i18n__help").setName(t("通用_标题_文本"));new import_obsidian20.ButtonComponent(i.controlEl).setIcon("i18n_qq").setTooltip(t("通用_QQ_描述")).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).onClick(()=>{window.open(Url.QQ_GROUP)}),this.settings.I18N_MODE_NDT&&new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("i18n-contributor").setTooltip(t("通用_贡献榜_描述")).onClick(()=>{new ContributorModal(this.app,this.i18n).open()}),this.settings.I18N_MODE_LDT&&this.settings.I18N_NAME_TRANSLATION&&new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("name-setting").setTooltip(t("通用_名称翻译_描述")).onClick(()=>{this.close(),new NameTranslationModal(this.app,this.i18n).open()}),new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("settings").setTooltip(t("通用_设置_描述")).onClick(()=>{this.settingPlugins.open(),this.settingPlugins.openTabById(this.i18n.manifest.id),this.close()}),new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("circle-help").setTooltip(t("通用_帮助_描述")).onClick(()=>{new WizardModal(this.app,this.i18n).open()}),new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("palette").setTooltip(t("通用_主题_描述")).onClick(()=>{this.i18n.settings.I18N_MODE=1,this.i18n.saveSettings(),this.i18n.pluginModal.close(),this.i18n.themeModal=new I18NThemeModal(this.app,this.i18n),this.i18n.themeModal.open()}),this.developerMode&&new import_obsidian20.ButtonComponent(i.controlEl).setClass("i18n-button").setClass(`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`).setClass(`is-${this.settings.I18N_BUTTON_SHAPE}`).setIcon("refresh-ccw").setTooltip("刷新插件").onClick(async()=>{this.close(),await this.reloadPlugin(this.i18n.manifest.id)});const n=new import_obsidian20.Setting(this.titleEl).setClass("i18n__search").setName(t("通用_搜索_标题"));n.addDropdown(t=>t.addOptions(I18N_SORT).setValue(this.settings.I18N_SORT).onChange(t=>{this.settings.I18N_SORT=t,this.i18n.saveSettings(),this.reloadShowData()}).selectEl.addClass("i18n-select")),n.addDropdown(t=>t.addOptions(I18N_TYPE).setValue(this.settings.I18N_TYPE).onChange(t=>{this.settings.I18N_TYPE=t,this.i18n.saveSettings(),this.reloadShowData()}).selectEl.addClass("i18n-select")),n.addSearch(t=>t.setValue(this.settings.I18N_SEARCH_TEXT).onChange(t=>{this.settings.I18N_SEARCH_TEXT=t,this.i18n.saveSettings(),this.reloadShowData()}).inputEl.addClass("i18n-input")),this.i18n.settings.I18N_RE_TEMP_MODE&&new import_obsidian20.Setting(this.titleEl).setClass("i18n__search").addText(e=>e.setValue(this.settings.I18N_RE_TEMP).setPlaceholder(t("功能_正则_占位符")).onChange(t=>{this.settings.I18N_RE_TEMP=t,this.i18n.saveSettings(),this.regexps=[...this.settings.I18N_RE_DATAS[this.settings.I18N_RE_MODE],...this.i18n.settings.I18N_RE_TEMP.split("|")].filter(t=>""!==t)}).inputEl.addClass("i18n__re-input","i18n-input")).infoEl.remove()}async showData(){this.plugins=Object.values(this.app.plugins.manifests),this.plugins=this.plugins.filter(t=>t.id!==this.i18n.manifest.id),this.enabledPlugins=this.app.plugins.enabledPlugins,""==this.settings.I18N_SEARCH_TEXT?this.showPlugins=this.plugins:this.showPlugins=this.plugins.filter(t=>-1!=t.name.toLowerCase().indexOf(this.settings.I18N_SEARCH_TEXT.toLowerCase()));const e=[];switch(this.settings.I18N_TYPE){case"0":break;case"1":for(const t of this.showPlugins)fs5.pathExistsSync(path3.join(this.basePath,t.dir,"lang",`${this.settings.I18N_LANGUAGE}.json`))&&e.push(t.id);this.showPlugins=this.showPlugins.filter(t=>!e.includes(t.id));break;case"2":for(const t of this.showPlugins){const s=path3.join(this.basePath,t.dir),i=path3.join(s,"lang","state.json"),n=fs5.pathExistsSync(i);fs5.pathExistsSync(path3.join(s,"lang"))&&n?0!=fs5.readJsonSync(i).state&&e.push(t.id):e.push(t.id)}this.showPlugins=this.showPlugins.filter(t=>!e.includes(t.id));break;case"3":for(const t of this.showPlugins){const s=path3.join(this.basePath,t.dir),i=path3.join(s,"lang","state.json"),n=fs5.pathExistsSync(i);fs5.pathExistsSync(path3.join(s,"lang"))&&n?1!=fs5.readJsonSync(i).state&&e.push(t.id):e.push(t.id)}this.showPlugins=this.showPlugins.filter(t=>!e.includes(t.id))}switch(this.settings.I18N_SORT){case"0":this.showPlugins.sort((t,e)=>t.name.localeCompare(e.name));break;case"1":this.showPlugins.sort((t,e)=>e.name.localeCompare(t.name))}for(const e of this.showPlugins){const s=path3.join(this.basePath,e.dir),i=path3.join(s,"lang"),n=path3.join(s,"lang",`${this.settings.I18N_LANGUAGE}.json`),a=path3.join(s,"lang","state.json"),o=fs5.pathExistsSync(i),r=fs5.pathExistsSync(n),l=path3.join(s,"manifest.json"),c=path3.join(s,"main.js"),_=path3.join(s,"duplicate.js"),d=new State(this.i18n,a);if(o&&!d.isStateDoc&&d.insert(),d.isStateDoc&&d.getState()&&e.version!=d.getPluginVersion())try{fs5.removeSync(_),d.reset(),this.i18n.notice.primary(t("通用_插件更新_前缀"),e.name)}catch(e){this.i18n.notice.error(t("通用_插件更新_前缀"),e)}let u,h,p,m,g=!0,f=!1;if(r)try{u=fs5.readJsonSync(n),g=isValidpluginTranslationFormat(u)}catch(t){g=!1}if(u&&!("translationVersion"in u.manifest)){if(d.getState()){fs5.unlinkSync(c),fs5.renameSync(_,c);const t=fs5.readJsonSync(n),e=fs5.readJsonSync(l);e.description=t.description.original,fs5.writeJsonSync(l,e,{spaces:4}),d.reset(),d.setType("extract"),d.setState(!1),d.setTranslationVersion(0)}u={manifest:{translationVersion:new Date("1949-10-01T15:00:00+08:00").getTime(),pluginVersion:u.manifest.pluginVersion},description:u.description,dict:u.dict},fs5.writeJsonSync(n,u,{spaces:4}),g=!0,this.i18n.notice.result("格式转换",!0,e.name)}this.i18n.pluginDirectoryMark&&(h=this.i18n.pluginDirectory.find(t=>t.id===e.id)),this.i18n.pluginDirectoryMark&&h&&g&&(p=e.version in h.translations?e.version:Object.keys(h.translations).slice(-1)[0],u&&u.manifest.translationVersion<h.translations[p]&&(f=!0)),m=!d.isStateDoc||(!["0","1"].includes(d.getType())||"0"===d.getType());const E=new import_obsidian20.Setting(this.contentEl);E.setClass("i18n__item"),E.nameEl.addClass("i18n__item-title");const T={mark:1,label:{color:"",text:""},text:""};if(d.isStateDoc?u&&g?(1===compareVersions(e.version,u.manifest.pluginVersion)?(T.label.color="warning",T.label.text=t("标签_已过时_文本")):d.getState()?(T.label.color="success",T.label.text=t("标签_已翻译_文本")):(T.label.color="danger",T.label.text=t("标签_未翻译_文本")),T.text=`<span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-primary is-${this.settings.I18N_TAG_SHAPE}">修改日期</span> ${formatTimestamp(fs5.statSync(n).mtimeMs)} <span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-primary is-${this.settings.I18N_TAG_SHAPE}">支持版本</span> ${null==u?void 0:u.manifest.pluginVersion}`):(T.label.color="danger",T.label.text=t("标签_译文有误_文本"),T.text=t("标签_译文有误_描述")):(T.label.color="info",T.label.text=t("标签_无译文_文本"),T.text=t("标签_无译文_描述")),this.i18n.ignoreMark&&this.i18n.ignorePlugins.includes(e.id)&&(T.label.color="primary",T.label.text=t("标签_自带翻译_文本"),T.text=t("标签_自带翻译_描述")),E.nameEl.innerHTML=`<span class="i18n-tag i18n-tag--${this.settings.I18N_TAG_TYPE}-${T.label.color} is-${this.settings.I18N_TAG_SHAPE}">${T.label.text}</span><span class="i18n__item-name">${e.name}</span> <span class="i18n__item-version">[${e.version}]</span> `,E.settingEl.onmouseover=(t=>{this.detailsEl.innerHTML=T.text}),E.settingEl.onmouseout=(e=>{this.detailsEl.innerHTML=t("标签_无_文本")}),this.settings.I18N_OPEN_SETTINGS&&this.enabledPlugins.has(e.id)){const s=new import_obsidian20.ExtraButtonComponent(E.controlEl).setIcon("settings").setTooltip(t("功能_打开_描述")).onClick(()=>{s.setDisabled(!0),this.settingPlugins.open(),this.settingPlugins.openTabById(e.id),this.close()})}const b=new import_obsidian20.ExtraButtonComponent(E.controlEl).setIcon("folder-open").setTooltip(t("功能_打开_描述")).onClick(()=>{b.setDisabled(!0),i18nOpen(this.i18n,s),b.setDisabled(!1)});if(o){const s=new import_obsidian20.ExtraButtonComponent(E.controlEl).setIcon("trash").setTooltip(t("功能_删除_描述")).onClick(()=>{s.setDisabled(!0);try{if(d.stateObj.state){fs5.removeSync(c),fs5.renameSync(_,c);const t=fs5.readJsonSync(n);if(t.hasOwnProperty("description")){const e=fs5.readJsonSync(l);e.description=t.description.original,fs5.writeJsonSync(l,e,{spaces:4})}}fs5.removeSync(i),this.reloadPlugin(e.id),this.i18n.notice.result(t("功能_删除_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_删除_前缀"),!1,e)}this.reloadShowData()})}if(g&&r&&this.settings.I18N_EDIT_MODE&&new import_obsidian20.ExtraButtonComponent(E.controlEl).setIcon("pencil").setTooltip(t("功能_编辑_描述")).onClick(()=>{this.i18n.editorPath=n,this.i18n.editorLoad("plugin",n,d)}),this.settings.I18N_SHARE_MODE&&g&&r){const s=new import_obsidian20.ExtraButtonComponent(E.controlEl);s.setIcon("cloud-upload"),s.setTooltip(t("功能_共享_描述")),s.onClick(async()=>{this.i18n.shareLoad(0,n,e)})}this.settings.I18N_MODE_LDT&&!r&&E.controlEl.createEl("button",{text:t("功能_提取_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{try{const s=fs5.readFileSync(c).toString(),a=fs5.readJsonSync(l),o=generatePlugin(e.version,a,s,this.settings.I18N_RE_LENGTH,this.regexps,this.settings.I18N_RE_FLAGS);fs5.ensureDirSync(i),fs5.writeJsonSync(n,o,{spaces:4}),d.insert(),d.setType("0"),this.i18n.notice.result(t("功能_提取_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_提取_前缀"),!1,`${e}`)}this.reloadShowData()})}),this.settings.I18N_MODE_LDT&&this.settings.I18N_INCREMENTAL_EXTRACTION&&r&&g&&E.controlEl.createEl("button",{text:t("功能_提取_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-info`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{if(d.isStateDoc&&!d.getState())try{const s=fs5.readJsonSync(n),a=generatePlugin(e.version,fs5.readJsonSync(l),fs5.readFileSync(c).toString(),this.settings.I18N_RE_LENGTH,this.regexps,this.settings.I18N_RE_FLAGS);a.manifest=s.manifest,a.description=s.description,a.dict={...a.dict,...s.dict},fs5.ensureDirSync(i),fs5.writeJsonSync(n,a,{spaces:4}),this.i18n.notice.result(t("功能_提取_前缀"),!0,`提取译文${Object.keys(a.dict).length-Object.keys(s.dict).length}条`)}catch(e){this.i18n.notice.result(t("功能_提取_前缀"),!1,`${e}`)}else this.i18n.notice.result(t("功能_提取_前缀"),!1,t("功能_提取_通知一"))})}),this.settings.I18N_MODE_NDT&&this.i18n.pluginDirectory&&g&&null!=h&&m&&(r&&1!=f||E.controlEl.createEl("button",{text:t(r?"功能_更新_文本":"功能_下载_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-success`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",async()=>{let s;"gitee"===this.i18n.settings.I18N_NDT_URL?s=await this.i18n.api.giteeGetTranslation("translation",e.id,p):"github"===this.i18n.settings.I18N_NDT_URL&&(s=await this.i18n.api.githubGetTranslation("translation",e.id,p)),void 0!==s&&null==u&&(u=s.data),void 0!==u&&void 0!==s?(this.i18n.downloadType="0",this.i18n.downloadPath=i,this.i18n.downloadCloudJson=s.data,void 0!==u&&(this.i18n.downloadLocalJson=u),this.i18n.downloadView=this,this.i18n.activateDownloadView(),await this.reloadShowData()):this.i18n.notice.result(t(r?"功能_更新_文本":"功能_下载_文本"),!1,t("功能_下载更新_通知一"))})})),this.settings.I18N_MODE_NIT&&!r&&E.controlEl.createEl("button",{text:"AI",cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",async()=>{try{const s=fs5.readFileSync(c).toString(),a=fs5.readJsonSync(l),o=generatePlugin(e.version,a,s,this.settings.I18N_RE_LENGTH,this.regexps,this.settings.I18N_RE_FLAGS);let r=0;const _=/(['"`])(.*)(\1)/;for(const e in o.dict){this.i18n.notice.info(t("功能_AI_文本"),`${r+=1}/${Object.keys(o.dict).length}`,this.settings.I18N_NIT_API_INTERVAL);const s=e.match(_);if(null!=s){const t=await ai(this.i18n,s[2]);t&&(o.dict[e]=t.state?e.replace(s[2],t.data):e),await sleep(this.settings.I18N_NIT_API_INTERVAL)}}fs5.ensureDirSync(i),fs5.writeJsonSync(n,o,{spaces:4}),d.insert(),d.setType("0"),this.reloadShowData()}catch(e){this.i18n.notice.result(t("功能_AI_文本"),!1,e)}})}),r&&d.isStateDoc&&g&&(0==d.getState()&&E.controlEl.createEl("button",{text:t("功能_替换_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{try{const s=fs5.readJsonSync(n);if(s&&Object.keys(s.dict).every(t=>s.dict[t]===t))return void this.i18n.notice.result(t("功能_替换_前缀"),!1,t("功能_替换_通知一"));fs5.copySync(c,_);let i=fs5.readFileSync(c).toString();i=this.translationMain(s,i),fs5.writeFileSync(c,i);const a=fs5.readJsonSync(l);a.description=s.description.translation,fs5.writeJsonSync(l,a,{spaces:4}),d.setState(!0),d.setPluginVersion(e.version),d.setTranslationVersion(s.manifest.translationVersion),this.enabledPlugins.has(e.id)&&this.reloadPlugin(e.id)}catch(e){this.i18n.notice.result(t("功能_替换_前缀"),!1,e)}this.reloadShowData()})}),1==d.getState()&&E.controlEl.createEl("button",{text:t("功能_还原_文本"),cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-danger`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},s=>{s.addEventListener("click",()=>{try{fs5.unlinkSync(c),fs5.renameSync(_,c);const s=fs5.readJsonSync(n),i=fs5.readJsonSync(l);i.description=s.description.original,fs5.writeJsonSync(l,i,{spaces:4}),d.reset(),this.enabledPlugins.has(e.id)&&this.reloadPlugin(e.id),this.i18n.notice.result(t("功能_还原_前缀"),!0)}catch(e){this.i18n.notice.result(t("功能_还原_前缀"),!1,e)}this.reloadShowData()})})),this.developerMode&&E.controlEl.createEl("button",{text:"测试",cls:["i18n-button",`i18n-button--${this.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",async()=>{})})}}async getContents(t){const e=await this.i18n.api.giteeGetContents(t),s=e.state;let i="",n="";return s&&(i=e.data.sha,e.data.encoding,n=JSON.parse(Buffer.from(e.data.content,"base64").toString("utf8"))),{state:s,count:n,sha:i}}async reloadShowData(){let t=0;const e=this.contentEl;t=e.scrollTop,e.empty(),await this.showData(),e.scrollTo(0,t)}async onOpen(){await this.showHead(),await this.showData()}async onClose(){this.contentEl.empty()}async reloadPlugin(t){this.enabledPlugins.has(t)&&(await this.app.plugins.disablePlugin(t),await this.app.plugins.enablePlugin(t))}translationMain(e,s){for(const t in e.dict)s=s.replaceAll(t,e.dict[t]);return this.i18n.notice.result(t("功能_替换_前缀"),!0,t("功能_替换_通知二")),s}},byteToHex=[];for(i=0;i<256;++i)byteToHex.push((i+256).toString(16).slice(1));function unsafeStringify(t,e=0){return(byteToHex[t[e+0]]+byteToHex[t[e+1]]+byteToHex[t[e+2]]+byteToHex[t[e+3]]+"-"+byteToHex[t[e+4]]+byteToHex[t[e+5]]+"-"+byteToHex[t[e+6]]+byteToHex[t[e+7]]+"-"+byteToHex[t[e+8]]+byteToHex[t[e+9]]+"-"+byteToHex[t[e+10]]+byteToHex[t[e+11]]+byteToHex[t[e+12]]+byteToHex[t[e+13]]+byteToHex[t[e+14]]+byteToHex[t[e+15]]).toLowerCase()}var rnds8=new Uint8Array(16);function rng(){if(!getRandomValues&&!(getRandomValues="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return getRandomValues(rnds8)}var randomUUID="undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),native_default={randomUUID:randomUUID};function v4(t,e,s){if(native_default.randomUUID&&!e&&!t)return native_default.randomUUID();var i=(t=t||{}).random||(t.rng||rng)();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,e){s=s||0;for(var n=0;n<16;++n)e[s+n]=i[n];return e}return unsafeStringify(i)}var v4_default=v4,import_obsidian21=require("obsidian"),Icons=()=>{(0,import_obsidian21.addIcon)("cloud-upload",'<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cloud-upload"><path d="M12 13v8"/><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"/><path d="m8 17 4-4 4 4"/></svg>'),(0,import_obsidian21.addIcon)("circle-help",'<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-help"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>'),(0,import_obsidian21.addIcon)("i18n_translate",'<svg t="1726147647142" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5625" width="100" height="100"><path d="M213.333333 640v85.333333a85.333333 85.333333 0 0 0 78.933334 85.077334L298.666667 810.666667h128v85.333333H298.666667a170.666667 170.666667 0 0 1-170.666667-170.666667v-85.333333h85.333333z m554.666667-213.333333l187.733333 469.333333h-91.946666l-51.242667-128h-174.506667l-51.157333 128h-91.904L682.666667 426.666667h85.333333z m-42.666667 123.093333L672.128 682.666667h106.325333L725.333333 549.76zM341.333333 85.333333v85.333334h170.666667v298.666666H341.333333v128H256v-128H85.333333V170.666667h170.666667V85.333333h85.333333z m384 42.666667a170.666667 170.666667 0 0 1 170.666667 170.666667v85.333333h-85.333333V298.666667a85.333333 85.333333 0 0 0-85.333334-85.333334h-128V128h128zM256 256H170.666667v128h85.333333V256z m170.666667 0H341.333333v128h85.333334V256z" p-id="5626" fill="currentColor"></path></svg>'),(0,import_obsidian21.addIcon)("i18n_qq",'<svg t="1727053933542" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1762" width="100" height="100"><path d="M538 902l-52 0c-12.667 0-21 2.333-25 7-12.667 12.667-30 22.167-52 28.5s-47.333 9.5-76 9.5c-36.667 0-71-6.333-103-19-4-2-7.833-4.167-11.5-6.5s-6.833-4.5-9.5-6.5-5.167-4.333-7.5-7-4.167-5.167-5.5-7.5-2.333-5-3-8-1-6.167-1-9.5c0-9.333 3.5-17.5 10.5-24.5S221 844.667 237 838c9.333-2.667 15.833-6 19.5-10s5.5-9 5.5-15c0-17.333-2-28-6-32-3.333-4-6.667-9-10-15l-12-16c-3.333-6-6.667-10.667-10-14-6.667-12.667-17.333-19-32-19-4 0-7.5 0.5-10.5 1.5s-6.833 2.833-11.5 5.5-8 4.667-10 6c-12.667 12-21.333 18.333-26 19-7.333 0-11.333-15.333-12-46l0-5c0-21.333 4-57.667 12-109 13.333-38.667 34.667-73 64-103 8.667-8 13-18.667 13-32l0-12c0-19.333 6.333-38.667 19-58 3.333-2.667 5.333-5.5 6-8.5s1-6.5 1-10.5l0-7c0-15.333 1-30.167 3-44.5s5.167-28.167 9.5-41.5 9.833-26.333 16.5-39 14.5-25.167 23.5-37.5 19.167-24.167 30.5-35.5c49.333-55.333 113.333-83 192-83 12.667 0 25 0.667 37 2s23.833 3.5 35.5 6.5 23 6.833 34 11.5 21.667 10 32 16 20.5 13 30.5 21 19.667 16.667 29 26c18 20 33 40 45 60s21.5 41.833 28.5 65.5S794 333.333 794 358l0 7c0 8.667 2 15 6 19 12.667 19.333 19 38.667 19 58l0 12c0 17.333 4.333 28 13 32 30 30 51.333 64.333 64 103 14 43.333 18.333 79.667 13 109 0 15.333-1.5 26.833-4.5 34.5s-7.833 13.167-14.5 16.5c-4.667 0-13.333-6.333-26-19-8.667-8.667-19.333-13-32-13l-12 0c-1.333 0-2.667 0.333-4 1s-2.833 1.167-4.5 1.5-3.167 1.167-4.5 2.5l-4 2-3.5 3.5c0 0-1 1.333-3 4s-2.833 4.167-2.5 4.5c-4.667 12.667-15.333 27.667-32 45-10 10-12.333 20.667-7 32 0 6 5.333 13.333 16 22l3 3c29.333 13.333 44.333 28.333 45 45 0 9.333-3 17.5-9 24.5s-15.667 13.833-29 20.5c-12.667 6-28 10.667-46 14s-37 5-57 5c-53.333 0-96-12.667-128-38 4.667 0 3.667-1.333-3-4L538 902zM685 1024c64 0 115-12.667 153-38 20.667-14 36.5-29.333 47.5-46s16.5-35.667 16.5-57c0-23.333-6.333-44.667-19-64l9 0c2 0 4.833-0.5 8.5-1.5s6.5-2.5 8.5-4.5c36-12 57.333-44 64-96 6-44.667-0.333-96-19-154-2.667-8.667-5.833-17.333-9.5-26s-7.667-17-12-25-9.167-16-14.5-24-10.833-15.667-16.5-23-11.833-15-18.5-23c0-28-6.333-58-19-90 0-32-3.667-62.5-11-91.5s-18.5-56.833-33.5-83.5-34.167-51.667-57.5-75c-10-10-20.333-19.167-31-27.5s-21.5-16-32.5-23-22.333-13.333-34-19S640.833 22 628.5 18 603.667 10.667 591 8s-25.5-4.667-38.5-6S526 0 512 0c-32 0-62.5 3.667-91.5 11S363.667 29.5 337 44.5s-51.667 34.167-75 57.5c-68 76-102 159.333-102 250-12.667 25.333-19 55.333-19 90l0 6c-9.333 7.333-17.833 15.833-25.5 25.5s-14.333 19.667-20 30-10.333 20.167-14 29.5S74 552.333 70 563c-18.667 58-25 109.333-19 154 3.333 24.667 10.667 45.167 22 61.5s25.333 27.833 42 34.5c2 0 6.333 1 13 3s11 3 13 3c-12.667 19.333-19 40.667-19 64 0 44 21.333 78.333 64 103 9.333 6 19.5 11.5 30.5 16.5s22.833 9 35.5 12 25.667 5.333 39 7c13.333 1.667 27.333 2.5 42 2.5 70.667 0 124-15 160-45l32 0C567 1009 620.333 1024 685 1024z" fill="currentColor" p-id="1763"></path></svg>'),(0,import_obsidian21.addIcon)("square-check-big",'<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-check-big"><path d="M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5"/><path d="m9 11 3 3L22 4"/></svg>'),(0,import_obsidian21.addIcon)("name-setting",'<svg t="1728458503829" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5311" width="100" height="100"><path d="M85.357714 797.988571h307.2v121.929143h-307.2V797.988571z m0-313.636571h394.971429v121.856h-394.971429V484.352z m0-313.709714h877.714286V292.571429h-877.714286V170.642286zM806.473143 468.114286l10.24 52.516571c23.113143 7.606857 44.105143 20.187429 61.659428 36.425143l49.737143-17.261714 42.934857 76.068571-39.350857 35.328a179.931429 179.931429 0 0 1 0 72.704l39.350857 35.328-42.934857 76.068572-49.737143-17.188572a170.422857 170.422857 0 0 1-61.659428 36.425143l-10.24 52.443429H720.457143l-10.313143-52.516572a170.934857 170.934857 0 0 1-61.659429-36.425143l-49.737142 17.261715-42.934858-76.068572 39.350858-35.328a179.931429 179.931429 0 0 1 0-72.704L555.885714 615.862857l43.008-76.068571 49.664 17.188571a170.422857 170.422857 0 0 1 61.659429-36.425143l10.24-52.443428h86.016z m-43.008 175.542857a43.446857 43.446857 0 0 0-42.934857 43.885714c0 24.210286 19.236571 43.885714 42.934857 43.885714a43.446857 43.446857 0 0 0 43.008-43.885714c0-24.210286-19.236571-43.885714-43.008-43.885714z" fill="currentColor" p-id="5312"></path></svg>'),(0,import_obsidian21.addIcon)("task",'<svg t="1728534890459" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4292" width="100" height="100"><path d="M663.608889 469.959111a22.186667 22.186667 0 0 0-5.176889 0.512 11.150222 11.150222 0 0 0-3.584-0.512H399.189333a10.126222 10.126222 0 0 0-2.218666 0.227556 27.136 27.136 0 0 0-21.390223 7.281777 28.103111 28.103111 0 0 0-8.931555 21.105778 28.387556 28.387556 0 0 0 8.931555 21.105778 27.591111 27.591111 0 0 0 21.390223 7.338667c0.739556 0.113778 1.422222 0.227556 2.218666 0.227555h255.715556a10.979556 10.979556 0 0 0 3.584-0.568889 22.186667 22.186667 0 0 0 5.176889 0.512 27.989333 27.989333 0 0 0 27.192889-28.615111 28.046222 28.046222 0 0 0-27.249778-28.615111z m103.253333 133.290667a27.761778 27.761778 0 0 0-5.233778 0.568889 12.344889 12.344889 0 0 0-3.527111-0.568889h-358.968889c-0.739556 0-1.422222 0.113778-2.218666 0.227555a27.192889 27.192889 0 0 0-21.390222 7.338667 28.387556 28.387556 0 0 0-8.931556 21.105778 28.273778 28.273778 0 0 0 8.931556 21.105778 26.794667 26.794667 0 0 0 21.390222 7.338666c0.739556 0.113778 1.422222 0.227556 2.218666 0.227556h359.025778a13.084444 13.084444 0 0 0 3.584-0.568889 27.591111 27.591111 0 0 0 32.256-28.103111 27.875556 27.875556 0 0 0-27.136-28.672z m0 133.404444a27.648 27.648 0 0 0-5.233778 0.512 12.288 12.288 0 0 0-3.527111-0.512h-358.968889a15.132444 15.132444 0 0 0-2.218666 0.170667 27.591111 27.591111 0 0 0-21.390222 7.338667 28.387556 28.387556 0 0 0-8.931556 21.105777 27.989333 27.989333 0 0 0 30.321778 28.387556 9.500444 9.500444 0 0 0 2.218666 0.227555h359.025778a12.913778 12.913778 0 0 0 3.584-0.455111 27.648 27.648 0 0 0 32.256-28.16 27.875556 27.875556 0 0 0-27.136-28.615111z m197.916445 223.459556V63.715556S966.314667 0 900.266667 0H119.182222C56.035556 0 54.613333 63.715556 54.613333 63.715556v896.512c0-0.113778-0.170667 63.715556 64.568889 63.715555h184.149334l1.820444 0.056889 1.934222-0.056889h593.180445c67.299556 0 64.512-63.886222 64.512-63.829333z m-712.305778 3.128889H140.572444v0.113777c-28.273778 0-30.606222-29.525333-30.606222-29.525333V86.528c0-28.16 30.663111-29.297778 30.663111-29.297778h111.786667l0.056889 906.012445z m628.622222 3.470222H306.744889V57.287111h286.037333v278.072889l85.560889-88.405333 86.357333 88.405333V57.287111h114.346667c32.028444 0 31.459556 29.354667 31.459556 29.354667v850.716444s0.512 29.354667-29.411556 29.354667z" fill="currentColor" p-id="4293"></path></svg>'),(0,import_obsidian21.addIcon)("a-large-small",'<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-a-large-small"><path d="M21 14h-5"/><path d="M16 16v-3.5a2.5 2.5 0 0 1 5 0V16"/><path d="M4.5 13h6"/><path d="m3 16 4.5-9 4.5 9"/></svg>'),(0,import_obsidian21.addIcon)("i18n-review",'<svg t="1732064818725" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  p-id="5189" width="100" height="100"><path d="M296 250c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zM480 394H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z" fill="currentColor" p-id="5190"></path><path d="M432 852H208V148h560v320c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z" fill="currentColor" p-id="5191"></path><path d="M872 764H728v-36.6c46.3-13.8 80-56.6 80-107.4 0-61.9-50.1-112-112-112s-112 50.1-112 112c0 50.7 33.7 93.6 80 107.4V764H520c-8.8 0-16 7.2-16 16v152c0 8.8 7.2 16 16 16h352c8.8 0 16-7.2 16-16V780c0-8.8-7.2-16-16-16zM646 620c0-27.6 22.4-50 50-50s50 22.4 50 50-22.4 50-50 50-50-22.4-50-50z m180 266H566v-60h260v60z" fill="currentColor" p-id="5192"></path></svg>'),(0,import_obsidian21.addIcon)("i18n-contributor",'<svg t="1729676848596" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4535" width="100" height="100"><path d="M983.16843438 31.92001063l-44.12028343-22.05810365C878.4389764 105.02764549 708.40340055 173.63126834 507.70114909 173.63126834c-195.89389684 0-362.44310986-65.41888744-426.69353642-157.03848197l-41.89069684 26.20749977c57.67443505 81.55180576 173.78280475 137.50751488 306.48740073 163.96908804-156.33197079 58.38094623-267.93497025 209.75103282-267.93497024 387.83812157 0 228.72494218 183.69706262 414.09453498 410.35138222 414.09453498 226.65567786 0 422.39468552-185.41714682 422.39468551-414.09453498 0-88.58974697-29.59875589-170.49752553-78.77059584-237.83622253l-41.032013 20.74562242c44.57136429 60.20157159 71.53836369 133.81327573 71.53836368 213.54581442 0 202.72396043-172.52059409 367.04902125-372.25682655 367.04902127-199.74030863 0-361.63605618-164.32506085-361.63605622-367.04902127 0-202.71852602 161.8957489-367.04358685 361.63605622-367.04358685 4.14532001 0 7.0325068-0.10190075 10.06914702-0.25543166C697.16714958 228.26906315 897.00935893 169.17752959 983.16843438 31.92001063L983.16843438 31.92001063zM457.46138942 414.75277009l-140.74524009 140.74524006c-19.42906636 19.43042464-19.42906636 50.94491261 0 70.37533726l140.74524009 140.74524005c19.43042464 19.43042464 50.95034702 19.43042464 70.37533722 0l140.74524008-140.74524005c19.43042464-19.43042464 19.43042464-50.94491261 0-70.37533726l-140.74524008-140.74524006C508.41173642 395.32370373 476.89181404 395.32370373 457.46138942 414.75277009L457.46138942 414.75277009zM620.92776774 622.22666096l-94.70922322 94.66166921c-17.40327994 17.40327994-45.68547845 17.40327994-63.14174683 0l-94.65623479-94.66166921c-17.40327994-17.4494757-17.40327994-45.68004403 0-63.13495281l94.65623479-94.65623479c17.45626837-17.45626837 45.68547845-17.45626837 63.14174683 0l94.70922322 94.65623479C638.38267651 576.54661826 638.38267651 604.77718661 620.92776774 622.22666096L620.92776774 622.22666096zM620.92776774 622.22666096" fill="currentColor" p-id="4536"></path></svg>')},icon_default=Icons,fs6=__toESM(require_lib()),path4=__toESM(require("path")),import_obsidian22=require("obsidian"),EDIT_VIEW_TYPE="i18n-edit-view",EditorView=class extends import_obsidian22.ItemView{constructor(t,e){if(super(t),this.translationDict=[],this.i18n=e,this.path=this.i18n.editorPath,this.type=this.i18n.editorType,this.i18n.editorState){const t=this.i18n.editorState.getType();this.i18n.editorState.setType("0"===t||"1"===t?t:"0")}}async onload(){var e,s;this.i18n.notice.reload();const i=this.contentEl;if(null==(e=i.parentElement)||e.getElementsByClassName("view-header")[0].remove(),i.addClass("i18n-edit__container"),""!==this.type&&""!==this.path&&(this.translationJson=fs6.readJsonSync(this.path),this.stats=fs6.statSync(this.path)),""!==this.type&&""!==this.path&&void 0!==this.translationJson){if("plugin"===this.type&&isPlugin(this.translationJson)){const e=i.createEl("div");e.addClass("i18n-edit__manifest"),e.createEl("span",{text:t("编辑器_行_修改日期"),cls:["i18n-edit__label-wrap"]}),e.createEl("input",{value:formatTimestamp_concise(this.stats.mtimeMs),cls:["i18n-edit__plugin-version-input"]}).disabled=!0,e.createEl("span",{text:t("编辑器_行_源代码"),cls:["i18n-edit__label-wrap"]}),e.createEl("input",{value:this.translationJson.description.original,cls:["i18n-edit__description-input"]}).disabled=!0;const s=i.createEl("div");s.addClass("i18n-edit__manifest"),s.createEl("span",{text:t("编辑器_行_插件版本"),cls:["i18n-edit__label-wrap"]});const n=s.createEl("input",{value:this.translationJson.manifest.pluginVersion,cls:["i18n-edit__plugin-version-input"]});n.addEventListener("input",()=>{this.translationJson.manifest.pluginVersion=n.value}),s.createEl("span",{text:t("编辑器_行_新描述"),cls:["i18n-edit__label-wrap"]});const a=s.createEl("input",{value:this.translationJson.description.translation,cls:["i18n-edit__description-input"]});a.addEventListener("input",()=>{this.translationJson.description.translation=a.value})}if("theme"===this.type&&isTheme(this.translationJson)){const e=i.createEl("div",{cls:["i18n-edit__manifest"]});e.createEl("span",{text:t("编辑器_行_修改日期"),cls:"i18n-edit__label-wrap"}),e.createEl("input",{value:formatTimestamp_concise(this.stats.mtimeMs),cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,e.createEl("span",{text:t("编辑器_行_主题版本"),cls:"i18n-edit__label-wrap"});const s=e.createEl("input",{value:this.translationJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__description-input"]});s.addEventListener("input",()=>{this.translationJson.manifest.pluginVersion=s.value}),e.createEl("span",{text:t("编辑器_行_文件大小"),cls:"i18n-edit__label-wrap"}),e.createEl("input",{value:`${(this.stats.size/1024/1024).toFixed(2)}M`,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}const e=i.createEl("div",{cls:["i18n-edit__dict"]}).createEl("table",{cls:["i18n-edit__table"]});for(const s in this.translationJson.dict)if(this.translationJson.dict.hasOwnProperty(s)){const i=e.createEl("tr");i.addClass("i18n-edit__table-row");const n={key:s,value:this.translationJson.dict[s],el:i};this.translationDict.push(n);const a=i.createEl("td");a.addClass("i18n-edit__table-key");const o=i.createEl("td");o.addClass("i18n-edit__table-value"),o.setAttribute("contenteditable","true"),o.addEventListener("input",()=>{o.textContent&&(n.value=o.textContent);const t=diff2(n.key,n.value);a.innerHTML=t.s1}),o.addEventListener("blur",()=>{const t=diff2(n.key,n.value);a.innerHTML=t.s1,o.innerHTML=t.s2}),this.i18n.settings.I18N_MODE_NIT&&i.createEl("td").createEl("button",{text:t("编辑器_通用_AI_按钮"),cls:["i18n-basic-button","i18n-basic-button--primary"]},async e=>{e.addEventListener("click",async()=>{const e=/(['"`])(.*)(\1)/;if("BAIDU"==this.i18n.settings.I18N_NIT_API){const s=n.key.match(e);if(null!=s){const e=await this.i18n.api.baiduAPI(s[2]),i=e.state?n.key.replace(s[2],e.data):n.key,r=diff2(n.key,i);n.value=i,a.innerHTML=r.s1,o.innerHTML=r.s2,this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_AI_通知一"))}}if("OPENAI"==this.i18n.settings.I18N_NIT_API){const i=n.key.match(e);if(null!=i){const e=await this.i18n.api.openAI("",i[2]);let r="";r="content"in e?s.replace(i[2],e.content):s;const l=diff2(n.key,r);n.value=r,a.innerHTML=l.s1,o.innerHTML=l.s2,this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_AI_通知一"))}}})}),i.createEl("td").createEl("button",{text:t("编辑器_通用_还原_按钮"),cls:["i18n-basic-button","i18n-basic-button--warning"]},async e=>{e.addEventListener("click",async()=>{n.value=n.key,a.textContent=n.key,o.textContent=n.key,this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_还原_通知一"),1e3)})}),i.createEl("td").createEl("button",{text:t("编辑器_通用_删除_按钮"),cls:["i18n-basic-button","i18n-basic-button--danger"]},async e=>{e.addEventListener("click",async()=>{i.remove(),this.translationDict=this.translationDict.filter(t=>t.key!==n.key),this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_删除_通知一"),1e3)})});const r=diff2(n.key,n.value);a.innerHTML=r.s1,o.innerHTML=r.s2}const n=i.createEl("div",{cls:["i18n-edit__search"]}),a=i.createEl("div",{cls:["i18n-edit__operate"]});let o,r=0,l=!1,c=!1;const _=new import_obsidian22.SearchComponent(n);_.onChange(t=>{(o=c?this.translationDict.filter(e=>e.key.includes(t)||e.value.includes(t)):this.translationDict.filter(e=>e.key.toLowerCase().includes(t.toLowerCase())||e.value.toLowerCase().includes(t.toLowerCase()))).length>0&&""!==t?(l=!0,u.setButtonText(`${r+1}/${o.length}`),o[r=0].el.scrollIntoView({behavior:"auto",block:"center"}),o[r].el.classList.remove("animate"),o[r].el.offsetWidth,o[r].el.classList.add("animate")):(l=!1,u.setButtonText("0/0"))}),_.setClass("i18n-edit__search-box"),_.inputEl.addClass("i18n-edit__search-input");const d=new import_obsidian22.ButtonComponent(n).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("a-large-small").setTooltip(t("编辑器_大小_按钮_提示")).onClick(()=>{c?(c=!1,d.buttonEl.removeClass("i18n-button--success"),d.setClass("i18n-button--primary")):(c=!0,d.buttonEl.removeClass("i18n-button--primary"),d.setClass("i18n-button--success"))});new import_obsidian22.ButtonComponent(n).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-up").setTooltip(t("编辑器_上个_按钮_提示")).onClick(()=>{l&&(r>0?r-=1:0==r&&(r=o.length-1),u.setButtonText(`${r+1}/${o.length}`),o[r].el.scrollIntoView({behavior:"auto",block:"center"}),o[r].el.classList.remove("animate"),o[r].el.offsetWidth,o[r].el.classList.add("animate"))}),new import_obsidian22.ButtonComponent(n).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-down").setTooltip(t("编辑器_下个_按钮_提示")).onClick(()=>{l&&(r<o.length-1?r+=1:r==o.length-1&&(r=0),u.setButtonText(`${r+1}/${o.length}`),o[r].el.scrollIntoView({behavior:"auto",block:"center"}),o[r].el.classList.remove("animate"),o[r].el.offsetWidth,o[r].el.classList.add("animate"))});const u=new import_obsidian22.ButtonComponent(n).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("0/0").setTooltip(t("编辑器_数量_按钮_提示")),h=new import_obsidian22.SearchComponent(a);null==(s=h.inputEl.parentElement)||s.addClass("i18n-edit__operate-input");const p=new import_obsidian22.ButtonComponent(a);p.setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left"),p.setButtonText(t("编辑器_新增_按钮")).setTooltip(t("编辑器_新增_按钮_提示")),p.onClick(()=>{const s=h.inputEl.value;if(h.inputEl.value="",void 0===this.translationDict.find(t=>t.key===s)){const i=e.createEl("tr");i.addClass("i18n-edit__table-row");const n={key:s,value:s,el:i};this.translationDict.push(n);const a=i.createEl("td");a.addClass("i18n-edit__table-key");const o=i.createEl("td");o.addClass("i18n-edit__table-value"),o.setAttribute("contenteditable","true"),o.addEventListener("input",()=>{o.textContent&&(n.value=o.textContent);const t=diff2(n.key,n.value);a.innerHTML=t.s1}),o.addEventListener("blur",()=>{const t=diff2(n.key,n.value);a.innerHTML=t.s1,o.innerHTML=t.s2}),this.i18n.settings.I18N_MODE_NIT&&i.createEl("td").createEl("button",{text:"AI",cls:["i18n-basic-button","i18n-basic-button--primary"]},async t=>{t.addEventListener("click",async()=>{const t=/(['"`])(.*)(\1)/;if("BAIDU"==this.i18n.settings.I18N_NIT_API){const e=n.key.match(t);if(null!=e){const t=await this.i18n.api.baiduAPI(e[2]),s=t.state?n.key.replace(e[2],t.data):n.key,i=diff2(n.key,s);n.value=s,a.innerHTML=i.s1,o.innerHTML=i.s2,this.i18n.notice.success("译文编辑器","翻译成功")}}if("OPENAI"==this.i18n.settings.I18N_NIT_API){const e=n.key.match(t);if(null!=e){const t=await this.i18n.api.openAI("",e[2]);let i="";i="content"in t?s.replace(e[2],t.content):s;const r=diff2(n.key,i);n.value=i,a.innerHTML=r.s1,o.innerHTML=r.s2,this.i18n.notice.success("译文编辑器","翻译成功")}}})}),i.createEl("td").createEl("button",{text:"还原",cls:["i18n-basic-button","i18n-basic-button--warning"]},async e=>{e.addEventListener("click",async()=>{n.value=n.key,a.textContent=n.key,o.textContent=n.key,this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_还原_通知一"))})}),i.createEl("td").createEl("button",{text:"删除",cls:["i18n-basic-button","i18n-basic-button--danger"]},async e=>{e.addEventListener("click",async()=>{i.remove(),this.translationDict=this.translationDict.filter(t=>t.key!==n.key),this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_删除_通知一"),1e3)})});const r=diff2(n.key,n.value);a.innerHTML=r.s1,o.innerHTML=r.s2,i.scrollIntoView({behavior:"auto",block:"center"}),this.i18n.notice.success(t("编辑器_通知_前缀"),t("编辑器_新增_通知一"),1e3)}else this.i18n.notice.error(t("编辑器_通知_前缀"),t("编辑器_新增_通知二"))}),new import_obsidian22.ButtonComponent(a).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-danger`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText(t("编辑器_删除_按钮")).setTooltip(t("编辑器_删除_按钮_提示")).onClick(()=>{const e=this.translationDict.length;this.translationDict=this.translationDict.filter(t=>{const e=t.key===t.value;return e&&t.el.remove(),!e});const s=e-this.translationDict.length;this.i18n.notice.success(t("编辑器_通知_前缀"),`共删除${s}项未翻译内容`)}),new import_obsidian22.ButtonComponent(a).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-warning`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText(t("编辑器_源码_按钮")).setTooltip(t("编辑器_源码_按钮_提示")).onClick(async()=>{"plugin"===this.i18n.editorType?i18nOpen(this.i18n,path4.join(this.path.split("\\").slice(0,-2).join("\\"),"main.js")):"theme"===this.i18n.editorType&&i18nOpen(this.i18n,path4.join(this.path.split("\\").slice(0,-2).join("\\"),"theme.css"))}),new import_obsidian22.ButtonComponent(a).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-warning`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText(t("编辑器_译文_按钮")).setTooltip(t("编辑器_译文_按钮_提示")).onClick(async()=>{i18nOpen(this.i18n,path4.join(this.path))}),new import_obsidian22.ButtonComponent(a).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText(t("编辑器_保存_按钮")).setTooltip(t("编辑器_保存_按钮_提示")).onClick(async()=>{try{const e={};this.translationDict.forEach(t=>{e[t.key]=t.value}),this.translationJson.dict=e,console.log(this.translationJson.dict),fs6.writeJsonSync(this.path,this.translationJson,{spaces:4}),this.i18n.notice.result(t("编辑器_通知_前缀"),!0,"")}catch(e){this.i18n.notice.result(t("编辑器_通知_前缀"),!1,e)}});const m=new import_obsidian22.ButtonComponent(a).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("0"===this.i18n.editorState.getType()?"检查更新":"不检查更新").setTooltip(t("编辑器_保存_按钮_提示")).onClick(async()=>{this.i18n.editorState.setType("0"===this.i18n.editorState.getType()?"1":"0"),m.setButtonText("0"===this.i18n.editorState.getType()?"检查更新":"不检查更新")})}else i.createEl("h1",{text:"Oh~抱歉，您的数据走丢了",cls:["i18n-empty"]})}async onunload(){this.i18n.notice.reload(),this.path="",this.contentEl.empty(),this.i18n.detachEditorView()}getViewType(){return EDIT_VIEW_TYPE}getDisplayText(){return t("编辑器_通用_名称")}getIcon(){return"i18n_translate"}},import_obsidian23=require("obsidian"),AgreementModal=class extends import_obsidian23.Modal{constructor(t,e){super(t),this.areement_1=!1,this.areement_2=!1,this.i18n=e}async Main(){this.contentEl.parentElement.addClass("i18n-areement__container"),this.titleEl.innerHTML="使用协议(请仔细阅读)";const t=this.contentEl;t.innerHTML="\n        <p class='i18n-agreement__text'>1. 插件翻译过程涉及直接修改原插件的源代码。尽管i18n工具会事先备份原始文件，但操作仍存在一定风险。</p>\n        <p class='i18n-agreement__text'>2. 强烈建议在首次尝试翻译插件之前，先备份您的插件目录。通常，插件目录位于 .obsidian/plugins 文件夹内。备份可以确保您的数据安全。</p>\n        <p class='i18n-agreement__text'>3. 在进行翻译时，请耐心等待。直到翻译成功或明确得知失败结果后，再继续您的后续操作。</p>\n        <p class='i18n-agreement__text'>4. 若在使用本插件进行翻译后，发现原插件的某些功能受到影响而失效，请卸载重新安装原插件，随后再次检验功能是否恢复正常。若问题依旧存在，再去原插件作者项目地址提交issues，避免因本插件导致原插件失效，去打扰原作者。(重要！重要！重要！)</p>\n        ";const e=new import_obsidian23.Setting(t);e.setClass("i18n-agreement__item"),e.setName("我已知晓风险"),e.addButton(t=>t.setClass("i18n-agreement__check-button").setIcon(this.areement_1?"square-check-big":"square").onClick(()=>{this.areement_1=!this.areement_1,t.setIcon(this.areement_1?"square-check-big":"square")}));const s=new import_obsidian23.Setting(t);s.setClass("i18n-agreement__item"),s.setName("如果遇到翻译后插件失效，点击还原即可恢复正常"),s.addButton(t=>t.setClass("i18n-agreement__check-button").setIcon(this.areement_2?"square-check-big":"square").onClick(()=>{this.areement_2=!this.areement_2,t.setIcon(this.areement_2?"square-check-big":"square")}));const i=new import_obsidian23.Setting(t);i.setClass("i18n-agreement__operate");const n=new import_obsidian23.ButtonComponent(i.controlEl);n.setButtonText("同意"),n.setClass("i18n-button"),n.setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`),n.setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`),n.onClick(async()=>{!0===this.areement_1&&!0===this.areement_2?(this.i18n.notice.result("I18N协议",!0),this.i18n.settings.I18N_AGREEMENT=!0,this.i18n.saveSettings(),await this.app.plugins.disablePlugin("i18n"),await this.app.plugins.enablePlugin("i18n"),this.close()):this.i18n.notice.result("I18N协议",!1,"请勾选使用协议")});const a=new import_obsidian23.ButtonComponent(i.controlEl);a.setButtonText("不同意"),a.setClass("i18n-button"),a.setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-danger`),a.setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`),a.onClick(async()=>{await this.app.plugins.disablePlugin("i18n"),this.close()})}async onOpen(){await this.Main()}async onClose(){this.contentEl.empty()}},Commands=(e,s)=>{s.addCommand({id:"i18n-translate",name:t("命令_打开翻译面板"),callback:()=>{new I18NPluginModal(e,s).open()}}),s.addCommand({id:"i18n-help",name:t("命令_打开帮助面板"),callback:()=>{new WizardModal(e,s).open()}})},command_default=Commands,fs7=__toESM(require_lib()),import_obsidian25=require("obsidian"),import_obsidian24=require("obsidian"),AgreementConfirmModal=class extends import_obsidian24.Modal{constructor(t,e,s){super(t),this.name="",this.i18n=e,this.onSubmit=s}async showHead(){const t=this.contentEl.parentElement;t.addClass("i18n-share-history__container"),t.removeChild(t.getElementsByClassName("modal-close-button")[0]),this.titleEl.addClass("i18n-share-history__title-box");const e=new import_obsidian24.Setting(this.titleEl);e.setClass("i18n-share-history__title"),e.setName("提交译文")}async showMain(){new import_obsidian24.Setting(this.contentEl).setName("请输入").addText(t=>t.setValue("我已仔细检查译文").setDisabled(!0)),new import_obsidian24.Setting(this.contentEl).setName("请输入").addText(t=>t.onChange(t=>{this.name=t}));const t=new import_obsidian24.Setting(this.contentEl);t.controlEl.createEl("button",{text:"确认",cls:["i18n-button",`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`,`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",()=>{this.onSubmit(this.name),this.close()})}),t.controlEl.createEl("button",{text:"取消",cls:["i18n-button",`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-info`,`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`]},t=>{t.addEventListener("click",()=>{this.close()})})}async onOpen(){await this.showHead(),await this.showMain()}async onClose(){this.contentEl.empty()}},SHARE_VIEW_TYPE="i18n-share-view",ShareView=class extends import_obsidian25.ItemView{constructor(t,e){super(t),this.directory=[],this.i18n=e,this.i18n.notice.reload(),this.type=this.i18n.shareType,this.path=this.i18n.sharePath,0===this.type?this.pluginObj=this.i18n.shareObj:this.themeObj=this.i18n.shareObj}async onload(){var t;const e=this.contentEl;if(e.addClass("i18n-review__container"),null==(t=e.parentElement)||t.getElementsByClassName("view-header")[0].remove(),""!==this.path&&(this.localJson=fs7.readJsonSync(this.path)),0===this.type&&isPlugin(this.localJson)){this.directory=(await this.i18n.api.giteeGetFile(`translation/directory/${this.i18n.settings.I18N_LANGUAGE}.json`)).data;const t=this.directory.find(t=>t.id===this.pluginObj.id);if(this.localJson&&t)this.localJson.manifest.pluginVersion in t.translations?(this.submissionType=2,this.cloudJson=(await this.i18n.api.giteeGetTranslation("translation",this.pluginObj.id,this.localJson.manifest.pluginVersion)).data):(this.submissionType=1,this.cloudJson=(await this.i18n.api.giteeGetTranslation("translation",this.pluginObj.id,Object.keys(t.translations).slice(-1)[0])).data);else if(this.localJson){this.submissionType=1;const t={manifest:{translationVersion:0,pluginVersion:this.localJson.manifest.pluginVersion},description:{original:this.localJson.description.original,translation:this.localJson.description.translation},dict:{}};this.cloudJson=t}}if(1===this.type&&isTheme(this.localJson)){this.directory=(await this.i18n.api.giteeGetFile(`theme/directory/${this.i18n.settings.I18N_LANGUAGE}.json`)).data;const t=this.directory.find(t=>t.id===this.themeObj.name);if(t&&this.localJson)this.localJson.manifest.pluginVersion in t.translations?(this.submissionType=2,this.cloudJson=(await this.i18n.api.giteeGetTranslation("theme",this.themeObj.name,this.localJson.manifest.pluginVersion)).data):(this.submissionType=1,this.cloudJson=(await this.i18n.api.giteeGetTranslation("theme",this.themeObj.name,Object.keys(t.translations).slice(-1)[0])).data);else if(this.localJson){this.submissionType=1;const t={manifest:{translationVersion:0,pluginVersion:this.localJson.manifest.pluginVersion},dict:{}};this.cloudJson=t}}if(null!=this.localJson&&void 0!==this.cloudJson){const t=this.getSubmissionTypeName();if(0===this.type&&isPlugin(this.localJson)&&isPlugin(this.cloudJson)){const s=e.createEl("div",{cls:["i18n-edit__manifest"]});s.createEl("span",{text:"提交类型",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:t,cls:["i18n-input","i18n-edit__plugin-version-input"]}).disabled=!0,s.createEl("span",{text:"源代码",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.localJson.description.original,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0;const i=e.createEl("div",{cls:["i18n-edit__manifest"]});i.createEl("span",{text:"插件标识",cls:"i18n-edit__label-wrap"}),i.createEl("input",{value:this.pluginObj.id,cls:"i18n-edit__plugin-version-input"}).disabled=!0,i.createEl("span",{text:"旧描述",cls:"i18n-edit__label-wrap"}),i.createEl("input",{value:this.cloudJson.description.translation,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0;const n=e.createEl("div",{cls:["i18n-edit__manifest"]});n.createEl("span",{text:"插件版本",cls:"i18n-edit__label-wrap"}),n.createEl("input",{value:this.localJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__plugin-version-input"]}).disabled=!0,n.createEl("span",{text:"新描述",cls:"i18n-edit__label-wrap"}),n.createEl("input",{value:this.localJson.description.translation,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}if(1===this.type&&isTheme(this.localJson)&&isTheme(this.cloudJson)){const s=e.createEl("div",{cls:["i18n-edit__manifest"]});s.createEl("span",{text:"提交类型",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:t,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,s.createEl("span",{text:"主题标识",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.themeObj.name,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,s.createEl("span",{text:"主题版本",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.localJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}let s;0===this.type&&isPlugin(this.cloudJson)&&isPlugin(this.localJson)&&(s=comparePlugin(this.cloudJson,this.localJson)),1===this.type&&isTheme(this.cloudJson)&&isTheme(this.localJson)&&(s=compareTheme(this.cloudJson,this.localJson));const i=e.createEl("div",{cls:["i18n-edit__dict"]});if(s){const t=i.createEl("table",{cls:["i18n-edit__table"]});for(const e in s.modified){const i=diff2(s.modified[e].oldValue,s.modified[e].newValue),n=t.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns-added"]});n.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"修改",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-warning`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=e,n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s1,n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s2}for(const e in s.added){const i=diff2(e,s.added[e]),n=t.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]});n.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"新增",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-success`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s1,n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s2}for(const e in s.removed){const i=diff2(e,s.removed[e]),n=t.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]});n.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"删除",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-danger`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s1,n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s2}for(const e in s.unchanged){const i=diff2(e,s.unchanged[e]),n=t.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]});n.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"初始",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-info`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s1,n.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=i.s2}}const n=e.createEl("div",{cls:["i18n-edit__search"]}),a=new import_obsidian25.SearchComponent(n).onChange(t=>{});a.setClass("i18n-edit__search-box"),a.inputEl.addClass("i18n-input","i18n-edit__search-input"),new import_obsidian25.ButtonComponent(n).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText(t).onClick(()=>{new AgreementConfirmModal(this.app,this.i18n,async t=>{if("我已仔细检查译文"===t){const t=await this.i18n.api.giteeGetAllIssue();let e;if(t.state&&(e=t.data.some(t=>t.title===`[${this.type}${this.submissionType}][${this.i18n.settings.I18N_LANGUAGE}][${0===this.type?this.pluginObj.id:this.themeObj.name}]`)),e)this.i18n.notice.result("共建云端",!1,"同一类型只能存在一个提交");else{const t=await this.i18n.api.giteePostIssue(`[${this.type}${this.submissionType}][${this.i18n.settings.I18N_LANGUAGE}][${0===this.type?this.pluginObj.id:this.themeObj.name}]`,deflate(JSON.stringify(this.localJson)),"");(null==t?void 0:t.state)&&(this.i18n.settings.I18N_ADMIN_VERIFY&&this.i18n.settings.I18N_ADMIN_MODE?(this.i18n.issue=t.data,this.i18n.activateAdminView()):window.open(`https://gitee.com/zero--two/obsidian-i18n-translation/issues/${t.data.number}`),a.getValue()&&await this.i18n.api.giteePostIssueComments(t.data.number,a.getValue())),this.i18n.notice.result("共建云端",!0)}}else this.i18n.notice.result("共建云端",!1,'请输入 "我已仔细检查译文"')}).open()})}else e.createEl("h1",{text:"Oh~抱歉，您的数据走丢了",cls:["i18n-empty"]})}async onunload(){this.contentEl.empty()}getViewType(){return SHARE_VIEW_TYPE}getDisplayText(){return"共建云端"}getIcon(){return"i18n_translate"}getTypeName(){let t="";switch(this.type){case 0:t="plugin";break;case 1:t="theme";break;default:t="未知类型"}return t}getSubmissionTypeName(){let t="";switch(this.submissionType){case 0:t="标记汉化";break;case 1:t=`提交${0===this.type?"插件":"主题"}`;break;case 2:t=`更新${0===this.type?"插件":"主题"}`;break;default:t="未知提交类型"}return t}},fs8=__toESM(require_lib()),path5=__toESM(require("path")),import_obsidian26=require("obsidian"),ADMIN_VIEW_TYPE="i18n-admin-view",AdminView=class extends import_obsidian26.ItemView{constructor(t,e){if(super(t),this.updateTranslationObj=[],this.errorView=!1,this.i18n=e,this.i18n.notice.reload(),this.i18n.issue){this.issueJson=JSON.parse(inflate(this.i18n.issue.body));const[t,e,s]=parseIssueTitle(this.i18n.issue.title);this.id=s,this.language=e,this.type=t[0],this.submissionType=t[1]}else this.errorView=!0}async onload(){var t;const e=this.contentEl;if(e.addClass("i18n-review__container"),null==(t=e.parentElement)||t.getElementsByClassName("view-header")[0].remove(),this.errorView)e.createEl("h1",{text:"Oh~抱歉，您的数据走丢了",cls:["i18n-empty"]});else{if("0"===this.type&&isPlugin(this.issueJson))if("0"===this.submissionType);else if("1"===this.submissionType){const t=(await this.i18n.api.giteeGetDirectoryAdmin("translation",this.language)).data.find(t=>t.id===this.id);if(t)"gitee"===this.i18n.settings.I18N_NDT_URL?this.cloudJson=(await this.i18n.api.giteeGetTranslation("translation",this.id,Object.keys(t.translations).slice(-1)[0])).data:"github"===this.i18n.settings.I18N_NDT_URL&&(this.cloudJson=(await this.i18n.api.githubGetTranslation("translation",this.id,Object.keys(t.translations).slice(-1)[0])).data);else{const t={manifest:{translationVersion:0,pluginVersion:this.issueJson.manifest.pluginVersion},description:{original:this.issueJson.description.original,translation:this.issueJson.description.translation},dict:{}};this.cloudJson=t}}else"2"===this.submissionType&&(this.cloudJson=(await this.i18n.api.giteeGetTranslation("translation",this.id,this.issueJson.manifest.pluginVersion)).data);if("1"===this.type&&isTheme(this.issueJson))if("0"===this.submissionType);else if("1"===this.submissionType){const t=(await this.i18n.api.giteeGetDirectoryAdmin("theme",this.language)).data.find(t=>t.id===this.id);if(t)"gitee"===this.i18n.settings.I18N_NDT_URL?this.cloudJson=(await this.i18n.api.giteeGetTranslation("theme",this.id,Object.keys(t.translations).slice(-1)[0])).data:"github"===this.i18n.settings.I18N_NDT_URL&&(this.cloudJson=(await this.i18n.api.githubGetTranslation("theme",this.id,Object.keys(t.translations).slice(-1)[0])).data);else{const t={manifest:{translationVersion:0,pluginVersion:this.issueJson.manifest.pluginVersion},dict:{}};this.cloudJson=t}}else"2"===this.submissionType&&(this.cloudJson=(await this.i18n.api.giteeGetTranslation("theme",this.id,this.issueJson.manifest.pluginVersion)).data);let t;if("0"===this.type&&isPlugin(this.issueJson)&&isPlugin(this.cloudJson)&&(t=comparePlugin(this.cloudJson,this.issueJson)),"1"===this.type&&isTheme(this.issueJson)&&isTheme(this.cloudJson)&&(t=compareTheme(this.cloudJson,this.issueJson)),"0"===this.type&&isPlugin(this.issueJson)&&isPlugin(this.cloudJson)){const t=e.createEl("div",{cls:["i18n-edit__manifest"]});t.createEl("span",{text:"译文作者",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.i18n.issue.user.name,cls:["i18n-input","i18n-edit__plugin-version-input"]}).disabled=!0,t.createEl("span",{text:"源代码",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.issueJson.description.original,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0;const s=e.createEl("div",{cls:["i18n-edit__manifest"]});s.createEl("span",{text:"插件标识",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.id,cls:"i18n-edit__plugin-version-input"}).disabled=!0,s.createEl("span",{text:"旧描述",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.cloudJson.description.translation,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0;const i=e.createEl("div",{cls:["i18n-edit__manifest"]});i.createEl("span",{text:"插件版本",cls:"i18n-edit__label-wrap"}),i.createEl("input",{value:this.issueJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__plugin-version-input"]}).disabled=!0,i.createEl("span",{text:"新描述",cls:"i18n-edit__label-wrap"}),i.createEl("input",{value:this.issueJson.description.translation,cls:["i18n-input","i18n-edit__description-input"]},t=>{t.addEventListener("input",()=>{this.issueJson.description.translation=t.value})})}if("1"===this.type&&isTheme(this.issueJson)&&isTheme(this.cloudJson)){const t=e.createEl("div",{cls:["i18n-edit__manifest"]});t.createEl("span",{text:"译文作者",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.i18n.issue.user.name,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,t.createEl("span",{text:"主题标识",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.id,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,t.createEl("span",{text:"主题版本",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.issueJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}const s=e.createEl("div",{cls:["i18n-edit__dict"]});if(t&&null!=this.issueJson&&void 0!==this.cloudJson){const e=s.createEl("table",{cls:["i18n-edit__table"]});for(const s in t.modified){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns-added"]}),n={type:"modified",key:s,value:t.modified[s].newValue,el:i,state:!0};this.updateTranslationObj.push(n);const a=diff2(n.key,n.value);i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"修改",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-warning`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]});const o=i.createEl("td",{cls:["i18n-review__cell"]});o.innerHTML=a.s1,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=t.modified[s].oldValue;const r=i.createEl("td",{cls:["i18n-review__cell"],attr:{contenteditable:"true"}});r.innerHTML=a.s2,r.addEventListener("input",()=>{r.textContent&&(n.value=r.textContent);const t=diff2(n.key,n.value);o.innerHTML=t.s1}),r.addEventListener("blur",()=>{const t=diff2(n.key,n.value);o.innerHTML=t.s1,r.innerHTML=t.s2}),i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"否决":"批准",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"否决":"批准"})})}for(const s in t.added){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"added",key:s,value:t.added[s],el:i,state:!0};this.updateTranslationObj.push(n),i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"新增",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-success`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]});const a=i.createEl("td",{cls:["i18n-review__cell"]}),o=i.createEl("td",{cls:["i18n-review__cell"],attr:{contenteditable:"true"}});o.addEventListener("input",()=>{o.textContent&&(n.value=o.textContent);const t=diff2(n.key,n.value);a.innerHTML=t.s1}),o.addEventListener("blur",()=>{const t=diff2(n.key,n.value);a.innerHTML=t.s1,o.innerHTML=t.s2}),i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"否决":"批准",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"否决":"批准"})});const r=diff2(n.key,n.value);a.innerHTML=r.s1,o.innerHTML=r.s2}for(const s in t.removed){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"removed",key:s,value:t.removed[s],el:i,state:!0};this.updateTranslationObj.push(n),i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"删除",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-danger`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]});const a=i.createEl("td",{cls:["i18n-review__cell"]}),o=i.createEl("td",{cls:["i18n-review__cell"]});i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"否决":"批准",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"否决":"批准"})});const r=diff2(n.key,n.value);a.innerHTML=r.s1,o.innerHTML=r.s2}for(const s in t.unchanged){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"unchanged",key:s,value:t.unchanged[s],el:i,state:!0};this.updateTranslationObj.push(n),i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"初始",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-info`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]});const a=i.createEl("td",{cls:["i18n-review__cell"]}),o=i.createEl("td",{cls:["i18n-review__cell"]});i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"否决":"批准",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"否决":"批准"})});const r=diff2(n.key,n.value);a.innerHTML=r.s1,o.innerHTML=r.s2}}const i=e.createEl("div",{cls:["i18n-edit__search"]});let n,a=0,o=!1,r=!1;const l=new import_obsidian26.SearchComponent(i).onChange(t=>{(n=r?this.updateTranslationObj.filter(e=>e.key.includes(t)||e.value.includes(t)):this.updateTranslationObj.filter(e=>e.key.toLowerCase().includes(t.toLowerCase())||e.value.toLowerCase().includes(t.toLowerCase()))).length>0&&""!==t?(o=!0,_.setButtonText(`${a+1}/${n.length}`),n[a=0].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate")):(o=!1,_.setButtonText("0/0"))});l.setClass("i18n-edit__search-box"),l.inputEl.addClass("i18n-input","i18n-edit__search-input");const c=new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("a-large-small").onClick(()=>{r?(r=!1,c.buttonEl.removeClass("i18n-button--success"),c.setClass("i18n-button--primary")):(r=!0,c.buttonEl.removeClass("i18n-button--primary"),c.setClass("i18n-button--success"))});new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-up").onClick(()=>{o&&(a>0?a-=1:0==a&&(a=n.length-1),_.setButtonText(`${a+1}/${n.length}`),n[a].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate"))}),new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-down").onClick(()=>{o&&(a<n.length-1?a+=1:a==n.length-1&&(a=0),_.setButtonText(`${a+1}/${n.length}`),n[a].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate"))});const _=new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("0/0");new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("测试").onClick(async()=>{const t=[...this.updateTranslationObj.filter(t=>"unchanged"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"modified"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"added"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"removed"===t.type&&!1===t.state).map(t=>[t.key,t.value])];this.issueJson.dict=Object.fromEntries(t);const e=path5.join(path5.normalize(this.app.vault.adapter.getBasePath()),".obsidian","0"===this.type?"plugins":"themes",this.id);fs8.pathExistsSync(e)?(fs8.ensureDirSync(path5.join(e,"lang")),fs8.writeJSONSync(path5.join(e,"lang",`${this.language}.test.json`),this.issueJson,{spaces:4}),i18nOpen(this.i18n,path5.join(e,"lang")),this.i18n.notice.result("审核",!0,"下载成功")):this.i18n.notice.result("审核",!1,`请确保 ${"0"===this.type?"插件":"主题"} [${this.id}] 已经安装`)}),new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-danger`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("关闭").onClick(async()=>{this.closeIssue()}),new import_obsidian26.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("合并").onClick(async()=>{this.merge()})}}async onunload(){this.i18n.notice.reload(),this.contentEl.empty()}async closeIssue(){const t=await this.i18n.api.giteePatchIssue(this.i18n.issue.number,"closed");t.state?(this.i18n.notice.result("审核",!0),this.i18n.issues=this.i18n.issues.filter(t=>t.number!==this.i18n.issue.number),this.i18n.admin.reloadShowData()):this.i18n.notice.result("审核",!1,t.data)}async merge(){const t=this.updateTranslationObj.filter(t=>"modified"===t.type&&!0===t.state).map(t=>[t.key,t.value]),e=this.updateTranslationObj.filter(t=>"unchanged"===t.type&&!0===t.state).map(t=>[t.key,t.value]),s=this.updateTranslationObj.filter(t=>"added"===t.type&&!0===t.state).map(t=>[t.key,t.value]),i=this.updateTranslationObj.filter(t=>"removed"===t.type&&!1===t.state).map(t=>[t.key,t.value]),n=this.updateTranslationObj.filter(t=>"removed"===t.type&&!0===t.state).map(t=>[t.key,t.value]),a=[...e,...t,...s,...i];this.issueJson.manifest.translationVersion=Date.now(),this.issueJson.dict=Object.fromEntries(a);const o="0"===this.type?"translation":"theme",r=await this.getContents(`${o}/directory/${this.language}.json`);if(!r.state)return void this.i18n.notice.result("Gitee",!1,"获取文件失败");const l=await this.i18n.api.giteeUpdateFileContent(`${o}/directory/${this.language}.json`,this.updateDirectory(r.count),r.sha,`#${this.i18n.issue.number} - 第1步: 写入目录`);if(!l.state)return void this.i18n.notice.result("审核",!1,`无法写入目录\n${l.data}\n请前往gitee手动还原后重试`);this.i18n.notice.result("审核",!0,"第1步: 写入目录\n请耐心等待审核流程运行完毕"),await sleep(1e3);const c=await this.getContents(`translation/contributor/${this.language}.json`);if(!c.state)return void this.i18n.notice.result("Gitee",!1,"获取文件失败");const _=await this.i18n.api.giteeUpdateFileContent(`translation/contributor/${this.language}.json`,this.updateContributor(c.count,t,s,n),c.sha,`#${this.i18n.issue.number} - 第2步: 写入贡献`);if(!_.state)return void this.i18n.notice.result("审核",!1,`无法写入贡献\n${_.data}\n请前往gitee手动还原后重试`);if(this.i18n.notice.result("审核",!0,"第2步: 写入目录\n请耐心等待审核流程运行完毕"),await sleep(1e3),"1"===this.submissionType){const t=await this.i18n.api.giteeCreateFileContent(`${o}/dict/${this.id}/${this.language}/${this.issueJson.manifest.pluginVersion}.json`,Buffer.from(JSON.stringify(this.issueJson,null,4)).toString("base64"),`#${this.i18n.issue.number} - 第3步: 写入译文`);if(!t.state)return void this.i18n.notice.result("审核",!1,`无法写入译文\n${t.data}\n请前往gitee手动还原后重试`);this.i18n.notice.result("审核",!0,"第3步: 写入译文\n请耐心等待审核流程运行完毕")}else if("2"===this.submissionType){const t=await this.getContents(`${o}/dict/${this.id}/${this.language}/${this.issueJson.manifest.pluginVersion}.json`);if(!t.state)return void this.i18n.notice.result("Gitee",!1,"获取文件失败");const e=await this.i18n.api.giteeUpdateFileContent(`${o}/dict/${this.id}/${this.language}/${this.issueJson.manifest.pluginVersion}.json`,Buffer.from(JSON.stringify(this.issueJson,null,4)).toString("base64"),t.sha,`#${this.i18n.issue.number} - 第3步: 写入译文`);if(!e.state)return void this.i18n.notice.result("审核",!1,`无法写入译文\n${e.data}\n请前往gitee手动还原后重试`);this.i18n.notice.result("审核",!0,"第3步: 写入译文\n请耐心等待审核流程运行完毕")}await sleep(1e3);const d=await this.i18n.api.giteePatchIssue(this.i18n.issue.number,"closed");d.state?(this.i18n.notice.result("审核",!0,"第4步: 关闭Issue"),this.i18n.issues=this.i18n.issues.filter(t=>t.number!==this.i18n.issue.number),this.i18n.admin.reloadShowData()):this.i18n.notice.result("审核",!1,`无法关闭Issue${d.data}\n请前往gitee手动关闭`)}async getContents(t){const e=await this.i18n.api.giteeGetContents(t),s=e.state;let i="",n=[];return s&&(i=e.data.sha,n=JSON.parse(Buffer.from(e.data.content,e.data.encoding).toString("utf8"))),{state:s,count:n,sha:i}}updateDirectory(t){const e=t.find(t=>t.id===this.id),s={...(null==e?void 0:e.translations)||{},[this.issueJson.manifest.pluginVersion]:this.issueJson.manifest.translationVersion};return e?e.translations=s:t.push({id:this.id,translations:s}),Buffer.from(JSON.stringify(t,null,4)).toString("base64")}updateContributor(t,e,s,i){const n=t.find(t=>t.login===this.i18n.issue.user.login);if(n)n.translation+=s.length,n.modification+=e.length,n.erasure+=i.length;else{const n={login:this.i18n.issue.user.login,name:this.i18n.issue.user.name,url:this.i18n.issue.user.html_url,translation:s.length,modification:e.length,erasure:i.length};t.push(n)}return Buffer.from(JSON.stringify(t,null,4)).toString("base64")}getViewType(){return ADMIN_VIEW_TYPE}getDisplayText(){return"审核面板"}getIcon(){return"i18n_translate"}},fs9=__toESM(require_lib()),path6=__toESM(require("path")),import_obsidian27=require("obsidian"),DOWNLOAD_VIEW_TYPE="i18n-download-view",DownloadView=class extends import_obsidian27.ItemView{constructor(t,e){super(t),this.updateTranslationObj=[],this.errorView=!1,this.i18n=e,this.i18n.notice.reload(),this.langDoc=this.i18n.downloadPath,this.localJson=this.i18n.downloadLocalJson,this.cloudJson=this.i18n.downloadCloudJson}async onload(){var t;const e=this.contentEl;if(e.addClass("i18n-review__container"),null==(t=e.parentElement)||t.getElementsByClassName("view-header")[0].remove(),this.errorView)e.createEl("h1",{text:"Oh~抱歉，您的数据走丢了",cls:["i18n-empty"]});else{let t;if("0"===this.i18n.downloadType&&isPlugin(this.localJson)&&isPlugin(this.cloudJson)&&(t=comparePlugin(this.localJson,this.cloudJson)),"1"===this.i18n.downloadType&&isTheme(this.localJson)&&isTheme(this.cloudJson)&&(t=compareTheme(this.localJson,this.cloudJson)),"0"===this.i18n.downloadType&&isPlugin(this.localJson)&&isPlugin(this.cloudJson)){const t=e.createEl("div",{cls:["i18n-edit__manifest"]});t.createEl("span",{text:"译文版本",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.cloudJson.manifest.translationVersion.toString(),cls:["i18n-input","i18n-edit__plugin-version-input"]}).disabled=!0,t.createEl("span",{text:"源代码",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.cloudJson.description.original,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0;const s=e.createEl("div",{cls:["i18n-edit__manifest"]});s.createEl("span",{text:"插件版本",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.cloudJson.manifest.pluginVersion,cls:"i18n-edit__plugin-version-input"}).disabled=!0,s.createEl("span",{text:"新描述",cls:"i18n-edit__label-wrap"}),s.createEl("input",{value:this.cloudJson.description.translation,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}if("1"===this.i18n.downloadType&&isTheme(this.localJson)&&isTheme(this.cloudJson)){const t=e.createEl("div",{cls:["i18n-edit__manifest"]});t.createEl("span",{text:"译文版本",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.cloudJson.manifest.translationVersion.toString(),cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0,t.createEl("span",{text:"主题版本",cls:"i18n-edit__label-wrap"}),t.createEl("input",{value:this.cloudJson.manifest.pluginVersion,cls:["i18n-input","i18n-edit__description-input"]}).disabled=!0}const s=e.createEl("div",{cls:["i18n-edit__dict"]});if(t&&null!=this.localJson&&void 0!==this.cloudJson){const e=s.createEl("table",{cls:["i18n-edit__table"]});for(const s in t.modified){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns-added"]}),n={type:"modified",key:s,value:t.modified[s].newValue,el:i,state:!0};this.updateTranslationObj.push(n);const a=diff2(n.key,n.value);i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"修改",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-warning`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s1,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=t.modified[s].oldValue,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s2,i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"禁止下载":"允许下载",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"禁止下载":"允许下载"})})}for(const s in t.added){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"added",key:s,value:t.added[s],el:i,state:!0};this.updateTranslationObj.push(n);const a=diff2(n.key,n.value);i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"新增",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-success`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s1,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s2,i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"禁止下载":"允许下载",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"禁止下载":"允许下载"})})}for(const s in t.removed){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"removed",key:s,value:t.removed[s],el:i,state:!0};this.updateTranslationObj.push(n);const a=diff2(n.key,n.value);i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"删除",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-danger`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s1,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s2,i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"禁止下载":"允许下载",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"禁止下载":"允许下载"})})}for(const s in t.unchanged){const i=e.createEl("tr",{cls:["i18n-review__row","i18n-review__row-columns"]}),n={type:"unchanged",key:s,value:t.unchanged[s],el:i,state:!0};this.updateTranslationObj.push(n);const a=diff2(n.key,n.value);i.createEl("td",{cls:["i18n-review__cell"]}).createEl("span",{text:"初始",cls:["i18n-tag",`i18n-tag--${this.i18n.settings.I18N_TAG_TYPE}-info`,`is-${this.i18n.settings.I18N_TAG_SHAPE}`]}),i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s1,i.createEl("td",{cls:["i18n-review__cell"]}).innerHTML=a.s2,i.createEl("td",{cls:["i18n-review__cell"]}).createEl("button",{text:n.state?"禁止下载":"允许下载",cls:["i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"]},async t=>{t.addEventListener("click",async()=>{t.removeClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),n.state=!n.state,t.addClass("i18n-basic-button",n.state?"i18n-basic-button--danger":"i18n-basic-button--success"),t.textContent=n.state?"禁止下载":"允许下载"})})}}const i=e.createEl("div",{cls:["i18n-edit__search"]});let n,a=0,o=!1,r=!1;const l=new import_obsidian27.SearchComponent(i).onChange(t=>{(n=r?this.updateTranslationObj.filter(e=>e.key.includes(t)||e.value.includes(t)):this.updateTranslationObj.filter(e=>e.key.toLowerCase().includes(t.toLowerCase())||e.value.toLowerCase().includes(t.toLowerCase()))).length>0&&""!==t?(o=!0,_.setButtonText(`${a+1}/${n.length}`),n[a=0].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate")):(o=!1,_.setButtonText("0/0"))});l.setClass("i18n-edit__search-box"),l.inputEl.addClass("i18n-input","i18n-edit__search-input");const c=new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("a-large-small").onClick(()=>{r?(r=!1,c.buttonEl.removeClass("i18n-button--success"),c.setClass("i18n-button--primary")):(r=!0,c.buttonEl.removeClass("i18n-button--primary"),c.setClass("i18n-button--success"))});new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-up").onClick(()=>{o&&(a>0?a-=1:0==a&&(a=n.length-1),_.setButtonText(`${a+1}/${n.length}`),n[a].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate"))}),new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setIcon("arrow-down").onClick(()=>{o&&(a<n.length-1?a+=1:a==n.length-1&&(a=0),_.setButtonText(`${a+1}/${n.length}`),n[a].el.scrollIntoView({behavior:"auto",block:"center"}),n[a].el.classList.remove("animate"),n[a].el.offsetWidth,n[a].el.classList.add("animate"))});const _=new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-primary`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("0/0");new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-danger`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("译文失效").onClick(async()=>{this.i18n.notice.result("下载视图",!0,"敬请期待")}),new import_obsidian27.ButtonComponent(i).setClass("i18n-button").setClass(`i18n-button--${this.i18n.settings.I18N_BUTTON_TYPE}-success`).setClass(`is-${this.i18n.settings.I18N_BUTTON_SHAPE}`).setClass("i18n-button--left").setButtonText("保存").onClick(async()=>{const t=[...this.updateTranslationObj.filter(t=>"unchanged"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"modified"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"added"===t.type&&!0===t.state).map(t=>[t.key,t.value]),...this.updateTranslationObj.filter(t=>"removed"===t.type&&!1===t.state).map(t=>[t.key,t.value])];this.cloudJson.dict=Object.fromEntries(t),fs9.ensureDirSync(this.langDoc),fs9.writeJSONSync(path6.join(this.langDoc,`${this.i18n.settings.I18N_LANGUAGE}.json`),this.cloudJson,{spaces:4}),await this.i18n.downloadView.reloadShowData(),this.i18n.notice.result("下载",!0,"下载成功")})}}async onunload(){this.i18n.notice.reload(),this.contentEl.empty()}getViewType(){return DOWNLOAD_VIEW_TYPE}getDisplayText(){return"下载视图"}getIcon(){return"i18n_translate"}},I18N=class extends import_obsidian28.Plugin{constructor(){super(...arguments),this.updatesMark=!1,this.ignoreMark=!0,this.editorType="",this.editorPath=""}async onload(){const e=Date.now();icon_default(),this.notice=new Notification(this.app,this),await this.loadSettings(),console.log(`%c ${this.manifest.name} %c v${this.manifest.version} `,"padding: 2px; border-radius: 2px 0 0 2px; color: #fff; background: #5B5B5B;","padding: 2px; border-radius: 0 2px 2px 0; color: #fff; background: #409EFF;"),this.settings.I18N_AGREEMENT?(this.api=new API(this),this.tapi=new TranslationAPI(this),this.firstRun(),this.settings.I18N_CHECK_UPDATES&&this.checkUpdates(),this.ignoreCache(),this.pliginDirectoryCache(),this.themeDirectoryCache(),this.settings.I18N_MODE_LDT&&this.settings.I18N_AUTOMATIC_UPDATE&&await this.i18nAutomaticUpdate(this.app),this.settings.I18N_MODE_IMT&&this.activateIMT(),this.addRibbonIcon("i18n_translate",t("通用_I18N_文本"),()=>{0===this.settings.I18N_MODE?(this.pluginModal=new I18NPluginModal(this.app,this),this.pluginModal.open()):1===this.settings.I18N_MODE&&(this.themeModal=new I18NThemeModal(this.app,this),this.themeModal.open())}),this.settings.I18N_ADMIN_VERIFY&&this.settings.I18N_ADMIN_MODE&&(this.i18nReviewEl=this.addRibbonIcon("i18n-review","I18N审核",t=>{this.admin=new AdminModal(this.app,this),this.admin.open()})),this.registerView(ADMIN_VIEW_TYPE,t=>new AdminView(t,this)),this.registerView(EDIT_VIEW_TYPE,t=>new EditorView(t,this)),this.registerView(SHARE_VIEW_TYPE,t=>new ShareView(t,this)),this.registerView(DOWNLOAD_VIEW_TYPE,t=>new DownloadView(t,this)),command_default(this.app,this),this.settings.I18N_MODE_LDT&&this.settings.I18N_NAME_TRANSLATION&&this.trenslatorPluginsName(),this.addSettingTab(new I18nSettingTab(this.app,this))):new AgreementModal(this.app,this).open();const s=Date.now();this.settings.I18N_START_TIME&&this.notice.success(t("设置_基础_启动耗时_标题"),`${((s-e)/1e3).toFixed(5)}s`)}async onunload(){this.detachAdminView(),this.detachEditorView(),this.detachShareView(),this.detachDownloadView(),this.restorePluginsName(),this.settings.I18N_MODE_IMT&&await this.enableIMT()}onUserEnable(){}async loadSettings(){this.settings=Object.assign({},DEFAULT_SETTINGS,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async firstRun(){this.settings.I18N_WIZARD&&(new WizardModal(this.app,this).open(),this.settings.I18N_UUID=v4_default(),this.settings.I18N_WIZARD=!1,this.saveSettings())}async checkUpdates(){const e=await this.api.version();e.state?this.manifest.version!==e.data.version&&(this.notice.primary(t("功能_检查更新_前缀"),`${t("功能_检查更新_通知一")}(${e.data.version})\n${e.data.content}`,1e4),this.updatesMark=!0,this.updatesVersion=e.data.version):this.notice.result(t("功能_检查更新_前缀"),!1,e.data)}async ignoreCache(){if(!this.settings.I18N_MODE_NDT||!this.settings.I18N_IGNORE)return void(this.ignoreMark=!1);const e=await this.api.getMark();if(!e.state)return this.ignoreMark=!1,void this.notice.result(t("设置_云端_标记汉化_标题"),!1,t("设置_云端_通知一"));try{this.ignorePlugins=e.data,this.ignoreMark=!0,this.settings.I18N_NOTICE&&this.notice.result(t("设置_云端_标记汉化_标题"),!0)}catch(e){this.ignoreMark=!1,this.notice.result(t("设置_云端_标记汉化_标题"),!1,e)}}async pliginDirectoryCache(){if(!this.settings.I18N_MODE_NDT)return void(this.pluginDirectoryMark=!1);const e=await this.api.giteeGetDirectory("translation");if(!e.state)return this.pluginDirectoryMark=!1,void this.notice.result(t("设置_云端_标题_缩写"),!1,t("设置_云端_通知一"));try{this.pluginDirectory=e.data,this.pluginDirectoryMark=!0,this.settings.I18N_NOTICE&&this.notice.result(t("设置_云端_标题_缩写"),!0,t("设置_云端_通知三"))}catch(e){this.pluginDirectoryMark=!1,this.notice.result(t("设置_云端_标题_缩写"),!1,e)}}async themeDirectoryCache(){if(!this.settings.I18N_MODE_NDT)return void(this.themeDirectoryMark=!1);const e=await this.api.giteeGetDirectory("theme");if(!e.state)return this.themeDirectoryMark=!1,void this.notice.result(t("设置_云端_标题_缩写"),!1,t("设置_云端_通知一"));try{this.themeDirectory=e.data,this.themeDirectoryMark=!0,this.settings.I18N_NOTICE&&this.notice.result(t("设置_云端_标题_缩写"),!0,t("设置_云端_通知二"))}catch(e){this.themeDirectoryMark=!1,this.notice.result(t("设置_云端_标题_缩写"),!1,e)}}async i18nAutomaticUpdate(e){var s;if(this.settings.I18N_MODE_LDT&&this.settings.I18N_AUTOMATIC_UPDATE){let i=[];this.settings.I18N_NOTICE&&this.notice.success(t("设置_本地_智能更新_标题"),t("设置_本地_智能更新_通知一")),i=Object.values(e.plugins.manifests).filter(t=>"i18n"!==t.id);let n=0;for(const a of i){const i=path7.join(path7.normalize(e.vault.adapter.getBasePath()),null!=(s=a.dir)?s:""),o=fs10.pathExistsSync(path7.join(i,"lang","state.json"))?new State(this,path7.join(i,"lang","state.json")):void 0;if(null!=o&&o.getState()&&a.version!=o.getPluginVersion())try{n+=1,fs10.removeSync(path7.join(i,"duplicate.js")),o.reset(),fs10.copySync(path7.join(i,"main.js"),path7.join(i,"duplicate.js"));const e=fs10.readJsonSync(path7.join(i,"lang",`${this.settings.I18N_LANGUAGE}.json`));let s=fs10.readFileSync(path7.join(i,"main.js")).toString();for(const t in e.dict)s=s.replaceAll(t,e.dict[t]);fs10.writeFileSync(path7.join(i,"main.js"),s);const r=fs10.readJsonSync(path7.join(i,"manifest.json"));r.description=e.description.translation,fs10.writeJsonSync(path7.join(i,"manifest.json"),r,{spaces:4}),o.setState(!0),o.setPluginVersion(a.version),o.setTranslationVersion(e.manifest.translationVersion),await this.app.plugins.disablePlugin(a.id),await this.app.plugins.enablePlugin(a.id)}catch(e){this.notice.error(t("设置_本地_智能更新_标题"),e)}}this.settings.I18N_NOTICE&&(0==n?this.notice.success(t("设置_本地_智能更新_标题"),t("设置_本地_智能更新_通知二")):this.notice.success(t("设置_本地_智能更新_标题"),`${t("设置_本地_智能更新_通知三")}${n}${t("设置_本地_智能更新_通知四")}`))}}trenslatorPluginsName(){const t=path7.join(path7.normalize(this.app.vault.adapter.getBasePath()),this.manifest.dir,"name.json");this.nameTranslationJSON=fs10.pathExistsSync(t)?fs10.readJsonSync(t):{},this.nameTranslationJSON=Object.keys(this.nameTranslationJSON).sort().reduce((t,e)=>(t[e]=this.nameTranslationJSON[e],t),{});const e=Object.values(this.app.plugins.manifests);this.originalPluginsManifests=JSON.parse(JSON.stringify(e)),e.forEach(t=>{this.nameTranslationJSON.hasOwnProperty(t.name)&&""!==this.nameTranslationJSON[t.name]&&(t.name=`${t.name} ${this.settings.I18N_NAME_TRANSLATION_PREFIX}${this.nameTranslationJSON[t.name]}${this.settings.I18N_NAME_TRANSLATION_SUFFIX}`)})}restorePluginsName(){if(void 0!==this.originalPluginsManifests){const t=new Map(this.originalPluginsManifests.map(t=>[t.id,t]));Object.values(this.app.plugins.manifests).forEach(e=>{const s=t.get(e.id);s&&(e.name=s.name)})}}reloadPluginsName(){this.restorePluginsName(),this.trenslatorPluginsName()}async activateEditorView(){const{workspace:t}=this.app;this.detachEditorView();let e=null;const s=t.getLeavesOfType(EDIT_VIEW_TYPE);s.length>0?e=s[0]:null!=(e=t.getLeaf("window"))&&await e.setViewState({type:EDIT_VIEW_TYPE,active:!0}),null!=e&&t.revealLeaf(e)}detachEditorView(){this.app.workspace.detachLeavesOfType(EDIT_VIEW_TYPE)}async activateAdminView(){const{workspace:t}=this.app;this.detachAdminView();let e=null;const s=t.getLeavesOfType(ADMIN_VIEW_TYPE);s.length>0?e=s[0]:null!=(e=t.getLeaf("window"))&&await e.setViewState({type:ADMIN_VIEW_TYPE,active:!0}),null!=e&&t.revealLeaf(e)}detachAdminView(){this.app.workspace.detachLeavesOfType(ADMIN_VIEW_TYPE)}async activateShareView(){const{workspace:t}=this.app;this.detachShareView();let e=null;const s=t.getLeavesOfType(SHARE_VIEW_TYPE);s.length>0?e=s[0]:null!=(e=t.getLeaf("window"))&&await e.setViewState({type:SHARE_VIEW_TYPE,active:!0}),null!=e&&t.revealLeaf(e)}detachShareView(){this.app.workspace.detachLeavesOfType(SHARE_VIEW_TYPE)}async activateDownloadView(){const{workspace:t}=this.app;this.detachDownloadView();let e=null;const s=t.getLeavesOfType(DOWNLOAD_VIEW_TYPE);s.length>0?e=s[0]:null!=(e=t.getLeaf("window"))&&await e.setViewState({type:DOWNLOAD_VIEW_TYPE,active:!0}),null!=e&&t.revealLeaf(e)}detachDownloadView(){this.app.workspace.detachLeavesOfType(DOWNLOAD_VIEW_TYPE)}activateIMT(){if(!window.immersiveTranslateConfig){window.immersiveTranslateConfig={pageRule:this.settings.I18N_IMT_CONFIG},console.log(window.immersiveTranslateConfig);const t=document.createElement("script");t.classList.add("imt-script"),t.async=!0,t.src=Url.SDK_URL,t.onload=(()=>{setTimeout(()=>{var t;const e=null==(t=document.querySelector("#immersive-translate-popup"))?void 0:t.shadowRoot;if(e){const t=e.querySelector(".imt-fb-container>div");t&&(t.style.display="none");const s=e.querySelector(".popup-container>footer");s&&(s.style.display="none")}},1e3)}),document.body.append(t)}}async enableIMT(){var t,e;const s=document.querySelector("#immersive-translate-popup"),i=document.querySelector("html");"dual"===(null==(t=null==i?void 0:i.getAttribute)?void 0:t.call(i,"imt-state"))&&restoreTranslate();const n=document.querySelector(".imt-script"),a=[...document.querySelectorAll('[data-id*="immersive-translate"]')],o=[];[s,n,a].filter(t=>!!t).forEach(t=>Array.isArray(t)?t.forEach(t=>t&&o.push(t)):t&&o.push(t)),o.forEach(t=>{var e;return null==(e=null==t?void 0:t.remove)?void 0:e.call(t)}),null==(e=document.querySelectorAll("[data-immersive-translate-walked]"))||e.forEach(t=>t.removeAttribute("data-immersive-translate-walked")),null==i||i.removeAttribute("imt-state"),null==i||i.removeAttribute("imt-trans-position"),await clearStorage()}async getAdmin(){const t=await this.api.giteeGetAllIssue();t.state?t.data.length>0?(this.issues=t.data,this.notice.result("获取",!0,`${this.issues.length}条待审核内容`)):this.notice.result("获取",!0,"暂时没有可审核任务"):this.notice.result("获取",!1,"获取失败,请检查网络后重试")}editorLoad(t,e,s){this.editorType=t,this.editorPath=e,this.editorState=s,this.activateEditorView()}shareLoad(t,e,s){this.shareType=t,this.sharePath=e,this.shareObj=s,this.activateShareView()}},main_default=I18N;