/* src/styles.scss */
.kofi-button {
  z-index: 999;
  position: absolute;
  bottom: var(--size-4-5);
  right: var(--size-4-5);
  height: 30px;
}
.kofi-button img {
  height: 100%;
}
.docxer-settings-heading {
  border-bottom: 1px solid var(--color-accent);
}
.docxer-settings-heading:not(:first-child) {
  margin-top: var(--size-4-10) !important;
}
#docxer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  font-size: 1rem;
  background-color: var(--background-secondary);
}
.docxer-embed {
  max-height: 800px;
  overflow: auto;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
}
.docx-wrapper {
  padding: 0 !important;
  background-color: var(--background-primary) !important;
  transform-origin: center top;
}
.docx-wrapper article {
  user-select: text;
}
