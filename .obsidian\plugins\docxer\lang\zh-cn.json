{"manifest": {"translationVersion": 1737955109663, "pluginVersion": "2.1.1"}, "description": {"original": "Import Word files easily. Adds a preview mode for .docx files and the ability to convert them to markdown (.md) files.", "translation": "轻松导入 Word 文件。添加 .docx 文件的预览模式以及将其转换为 Markdown (.md) 文件的功能。"}, "dict": {".setName(\"Delete source file after conversion\")": ".setName(\"转换后删除源文件\")", ".setName(\"Import docx comments\")": ".setName(\"导入docx注释\")", ".setName(\"Attachments\")": ".setName(\"附件\")", ".setName(\"Fallback attachment name\")": ".setName(\"备用附件名称\")", ".setName(\"Attachments folder\")": ".setName(\"附件文件夹\")", ".setName(\"Custom attachments folder\")": ".setName(\"定制附件文件夹\")", ".setDesc(\"Delete source file after pressing the conversion button.\")": ".setDesc(\"按转换按钮后删除源文件.\")", ".setDesc(\"Import comments from docx files using reference links. Comments will be placed at the end of the markdown file.\")": ".setDesc(\"使用引用链接从 docx 文件导入注释。注释将放置在标记文件的末尾.\")", ".setDesc(\"Settings related to attachments extracted during file conversion.\")": ".setDesc(\"与文件转换过程中提取的附件相关的设置.\")", ".setDesc(\"Fallback name if the attachment file has no alt text or is written using only invalid characters.\")": ".setDesc(\"如果附件文件没有alt文本或仅使用无效字符编写，则为备用名称.\")", ".setDesc(\"Specify the destination for attachments extracted during file conversion.\")": ".setDesc(\"指定在文件转换期间提取的附件的目标.\")", ".setDesc(\"Specify the name of the folder where attachments will be extracted.\")": ".setDesc(\"指定将提取附件的文件夹的名称.\")", ".setPlaceholder(\"Attachments\")": ".setPlaceholder(\"附件\")", ".innerText = \"This is a preview. To edit, convert it to markdown.\"": ".innerText = \"这是预览。要编辑，请将其转换为markdown.\"", ".innerText = \"Convert\"": ".innerText = \"转化\""}}