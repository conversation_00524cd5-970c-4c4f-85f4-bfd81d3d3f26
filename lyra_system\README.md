# Lyra AI提示词优化专家系统

## 项目概述

Lyra是一个专业的AI提示词优化专家系统，采用四维方法论（分解、诊断、开发、交付）对各种AI平台的提示词进行系统化优化。

## 核心功能

- **四维优化方法论**：通过分解、诊断、开发、交付四个维度系统化分析和优化提示词
- **双模式运行**：DETAIL模式提供深度分析，BASIC模式提供快速优化建议
- **多平台适配**：针对ChatGPT、Claude、Gemini等不同AI平台提供定制化优化建议
- **智能复杂性检测**：自动分析提示词复杂程度并选择合适的处理模式
- **标准化响应格式**：提供统一的分析报告和优化建议输出格式

## 技术栈

- **后端**: Python + FastAPI
- **前端**: React + TypeScript + Tailwind CSS
- **AI集成**: OpenAI API, Anthropic API, Google AI API
- **数据处理**: pandas, numpy, spaCy, NLTK
- **数据存储**: SQLite
- **部署**: Docker

## 项目结构

```
lyra_system/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── core/           # 核心业务逻辑
│   │   ├── api/            # API路由
│   │   ├── models/         # 数据模型
│   │   └── services/       # 业务服务
│   ├── requirements.txt
│   └── main.py
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   ├── package.json
│   └── tailwind.config.js
├── docker-compose.yml
└── README.md
```

## 快速开始

### 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### 前端启动
```bash
cd frontend
npm install
npm start
```

## 使用说明

1. 访问系统主界面
2. 选择目标AI平台（ChatGPT、Claude、Gemini等）
3. 选择运行模式（DETAIL或BASIC）
4. 输入需要优化的提示词
5. 获取优化建议和分析报告

## 四维方法论

1. **分解（DECONSTRUCT）**：提取核心意图、关键实体和上下文
2. **诊断（DIAGNOSE）**：审查清晰度缺口和歧义
3. **开发（DEVELOP）**：根据请求类型选择最佳技术
4. **交付（DELIVER）**：构建优化后的提示词

## 许可证

MIT License