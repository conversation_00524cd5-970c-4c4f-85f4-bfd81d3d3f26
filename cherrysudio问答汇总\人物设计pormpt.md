---
date created: 星期六, 二月 8日 2025, 5:31:13 下午
date modified: 星期四, 四月 3日 2025, 4:23:24 下午
---
这是你的新*仓库*。

写点笔记，[[创建链接]]，或者试一试[导入器](https://help.obsidian.md/Plugins/Importer)插件!

当你准备好了，就将该笔记文件删除，使这个仓库为你所用。  
通过 **DeepSeek** 设计小说人物可按照以下结构化流程实现，从基础设定到深度塑造，逐步构建立体角色： --- ###  
**一、基础角色设定**  
1️⃣ **核心参数生成**  
**二、深度人格扩展**  
2️⃣ **动机与矛盾挖掘**  
**三、关系网络构建**  
3️⃣ **社交锚点设计**  
**四、动态发展设计**  
4️⃣ **角色弧光规划**  
**五、风格适配优化**  
5️⃣ **体裁与文风匹配**  
**六、细化描写模块**  
6️⃣ **标志性特征强化**  
**七、心理动线模拟**  
7️⃣ **关键抉择推演**  
**八、角色关系测试**  
8️⃣ **压力场景互动**