:root{--left-indicator: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="black" d="M58.079 20.579L28.663 50l29.417 29.421l8.842 -8.842L46.338 50l20.583 -20.579z"></path></svg>');--right-indicator: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="black" d="M41.921 79.421L71.338 50l-29.417 -29.421l-8.842 8.842L53.663 50l-20.583 20.579z"></path></svg>');--up-indicator: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="black" d="M26.221 55.388l5.892 5.892L50 43.392l17.888 17.888l5.892 -5.892L50 31.608z"></path></svg>');--down-indicator: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="black" d="M67.888 38.721L50 56.608L32.113 38.721l-5.892 5.892L50 68.392l23.779 -23.779z"></path></svg>')}.side-dock-actions .sortable-ghost,.status-bar .sortable-ghost,.view-actions .sortable-ghost,.workspace-tab-header-container .sortable-ghost{visibility:hidden}.side-dock-ribbon div.separator{cursor:grab;padding:0 4px;display:flex;place-items:center;justify-content:center;height:1em}div.separator svg{background-color:currentColor}.side-dock-ribbon div.separator .glyph{display:flex;align-self:flex-end}.side-dock-ribbon div.separator:hover{background:none}.side-dock-ribbon div.separator svg{-webkit-mask-image:var(--up-indicator)}.side-dock-ribbon div.separator.is-collapsed.bt-sortable-chosen svg{-webkit-mask-image:var(--up-indicator)}.side-dock-ribbon div.separator.is-collapsed svg{-webkit-mask-image:var(--down-indicator)}.hider-ribbon .side-dock-ribbon div.separator{height:26px}.hider-ribbon .side-dock-ribbon div.separator .glyph{display:flex;align-self:flex-end}.hider-ribbon .side-dock-ribbon div.separator svg{-webkit-mask-image:var(--left-indicator)}.hider-ribbon .side-dock-ribbon div.separator.is-collapsed.bt-sortable-chosen svg{-webkit-mask-image:var(--left-indicator)}.hider-ribbon .side-dock-ribbon div.separator.is-collapsed svg{-webkit-mask-image:var(--right-indicator)}.view-actions div.separator.is-collapsed{transform:rotateY(180deg)}.status-bar .is-hidden,.side-dock-ribbon .is-hidden,.view-actions .is-hidden{--is-hidden-display: none;display:var(--is-hidden-display)!important}.status-bar div.separator{--cursor: grab;cursor:grab;padding:0 4px;display:flex;align-items:center}.status-bar div.separator .glyph{display:flex}.status-bar div.separator svg,.status-bar div.separator.is-collapsed.bt-sortable-chosen svg{-webkit-mask-image:var(--right-indicator)}.status-bar div.separator.is-collapsed svg{-webkit-mask-image:var(--left-indicator)}.side-dock-ribbon div.side-dock-ribbon-action.bt-sortable-chosen,.side-dock-ribbon div.separator.bt-sortable-chosen,.status-bar div.separator.bt-sortable-chosen{--cursor: grabbing;cursor:grabbing}body.is-dragging .tooltip{display:none}.workspace-tab-header-container.is-dragging .workspace-tab-header,.workspace-tab-header-container.is-dragging .workspace-tab-container-before,.workspace-tab-header-container.is-dragging .workspace-tab-container-after,.workspace-tab-header-container.is-dragging .workspace-tab-header-inner-icon:hover{background:none}.workspace-leaf-content[data-type=file-explorer] .nav-header .search-input-container{display:none}.workspace-leaf-content[data-type=file-explorer] .nav-header .search-input-container.is-active{display:block}div.nav-action-button[data-sort-method]+div.nav-action-button.drag-to-rearrange{display:none}div.nav-action-button[data-sort-method=custom]+div.nav-action-button.drag-to-rearrange{display:flex}.nav-files-container .sortable-fallback{display:none}div.nav-buttons-container>div.nav-action-button.hide{display:none}.search-input-container.filter{margin:5px}.is-moved{background-color:var(--background-secondary-alt)!important;border-color:var(--background-secondary-alt)!important}
