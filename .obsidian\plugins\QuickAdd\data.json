{"choices": [{"id": "b21f77a1-5779-4325-a0d6-1f2c82fb9619", "name": "小说情节闪念短", "type": "Capture", "command": true, "appendLink": false, "captureTo": "春宵载酒小说设定/灵感随记/小说闪念短.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": true, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "-{{DATE:HH:mm}}{{VALUE:请输入你的内容}}"}, "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": true, "task": true, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}], "macros": [], "inputPrompt": "multi-line", "devMode": false, "templateFolderPath": "", "announceUpdates": true, "version": "1.18.0", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}