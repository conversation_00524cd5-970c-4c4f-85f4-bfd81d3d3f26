{"manifest": {"translationVersion": -639075600000, "pluginVersion": "2.5.4"}, "description": {"original": "Get, change or otherwise interact with your notes in Obsidian via a REST API.", "translation": "通过REST API获取、更改或以其他方式与您在Obsidian中的笔记交互。"}, "dict": {".setDesc(\"This configures the port on which your REST API will listen for HTTPS connections.  It is recommended that you leave this port with its default setting as tools integrating with this API may expect the default port to be in use.  Under no circumstances is it recommended that you expose this service directly to the internet.\"": ".setDesc(\"这将配置 REST API 将在其上侦听 HTTPS 连接的端口。建议您保留此端口的默认设置，因为与此 API 集成的工具可能会期望使用默认端口。在任何情况下，都不建议您将此服务直接暴露在互联网上。\"", ".setDesc(\"Enables a non-encrypted (HTTP) server on the port designated below.  By default this plugin requires a secure HTTPS connection, but in safe environments you may turn on the non-encrypted server to simplify interacting with the API. Interactions with the API will still require the API Key shown above.  Under no circumstances is it recommended that you expose this service to the internet, especially if you turn on this feature!\"": ".setDesc(\"在下面指定的端口上启用非加密(HTTP)服务器。默认情况下，此插件需要安全的 HTTPS 连接，但在安全环境中，您可以打开非加密服务器，以简化与 API 的交互。与 API 的交互仍然需要上面显示的 API 密钥。在任何情况下，都不建议您将此服务暴露在互联网上，尤其是当您打开此功能时！\"", ".setDesc(`Advanced settings are dangerous and may make your environment less secure.`": ".setDesc(`高级设置很危险，可能会降低环境的安全性。`", "setName(\"Encrypted (HTTPS) Server Port\"": "setName(\"加密(HTTPS)服务器端口\"", "setName(\"Enable Non-encrypted (HTTP) Server\"": "setName(\"启用非加密(HTTP)服务器\"", "setName(\"Non-encrypted (HTTP) Server Port\"": "setName(\"非加密(HTTP)服务器端口\"", "setName(\"Reset Cryptography\"": "setName(\"重置加密\"", "setName(\"Restore Default Settings\"": "setName(\"恢复默认设置\"", "setName(\"Show advanced settings\"": "set<PERSON>ame(\"显示高级设置\"", "setName(\"API Key\"": "setName(\"API 密钥\"", "setName(\"Certificate\"": "<PERSON><PERSON><PERSON>(\"证明书\"", "setName(\"Public Key\"": "setName(\"公钥\"", "setName(\"Private Key\"": "setName(\"私钥\"", "setName(\"Authorization Header\"": "setName(\"授权标头\"", "setName(\"Binding Host\"": "setName(\"绑定主机\"", "text: \"Your API Key\"": "text: \"您的 API 密钥\"", "text: \"This must be passed in all requests via an authorization header.\"": "text: \"这必须通过授权标头在所有请求中传递。\"", "text: \"Example header: \"": "text: \"标题示例:\"", "text: \"See more information and examples in our interactive OpenAPI documentation.\"": "text: \"请参阅我们的交互式 OpenAPI 文档中的更多信息和示例。\"", "text: \"By default this plugin uses a self-signed certificate for HTTPS; you may want to \"": "text: \"默认情况下，此插件使用 HTTPS 的自签名证书；你可能想\"", "text: \"download this certificate\"": "text: \"下载此证书\"", "text: \" to use it for validating your connection's security by adding it as a trusted certificate authority in the browser or tool you are using for interacting with this API.\"": "text: \"通过在用于与此 API 交互的浏览器或工具中将其添加为受信任的证书颁发机构，将其用于验证连接的安全性。\"", "text: \"Advanced Settings\"": "text: \"高级设置\"", "`Pressing this button will cause your certificate,": "`按下此按钮将导致您的证书,", "private and public keys, and API key to be regenerated\\.`": "私钥和公钥，以及要重新生成的API密钥\\.`", "private and public keys, and API key to be regenerated.`": "私钥和公钥以及要重新生成的API密钥。`", ".setButtonText(\"Reset Crypo\"": ".setButtonText(\"重设密匙\"", ".setButtonText(\"Restore Defaults\"": ".setButtonText(\"恢复默认值\"", "The settings below are potentially dangerous and": "以下设置具有潜在危险", "are intended for use only by people who know what": "只供知道什么的人使用", "they are doing. Do not change any of these settings if": "他们正在做。如果", "you do not understand what that setting is used for": "你不明白那个设置是用来做什么的", "and what security impacts changing that setting will have.": "以及更改该设置将产生什么安全影响。", "Use of this software is licensed to you under the": "本软件的使用许可给您", "MIT license, and it is important that you understand that": "麻省理工学院执照，重要的是你要明白", "this license provides you with no warranty.": "此许可证不为您提供任何担保。", "For the complete license text please see": "有关完整的许可文本，请参阅", "Pressing this button will reset this plugin's": "按下此按钮将重置此插件的", "settings to defaults.": "设置为默认值。", "text: \"How to Access\"": "text: \"如何访问\"", "text: \"Local REST API\"": "text: \"本地 REST API\"", "text: \"For example, the following request will return all notes in the root directory of your vault:\"": "text: \"例如，以下请求将返回vault根目录中的所有笔记：\"", "text: \"Settings\"": "text: \"设置\"", "You can access Obsidian Local REST API via the following URLs:": "您可以通过以下URL访问Obsidian本地REST API：", "Your API Key must be passed in requests via an authorization header": "您的API密钥必须通过授权头传入请求", "<a href=\"javascript:navigator.clipboard.writeText('${this.plugin.settings.apiKey}')\">(copy)</a>:": "<a href=\"javascript:navigator.clipboard.writeText('${this.plugin.settings.apiKey}')\">(复制)</a>:", "Comprehensive documentation of what API endpoints are available can": "API 端点可用的全面文档可以", "be found in": "参阅", "<a href=\"https://coddingtonbear.github.io/obsidian-local-rest-api/\">the online docs</a>.": "<a href=\"https://coddingtonbear.github.io/obsidian-local-rest-api/\">在线文档</a>.", ".setName(\"Reset All Cryptography\")": ".setName(\"重置所有 Cryptography\")", "private key, public key, and API key to be regenerated.": "要重新生成的私钥、公钥和API密钥。", "This settings panel will be closed when you press this.`)": "按下此按钮时，此设置面板将关闭。`)", ".setButtonText(\"Reset All Crypto\")": ".setButtonText(\"重置所有 Crypto\")", ".setName(\"Re-generate Certificates\")": ".set<PERSON>ame(\"重新生成证书\")", "private key,  and public key to be re-generated, but your API key will remain unchanged.": "私钥和公钥，但您的 API 密钥将保持不变。", ".setButtonText(\"Re-generate Certificates\")": ".setButtonText(\"重新生成证书\")", ".setName(\"Enable Encrypted (HTTPs) Server\")": ".setName(\"启用加密（HTTP）服务器\")", "This controls whether the HTTPs server is enabled.  You almost certainly want to leave this switch in its default state ('on'),": "这控制是否启用HTTPs服务器。您几乎肯定希望将此开关保持在默认状态（'开'）,", "but may find it useful to turn this switch off for": "但可能会发现关闭此开关对", "troubleshooting.": "故障排除。", "Requires that <a href=\"https://127.0.0.1:${this.plugin.settings.port}/${CERT_NAME}\">this certificate</a> be": "要求 <a href=\"https://127.0.0.1:${this.plugin.settings.port}/${CERT_NAME}\">这个证书</a> be", "configured as a trusted certificate authority for": "配置为受信任的证书颁发机构", "your browser.  See <a href=\"https://github.com/coddingtonbear/obsidian-web/wiki/How-do-I-get-my-browser-trust-my-Obsidian-Local-REST-API-certificate%3F\">wiki</a> for more information.": "您的浏览器。请参阅 <a href=\"https://github.com/coddingtonbear/obsidian-web/wiki/How-do-I-get-my-browser-trust-my-Obsidian-Local-REST-API-certificate%3F\">wiki</a> 详情.", "Non-encrypted (HTTP) API URL": "未加密（HTTP）API URL", "Encrypted (HTTPS) API URL<br /><br />": "加密（HTTPS）API URL<br /><br />", ">(copy)</a><br />": ">(复制)</a><br />"}}