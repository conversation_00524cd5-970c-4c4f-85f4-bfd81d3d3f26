{"manifest": {"translationVersion": 1737001088035, "pluginVersion": "1.0.12"}, "description": {"original": "AI chat with note context, smart writing assistance, and one-click edits for your vault.", "translation": "基于笔记上下文的AI聊天，智能写作辅助，以及一键编辑功能"}, "dict": {"Notice(\"Please enter a content for your template\")": "Notice(\"请输入模板内容\")", "Notice(\"Please enter a name for your template\")": "Notice(\"请输入模板名称\")", "Notice(`Template created: ${G}`)": "Notice(`Template created: ${G}`)", "Notice(\"A template with this name already exists\")": "Notice(\"已存在同名模板\")", "Notice(\"Failed to create template\")": "Notice(\"创建模板失败\")", "Notice(\"Failed to load conversation\")": "Notice(\"加载对话失败\")", "Notice(T.message)": "Notice(T.message)", "Notice(\"Failed to save chat history\")": "Notice(\"保存聊天记录失败\")", "Notice(W.message)": "Notice(W.message)", "Notice(\"Rebuilding vault index...\",0)": "Notice(\"正在重建知识库索引...\",0)", "Notice(\"Updating vault index...\",0)": "Notice(\"正在更新知识库索引...\",0)", "name:\"Open chat\"": "name:\"打开聊天\"", "name:\"Add selection to chat\"": "name:\"将选中内容加入聊天\"", "name:\"Rebuild entire vault index\"": "name:\"重建整个知识库索引\"", "name:\"Update index for modified files\"": "name:\"更新已修改文件的索引\"", "link:\"+l.slice(5).toLowerCase()}});var wtI=g((rHl,QtI)=>{\"": "link:\"+l.slice(5).toLowerCase()}});var wtI=g((rHl,QtI)=>{\"", "link:\"xmlns:xlink\"": "link:\"xmlns:xlink\"", "link:\"+l.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}});s();Z();var UM=_c({space:\"": "link:\"+l.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}});s();Z();var UM=_c({space:\"", "text:\"\"}s();Z();var UH=require(\"": "text:\"\"}s();Z();var UH=require(\"", "text:`${W}${h}${V}\n\n${G}\n\n`": "text:`${W}${h}${V}\n\n${G}\n\n`", "text:\"reference\"": "text:\"reference\"", "text: \",m)}};return(0,Ft.jsxs)(\"": "text: \",m)}};return(0,Ft.jsxs)(\"", "text:\"How to obtain API keys\"": "text:\"如何获取API密钥\"", "text:\"No files match the exclusion patterns\"": "text:\"没有文件匹配排除模式\"", "text:\"No inclusion patterns specified - all files will be included (except those matching exclusion patterns)\"": "text:\"未指定包含模式 - 所有文件都将被包含（除了匹配排除模式的文件）\"", "text:\"No files match the inclusion patterns\"": "text:\"没有文件匹配包含模式\"", ".setDesc(\"Enter your API keys for the services you want to use\")": ".setDesc(\"输入您要使用的服务的API密钥\")", ".setDesc(\"Choose the model you want to use for chat\")": ".setDesc(\"选择您要用于聊天的模型\")", ".setDesc(\"Choose the model you want to use for apply\")": ".setDesc(\"选择您要用于应用的模型\")", ".setDesc(\"Choose the model you want to use for embeddings\")": ".setDesc(\"选择您要用于嵌入的模型\")", ".setDesc(\"This prompt will be added to the beginning of every chat.\")": ".setDesc(\"此提示将添加到每次聊天的开头。\")", ".setDesc(\"The API endpoint for your Ollama service (e.g., http://127.0.0.1:11434)\")": ".setDesc(\"您的Ollama服务的API端点（例如：http://127.0.0.1:11434）\")", ".setDesc(\"Select a model from your Ollama instance\")": ".setDesc(\"从您的Ollama实例中选择一个模型\")", ".setDesc(\"The API endpoint for your OpenAI-compatible service (e.g., https://api.example.com/v1)\")": ".setDesc(\"您的OpenAI兼容服务的API端点（例如：https://api.example.com/v1）\")", ".setDesc(\"Your authentication key for the OpenAI-compatible service\")": ".setDesc(\"您的OpenAI兼容服务的认证密钥\")", ".setDesc(\"The specific model to use with your service (e.g., llama-3.1-70b, mixtral-8x7b)\")": ".setDesc(\"与您的服务一起使用的特定模型（例如：llama-3.1-70b, mixtral-8x7b）\")", ".setDesc('Files matching ANY of these patterns will be excluded from indexing. One pattern per line. Uses glob patterns (e.g., \"private/*\", \"*.tmp\"). Leave empty to exclude nothing. After changing this, use the command \"Rebuild entire vault index\" to apply changes.')": ".setDesc('匹配这些模式中任意一个的文件将被排除在索引之外。每行一个模式。使用glob模式（例如：\"private/*\", \"*.tmp\"）。留空则不排除任何文件。更改后，请使用\"重建整个知识库索引\"命令来应用更改。')", ".setDesc('Set the chunk size for text splitting. After changing this, please re-index the vault using the \"Rebuild entire vault index\" command.')": ".setDesc('设置文本分块的大小。更改后，请使用\"重建整个知识库索引\"命令重新索引知识库。')", ".setDesc(\"Maximum number of tokens before switching to RAG. If the total tokens from mentioned files exceed this, RAG will be used instead of including all file contents.\")": ".setDesc(\"切换到RAG之前的最大token数。如果提及文件的总token数超过此值，将使用RAG而不是包含所有文件内容。\")", ".setDesc(\"Minimum similarity score for RAG results. Higher values return more relevant but potentially fewer results.\")": ".setDesc(\"RAG结果的最小相似度分数。值越高返回的结果越相关，但可能数量越少。\")", ".setDesc(\"Maximum number of RAG results to include in the prompt. Higher values provide more context but increase token usage.\")": ".setDesc(\"要包含在提示中的RAG结果的最大数量。值越高提供的上下文越多，但会增加token使用量。\")", ".setPlaceholder(\"Enter your API key\")": ".setPlaceholder(\"输入您的API密钥\")"}}