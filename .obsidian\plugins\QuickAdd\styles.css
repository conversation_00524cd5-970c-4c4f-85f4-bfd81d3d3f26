.modal-button-container{display:flex;flex-direction:row-reverse;justify-content:flex-start;margin-top:1rem;gap:.5rem}.configureMacroDiv{display:grid;grid-template-rows:1fr;min-width:12rem}.configureMacroDivItem{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.configureMacroDivItemButton{display:flex;align-content:center;justify-content:center;margin-bottom:10px}.macroContainer{display:grid;grid-template-rows:repeat(auto-fill,120px);grid-gap:40px;overflow-y:auto;max-height:30em;padding:2em}@media screen and (max-width: 540px){.macroContainer1,.macroContainer2,.macroContainer3{grid-template-columns:repeat(1,1fr)}.wideInputPromptInputEl{width:20rem;max-width:100%;height:3rem;direction:inherit;text-align:inherit}}@media screen and (max-width: 540px) and (max-width: 780px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:30rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}@media screen and (min-width: 781px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:40rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}.addMacroBarContainer{display:flex;align-content:center;justify-content:space-around;margin-top:20px}.captureToActiveFileContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.choiceNameHeader{text-align:center}.choiceNameHeader:hover{cursor:pointer}.folderInputContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:8px;gap:4px}.selectMacroDropdownContainer{display:flex;align-content:center;justify-content:center}.quickAddModal .modal{min-width:35%;overflow-y:auto;max-height:70%}.checkboxRowContainer{margin:30px 0;display:grid;grid-template-rows:auto;align-content:center;gap:5px}.checkboxRow{display:flex;justify-content:space-between;align-content:center}.checkboxRow .checkbox-container{flex-shrink:0}.checkboxRow span{font-size:16px;word-break:break-all}.submitButtonContainer{display:flex;align-content:center;justify-content:center;gap:.5rem}.chooseFolderWhenCreatingNoteContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.chooseFolderFromSubfolderContainer{margin:20px 0 0}.clickable:hover{cursor:pointer}.quickAddCommandListItem{display:flex;flex:1 1 auto;align-items:center;justify-content:space-between}.quickCommandContainer{display:flex;justify-content:flex-end;align-content:center;margin-bottom:1em;gap:4px}.yesNoPromptButtonContainer{display:flex;align-items:center;justify-content:space-around;margin-top:2rem;gap:.5rem}.yesNoPromptParagraph{text-align:center}.suggestion-container{background-color:var(--modal-background);z-index:100000;overflow-y:auto}.qaFileSuggestionItem{display:flex;align-items:center;justify-content:space-between;padding:6px 12px;margin:1px 0;cursor:pointer;border-radius:3px;transition:all .12s cubic-bezier(.25,.46,.45,.94);position:relative;min-height:32px;font-size:13px;line-height:1.3;width:100%}.qaFileSuggestionItem:hover{background-color:var(--background-modifier-hover);transform:translate(2px)}.qaFileSuggestionItem.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent);box-shadow:0 2px 8px #0000001a}.qaFileSuggestionItem.is-selected .suggestion-sub-text{color:var(--text-on-accent-inverted);opacity:.8}.qa-suggest-exact:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--interactive-accent);border-radius:0 1px 1px 0}.qa-suggest-alias:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-orange, #ff8c00);border-radius:0 1px 1px 0}.qa-suggest-fuzzy:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--text-faint);border-radius:0 1px 1px 0}.qa-suggest-unresolved:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-red, #e74c3c);border-radius:0 1px 1px 0}.qa-suggest-heading:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-blue, #3498db);border-radius:0 1px 1px 0}.qa-suggest-block:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-purple, #9b59b6);border-radius:0 1px 1px 0}.qa-suggestion-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0}.qaFileSuggestionItem .suggestion-main-text{font-weight:500;color:var(--text-normal);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:200px}.qaFileSuggestionItem .suggestion-sub-text{font-size:11px;color:var(--text-muted);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:150px;margin-left:auto;padding-left:8px;opacity:.7}.qa-suggestion-pill{display:inline-block;padding:1px 4px;border-radius:2px;font-size:9px;font-weight:600;text-transform:uppercase;letter-spacing:.3px;line-height:1;opacity:.8}.qa-alias-pill{background:var(--color-orange, #ff8c00);color:#fff}.qa-unresolved-pill{background:var(--color-red, #e74c3c);color:#fff}.qa-create-pill{background:var(--color-green, #27ae60);color:#fff}.qa-heading-pill{background:var(--color-blue, #3498db);color:#fff}.qa-block-pill{background:var(--color-purple, #9b59b6);color:#fff}.qa-file-tooltip{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;padding:8px 10px;max-width:280px;box-shadow:0 4px 20px #0000001f;z-index:1000;pointer-events:none;font-size:12px}.qa-tooltip-header{font-weight:600;font-size:13px;margin-bottom:4px;color:var(--text-normal)}.qa-tooltip-content{color:var(--text-muted);line-height:1.3}.qa-tooltip-content div{margin-bottom:2px}.qaFileSuggestionItem{will-change:transform,background-color;backface-visibility:hidden}.qaFileSuggestionItem:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:-2px}.theme-dark .qa-file-tooltip{box-shadow:0 4px 20px #0000004d;border-color:var(--background-modifier-border-hover)}.theme-dark .qaFileSuggestionItem:hover{background-color:#ffffff0d}@media (prefers-contrast: high){.qa-suggest-exact:before,.qa-suggest-alias:before,.qa-suggest-fuzzy:before,.qa-suggest-unresolved:before,.qa-suggest-heading:before,.qa-suggest-block:before{width:3px}.qa-suggestion-pill{font-weight:700}}@media (prefers-reduced-motion: reduce){.qaFileSuggestionItem{transition:none}.qaFileSuggestionItem:hover{transform:none}}.choiceListItem{display:flex;font-size:16px;align-items:center;margin:12px 0 0;transition:1s ease-in-out}.choiceListItemName{flex:1 0 0}.choiceListItemName p{margin:0;display:inline}.quickadd-choice-suggestion p{margin:0}.macroDropdownContainer{display:flex;align-content:center;justify-content:center;margin-bottom:10px;gap:10px}.macro-choice-buttonsContainer{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:10px}@media only screen and (max-width: 600px){.macroDropdownContainer{flex-direction:column;align-items:center}.macroDropdownContainer .macro-choice-buttonsContainer{gap:20px}}.quickadd-update-modal-container{display:flex;flex-direction:column;align-items:center;justify-content:center}.quickadd-update-modal{min-width:35%;max-width:90%;max-height:70%;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;overflow-x:hidden;overflow-y:auto}.quickadd-update-modal pre{white-space:pre-wrap;word-wrap:break-word;overflow-x:auto;max-width:100%}.quickadd-update-modal code{word-wrap:break-word;white-space:pre-wrap;max-width:100%;overflow-x:auto}.quickadd-update-modal img{width:100%;height:auto;margin:5px}.quickadd-bmac-container{display:flex;justify-content:center;align-items:center}
