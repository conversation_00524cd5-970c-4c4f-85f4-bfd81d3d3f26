{"dualConfigs": false, "showConsoleLog": false, "desktop": {"shortDelaySeconds": 5, "longDelaySeconds": 15, "delayBetweenPlugins": 40, "defaultStartupType": null, "showDescriptions": true, "enableDependencies": false, "plugins": {"Enhanced-editing": {"startupType": "instant"}, "obsidian-bartender": {"startupType": "instant"}, "obsidian42-brat": {"startupType": "instant"}, "copilot": {"startupType": "disabled"}, "dataview": {"startupType": "long"}, "docxer": {"startupType": "long"}, "editing-toolbar": {"startupType": "instant"}, "obsidian-excalidraw-plugin": {"startupType": "long"}, "file-explorer-note-count": {"startupType": "instant"}, "i18n": {"startupType": "instant"}, "obsidian-kanban": {"startupType": "instant"}, "obsidian-linter": {"startupType": "instant"}, "obsidian-local-rest-api": {"startupType": "short"}, "open-in-new-tab": {"startupType": "instant"}, "obsidian-outliner": {"startupType": "instant"}, "outliner-md": {"startupType": "instant"}, "pkmer": {"startupType": "long"}, "quickadd": {"startupType": "instant"}, "remember-cursor-position": {"startupType": "instant"}, "remotely-save": {"startupType": "instant"}, "smart-composer": {"startupType": "instant"}, "smart-connections": {"startupType": "disabled"}, "obsidian-style-settings": {"startupType": "instant"}, "tars": {"startupType": "disabled"}, "obsidian-tasks-plugin": {"startupType": "instant"}, "templater-obsidian": {"startupType": "instant"}, "obsidian-memos": {"startupType": "short"}, "make-md": {"startupType": "instant"}, "recent-files-obsidian": {"startupType": "instant"}}, "loadOrder": []}}