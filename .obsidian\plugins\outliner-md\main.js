"use strict";var ol=Object.defineProperty;var cl=(e,t,n)=>t in e?ol(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Xt=(e,t,n)=>(cl(e,typeof t!="symbol"?t+"":t,n),n);const O=require("obsidian"),rt=require("@codemirror/state"),J=require("@codemirror/view"),Vt=require("@codemirror/language");function Oe(e,t){const n=Object.keys(t).map(r=>dl(e,r,t[r]));return n.length===1?n[0]:function(){n.forEach(r=>r())}}function dl(e,t,n){const r=e[t],u=e.hasOwnProperty(t),i=u?r:function(){return Object.getPrototypeOf(e)[t].apply(this,arguments)};let a=n(i);return r&&Object.setPrototypeOf(a,r),Object.setPrototypeOf(s,a),e[t]=s,o;function s(...c){return a===i&&e[t]===s&&o(),a.apply(this,c)}function o(){e[t]===s&&(u?e[t]=i:delete e[t]),a!==i&&(a=i,Object.setPrototypeOf(s,r||Function))}}var $a=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ll(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const fl={mode:"lazy"};var zo=fl;const hl=RegExp.prototype.exec;var Qi=hl,ml={_hasUFlag:!1,shouldRun:function(t){var n=t.flags.includes("s");return n?(t.flags=t.flags.replace("s",""),this._hasUFlag=t.flags.includes("u"),!0):!1},Char:function(t){var n=t.node;if(!(n.kind!=="meta"||n.value!==".")){var r="\\uFFFF",u="￿";this._hasUFlag&&(r="\\u{10FFFF}",u="􏿿"),t.replace({type:"CharacterClass",expressions:[{type:"ClassRange",from:{type:"Char",value:"\\0",kind:"decimal",symbol:"\0"},to:{type:"Char",value:r,kind:"unicode",symbol:u}}]})}}},pl={_groupNames:{},init:function(){this._groupNames={}},getExtra:function(){return this._groupNames},Group:function(t){var n=t.node;n.name&&(this._groupNames[n.name]=n.number,delete n.name,delete n.nameRaw)},Backreference:function(t){var n=t.node;n.kind==="name"&&(n.kind="number",n.reference=n.number,delete n.referenceRaw)}},gl={RegExp:function(t){var n=t.node;n.flags.includes("x")&&(n.flags=n.flags.replace("x",""))}},Dl={dotAll:ml,namedCapturingGroups:pl,xFlag:gl};function de(e){return e?bl[e.type](e):""}var bl={RegExp:function(t){return"/"+de(t.body)+"/"+t.flags},Alternative:function(t){return(t.expressions||[]).map(de).join("")},Disjunction:function(t){return de(t.left)+"|"+de(t.right)},Group:function(t){var n=de(t.expression);return t.capturing?t.name?"(?<"+(t.nameRaw||t.name)+">"+n+")":"("+n+")":"(?:"+n+")"},Backreference:function(t){switch(t.kind){case"number":return"\\"+t.reference;case"name":return"\\k<"+(t.referenceRaw||t.reference)+">";default:throw new TypeError("Unknown Backreference kind: "+t.kind)}},Assertion:function(t){switch(t.kind){case"^":case"$":case"\\b":case"\\B":return t.kind;case"Lookahead":var n=de(t.assertion);return t.negative?"(?!"+n+")":"(?="+n+")";case"Lookbehind":var r=de(t.assertion);return t.negative?"(?<!"+r+")":"(?<="+r+")";default:throw new TypeError("Unknown Assertion kind: "+t.kind)}},CharacterClass:function(t){var n=t.expressions.map(de).join("");return t.negative?"[^"+n+"]":"["+n+"]"},ClassRange:function(t){return de(t.from)+"-"+de(t.to)},Repetition:function(t){return""+de(t.expression)+de(t.quantifier)},Quantifier:function(t){var n=void 0,r=t.greedy?"":"?";switch(t.kind){case"+":case"?":case"*":n=t.kind;break;case"Range":t.from===t.to?n="{"+t.from+"}":t.to?n="{"+t.from+","+t.to+"}":n="{"+t.from+",}";break;default:throw new TypeError("Unknown Quantifier kind: "+t.kind)}return""+n+r},Char:function(t){var n=t.value;switch(t.kind){case"simple":return t.escaped?"\\"+n:n;case"hex":case"unicode":case"oct":case"decimal":case"control":case"meta":return n;default:throw new TypeError("Unknown Char kind: "+t.kind)}},UnicodeProperty:function(t){var n=t.negative?"P":"p",r=void 0;return!t.shorthand&&!t.binary?r=t.name+"=":r="","\\"+n+"{"+r+t.value+"}"}},Go={generate:de},Xi={General_Category:"gc",Script:"sc",Script_Extensions:"scx"},Ar=Hu(Xi),ju={ASCII:"ASCII",ASCII_Hex_Digit:"AHex",Alphabetic:"Alpha",Any:"Any",Assigned:"Assigned",Bidi_Control:"Bidi_C",Bidi_Mirrored:"Bidi_M",Case_Ignorable:"CI",Cased:"Cased",Changes_When_Casefolded:"CWCF",Changes_When_Casemapped:"CWCM",Changes_When_Lowercased:"CWL",Changes_When_NFKC_Casefolded:"CWKCF",Changes_When_Titlecased:"CWT",Changes_When_Uppercased:"CWU",Dash:"Dash",Default_Ignorable_Code_Point:"DI",Deprecated:"Dep",Diacritic:"Dia",Emoji:"Emoji",Emoji_Component:"Emoji_Component",Emoji_Modifier:"Emoji_Modifier",Emoji_Modifier_Base:"Emoji_Modifier_Base",Emoji_Presentation:"Emoji_Presentation",Extended_Pictographic:"Extended_Pictographic",Extender:"Ext",Grapheme_Base:"Gr_Base",Grapheme_Extend:"Gr_Ext",Hex_Digit:"Hex",IDS_Binary_Operator:"IDSB",IDS_Trinary_Operator:"IDST",ID_Continue:"IDC",ID_Start:"IDS",Ideographic:"Ideo",Join_Control:"Join_C",Logical_Order_Exception:"LOE",Lowercase:"Lower",Math:"Math",Noncharacter_Code_Point:"NChar",Pattern_Syntax:"Pat_Syn",Pattern_White_Space:"Pat_WS",Quotation_Mark:"QMark",Radical:"Radical",Regional_Indicator:"RI",Sentence_Terminal:"STerm",Soft_Dotted:"SD",Terminal_Punctuation:"Term",Unified_Ideograph:"UIdeo",Uppercase:"Upper",Variation_Selector:"VS",White_Space:"space",XID_Continue:"XIDC",XID_Start:"XIDS"},gn=Hu(ju),ta={Cased_Letter:"LC",Close_Punctuation:"Pe",Connector_Punctuation:"Pc",Control:["Cc","cntrl"],Currency_Symbol:"Sc",Dash_Punctuation:"Pd",Decimal_Number:["Nd","digit"],Enclosing_Mark:"Me",Final_Punctuation:"Pf",Format:"Cf",Initial_Punctuation:"Pi",Letter:"L",Letter_Number:"Nl",Line_Separator:"Zl",Lowercase_Letter:"Ll",Mark:["M","Combining_Mark"],Math_Symbol:"Sm",Modifier_Letter:"Lm",Modifier_Symbol:"Sk",Nonspacing_Mark:"Mn",Number:"N",Open_Punctuation:"Ps",Other:"C",Other_Letter:"Lo",Other_Number:"No",Other_Punctuation:"Po",Other_Symbol:"So",Paragraph_Separator:"Zp",Private_Use:"Co",Punctuation:["P","punct"],Separator:"Z",Space_Separator:"Zs",Spacing_Mark:"Mc",Surrogate:"Cs",Symbol:"S",Titlecase_Letter:"Lt",Unassigned:"Cn",Uppercase_Letter:"Lu"},bu=Hu(ta),ea={Adlam:"Adlm",Ahom:"Ahom",Anatolian_Hieroglyphs:"Hluw",Arabic:"Arab",Armenian:"Armn",Avestan:"Avst",Balinese:"Bali",Bamum:"Bamu",Bassa_Vah:"Bass",Batak:"Batk",Bengali:"Beng",Bhaiksuki:"Bhks",Bopomofo:"Bopo",Brahmi:"Brah",Braille:"Brai",Buginese:"Bugi",Buhid:"Buhd",Canadian_Aboriginal:"Cans",Carian:"Cari",Caucasian_Albanian:"Aghb",Chakma:"Cakm",Cham:"Cham",Cherokee:"Cher",Common:"Zyyy",Coptic:["Copt","Qaac"],Cuneiform:"Xsux",Cypriot:"Cprt",Cyrillic:"Cyrl",Deseret:"Dsrt",Devanagari:"Deva",Dogra:"Dogr",Duployan:"Dupl",Egyptian_Hieroglyphs:"Egyp",Elbasan:"Elba",Ethiopic:"Ethi",Georgian:"Geor",Glagolitic:"Glag",Gothic:"Goth",Grantha:"Gran",Greek:"Grek",Gujarati:"Gujr",Gunjala_Gondi:"Gong",Gurmukhi:"Guru",Han:"Hani",Hangul:"Hang",Hanifi_Rohingya:"Rohg",Hanunoo:"Hano",Hatran:"Hatr",Hebrew:"Hebr",Hiragana:"Hira",Imperial_Aramaic:"Armi",Inherited:["Zinh","Qaai"],Inscriptional_Pahlavi:"Phli",Inscriptional_Parthian:"Prti",Javanese:"Java",Kaithi:"Kthi",Kannada:"Knda",Katakana:"Kana",Kayah_Li:"Kali",Kharoshthi:"Khar",Khmer:"Khmr",Khojki:"Khoj",Khudawadi:"Sind",Lao:"Laoo",Latin:"Latn",Lepcha:"Lepc",Limbu:"Limb",Linear_A:"Lina",Linear_B:"Linb",Lisu:"Lisu",Lycian:"Lyci",Lydian:"Lydi",Mahajani:"Mahj",Makasar:"Maka",Malayalam:"Mlym",Mandaic:"Mand",Manichaean:"Mani",Marchen:"Marc",Medefaidrin:"Medf",Masaram_Gondi:"Gonm",Meetei_Mayek:"Mtei",Mende_Kikakui:"Mend",Meroitic_Cursive:"Merc",Meroitic_Hieroglyphs:"Mero",Miao:"Plrd",Modi:"Modi",Mongolian:"Mong",Mro:"Mroo",Multani:"Mult",Myanmar:"Mymr",Nabataean:"Nbat",New_Tai_Lue:"Talu",Newa:"Newa",Nko:"Nkoo",Nushu:"Nshu",Ogham:"Ogam",Ol_Chiki:"Olck",Old_Hungarian:"Hung",Old_Italic:"Ital",Old_North_Arabian:"Narb",Old_Permic:"Perm",Old_Persian:"Xpeo",Old_Sogdian:"Sogo",Old_South_Arabian:"Sarb",Old_Turkic:"Orkh",Oriya:"Orya",Osage:"Osge",Osmanya:"Osma",Pahawh_Hmong:"Hmng",Palmyrene:"Palm",Pau_Cin_Hau:"Pauc",Phags_Pa:"Phag",Phoenician:"Phnx",Psalter_Pahlavi:"Phlp",Rejang:"Rjng",Runic:"Runr",Samaritan:"Samr",Saurashtra:"Saur",Sharada:"Shrd",Shavian:"Shaw",Siddham:"Sidd",SignWriting:"Sgnw",Sinhala:"Sinh",Sogdian:"Sogd",Sora_Sompeng:"Sora",Soyombo:"Soyo",Sundanese:"Sund",Syloti_Nagri:"Sylo",Syriac:"Syrc",Tagalog:"Tglg",Tagbanwa:"Tagb",Tai_Le:"Tale",Tai_Tham:"Lana",Tai_Viet:"Tavt",Takri:"Takr",Tamil:"Taml",Tangut:"Tang",Telugu:"Telu",Thaana:"Thaa",Thai:"Thai",Tibetan:"Tibt",Tifinagh:"Tfng",Tirhuta:"Tirh",Ugaritic:"Ugar",Vai:"Vaii",Warang_Citi:"Wara",Yi:"Yiii",Zanabazar_Square:"Zanb"},yu=Hu(ea);function Hu(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(Array.isArray(r))for(var u=0;u<r.length;u++)t[r[u]]=n;else t[r]=n}return t}function yl(e){return Xi.hasOwnProperty(e)||Ar.hasOwnProperty(e)||ju.hasOwnProperty(e)||gn.hasOwnProperty(e)}function vl(e,t){return El(e)?Ko(t):Fl(e)?Jo(t):!1}function wl(e){return Ar.hasOwnProperty(e)||gn.hasOwnProperty(e)}function El(e){return e==="General_Category"||e=="gc"}function Fl(e){return e==="Script"||e==="Script_Extensions"||e==="sc"||e==="scx"}function Ko(e){return ta.hasOwnProperty(e)||bu.hasOwnProperty(e)}function Jo(e){return ea.hasOwnProperty(e)||yu.hasOwnProperty(e)}function Cl(e){return ju.hasOwnProperty(e)||gn.hasOwnProperty(e)}function kl(e){return Ar.hasOwnProperty(e)?Ar[e]:gn.hasOwnProperty(e)?gn[e]:null}function xl(e){return bu.hasOwnProperty(e)?bu[e]:yu.hasOwnProperty(e)?yu[e]:gn.hasOwnProperty(e)?gn[e]:null}var Sl={isAlias:wl,isValidName:yl,isValidValue:vl,isGeneralCategoryValue:Ko,isScriptCategoryValue:Jo,isBinaryPropertyName:Cl,getCanonicalName:kl,getCanonicalValue:xl,NON_BINARY_PROP_NAMES_TO_ALIASES:Xi,NON_BINARY_ALIASES_TO_PROP_NAMES:Ar,BINARY_PROP_NAMES_TO_ALIASES:ju,BINARY_ALIASES_TO_PROP_NAMES:gn,GENERAL_CATEGORY_VALUE_TO_ALIASES:ta,GENERAL_CATEGORY_VALUE_ALIASES_TO_VALUES:bu,SCRIPT_VALUE_TO_ALIASES:ea,SCRIPT_VALUE_ALIASES_TO_VALUE:yu},Tl=function(){function e(t,n){var r=[],u=!0,i=!1,a=void 0;try{for(var s=t[Symbol.iterator](),o;!(u=(o=s.next()).done)&&(r.push(o.value),!(n&&r.length===n));u=!0);}catch(c){i=!0,a=c}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return r}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();function Qo(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var Re=void 0,Ut={},Y=void 0,I=void 0;function X(e,t){return Ut.options.captureLocations?!e||!t?e||t:{startOffset:e.startOffset,endOffset:t.endOffset,startLine:e.startLine,endLine:t.endLine,startColumn:e.startColumn,endColumn:t.endColumn}:null}var Xo="$",Al=[[-1,1,function(e,t){I=X(t,t),Y=e}],[0,4,function(e,t,n,r,u,i,a,s){I=X(u,s),Y=Pt({type:"RegExp",body:t,flags:Pl(r)},Kr(u,s||a))}],[1,1,function(e,t){I=X(t,t),Y=e}],[1,0,function(){I=null,Y=""}],[2,1,function(e,t){I=X(t,t),Y=e}],[2,2,function(e,t,n,r){I=X(n,r),Y=e+t}],[3,1,function(e,t){I=X(t,t),Y=e}],[4,1,function(e,t){I=X(t,t),Y=e}],[4,3,function(e,t,n,r,u,i){I=X(r,i);var a=null;u&&(a=Kr(r||u,i||u)),Y=Pt({type:"Disjunction",left:e,right:n},a)}],[5,1,function(e,t){if(I=X(t,t),e.length===0){Y=null;return}e.length===1?Y=Pt(e[0],I):Y=Pt({type:"Alternative",expressions:e},I)}],[6,0,function(){I=null,Y=[]}],[6,2,function(e,t,n,r){I=X(n,r),Y=e.concat(t)}],[7,1,function(e,t){I=X(t,t),Y=Pt(Object.assign({type:"Assertion"},e),I)}],[7,2,function(e,t,n,r){I=X(n,r),Y=e,t&&(Y=Pt({type:"Repetition",expression:e,quantifier:t},I))}],[8,1,function(e,t){I=X(t,t),Y={kind:"^"}}],[8,1,function(e,t){I=X(t,t),Y={kind:"$"}}],[8,1,function(e,t){I=X(t,t),Y={kind:"\\b"}}],[8,1,function(e,t){I=X(t,t),Y={kind:"\\B"}}],[8,3,function(e,t,n,r,u,i){I=X(r,i),Y={kind:"Lookahead",assertion:t}}],[8,3,function(e,t,n,r,u,i){I=X(r,i),Y={kind:"Lookahead",negative:!0,assertion:t}}],[8,3,function(e,t,n,r,u,i){I=X(r,i),Y={kind:"Lookbehind",assertion:t}}],[8,3,function(e,t,n,r,u,i){I=X(r,i),Y={kind:"Lookbehind",negative:!0,assertion:t}}],[9,1,function(e,t){I=X(t,t),Y=e}],[9,1,function(e,t){I=X(t,t),Y=e}],[9,1,function(e,t){I=X(t,t),Y=e}],[10,1,function(e,t){I=X(t,t),Y=se(e,"simple",I)}],[10,1,function(e,t){I=X(t,t),Y=se(e.slice(1),"simple",I),Y.escaped=!0}],[10,1,function(e,t){I=X(t,t),Y=se(e,"unicode",I),Y.isSurrogatePair=!0}],[10,1,function(e,t){I=X(t,t),Y=se(e,"unicode",I)}],[10,1,function(e,t){I=X(t,t),Y=Bl(e,I)}],[10,1,function(e,t){I=X(t,t),Y=se(e,"control",I)}],[10,1,function(e,t){I=X(t,t),Y=se(e,"hex",I)}],[10,1,function(e,t){I=X(t,t),Y=se(e,"oct",I)}],[10,1,function(e,t){I=X(t,t),Y=Nl(e,I)}],[10,1,function(e,t){I=X(t,t),Y=se(e,"meta",I)}],[10,1,function(e,t){I=X(t,t),Y=se(e,"meta",I)}],[10,1,function(e,t){I=X(t,t),Y=Vl(e,t)}],[11,1,function(e,t){I=X(t,t),Y=e}],[11,0],[12,1,function(e,t){I=X(t,t),Y=e}],[12,2,function(e,t,n,r){I=X(n,r),e.greedy=!1,Y=e}],[13,1,function(e,t){I=X(t,t),Y=Pt({type:"Quantifier",kind:e,greedy:!0},I)}],[13,1,function(e,t){I=X(t,t),Y=Pt({type:"Quantifier",kind:e,greedy:!0},I)}],[13,1,function(e,t){I=X(t,t),Y=Pt({type:"Quantifier",kind:e,greedy:!0},I)}],[13,1,function(e,t){I=X(t,t);var n=hi(e);Y=Pt({type:"Quantifier",kind:"Range",from:n[0],to:n[0],greedy:!0},I)}],[13,1,function(e,t){I=X(t,t),Y=Pt({type:"Quantifier",kind:"Range",from:hi(e)[0],greedy:!0},I)}],[13,1,function(e,t){I=X(t,t);var n=hi(e);Y=Pt({type:"Quantifier",kind:"Range",from:n[0],to:n[1],greedy:!0},I)}],[14,1,function(e,t){I=X(t,t),Y=e}],[14,1,function(e,t){I=X(t,t),Y=e}],[15,3,function(e,t,n,r,u,i){I=X(r,i);var a=String(e),s=ec(a);if(!Ut.options.allowGroupNameDuplicates&&Or.hasOwnProperty(s))throw new SyntaxError('Duplicate of the named group "'+s+'".');Or[s]=e.groupNumber,Y=Pt({type:"Group",capturing:!0,name:s,nameRaw:a,number:e.groupNumber,expression:t},I)}],[15,3,function(e,t,n,r,u,i){I=X(r,i),Y=Pt({type:"Group",capturing:!0,number:e.groupNumber,expression:t},I)}],[16,3,function(e,t,n,r,u,i){I=X(r,i),Y=Pt({type:"Group",capturing:!1,expression:t},I)}],[17,3,function(e,t,n,r,u,i){I=X(r,i),Y=Pt({type:"CharacterClass",negative:!0,expressions:t},I)}],[17,3,function(e,t,n,r,u,i){I=X(r,i),Y=Pt({type:"CharacterClass",expressions:t},I)}],[18,0,function(){I=null,Y=[]}],[18,1,function(e,t){I=X(t,t),Y=e}],[19,1,function(e,t){I=X(t,t),Y=[e]}],[19,2,function(e,t,n,r){I=X(n,r),Y=[e].concat(t)}],[19,4,function(e,t,n,r,u,i,a,s){I=X(u,s),ja(e,n),Y=[Pt({type:"ClassRange",from:e,to:n},Kr(u,a))],r&&(Y=Y.concat(r))}],[20,1,function(e,t){I=X(t,t),Y=e}],[20,2,function(e,t,n,r){I=X(n,r),Y=[e].concat(t)}],[20,4,function(e,t,n,r,u,i,a,s){I=X(u,s),ja(e,n),Y=[Pt({type:"ClassRange",from:e,to:n},Kr(u,a))],r&&(Y=Y.concat(r))}],[21,1,function(e,t){I=X(t,t),Y=se(e,"simple",I)}],[21,1,function(e,t){I=X(t,t),Y=e}],[22,1,function(e,t){I=X(t,t),Y=e}],[22,1,function(e,t){I=X(t,t),Y=se(e,"meta",I)}]],Ia={SLASH:"23",CHAR:"24",BAR:"25",BOS:"26",EOS:"27",ESC_b:"28",ESC_B:"29",POS_LA_ASSERT:"30",R_PAREN:"31",NEG_LA_ASSERT:"32",POS_LB_ASSERT:"33",NEG_LB_ASSERT:"34",ESC_CHAR:"35",U_CODE_SURROGATE:"36",U_CODE:"37",U_PROP_VALUE_EXP:"38",CTRL_CH:"39",HEX_CODE:"40",OCT_CODE:"41",DEC_CODE:"42",META_CHAR:"43",ANY:"44",NAMED_GROUP_REF:"45",Q_MARK:"46",STAR:"47",PLUS:"48",RANGE_EXACT:"49",RANGE_OPEN:"50",RANGE_CLOSED:"51",NAMED_CAPTURE_GROUP:"52",L_PAREN:"53",NON_CAPTURE_GROUP:"54",NEG_CLASS:"55",R_BRACKET:"56",L_BRACKET:"57",DASH:"58",$:"59"},fi=[{0:1,23:"s2"},{59:"acc"},{3:3,4:4,5:5,6:6,23:"r10",24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"s7"},{23:"r6",25:"s12"},{23:"r7",25:"r7",31:"r7"},{7:14,8:15,9:16,10:25,14:27,15:42,16:43,17:26,23:"r9",24:"s28",25:"r9",26:"s17",27:"s18",28:"s19",29:"s20",30:"s21",31:"r9",32:"s22",33:"s23",34:"s24",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",52:"s44",53:"s45",54:"s46",55:"s40",57:"s41"},{1:8,2:9,24:"s10",59:"r3"},{59:"r1"},{24:"s11",59:"r2"},{24:"r4",59:"r4"},{24:"r5",59:"r5"},{5:13,6:6,23:"r10",24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r8",25:"r8",31:"r8"},{23:"r11",24:"r11",25:"r11",26:"r11",27:"r11",28:"r11",29:"r11",30:"r11",31:"r11",32:"r11",33:"r11",34:"r11",35:"r11",36:"r11",37:"r11",38:"r11",39:"r11",40:"r11",41:"r11",42:"r11",43:"r11",44:"r11",45:"r11",52:"r11",53:"r11",54:"r11",55:"r11",57:"r11"},{23:"r12",24:"r12",25:"r12",26:"r12",27:"r12",28:"r12",29:"r12",30:"r12",31:"r12",32:"r12",33:"r12",34:"r12",35:"r12",36:"r12",37:"r12",38:"r12",39:"r12",40:"r12",41:"r12",42:"r12",43:"r12",44:"r12",45:"r12",52:"r12",53:"r12",54:"r12",55:"r12",57:"r12"},{11:47,12:48,13:49,23:"r38",24:"r38",25:"r38",26:"r38",27:"r38",28:"r38",29:"r38",30:"r38",31:"r38",32:"r38",33:"r38",34:"r38",35:"r38",36:"r38",37:"r38",38:"r38",39:"r38",40:"r38",41:"r38",42:"r38",43:"r38",44:"r38",45:"r38",46:"s52",47:"s50",48:"s51",49:"s53",50:"s54",51:"s55",52:"r38",53:"r38",54:"r38",55:"r38",57:"r38"},{23:"r14",24:"r14",25:"r14",26:"r14",27:"r14",28:"r14",29:"r14",30:"r14",31:"r14",32:"r14",33:"r14",34:"r14",35:"r14",36:"r14",37:"r14",38:"r14",39:"r14",40:"r14",41:"r14",42:"r14",43:"r14",44:"r14",45:"r14",52:"r14",53:"r14",54:"r14",55:"r14",57:"r14"},{23:"r15",24:"r15",25:"r15",26:"r15",27:"r15",28:"r15",29:"r15",30:"r15",31:"r15",32:"r15",33:"r15",34:"r15",35:"r15",36:"r15",37:"r15",38:"r15",39:"r15",40:"r15",41:"r15",42:"r15",43:"r15",44:"r15",45:"r15",52:"r15",53:"r15",54:"r15",55:"r15",57:"r15"},{23:"r16",24:"r16",25:"r16",26:"r16",27:"r16",28:"r16",29:"r16",30:"r16",31:"r16",32:"r16",33:"r16",34:"r16",35:"r16",36:"r16",37:"r16",38:"r16",39:"r16",40:"r16",41:"r16",42:"r16",43:"r16",44:"r16",45:"r16",52:"r16",53:"r16",54:"r16",55:"r16",57:"r16"},{23:"r17",24:"r17",25:"r17",26:"r17",27:"r17",28:"r17",29:"r17",30:"r17",31:"r17",32:"r17",33:"r17",34:"r17",35:"r17",36:"r17",37:"r17",38:"r17",39:"r17",40:"r17",41:"r17",42:"r17",43:"r17",44:"r17",45:"r17",52:"r17",53:"r17",54:"r17",55:"r17",57:"r17"},{4:57,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:59,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:61,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:63,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r22",24:"r22",25:"r22",26:"r22",27:"r22",28:"r22",29:"r22",30:"r22",31:"r22",32:"r22",33:"r22",34:"r22",35:"r22",36:"r22",37:"r22",38:"r22",39:"r22",40:"r22",41:"r22",42:"r22",43:"r22",44:"r22",45:"r22",46:"r22",47:"r22",48:"r22",49:"r22",50:"r22",51:"r22",52:"r22",53:"r22",54:"r22",55:"r22",57:"r22"},{23:"r23",24:"r23",25:"r23",26:"r23",27:"r23",28:"r23",29:"r23",30:"r23",31:"r23",32:"r23",33:"r23",34:"r23",35:"r23",36:"r23",37:"r23",38:"r23",39:"r23",40:"r23",41:"r23",42:"r23",43:"r23",44:"r23",45:"r23",46:"r23",47:"r23",48:"r23",49:"r23",50:"r23",51:"r23",52:"r23",53:"r23",54:"r23",55:"r23",57:"r23"},{23:"r24",24:"r24",25:"r24",26:"r24",27:"r24",28:"r24",29:"r24",30:"r24",31:"r24",32:"r24",33:"r24",34:"r24",35:"r24",36:"r24",37:"r24",38:"r24",39:"r24",40:"r24",41:"r24",42:"r24",43:"r24",44:"r24",45:"r24",46:"r24",47:"r24",48:"r24",49:"r24",50:"r24",51:"r24",52:"r24",53:"r24",54:"r24",55:"r24",57:"r24"},{23:"r25",24:"r25",25:"r25",26:"r25",27:"r25",28:"r25",29:"r25",30:"r25",31:"r25",32:"r25",33:"r25",34:"r25",35:"r25",36:"r25",37:"r25",38:"r25",39:"r25",40:"r25",41:"r25",42:"r25",43:"r25",44:"r25",45:"r25",46:"r25",47:"r25",48:"r25",49:"r25",50:"r25",51:"r25",52:"r25",53:"r25",54:"r25",55:"r25",56:"r25",57:"r25",58:"r25"},{23:"r26",24:"r26",25:"r26",26:"r26",27:"r26",28:"r26",29:"r26",30:"r26",31:"r26",32:"r26",33:"r26",34:"r26",35:"r26",36:"r26",37:"r26",38:"r26",39:"r26",40:"r26",41:"r26",42:"r26",43:"r26",44:"r26",45:"r26",46:"r26",47:"r26",48:"r26",49:"r26",50:"r26",51:"r26",52:"r26",53:"r26",54:"r26",55:"r26",56:"r26",57:"r26",58:"r26"},{23:"r27",24:"r27",25:"r27",26:"r27",27:"r27",28:"r27",29:"r27",30:"r27",31:"r27",32:"r27",33:"r27",34:"r27",35:"r27",36:"r27",37:"r27",38:"r27",39:"r27",40:"r27",41:"r27",42:"r27",43:"r27",44:"r27",45:"r27",46:"r27",47:"r27",48:"r27",49:"r27",50:"r27",51:"r27",52:"r27",53:"r27",54:"r27",55:"r27",56:"r27",57:"r27",58:"r27"},{23:"r28",24:"r28",25:"r28",26:"r28",27:"r28",28:"r28",29:"r28",30:"r28",31:"r28",32:"r28",33:"r28",34:"r28",35:"r28",36:"r28",37:"r28",38:"r28",39:"r28",40:"r28",41:"r28",42:"r28",43:"r28",44:"r28",45:"r28",46:"r28",47:"r28",48:"r28",49:"r28",50:"r28",51:"r28",52:"r28",53:"r28",54:"r28",55:"r28",56:"r28",57:"r28",58:"r28"},{23:"r29",24:"r29",25:"r29",26:"r29",27:"r29",28:"r29",29:"r29",30:"r29",31:"r29",32:"r29",33:"r29",34:"r29",35:"r29",36:"r29",37:"r29",38:"r29",39:"r29",40:"r29",41:"r29",42:"r29",43:"r29",44:"r29",45:"r29",46:"r29",47:"r29",48:"r29",49:"r29",50:"r29",51:"r29",52:"r29",53:"r29",54:"r29",55:"r29",56:"r29",57:"r29",58:"r29"},{23:"r30",24:"r30",25:"r30",26:"r30",27:"r30",28:"r30",29:"r30",30:"r30",31:"r30",32:"r30",33:"r30",34:"r30",35:"r30",36:"r30",37:"r30",38:"r30",39:"r30",40:"r30",41:"r30",42:"r30",43:"r30",44:"r30",45:"r30",46:"r30",47:"r30",48:"r30",49:"r30",50:"r30",51:"r30",52:"r30",53:"r30",54:"r30",55:"r30",56:"r30",57:"r30",58:"r30"},{23:"r31",24:"r31",25:"r31",26:"r31",27:"r31",28:"r31",29:"r31",30:"r31",31:"r31",32:"r31",33:"r31",34:"r31",35:"r31",36:"r31",37:"r31",38:"r31",39:"r31",40:"r31",41:"r31",42:"r31",43:"r31",44:"r31",45:"r31",46:"r31",47:"r31",48:"r31",49:"r31",50:"r31",51:"r31",52:"r31",53:"r31",54:"r31",55:"r31",56:"r31",57:"r31",58:"r31"},{23:"r32",24:"r32",25:"r32",26:"r32",27:"r32",28:"r32",29:"r32",30:"r32",31:"r32",32:"r32",33:"r32",34:"r32",35:"r32",36:"r32",37:"r32",38:"r32",39:"r32",40:"r32",41:"r32",42:"r32",43:"r32",44:"r32",45:"r32",46:"r32",47:"r32",48:"r32",49:"r32",50:"r32",51:"r32",52:"r32",53:"r32",54:"r32",55:"r32",56:"r32",57:"r32",58:"r32"},{23:"r33",24:"r33",25:"r33",26:"r33",27:"r33",28:"r33",29:"r33",30:"r33",31:"r33",32:"r33",33:"r33",34:"r33",35:"r33",36:"r33",37:"r33",38:"r33",39:"r33",40:"r33",41:"r33",42:"r33",43:"r33",44:"r33",45:"r33",46:"r33",47:"r33",48:"r33",49:"r33",50:"r33",51:"r33",52:"r33",53:"r33",54:"r33",55:"r33",56:"r33",57:"r33",58:"r33"},{23:"r34",24:"r34",25:"r34",26:"r34",27:"r34",28:"r34",29:"r34",30:"r34",31:"r34",32:"r34",33:"r34",34:"r34",35:"r34",36:"r34",37:"r34",38:"r34",39:"r34",40:"r34",41:"r34",42:"r34",43:"r34",44:"r34",45:"r34",46:"r34",47:"r34",48:"r34",49:"r34",50:"r34",51:"r34",52:"r34",53:"r34",54:"r34",55:"r34",56:"r34",57:"r34",58:"r34"},{23:"r35",24:"r35",25:"r35",26:"r35",27:"r35",28:"r35",29:"r35",30:"r35",31:"r35",32:"r35",33:"r35",34:"r35",35:"r35",36:"r35",37:"r35",38:"r35",39:"r35",40:"r35",41:"r35",42:"r35",43:"r35",44:"r35",45:"r35",46:"r35",47:"r35",48:"r35",49:"r35",50:"r35",51:"r35",52:"r35",53:"r35",54:"r35",55:"r35",56:"r35",57:"r35",58:"r35"},{23:"r36",24:"r36",25:"r36",26:"r36",27:"r36",28:"r36",29:"r36",30:"r36",31:"r36",32:"r36",33:"r36",34:"r36",35:"r36",36:"r36",37:"r36",38:"r36",39:"r36",40:"r36",41:"r36",42:"r36",43:"r36",44:"r36",45:"r36",46:"r36",47:"r36",48:"r36",49:"r36",50:"r36",51:"r36",52:"r36",53:"r36",54:"r36",55:"r36",56:"r36",57:"r36",58:"r36"},{10:70,18:65,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{10:70,18:83,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{23:"r47",24:"r47",25:"r47",26:"r47",27:"r47",28:"r47",29:"r47",30:"r47",31:"r47",32:"r47",33:"r47",34:"r47",35:"r47",36:"r47",37:"r47",38:"r47",39:"r47",40:"r47",41:"r47",42:"r47",43:"r47",44:"r47",45:"r47",46:"r47",47:"r47",48:"r47",49:"r47",50:"r47",51:"r47",52:"r47",53:"r47",54:"r47",55:"r47",57:"r47"},{23:"r48",24:"r48",25:"r48",26:"r48",27:"r48",28:"r48",29:"r48",30:"r48",31:"r48",32:"r48",33:"r48",34:"r48",35:"r48",36:"r48",37:"r48",38:"r48",39:"r48",40:"r48",41:"r48",42:"r48",43:"r48",44:"r48",45:"r48",46:"r48",47:"r48",48:"r48",49:"r48",50:"r48",51:"r48",52:"r48",53:"r48",54:"r48",55:"r48",57:"r48"},{4:85,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:87,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{4:89,5:5,6:6,24:"r10",25:"r10",26:"r10",27:"r10",28:"r10",29:"r10",30:"r10",31:"r10",32:"r10",33:"r10",34:"r10",35:"r10",36:"r10",37:"r10",38:"r10",39:"r10",40:"r10",41:"r10",42:"r10",43:"r10",44:"r10",45:"r10",52:"r10",53:"r10",54:"r10",55:"r10",57:"r10"},{23:"r13",24:"r13",25:"r13",26:"r13",27:"r13",28:"r13",29:"r13",30:"r13",31:"r13",32:"r13",33:"r13",34:"r13",35:"r13",36:"r13",37:"r13",38:"r13",39:"r13",40:"r13",41:"r13",42:"r13",43:"r13",44:"r13",45:"r13",52:"r13",53:"r13",54:"r13",55:"r13",57:"r13"},{23:"r37",24:"r37",25:"r37",26:"r37",27:"r37",28:"r37",29:"r37",30:"r37",31:"r37",32:"r37",33:"r37",34:"r37",35:"r37",36:"r37",37:"r37",38:"r37",39:"r37",40:"r37",41:"r37",42:"r37",43:"r37",44:"r37",45:"r37",52:"r37",53:"r37",54:"r37",55:"r37",57:"r37"},{23:"r39",24:"r39",25:"r39",26:"r39",27:"r39",28:"r39",29:"r39",30:"r39",31:"r39",32:"r39",33:"r39",34:"r39",35:"r39",36:"r39",37:"r39",38:"r39",39:"r39",40:"r39",41:"r39",42:"r39",43:"r39",44:"r39",45:"r39",46:"s56",52:"r39",53:"r39",54:"r39",55:"r39",57:"r39"},{23:"r41",24:"r41",25:"r41",26:"r41",27:"r41",28:"r41",29:"r41",30:"r41",31:"r41",32:"r41",33:"r41",34:"r41",35:"r41",36:"r41",37:"r41",38:"r41",39:"r41",40:"r41",41:"r41",42:"r41",43:"r41",44:"r41",45:"r41",46:"r41",52:"r41",53:"r41",54:"r41",55:"r41",57:"r41"},{23:"r42",24:"r42",25:"r42",26:"r42",27:"r42",28:"r42",29:"r42",30:"r42",31:"r42",32:"r42",33:"r42",34:"r42",35:"r42",36:"r42",37:"r42",38:"r42",39:"r42",40:"r42",41:"r42",42:"r42",43:"r42",44:"r42",45:"r42",46:"r42",52:"r42",53:"r42",54:"r42",55:"r42",57:"r42"},{23:"r43",24:"r43",25:"r43",26:"r43",27:"r43",28:"r43",29:"r43",30:"r43",31:"r43",32:"r43",33:"r43",34:"r43",35:"r43",36:"r43",37:"r43",38:"r43",39:"r43",40:"r43",41:"r43",42:"r43",43:"r43",44:"r43",45:"r43",46:"r43",52:"r43",53:"r43",54:"r43",55:"r43",57:"r43"},{23:"r44",24:"r44",25:"r44",26:"r44",27:"r44",28:"r44",29:"r44",30:"r44",31:"r44",32:"r44",33:"r44",34:"r44",35:"r44",36:"r44",37:"r44",38:"r44",39:"r44",40:"r44",41:"r44",42:"r44",43:"r44",44:"r44",45:"r44",46:"r44",52:"r44",53:"r44",54:"r44",55:"r44",57:"r44"},{23:"r45",24:"r45",25:"r45",26:"r45",27:"r45",28:"r45",29:"r45",30:"r45",31:"r45",32:"r45",33:"r45",34:"r45",35:"r45",36:"r45",37:"r45",38:"r45",39:"r45",40:"r45",41:"r45",42:"r45",43:"r45",44:"r45",45:"r45",46:"r45",52:"r45",53:"r45",54:"r45",55:"r45",57:"r45"},{23:"r46",24:"r46",25:"r46",26:"r46",27:"r46",28:"r46",29:"r46",30:"r46",31:"r46",32:"r46",33:"r46",34:"r46",35:"r46",36:"r46",37:"r46",38:"r46",39:"r46",40:"r46",41:"r46",42:"r46",43:"r46",44:"r46",45:"r46",46:"r46",52:"r46",53:"r46",54:"r46",55:"r46",57:"r46"},{23:"r40",24:"r40",25:"r40",26:"r40",27:"r40",28:"r40",29:"r40",30:"r40",31:"r40",32:"r40",33:"r40",34:"r40",35:"r40",36:"r40",37:"r40",38:"r40",39:"r40",40:"r40",41:"r40",42:"r40",43:"r40",44:"r40",45:"r40",52:"r40",53:"r40",54:"r40",55:"r40",57:"r40"},{25:"s12",31:"s58"},{23:"r18",24:"r18",25:"r18",26:"r18",27:"r18",28:"r18",29:"r18",30:"r18",31:"r18",32:"r18",33:"r18",34:"r18",35:"r18",36:"r18",37:"r18",38:"r18",39:"r18",40:"r18",41:"r18",42:"r18",43:"r18",44:"r18",45:"r18",52:"r18",53:"r18",54:"r18",55:"r18",57:"r18"},{25:"s12",31:"s60"},{23:"r19",24:"r19",25:"r19",26:"r19",27:"r19",28:"r19",29:"r19",30:"r19",31:"r19",32:"r19",33:"r19",34:"r19",35:"r19",36:"r19",37:"r19",38:"r19",39:"r19",40:"r19",41:"r19",42:"r19",43:"r19",44:"r19",45:"r19",52:"r19",53:"r19",54:"r19",55:"r19",57:"r19"},{25:"s12",31:"s62"},{23:"r20",24:"r20",25:"r20",26:"r20",27:"r20",28:"r20",29:"r20",30:"r20",31:"r20",32:"r20",33:"r20",34:"r20",35:"r20",36:"r20",37:"r20",38:"r20",39:"r20",40:"r20",41:"r20",42:"r20",43:"r20",44:"r20",45:"r20",52:"r20",53:"r20",54:"r20",55:"r20",57:"r20"},{25:"s12",31:"s64"},{23:"r21",24:"r21",25:"r21",26:"r21",27:"r21",28:"r21",29:"r21",30:"r21",31:"r21",32:"r21",33:"r21",34:"r21",35:"r21",36:"r21",37:"r21",38:"r21",39:"r21",40:"r21",41:"r21",42:"r21",43:"r21",44:"r21",45:"r21",52:"r21",53:"r21",54:"r21",55:"r21",57:"r21"},{56:"s72"},{56:"r55"},{10:70,20:73,21:75,22:76,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r56",58:"s74"},{24:"r62",28:"r62",35:"r62",36:"r62",37:"r62",38:"r62",39:"r62",40:"r62",41:"r62",42:"r62",43:"r62",44:"r62",45:"r62",56:"r62",58:"r62"},{24:"r63",28:"r63",35:"r63",36:"r63",37:"r63",38:"r63",39:"r63",40:"r63",41:"r63",42:"r63",43:"r63",44:"r63",45:"r63",56:"r63",58:"r63"},{24:"r64",28:"r64",35:"r64",36:"r64",37:"r64",38:"r64",39:"r64",40:"r64",41:"r64",42:"r64",43:"r64",44:"r64",45:"r64",56:"r64",58:"r64"},{24:"r65",28:"r65",35:"r65",36:"r65",37:"r65",38:"r65",39:"r65",40:"r65",41:"r65",42:"r65",43:"r65",44:"r65",45:"r65",56:"r65",58:"r65"},{23:"r52",24:"r52",25:"r52",26:"r52",27:"r52",28:"r52",29:"r52",30:"r52",31:"r52",32:"r52",33:"r52",34:"r52",35:"r52",36:"r52",37:"r52",38:"r52",39:"r52",40:"r52",41:"r52",42:"r52",43:"r52",44:"r52",45:"r52",46:"r52",47:"r52",48:"r52",49:"r52",50:"r52",51:"r52",52:"r52",53:"r52",54:"r52",55:"r52",57:"r52"},{56:"r57"},{10:70,21:77,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r62",58:"s68"},{56:"r59"},{10:70,20:79,21:75,22:76,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r63",58:"s80"},{10:70,18:78,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{56:"r58"},{56:"r60"},{10:70,21:81,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r62",58:"s68"},{10:70,18:82,19:66,21:67,22:69,24:"s28",28:"s71",35:"s29",36:"s30",37:"s31",38:"s32",39:"s33",40:"s34",41:"s35",42:"s36",43:"s37",44:"s38",45:"s39",56:"r54",58:"s68"},{56:"r61"},{56:"s84"},{23:"r53",24:"r53",25:"r53",26:"r53",27:"r53",28:"r53",29:"r53",30:"r53",31:"r53",32:"r53",33:"r53",34:"r53",35:"r53",36:"r53",37:"r53",38:"r53",39:"r53",40:"r53",41:"r53",42:"r53",43:"r53",44:"r53",45:"r53",46:"r53",47:"r53",48:"r53",49:"r53",50:"r53",51:"r53",52:"r53",53:"r53",54:"r53",55:"r53",57:"r53"},{25:"s12",31:"s86"},{23:"r49",24:"r49",25:"r49",26:"r49",27:"r49",28:"r49",29:"r49",30:"r49",31:"r49",32:"r49",33:"r49",34:"r49",35:"r49",36:"r49",37:"r49",38:"r49",39:"r49",40:"r49",41:"r49",42:"r49",43:"r49",44:"r49",45:"r49",46:"r49",47:"r49",48:"r49",49:"r49",50:"r49",51:"r49",52:"r49",53:"r49",54:"r49",55:"r49",57:"r49"},{25:"s12",31:"s88"},{23:"r50",24:"r50",25:"r50",26:"r50",27:"r50",28:"r50",29:"r50",30:"r50",31:"r50",32:"r50",33:"r50",34:"r50",35:"r50",36:"r50",37:"r50",38:"r50",39:"r50",40:"r50",41:"r50",42:"r50",43:"r50",44:"r50",45:"r50",46:"r50",47:"r50",48:"r50",49:"r50",50:"r50",51:"r50",52:"r50",53:"r50",54:"r50",55:"r50",57:"r50"},{25:"s12",31:"s90"},{23:"r51",24:"r51",25:"r51",26:"r51",27:"r51",28:"r51",29:"r51",30:"r51",31:"r51",32:"r51",33:"r51",34:"r51",35:"r51",36:"r51",37:"r51",38:"r51",39:"r51",40:"r51",41:"r51",42:"r51",43:"r51",44:"r51",45:"r51",46:"r51",47:"r51",48:"r51",49:"r51",50:"r51",51:"r51",52:"r51",53:"r51",54:"r51",55:"r51",57:"r51"}],ae=[],le=void 0,Ol=[[/^#[^\n]+/,function(){}],[/^\s+/,function(){}],[/^-/,function(){return"DASH"}],[/^\//,function(){return"CHAR"}],[/^#/,function(){return"CHAR"}],[/^\|/,function(){return"CHAR"}],[/^\./,function(){return"CHAR"}],[/^\{/,function(){return"CHAR"}],[/^\{\d+\}/,function(){return"RANGE_EXACT"}],[/^\{\d+,\}/,function(){return"RANGE_OPEN"}],[/^\{\d+,\d+\}/,function(){return"RANGE_CLOSED"}],[/^\\k<(([\u0041-\u005a\u0061-\u007a\u00aa\u00b5\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u05d0-\u05ea\u05ef-\u05f2\u0620-\u064a\u066e-\u066f\u0671-\u06d3\u06d5\u06e5-\u06e6\u06ee-\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4-\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc-\u09dd\u09df-\u09e1\u09f0-\u09f1\u09fc\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0-\u0ae1\u0af9\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3d\u0b5c-\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d\u0c58-\u0c5a\u0c60-\u0c61\u0c80\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0-\u0ce1\u0cf1-\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d54-\u0d56\u0d5f-\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32-\u0e33\u0e40-\u0e46\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0eb0\u0eb2-\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0edc-\u0edf\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u1000-\u102a\u103f\u1050-\u1055\u105a-\u105d\u1061\u1065-\u1066\u106e-\u1070\u1075-\u1081\u108e\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u1820-\u1878\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191e\u1950-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u1a00-\u1a16\u1a20-\u1a54\u1aa7\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae-\u1baf\u1bba-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1ce9-\u1cec\u1cee-\u1cf3\u1cf5-\u1cf6\u1cfa\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2cf2-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303c\u3041-\u3096\u309b-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a-\ua62b\ua640-\ua66e\ua67f-\ua69d\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua8fd-\ua8fe\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\ua9e0-\ua9e4\ua9e6-\ua9ef\ua9fa-\ua9fe\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa60-\uaa76\uaa7a\uaa7e-\uaaaf\uaab1\uaab5-\uaab6\uaab9-\uaabd\uaac0\uaac2\uaadb-\uaadd\uaae0-\uaaea\uaaf2-\uaaf4\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45\udfe0-\udff6]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f\udc80-\udcaf\udcc4-\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08-\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67-\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf50\udf93-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud838[\udd00-\udd2c\udd37-\udd3d\udd4e\udec0-\udeeb]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd4b]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\}))(([\u0030-\u0039\u0041-\u005a\u005f\u0061-\u007a\u00aa\u00b5\u00b7\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u05d0-\u05ea\u05ef-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u07fd\u0800-\u082d\u0840-\u085b\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u08d3-\u08e1\u08e3-\u0963\u0966-\u096f\u0971-\u0983\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7-\u09c8\u09cb-\u09ce\u09d7\u09dc-\u09dd\u09df-\u09e3\u09e6-\u09f1\u09fc\u09fe\u0a01-\u0a03\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a3c\u0a3e-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0af9-\u0aff\u0b01-\u0b03\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47-\u0b48\u0b4b-\u0b4d\u0b56-\u0b57\u0b5c-\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82-\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c00-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c58-\u0c5a\u0c60-\u0c63\u0c66-\u0c6f\u0c80-\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5-\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1-\u0cf2\u0d00-\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d54-\u0d57\u0d5f-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82-\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0de6-\u0def\u0df2-\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18-\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1369-\u1371\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772-\u1773\u1780-\u17d3\u17d7\u17dc-\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1878\u1880-\u18aa\u18b0-\u18f5\u1900-\u191e\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19da\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1ab0-\u1abd\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1cd0-\u1cd2\u1cd4-\u1cfa\u1d00-\u1df9\u1dfb-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u203f-\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua827\ua840-\ua873\ua880-\ua8c5\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua8fd-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\ua9e0-\ua9fe\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabea\uabec-\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe2f\ufe33-\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\uddfd\ude80-\ude9c\udea0-\uded0\udee0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf7a\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udca0-\udca9\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00-\ude03\ude05-\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude38-\ude3a\ude3f\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee6\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd27\udd30-\udd39\udf00-\udf1c\udf27\udf30-\udf50\udfe0-\udff6]|\ud804[\udc00-\udc46\udc66-\udc6f\udc7f-\udcba\udcd0-\udce8\udcf0-\udcf9\udd00-\udd34\udd36-\udd3f\udd44-\udd46\udd50-\udd73\udd76\udd80-\uddc4\uddc9-\uddcc\uddd0-\uddda\udddc\ude00-\ude11\ude13-\ude37\ude3e\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udeea\udef0-\udef9\udf00-\udf03\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3b-\udf44\udf47-\udf48\udf4b-\udf4d\udf50\udf57\udf5d-\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc00-\udc4a\udc50-\udc59\udc5e-\udc5f\udc80-\udcc5\udcc7\udcd0-\udcd9\udd80-\uddb5\uddb8-\uddc0\uddd8-\udddd\ude00-\ude40\ude44\ude50-\ude59\ude80-\udeb8\udec0-\udec9\udf00-\udf1a\udf1d-\udf2b\udf30-\udf39]|\ud806[\udc00-\udc3a\udca0-\udce9\udcff\udda0-\udda7\uddaa-\uddd7\uddda-\udde1\udde3-\udde4\ude00-\ude3e\ude47\ude50-\ude99\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc40\udc50-\udc59\udc72-\udc8f\udc92-\udca7\udca9-\udcb6\udd00-\udd06\udd08-\udd09\udd0b-\udd36\udd3a\udd3c-\udd3d\udd3f-\udd47\udd50-\udd59\udd60-\udd65\udd67-\udd68\udd6a-\udd8e\udd90-\udd91\udd93-\udd98\udda0-\udda9\udee0-\udef6]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude60-\ude69\uded0-\udeed\udef0-\udef4\udf00-\udf36\udf40-\udf43\udf50-\udf59\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf4f-\udf87\udf8f-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9d-\udc9e]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb\udfce-\udfff]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23-\udc24\udc26-\udc2a\udd00-\udd2c\udd30-\udd3d\udd40-\udd49\udd4e\udec0-\udef9]|\ud83a[\udc00-\udcc4\udcd0-\udcd6\udd00-\udd4b\udd50-\udd59]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\udb40[\udd00-\uddef])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\})|[\u200c\u200d])*>/,function(){var e=Re.slice(3,-1);return Ha(e,this.getCurrentState()),"NAMED_GROUP_REF"}],[/^\\b/,function(){return"ESC_b"}],[/^\\B/,function(){return"ESC_B"}],[/^\\c[a-zA-Z]/,function(){return"CTRL_CH"}],[/^\\0\d{1,2}/,function(){return"OCT_CODE"}],[/^\\0/,function(){return"DEC_CODE"}],[/^\\\d{1,3}/,function(){return"DEC_CODE"}],[/^\\u[dD][89abAB][0-9a-fA-F]{2}\\u[dD][c-fC-F][0-9a-fA-F]{2}/,function(){return"U_CODE_SURROGATE"}],[/^\\u\{[0-9a-fA-F]{1,}\}/,function(){return"U_CODE"}],[/^\\u[0-9a-fA-F]{4}/,function(){return"U_CODE"}],[/^\\[pP]\{\w+(?:=\w+)?\}/,function(){return"U_PROP_VALUE_EXP"}],[/^\\x[0-9a-fA-F]{2}/,function(){return"HEX_CODE"}],[/^\\[tnrdDsSwWvf]/,function(){return"META_CHAR"}],[/^\\\//,function(){return"ESC_CHAR"}],[/^\\[ #]/,function(){return"ESC_CHAR"}],[/^\\[\^\$\.\*\+\?\(\)\\\[\]\{\}\|\/]/,function(){return"ESC_CHAR"}],[/^\\[^*?+\[()\\|]/,function(){var e=this.getCurrentState();if(e==="u_class"&&Re==="\\-")return"ESC_CHAR";if(e==="u"||e==="xu"||e==="u_class")throw new SyntaxError("invalid Unicode escape "+Re);return"ESC_CHAR"}],[/^\(/,function(){return"CHAR"}],[/^\)/,function(){return"CHAR"}],[/^\(\?=/,function(){return"POS_LA_ASSERT"}],[/^\(\?!/,function(){return"NEG_LA_ASSERT"}],[/^\(\?<=/,function(){return"POS_LB_ASSERT"}],[/^\(\?<!/,function(){return"NEG_LB_ASSERT"}],[/^\(\?:/,function(){return"NON_CAPTURE_GROUP"}],[/^\(\?<(([\u0041-\u005a\u0061-\u007a\u00aa\u00b5\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u05d0-\u05ea\u05ef-\u05f2\u0620-\u064a\u066e-\u066f\u0671-\u06d3\u06d5\u06e5-\u06e6\u06ee-\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4-\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc-\u09dd\u09df-\u09e1\u09f0-\u09f1\u09fc\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0-\u0ae1\u0af9\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3d\u0b5c-\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d\u0c58-\u0c5a\u0c60-\u0c61\u0c80\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0-\u0ce1\u0cf1-\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d54-\u0d56\u0d5f-\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32-\u0e33\u0e40-\u0e46\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0eb0\u0eb2-\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0edc-\u0edf\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u1000-\u102a\u103f\u1050-\u1055\u105a-\u105d\u1061\u1065-\u1066\u106e-\u1070\u1075-\u1081\u108e\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u1820-\u1878\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191e\u1950-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u1a00-\u1a16\u1a20-\u1a54\u1aa7\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae-\u1baf\u1bba-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1ce9-\u1cec\u1cee-\u1cf3\u1cf5-\u1cf6\u1cfa\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2cf2-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303c\u3041-\u3096\u309b-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a-\ua62b\ua640-\ua66e\ua67f-\ua69d\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua8fd-\ua8fe\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\ua9e0-\ua9e4\ua9e6-\ua9ef\ua9fa-\ua9fe\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa60-\uaa76\uaa7a\uaa7e-\uaaaf\uaab1\uaab5-\uaab6\uaab9-\uaabd\uaac0\uaac2\uaadb-\uaadd\uaae0-\uaaea\uaaf2-\uaaf4\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45\udfe0-\udff6]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc5f\udc80-\udcaf\udcc4-\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udeb8\udf00-\udf1a]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff\udda0-\udda7\uddaa-\uddd0\udde1\udde3\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08-\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67-\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf50\udf93-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud838[\udd00-\udd2c\udd37-\udd3d\udd4e\udec0-\udeeb]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd4b]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\}))(([\u0030-\u0039\u0041-\u005a\u005f\u0061-\u007a\u00aa\u00b5\u00b7\u00ba\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376-\u0377\u037a-\u037d\u037f\u0386-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u052f\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u05d0-\u05ea\u05ef-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u07fd\u0800-\u082d\u0840-\u085b\u0860-\u086a\u08a0-\u08b4\u08b6-\u08bd\u08d3-\u08e1\u08e3-\u0963\u0966-\u096f\u0971-\u0983\u0985-\u098c\u098f-\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7-\u09c8\u09cb-\u09ce\u09d7\u09dc-\u09dd\u09df-\u09e3\u09e6-\u09f1\u09fc\u09fe\u0a01-\u0a03\u0a05-\u0a0a\u0a0f-\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32-\u0a33\u0a35-\u0a36\u0a38-\u0a39\u0a3c\u0a3e-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2-\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0af9-\u0aff\u0b01-\u0b03\u0b05-\u0b0c\u0b0f-\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32-\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47-\u0b48\u0b4b-\u0b4d\u0b56-\u0b57\u0b5c-\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82-\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99-\u0b9a\u0b9c\u0b9e-\u0b9f\u0ba3-\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c00-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c58-\u0c5a\u0c60-\u0c63\u0c66-\u0c6f\u0c80-\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5-\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1-\u0cf2\u0d00-\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d54-\u0d57\u0d5f-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82-\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0de6-\u0def\u0df2-\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81-\u0e82\u0e84\u0e86-\u0e8a\u0e8c-\u0ea3\u0ea5\u0ea7-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18-\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1369-\u1371\u1380-\u138f\u13a0-\u13f5\u13f8-\u13fd\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f8\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772-\u1773\u1780-\u17d3\u17d7\u17dc-\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1878\u1880-\u18aa\u18b0-\u18f5\u1900-\u191e\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19da\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1ab0-\u1abd\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1c80-\u1c88\u1c90-\u1cba\u1cbd-\u1cbf\u1cd0-\u1cd2\u1cd4-\u1cfa\u1d00-\u1df9\u1dfb-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u203f-\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2118-\u211d\u2124\u2126\u2128\u212a-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312f\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fef\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua7bf\ua7c2-\ua7c6\ua7f7-\ua827\ua840-\ua873\ua880-\ua8c5\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua8fd-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\ua9e0-\ua9fe\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uab30-\uab5a\uab5c-\uab67\uab70-\uabea\uabec-\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe2f\ufe33-\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]|\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c-\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\uddfd\ude80-\ude9c\udea0-\uded0\udee0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf7a\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udca0-\udca9\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37-\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4-\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe-\uddbf\ude00-\ude03\ude05-\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude38-\ude3a\ude3f\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee6\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd27\udd30-\udd39\udf00-\udf1c\udf27\udf30-\udf50\udfe0-\udff6]|\ud804[\udc00-\udc46\udc66-\udc6f\udc7f-\udcba\udcd0-\udce8\udcf0-\udcf9\udd00-\udd34\udd36-\udd3f\udd44-\udd46\udd50-\udd73\udd76\udd80-\uddc4\uddc9-\uddcc\uddd0-\uddda\udddc\ude00-\ude11\ude13-\ude37\ude3e\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udeea\udef0-\udef9\udf00-\udf03\udf05-\udf0c\udf0f-\udf10\udf13-\udf28\udf2a-\udf30\udf32-\udf33\udf35-\udf39\udf3b-\udf44\udf47-\udf48\udf4b-\udf4d\udf50\udf57\udf5d-\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc00-\udc4a\udc50-\udc59\udc5e-\udc5f\udc80-\udcc5\udcc7\udcd0-\udcd9\udd80-\uddb5\uddb8-\uddc0\uddd8-\udddd\ude00-\ude40\ude44\ude50-\ude59\ude80-\udeb8\udec0-\udec9\udf00-\udf1a\udf1d-\udf2b\udf30-\udf39]|\ud806[\udc00-\udc3a\udca0-\udce9\udcff\udda0-\udda7\uddaa-\uddd7\uddda-\udde1\udde3-\udde4\ude00-\ude3e\ude47\ude50-\ude99\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc40\udc50-\udc59\udc72-\udc8f\udc92-\udca7\udca9-\udcb6\udd00-\udd06\udd08-\udd09\udd0b-\udd36\udd3a\udd3c-\udd3d\udd3f-\udd47\udd50-\udd59\udd60-\udd65\udd67-\udd68\udd6a-\udd8e\udd90-\udd91\udd93-\udd98\udda0-\udda9\udee0-\udef6]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\ude60-\ude69\uded0-\udeed\udef0-\udef4\udf00-\udf36\udf40-\udf43\udf50-\udf59\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf4a\udf4f-\udf87\udf8f-\udf9f\udfe0-\udfe1\udfe3]|\ud81c[\udc00-\udfff]|\ud81d[\udc00-\udfff]|\ud81e[\udc00-\udfff]|\ud81f[\udc00-\udfff]|\ud820[\udc00-\udfff]|\ud821[\udc00-\udff7]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd50-\udd52\udd64-\udd67\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9d-\udc9e]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e-\udc9f\udca2\udca5-\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb\udfce-\udfff]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23-\udc24\udc26-\udc2a\udd00-\udd2c\udd30-\udd3d\udd40-\udd49\udd4e\udec0-\udef9]|\ud83a[\udc00-\udcc4\udcd0-\udcd6\udd00-\udd4b\udd50-\udd59]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21-\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51-\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61-\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud840[\udc00-\udfff]|\ud841[\udc00-\udfff]|\ud842[\udc00-\udfff]|\ud843[\udc00-\udfff]|\ud844[\udc00-\udfff]|\ud845[\udc00-\udfff]|\ud846[\udc00-\udfff]|\ud847[\udc00-\udfff]|\ud848[\udc00-\udfff]|\ud849[\udc00-\udfff]|\ud84a[\udc00-\udfff]|\ud84b[\udc00-\udfff]|\ud84c[\udc00-\udfff]|\ud84d[\udc00-\udfff]|\ud84e[\udc00-\udfff]|\ud84f[\udc00-\udfff]|\ud850[\udc00-\udfff]|\ud851[\udc00-\udfff]|\ud852[\udc00-\udfff]|\ud853[\udc00-\udfff]|\ud854[\udc00-\udfff]|\ud855[\udc00-\udfff]|\ud856[\udc00-\udfff]|\ud857[\udc00-\udfff]|\ud858[\udc00-\udfff]|\ud859[\udc00-\udfff]|\ud85a[\udc00-\udfff]|\ud85b[\udc00-\udfff]|\ud85c[\udc00-\udfff]|\ud85d[\udc00-\udfff]|\ud85e[\udc00-\udfff]|\ud85f[\udc00-\udfff]|\ud860[\udc00-\udfff]|\ud861[\udc00-\udfff]|\ud862[\udc00-\udfff]|\ud863[\udc00-\udfff]|\ud864[\udc00-\udfff]|\ud865[\udc00-\udfff]|\ud866[\udc00-\udfff]|\ud867[\udc00-\udfff]|\ud868[\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86a[\udc00-\udfff]|\ud86b[\udc00-\udfff]|\ud86c[\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud86f[\udc00-\udfff]|\ud870[\udc00-\udfff]|\ud871[\udc00-\udfff]|\ud872[\udc00-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud874[\udc00-\udfff]|\ud875[\udc00-\udfff]|\ud876[\udc00-\udfff]|\ud877[\udc00-\udfff]|\ud878[\udc00-\udfff]|\ud879[\udc00-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]|\udb40[\udd00-\uddef])|[$_]|(\\u[0-9a-fA-F]{4}|\\u\{[0-9a-fA-F]{1,}\})|[\u200c\u200d])*>/,function(){return Re=Re.slice(3,-1),Ha(Re,this.getCurrentState()),"NAMED_CAPTURE_GROUP"}],[/^\(/,function(){return"L_PAREN"}],[/^\)/,function(){return"R_PAREN"}],[/^[*?+[^$]/,function(){return"CHAR"}],[/^\\\]/,function(){return"ESC_CHAR"}],[/^\]/,function(){return this.popState(),"R_BRACKET"}],[/^\^/,function(){return"BOS"}],[/^\$/,function(){return"EOS"}],[/^\*/,function(){return"STAR"}],[/^\?/,function(){return"Q_MARK"}],[/^\+/,function(){return"PLUS"}],[/^\|/,function(){return"BAR"}],[/^\./,function(){return"ANY"}],[/^\//,function(){return"SLASH"}],[/^[^*?+\[()\\|]/,function(){return"CHAR"}],[/^\[\^/,function(){var e=this.getCurrentState();return this.pushState(e==="u"||e==="xu"?"u_class":"class"),"NEG_CLASS"}],[/^\[/,function(){var e=this.getCurrentState();return this.pushState(e==="u"||e==="xu"?"u_class":"class"),"L_BRACKET"}]],Ml={INITIAL:[8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],u:[8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],xu:[0,1,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],x:[0,1,8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50,51],u_class:[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],class:[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,20,22,23,24,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51]},Va={type:Xo,value:""};le={initString:function(t){return this._string=t,this._cursor=0,this._states=["INITIAL"],this._tokensQueue=[],this._currentLine=1,this._currentColumn=0,this._currentLineBeginOffset=0,this._tokenStartOffset=0,this._tokenEndOffset=0,this._tokenStartLine=1,this._tokenEndLine=1,this._tokenStartColumn=0,this._tokenEndColumn=0,this},getStates:function(){return this._states},getCurrentState:function(){return this._states[this._states.length-1]},pushState:function(t){this._states.push(t)},begin:function(t){this.pushState(t)},popState:function(){return this._states.length>1?this._states.pop():this._states[0]},getNextToken:function(){if(this._tokensQueue.length>0)return this.onToken(this._toToken(this._tokensQueue.shift()));if(!this.hasMoreTokens())return this.onToken(Va);for(var t=this._string.slice(this._cursor),n=Ml[this.getCurrentState()],r=0;r<n.length;r++){var u=n[r],i=Ol[u],a=this._match(t,i[0]);if(t===""&&a===""&&this._cursor++,a!==null){Re=a,Re.length;var s=i[1].call(this);if(!s)return this.getNextToken();if(Array.isArray(s)){var o=s.slice(1);if(s=s[0],o.length>0){var c;(c=this._tokensQueue).unshift.apply(c,Qo(o))}}return this.onToken(this._toToken(s,Re))}}if(this.isEOF())return this._cursor++,Va;this.throwUnexpectedToken(t[0],this._currentLine,this._currentColumn)},throwUnexpectedToken:function(t,n,r){var u=this._string.split(`
`)[n-1],i="";if(u){var a=" ".repeat(r);i=`

`+u+`
`+a+`^
`}throw new SyntaxError(i+'Unexpected token: "'+t+'" at '+n+":"+r+".")},getCursor:function(){return this._cursor},getCurrentLine:function(){return this._currentLine},getCurrentColumn:function(){return this._currentColumn},_captureLocation:function(t){var n=/\n/g;this._tokenStartOffset=this._cursor,this._tokenStartLine=this._currentLine,this._tokenStartColumn=this._tokenStartOffset-this._currentLineBeginOffset;for(var r=void 0;(r=n.exec(t))!==null;)this._currentLine++,this._currentLineBeginOffset=this._tokenStartOffset+r.index+1;this._tokenEndOffset=this._cursor+t.length,this._tokenEndLine=this._currentLine,this._tokenEndColumn=this._currentColumn=this._tokenEndOffset-this._currentLineBeginOffset},_toToken:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return{type:t,value:n,startOffset:this._tokenStartOffset,endOffset:this._tokenEndOffset,startLine:this._tokenStartLine,endLine:this._tokenEndLine,startColumn:this._tokenStartColumn,endColumn:this._tokenEndColumn}},isEOF:function(){return this._cursor===this._string.length},hasMoreTokens:function(){return this._cursor<=this._string.length},_match:function(t,n){var r=t.match(n);return r?(this._captureLocation(r[0]),this._cursor+=r[0].length,r[0]):null},onToken:function(t){return t}};Ut.lexer=le;Ut.tokenizer=le;Ut.options={captureLocations:!0};var Cn={setOptions:function(t){return Ut.options=t,this},getOptions:function(){return Ut.options},parse:function(t,n){if(!le)throw new Error("Tokenizer instance wasn't specified.");le.initString(t);var r=Ut.options;n&&(Ut.options=Object.assign({},Ut.options,n)),Cn.onParseBegin(t,le,Ut.options),ae.length=0,ae.push(0);var u=le.getNextToken(),i=null;do{u||(Ut.options=r,nc());var a=ae[ae.length-1],s=Ia[u.type];fi[a].hasOwnProperty(s)||(Ut.options=r,qa(u));var o=fi[a][s];if(o[0]==="s"){var c=null;Ut.options.captureLocations&&(c={startOffset:u.startOffset,endOffset:u.endOffset,startLine:u.startLine,endLine:u.endLine,startColumn:u.startColumn,endColumn:u.endColumn}),i=this.onShift(u),ae.push({symbol:Ia[i.type],semanticValue:i.value,loc:c},Number(o.slice(1))),u=le.getNextToken()}else if(o[0]==="r"){var d=o.slice(1),l=Al[d],h=typeof l[2]=="function",f=h?[]:null,m=h&&Ut.options.captureLocations?[]:null;if(l[1]!==0)for(var b=l[1];b-- >0;){ae.pop();var g=ae.pop();h&&(f.unshift(g.semanticValue),m&&m.unshift(g.loc))}var y={symbol:l[0]};if(h){Re=i?i.value:null,i&&i.value.length;var C=m!==null?f.concat(m):f;l[2].apply(l,Qo(C)),y.semanticValue=Y,m&&(y.loc=I)}var B=ae[ae.length-1],E=l[0];ae.push(y,fi[B][E])}else if(o==="acc"){ae.pop();var S=ae.pop();return(ae.length!==1||ae[0]!==0||le.hasMoreTokens())&&(Ut.options=r,qa(u)),S.hasOwnProperty("semanticValue")?(Ut.options=r,Cn.onParseEnd(S.semanticValue),S.semanticValue):(Cn.onParseEnd(),Ut.options=r,!0)}}while(le.hasMoreTokens()||ae.length>1)},setTokenizer:function(t){return le=t,Cn},getTokenizer:function(){return le},onParseBegin:function(t,n,r){},onParseEnd:function(t){},onShift:function(t){return t}},na=0,Or={},tc="";Cn.onParseBegin=function(e,t){tc=e,na=0,Or={};var n=e.lastIndexOf("/"),r=e.slice(n);r.includes("x")&&r.includes("u")?t.pushState("xu"):(r.includes("x")&&t.pushState("x"),r.includes("u")&&t.pushState("u"))};Cn.onShift=function(e){return(e.type==="L_PAREN"||e.type==="NAMED_CAPTURE_GROUP")&&(e.value=new String(e.value),e.value.groupNumber=++na),e};function hi(e){var t=e.match(/\d+/g).map(Number);if(Number.isFinite(t[1])&&t[1]<t[0])throw new SyntaxError("Numbers out of order in "+e+" quantifier");return t}function ja(e,t){if(e.kind==="control"||t.kind==="control"||!isNaN(e.codePoint)&&!isNaN(t.codePoint)&&e.codePoint>t.codePoint)throw new SyntaxError("Range "+e.value+"-"+t.value+" out of order in character class")}var Rn=Sl;function Bl(e,t){var n=e[1]==="P",r=e.indexOf("="),u=e.slice(3,r!==-1?r:-1),i=void 0,a=r===-1&&Rn.isGeneralCategoryValue(u),s=r===-1&&Rn.isBinaryPropertyName(u);if(a)i=u,u="General_Category";else if(s)i=u;else{if(!Rn.isValidName(u))throw new SyntaxError("Invalid unicode property name: "+u+".");if(i=e.slice(r+1,-1),!Rn.isValidValue(u,i))throw new SyntaxError("Invalid "+u+" unicode property value: "+i+".")}return Pt({type:"UnicodeProperty",name:u,value:i,negative:n,shorthand:a,binary:s,canonicalName:Rn.getCanonicalName(u)||u,canonicalValue:Rn.getCanonicalValue(i)||i},t)}function se(e,t,n){var r=void 0,u=void 0;switch(t){case"decimal":u=Number(e.slice(1)),r=String.fromCodePoint(u);break;case"oct":u=parseInt(e.slice(1),8),r=String.fromCodePoint(u);break;case"hex":case"unicode":if(e.lastIndexOf("\\u")>0){var i=e.split("\\u").slice(1),a=Tl(i,2),s=a[0],o=a[1];s=parseInt(s,16),o=parseInt(o,16),u=(s-55296)*1024+(o-56320)+65536,r=String.fromCodePoint(u)}else{var c=e.slice(2).replace("{","");if(u=parseInt(c,16),u>1114111)throw new SyntaxError("Bad character escape sequence: "+e);r=String.fromCodePoint(u)}break;case"meta":switch(e){case"\\t":r="	",u=r.codePointAt(0);break;case"\\n":r=`
`,u=r.codePointAt(0);break;case"\\r":r="\r",u=r.codePointAt(0);break;case"\\v":r="\v",u=r.codePointAt(0);break;case"\\f":r="\f",u=r.codePointAt(0);break;case"\\b":r="\b",u=r.codePointAt(0);case"\\0":r="\0",u=0;case".":r=".",u=NaN;break;default:u=NaN}break;case"simple":r=e,u=r.codePointAt(0);break}return Pt({type:"Char",value:e,kind:t,symbol:r,codePoint:u},n)}var Ll="gimsuxy";function Pl(e){var t=new Set,n=!0,r=!1,u=void 0;try{for(var i=e[Symbol.iterator](),a;!(n=(a=i.next()).done);n=!0){var s=a.value;if(t.has(s)||!Ll.includes(s))throw new SyntaxError("Invalid flags: "+e);t.add(s)}}catch(o){r=!0,u=o}finally{try{!n&&i.return&&i.return()}finally{if(r)throw u}}return e.split("").sort().join("")}function Nl(e,t){var n=Number(e.slice(1));return n>0&&n<=na?Pt({type:"Backreference",kind:"number",number:n,reference:n},t):se(e,"decimal",t)}var _l=/^\\u[0-9a-fA-F]{4}/,Rl=/^\\u\{[0-9a-fA-F]{1,}\}/,$l=/\\u\{[0-9a-fA-F]{1,}\}/;function Ha(e,t){var n=$l.test(e),r=t==="u"||t==="xu"||t==="u_class";if(n&&!r)throw new SyntaxError('invalid group Unicode name "'+e+'", use `u` flag.');return e}var Il=/\\u(?:([dD][89aAbB][0-9a-fA-F]{2})\\u([dD][c-fC-F][0-9a-fA-F]{2})|([dD][89aAbB][0-9a-fA-F]{2})|([dD][c-fC-F][0-9a-fA-F]{2})|([0-9a-ce-fA-CE-F][0-9a-fA-F]{3}|[dD][0-7][0-9a-fA-F]{2})|\{(0*(?:[0-9a-fA-F]{1,5}|10[0-9a-fA-F]{4}))\})/;function ec(e){return e.replace(new RegExp(Il,"g"),function(t,n,r,u,i,a,s){return n?String.fromCodePoint(parseInt(n,16),parseInt(r,16)):u?String.fromCodePoint(parseInt(u,16)):i?String.fromCodePoint(parseInt(i,16)):a?String.fromCodePoint(parseInt(a,16)):s?String.fromCodePoint(parseInt(s,16)):t})}function Vl(e,t){var n=e.slice(3,-1),r=ec(n);if(Or.hasOwnProperty(r))return Pt({type:"Backreference",kind:"name",number:Or[r],reference:r,referenceRaw:n},t);var u=null,i=null,a=null,s=null;t&&(u=t.startOffset,i=t.startLine,a=t.endLine,s=t.startColumn);var o=/^[\w$<>]/,c=void 0,d=[se(e.slice(1,2),"simple",u?{startLine:i,endLine:a,startColumn:s,startOffset:u,endOffset:u+=2,endColumn:s+=2}:null)];for(d[0].escaped=!0,e=e.slice(2);e.length>0;){var l=null;(l=e.match(_l))||(l=e.match(Rl))?(u&&(c={startLine:i,endLine:a,startColumn:s,startOffset:u,endOffset:u+=l[0].length,endColumn:s+=l[0].length}),d.push(se(l[0],"unicode",c)),e=e.slice(l[0].length)):(l=e.match(o))&&(u&&(c={startLine:i,endLine:a,startColumn:s,startOffset:u,endOffset:++u,endColumn:++s}),d.push(se(l[0],"simple",c)),e=e.slice(1))}return d}function Pt(e,t){return Ut.options.captureLocations&&(e.loc={source:tc.slice(t.startOffset,t.endOffset),start:{line:t.startLine,column:t.startColumn,offset:t.startOffset},end:{line:t.endLine,column:t.endColumn,offset:t.endOffset}}),e}function Kr(e,t){return Ut.options.captureLocations?{startOffset:e.startOffset,endOffset:t.endOffset,startLine:e.startLine,endLine:t.endLine,startColumn:e.startColumn,endColumn:t.endColumn}:null}function qa(e){e.type===Xo&&nc(),le.throwUnexpectedToken(e.value,e.startLine,e.startColumn)}function nc(){jl("Unexpected end of input.")}function jl(e){throw new SyntaxError(e)}var Hl=Cn,Mr=Hl,ql=Mr.parse.bind(Mr);Mr.parse=function(e,t){return ql(""+e,t)};Mr.setOptions({captureLocations:!1});var qu=Mr,Ul=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function Yl(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Jr="expressions",Ua="expression",ra=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;Yl(this,e),this.node=t,this.parentPath=n,this.parent=n?n.node:null,this.property=r,this.index=u}return Ul(e,[{key:"_enforceProp",value:function(n){if(!this.node.hasOwnProperty(n))throw new Error("Node of type "+this.node.type+` doesn't have "`+n+'" collection.')}},{key:"setChild",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,i=void 0;return r!=null?(u||(u=Jr),this._enforceProp(u),this.node[u][r]=n,i=e.getForNode(n,this,u,r)):(u||(u=Ua),this._enforceProp(u),this.node[u]=n,i=e.getForNode(n,this,u,null)),i}},{key:"appendChild",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;r||(r=Jr),this._enforceProp(r);var u=this.node[r].length;return this.setChild(n,u,r)}},{key:"insertChildAt",value:function(n,r){var u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Jr;this._enforceProp(u),this.node[u].splice(r,0,n),r<=e.getTraversingIndex()&&e.updateTraversingIndex(1),this._rebuildIndex(this.node,u)}},{key:"remove",value:function(){if(!this.isRemoved()&&(e.registry.delete(this.node),this.node=null,!!this.parent)){if(this.index!==null){this.parent[this.property].splice(this.index,1),this.index<=e.getTraversingIndex()&&e.updateTraversingIndex(-1),this._rebuildIndex(this.parent,this.property),this.index=null,this.property=null;return}delete this.parent[this.property],this.property=null}}},{key:"_rebuildIndex",value:function(n,r){for(var u=e.getForNode(n),i=0;i<n[r].length;i++){var a=e.getForNode(n[r][i],u,r,i);a.index=i}}},{key:"isRemoved",value:function(){return this.node===null}},{key:"replace",value:function(n){return e.registry.delete(this.node),this.node=n,this.parent?(this.index!==null?this.parent[this.property][this.index]=n:this.parent[this.property]=n,e.getForNode(n,this.parentPath,this.property,this.index)):null}},{key:"update",value:function(n){Object.assign(this.node,n)}},{key:"getParent",value:function(){return this.parentPath}},{key:"getChild",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.node.expressions?e.getForNode(this.node.expressions[n],this,Jr,n):this.node.expression&&n==0?e.getForNode(this.node.expression,this,Ua):null}},{key:"hasEqualSource",value:function(n){return JSON.stringify(this.node,mi)===JSON.stringify(n.node,mi)}},{key:"jsonEncode",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=n.format,u=n.useLoc;return JSON.stringify(this.node,u?null:mi,r)}},{key:"getPreviousSibling",value:function(){return!this.parent||this.index==null?null:e.getForNode(this.parent[this.property][this.index-1],e.getForNode(this.parent),this.property,this.index-1)}},{key:"getNextSibling",value:function(){return!this.parent||this.index==null?null:e.getForNode(this.parent[this.property][this.index+1],e.getForNode(this.parent),this.property,this.index+1)}}],[{key:"getForNode",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1;if(!n)return null;e.registry.has(n)||e.registry.set(n,new e(n,r,u,i==-1?null:i));var a=e.registry.get(n);return r!==null&&(a.parentPath=r,a.parent=a.parentPath.node),u!==null&&(a.property=u),i>=0&&(a.index=i),a}},{key:"initRegistry",value:function(){e.registry||(e.registry=new Map),e.registry.clear()}},{key:"updateTraversingIndex",value:function(n){return e.traversingIndexStack[e.traversingIndexStack.length-1]+=n}},{key:"getTraversingIndex",value:function(){return e.traversingIndexStack[e.traversingIndexStack.length-1]}}]),e}();ra.initRegistry();ra.traversingIndexStack=[];function mi(e,t){if(e!=="loc")return t}var ua=ra,Zn=ua;function Zl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.pre,r=t.post,u=t.skipProperty;function i(a,s,o,c){if(!(!a||typeof a.type!="string")){var d=void 0;if(n&&(d=n(a,s,o,c)),d!==!1){s&&s[o]&&(isNaN(c)?a=s[o]:a=s[o][c]);for(var l in a)if(a.hasOwnProperty(l)){if(u?u(l,a):l[0]==="$")continue;var h=a[l];if(Array.isArray(h)){var f=0;for(Zn.traversingIndexStack.push(f);f<h.length;)i(h[f],a,l,f),f=Zn.updateTraversingIndex(1);Zn.traversingIndexStack.pop()}else i(h,a,l)}}r&&r(a,s,o,c)}}i(e,null)}var rc={traverse:function(t,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{asNodes:!1};Array.isArray(n)||(n=[n]),n=n.filter(function(i){return typeof i.shouldRun!="function"?!0:i.shouldRun(t)}),Zn.initRegistry(),n.forEach(function(i){typeof i.init=="function"&&i.init(t)});function u(i,a,s,o){var c=Zn.getForNode(a),d=Zn.getForNode(i,c,s,o);return d}Zl(t,{pre:function(a,s,o,c){var d=void 0;r.asNodes||(d=u(a,s,o,c));var l=!0,h=!1,f=void 0;try{for(var m=n[Symbol.iterator](),b;!(l=(b=m.next()).done);l=!0){var g=b.value;if(typeof g["*"]=="function")if(d){if(!d.isRemoved()){var y=g["*"](d);if(y===!1)return!1}}else g["*"](a,s,o,c);var C=void 0;if(typeof g[a.type]=="function"?C=g[a.type]:typeof g[a.type]=="object"&&typeof g[a.type].pre=="function"&&(C=g[a.type].pre),C)if(d){if(!d.isRemoved()){var B=C.call(g,d);if(B===!1)return!1}}else C.call(g,a,s,o,c)}}catch(E){h=!0,f=E}finally{try{!l&&m.return&&m.return()}finally{if(h)throw f}}},post:function(a,s,o,c){if(a){var d=void 0;r.asNodes||(d=u(a,s,o,c));var l=!0,h=!1,f=void 0;try{for(var m=n[Symbol.iterator](),b;!(l=(b=m.next()).done);l=!0){var g=b.value,y=void 0;if(typeof g[a.type]=="object"&&typeof g[a.type].post=="function"&&(y=g[a.type].post),y)if(d){if(!d.isRemoved()){var C=y.call(g,d);if(C===!1)return!1}}else y.call(g,a,s,o,c)}}catch(B){h=!0,f=B}finally{try{!l&&m.return&&m.return()}finally{if(h)throw f}}}},skipProperty:function(a){return a==="loc"}})}},Wl=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function zl(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ya=Go,Gl=qu,Kl=rc,Za=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;zl(this,e),this._ast=t,this._source=null,this._string=null,this._regexp=null,this._extra=n}return Wl(e,[{key:"getAST",value:function(){return this._ast}},{key:"setExtra",value:function(n){this._extra=n}},{key:"getExtra",value:function(){return this._extra}},{key:"toRegExp",value:function(){return this._regexp||(this._regexp=new RegExp(this.getSource(),this._ast.flags)),this._regexp}},{key:"getSource",value:function(){return this._source||(this._source=Ya.generate(this._ast.body)),this._source}},{key:"getFlags",value:function(){return this._ast.flags}},{key:"toString",value:function(){return this._string||(this._string=Ya.generate(this._ast)),this._string}}]),e}(),ia={TransformResult:Za,transform:function(t,n){var r=t;return t instanceof RegExp&&(t=""+t),typeof t=="string"&&(r=Gl.parse(t,{captureLocations:!0})),Kl.traverse(r,n),new Za(r)}},Qr=Dl,Jl=ia,Ql={transform:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=n.length>0?n:Object.keys(Qr),u=void 0,i={};return r.forEach(function(a){if(!Qr.hasOwnProperty(a))throw new Error("Unknown compat-transform: "+a+". Available transforms are: "+Object.keys(Qr).join(", "));var s=Qr[a];u=Jl.transform(t,s),t=u.getAST(),typeof s.getExtra=="function"&&(i[a]=s.getExtra())}),u.setExtra(i),u}},Xl=function e(t){if(t===null||typeof t!="object")return t;var n=void 0;Array.isArray(t)?n=[]:n={};for(var r in t)n[r]=e(t[r]);return n},tf={shouldRun:function(t){return t.flags.includes("u")},Char:function(t){var n=t.node;n.kind!=="unicode"||!n.isSurrogatePair||isNaN(n.codePoint)||(n.value="\\u{"+n.codePoint.toString(16)+"}",delete n.isSurrogatePair)}},Wa="A".codePointAt(0),za="Z".codePointAt(0),Ga="a".codePointAt(0),Ka="z".codePointAt(0),Ja="0".codePointAt(0),Qa="9".codePointAt(0),ef={Char:function(t){var n=t.node,r=t.parent;if(!(isNaN(n.codePoint)||n.kind==="simple")&&!(r.type==="ClassRange"&&!nf(r))&&rf(n.codePoint)){var u=String.fromCodePoint(n.codePoint),i={type:"Char",kind:"simple",value:u,symbol:u,codePoint:n.codePoint};uf(u,r.type)&&(i.escaped=!0),t.replace(i)}}};function nf(e){var t=e.from,n=e.to;return t.codePoint>=Ja&&t.codePoint<=Qa&&n.codePoint>=Ja&&n.codePoint<=Qa||t.codePoint>=Wa&&t.codePoint<=za&&n.codePoint>=Wa&&n.codePoint<=za||t.codePoint>=Ga&&t.codePoint<=Ka&&n.codePoint>=Ga&&n.codePoint<=Ka}function rf(e){return e>=32&&e<=126}function uf(e,t){return t==="ClassRange"||t==="CharacterClass"?/[\]\\^-]/.test(e):/[*[()+?^$./\\|{}]/.test(e)}var Xa="A".codePointAt(0),ts="Z".codePointAt(0),af={_AZClassRanges:null,_hasUFlag:!1,init:function(t){this._AZClassRanges=new Set,this._hasUFlag=t.flags.includes("u")},shouldRun:function(t){return t.flags.includes("i")},Char:function(t){var n=t.node,r=t.parent;if(!isNaN(n.codePoint)&&!(!this._hasUFlag&&n.codePoint>=4096)){if(r.type==="ClassRange"){if(!this._AZClassRanges.has(r)&&!sf(r))return;this._AZClassRanges.add(r)}var u=n.symbol.toLowerCase();u!==n.symbol&&(n.value=of(u,n),n.symbol=u,n.codePoint=u.codePointAt(0))}}};function sf(e){var t=e.from,n=e.to;return t.codePoint>=Xa&&t.codePoint<=ts&&n.codePoint>=Xa&&n.codePoint<=ts}function of(e,t){var n=e.codePointAt(0);if(t.kind==="decimal")return"\\"+n;if(t.kind==="oct")return"\\0"+n.toString(8);if(t.kind==="hex")return"\\x"+n.toString(16);if(t.kind==="unicode")if(t.isSurrogatePair){var r=cf(n),u=r.lead,i=r.trail;return"\\u"+"0".repeat(4-u.length)+u+"\\u"+"0".repeat(4-i.length)+i}else{if(t.value.includes("{"))return"\\u{"+n.toString(16)+"}";var a=n.toString(16);return"\\u"+"0".repeat(4-a.length)+a}return e}function cf(e){var t=Math.floor((e-65536)/1024)+55296,n=(e-65536)%1024+56320;return{lead:t.toString(16),trail:n.toString(16)}}var df={CharacterClass:function(t){for(var n=t.node,r={},u=0;u<n.expressions.length;u++){var i=t.getChild(u),a=i.jsonEncode();r.hasOwnProperty(a)&&(i.remove(),u--),r[a]=!0}}};function lf(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}function uc(e){if(e.type!=="Disjunction")throw new TypeError('Expected "Disjunction" node, got "'+e.type+'"');var t=[];return e.left&&e.left.type==="Disjunction"?t.push.apply(t,lf(uc(e.left)).concat([e.right])):t.push(e.left,e.right),t}function ff(e){return e.reduce(function(t,n){return{type:"Disjunction",left:t,right:n}})}function hf(e){e.kind==="*"?e.kind="+":e.kind==="+"?(e.kind="Range",e.from=2,delete e.to):e.kind==="?"?(e.kind="Range",e.from=1,e.to=2):e.kind==="Range"&&(e.from+=1,e.to&&(e.to+=1))}var aa={disjunctionToList:uc,listToDisjunction:ff,increaseQuantifierByOne:hf},mf=aa,pf=mf.increaseQuantifierByOne,gf={Repetition:function(t){var n=t.node,r=t.parent;if(!(r.type!=="Alternative"||!t.index)){var u=t.getPreviousSibling();if(u)if(u.node.type==="Repetition"){if(!u.getChild().hasEqualSource(t.getChild()))return;var i=es(u.node.quantifier),a=i.from,s=i.to,o=es(n.quantifier),c=o.from,d=o.to;if(u.node.quantifier.greedy!==n.quantifier.greedy&&!Xr(u.node.quantifier)&&!Xr(n.quantifier))return;n.quantifier.kind="Range",n.quantifier.from=a+c,s&&d?n.quantifier.to=s+d:delete n.quantifier.to,(Xr(u.node.quantifier)||Xr(n.quantifier))&&(n.quantifier.greedy=!0),u.remove()}else{if(!u.hasEqualSource(t.getChild()))return;pf(n.quantifier),u.remove()}}}};function Xr(e){return e.greedy&&(e.kind==="+"||e.kind==="*"||e.kind==="Range"&&!e.to)}function es(e){var t=void 0,n=void 0;return e.kind==="*"?t=0:e.kind==="+"?t=1:e.kind==="?"?(t=0,n=1):(t=e.from,e.to&&(n=e.to)),{from:t,to:n}}var Df={Quantifier:function(t){var n=t.node;n.kind==="Range"&&(bf(t),yf(t),vf(t))}};function bf(e){var t=e.node;t.from!==0||t.to||(t.kind="*",delete t.from)}function yf(e){var t=e.node;t.from!==1||t.to||(t.kind="+",delete t.from)}function vf(e){var t=e.node;t.from!==1||t.to!==1||e.parentPath.replace(e.parentPath.node.expression)}var wf={ClassRange:function(t){var n=t.node;n.from.codePoint===n.to.codePoint?t.replace(n.from):n.from.codePoint===n.to.codePoint-1&&(t.getParent().insertChildAt(n.to,t.index+1),t.replace(n.from))}};function ns(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var Ef={_hasIFlag:!1,_hasUFlag:!1,init:function(t){this._hasIFlag=t.flags.includes("i"),this._hasUFlag=t.flags.includes("u")},CharacterClass:function(t){Ff(t),Cf(t,this._hasIFlag,this._hasUFlag),kf(t)}};function Ff(e){var t=e.node;t.expressions.forEach(function(n,r){xf(n)&&e.getChild(r).replace({type:"Char",value:"\\d",kind:"meta"})})}function Cf(e,t,n){var r=e.node,u=null,i=null,a=null,s=null,o=null,c=null;r.expressions.forEach(function(d,l){sa(d,"\\d")?u=e.getChild(l):Sf(d)?i=e.getChild(l):Tf(d)?a=e.getChild(l):Af(d)?s=e.getChild(l):t&&n&&kr(d,383)?o=e.getChild(l):t&&n&&kr(d,8490)&&(c=e.getChild(l))}),u&&(i&&a||t&&(i||a))&&s&&(!n||!t||o&&c)&&(u.replace({type:"Char",value:"\\w",kind:"meta"}),i&&i.remove(),a&&a.remove(),s.remove(),o&&o.remove(),c&&c.remove())}var pi=[function(e){return ic(e," ")}].concat(ns(["\\f","\\n","\\r","\\t","\\v"].map(function(e){return function(t){return sa(t,e)}})),ns([160,5760,8232,8233,8239,8287,12288,65279].map(function(e){return function(t){return kr(t,e)}})),[function(e){return e.type==="ClassRange"&&kr(e.from,8192)&&kr(e.to,8202)}]);function kf(e){var t=e.node;if(!(t.expressions.length<pi.length||!pi.every(function(r){return t.expressions.some(function(u){return r(u)})}))){var n=t.expressions.find(function(r){return sa(r,"\\n")});n.value="\\s",n.symbol=void 0,n.codePoint=NaN,t.expressions.map(function(r,u){return pi.some(function(i){return i(r)})?e.getChild(u):void 0}).filter(Boolean).forEach(function(r){return r.remove()})}}function xf(e){return e.type==="ClassRange"&&e.from.value==="0"&&e.to.value==="9"}function ic(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"simple";return e.type==="Char"&&e.value===t&&e.kind===n}function sa(e,t){return ic(e,t,"meta")}function Sf(e){return e.type==="ClassRange"&&e.from.value==="a"&&e.to.value==="z"}function Tf(e){return e.type==="ClassRange"&&e.from.value==="A"&&e.to.value==="Z"}function Af(e){return e.type==="Char"&&e.value==="_"&&e.kind==="simple"}function kr(e,t){return e.type==="Char"&&e.kind==="unicode"&&e.codePoint===t}var Of={CharacterClass:function(t){var n=t.node;if(!(n.expressions.length!==1||!Pf(t)||!Mf(n.expressions[0]))){var r=n.expressions[0],u=r.value,i=r.kind,a=r.escaped;if(n.negative){if(!Bf(u))return;u=Lf(u)}t.replace({type:"Char",value:u,kind:i,escaped:a||Nf(u)})}}};function Mf(e){return e.type==="Char"&&e.value!=="\\b"}function Bf(e){return/^\\[dwsDWS]$/.test(e)}function Lf(e){return/[dws]/.test(e)?e.toUpperCase():e.toLowerCase()}function Pf(e){var t=e.parent,n=e.index;if(t.type!=="Alternative")return!0;var r=t.expressions[n-1];return r==null?!0:!(r.type==="Backreference"&&r.kind==="number"||r.type==="Char"&&r.kind==="decimal")}function Nf(e){return/[*[()+?$./{}|]/.test(e)}var _f={_hasXFlag:!1,init:function(t){this._hasXFlag=t.flags.includes("x")},Char:function(t){var n=t.node;n.escaped&&Rf(t,this._hasXFlag)&&delete n.escaped}};function Rf(e,t){var n=e.node.value,r=e.index,u=e.parent;return u.type!=="CharacterClass"&&u.type!=="ClassRange"?!If(n,r,u,t):!$f(n,r,u)}function $f(e,t,n){return e==="^"?t===0&&!n.negative:e==="-"?!0:/[\]\\]/.test(e)}function If(e,t,n,r){return e==="{"?Vf(t,n):e==="}"?jf(t,n):r&&/[ #]/.test(e)?!0:/[*[()+?^$./\\|]/.test(e)}function vu(e,t,n){for(var r=e,u=(n?r>=0:r<t.expressions.length)&&t.expressions[r];u&&u.type==="Char"&&u.kind==="simple"&&!u.escaped&&/\d/.test(u.value);)n?r--:r++,u=(n?r>=0:r<t.expressions.length)&&t.expressions[r];return Math.abs(e-r)}function Wn(e,t){return e&&e.type==="Char"&&e.kind==="simple"&&!e.escaped&&e.value===t}function Vf(e,t){if(e==null)return!1;var n=vu(e+1,t),r=e+n+1,u=r<t.expressions.length&&t.expressions[r];if(n){if(Wn(u,"}"))return!0;if(Wn(u,","))return n=vu(r+1,t),r=r+n+1,u=r<t.expressions.length&&t.expressions[r],Wn(u,"}")}return!1}function jf(e,t){if(e==null)return!1;var n=vu(e-1,t,!0),r=e-n-1,u=r>=0&&t.expressions[r];return n&&Wn(u,"{")?!0:Wn(u,",")?(n=vu(r-1,t,!0),r=r-n-1,u=r<t.expressions.length&&t.expressions[r],n&&Wn(u,"{")):!1}var Hf={_hasIUFlags:!1,init:function(t){this._hasIUFlags=t.flags.includes("i")&&t.flags.includes("u")},CharacterClass:function(t){var n=t.node,r=n.expressions,u=[];r.forEach(function(o){Ye(o)&&u.push(o.value)}),r.sort(qf);for(var i=0;i<r.length;i++){var a=r[i];if(Uf(a,u,this._hasIUFlags)||Yf(a,r[i-1])||Zf(a,r[i+1]))r.splice(i,1),i--;else{var s=Wf(a,i,r);r.splice(i-s+1,s),i-=s}}}};function qf(e,t){var n=tu(e),r=tu(t);if(n===r){if(e.type==="ClassRange"&&t.type!=="ClassRange")return-1;if(t.type==="ClassRange"&&e.type!=="ClassRange")return 1;if(e.type==="ClassRange"&&t.type==="ClassRange")return tu(e.to)-tu(t.to);if(Ye(e)&&Ye(t)||rs(e)&&rs(t))return e.value<t.value?-1:1}return n-r}function tu(e){return e.type==="Char"?e.value==="-"||e.kind==="control"?1/0:e.kind==="meta"&&isNaN(e.codePoint)?-1:e.codePoint:e.from.codePoint}function Ye(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return e.type==="Char"&&e.kind==="meta"&&(t?e.value===t:/^\\[dws]$/i.test(e.value))}function rs(e){return e.type==="Char"&&e.kind==="control"}function Uf(e,t,n){for(var r=0;r<t.length;r++)if(Mi(e,t[r],n))return!0;return!1}function Mi(e,t,n){return e.type==="ClassRange"?Mi(e.from,t,n)&&Mi(e.to,t,n):t==="\\S"&&(Ye(e,"\\w")||Ye(e,"\\d"))||t==="\\D"&&(Ye(e,"\\W")||Ye(e,"\\s"))||t==="\\w"&&Ye(e,"\\d")||t==="\\W"&&Ye(e,"\\s")?!0:e.type!=="Char"||isNaN(e.codePoint)?!1:t==="\\s"?us(e):t==="\\S"?!us(e):t==="\\d"?Bi(e):t==="\\D"?!Bi(e):t==="\\w"?Li(e,n):t==="\\W"?!Li(e,n):!1}function us(e){return e.codePoint===9||e.codePoint===10||e.codePoint===11||e.codePoint===12||e.codePoint===13||e.codePoint===32||e.codePoint===160||e.codePoint===5760||e.codePoint>=8192&&e.codePoint<=8202||e.codePoint===8232||e.codePoint===8233||e.codePoint===8239||e.codePoint===8287||e.codePoint===12288||e.codePoint===65279}function Bi(e){return e.codePoint>=48&&e.codePoint<=57}function Li(e,t){return Bi(e)||e.codePoint>=65&&e.codePoint<=90||e.codePoint>=97&&e.codePoint<=122||e.value==="_"||t&&(e.codePoint===383||e.codePoint===8490)}function Yf(e,t){if(t&&t.type==="ClassRange"){if(Pi(e,t))return!0;if(wu(e)&&t.to.codePoint===e.codePoint-1)return t.to=e,!0;if(e.type==="ClassRange"&&e.from.codePoint<=t.to.codePoint+1&&e.to.codePoint>=t.from.codePoint-1)return e.from.codePoint<t.from.codePoint&&(t.from=e.from),e.to.codePoint>t.to.codePoint&&(t.to=e.to),!0}return!1}function Zf(e,t){return t&&t.type==="ClassRange"&&wu(e)&&t.from.codePoint===e.codePoint+1?(t.from=e,!0):!1}function Pi(e,t){return e.type==="Char"&&isNaN(e.codePoint)?!1:e.type==="ClassRange"?Pi(e.from,t)&&Pi(e.to,t):e.codePoint>=t.from.codePoint&&e.codePoint<=t.to.codePoint}function Wf(e,t,n){if(!wu(e))return 0;for(var r=0;t>0;){var u=n[t],i=n[t-1];if(wu(i)&&i.codePoint===u.codePoint-1)r++,t--;else break}return r>1?(n[t]={type:"ClassRange",from:n[t],to:e},r):0}function wu(e){return e&&e.type==="Char"&&!isNaN(e.codePoint)&&(Li(e,!1)||e.kind==="unicode"||e.kind==="hex"||e.kind==="oct"||e.kind==="decimal")}var zf=ua,ac=aa,Gf=ac.disjunctionToList,Kf=ac.listToDisjunction,Jf={Disjunction:function(t){var n=t.node,r={},u=Gf(n).filter(function(i){var a=i?zf.getForNode(i).jsonEncode():"null";return r.hasOwnProperty(a)?!1:(r[a]=i,!0)});t.replace(Kf(u))}},Qf={Disjunction:function(t){var n=t.node,r=t.parent;if(is[r.type]){var u=new Map;if(!(!hu(n,u)||!u.size)){var i={type:"CharacterClass",expressions:Array.from(u.keys()).sort().map(function(a){return u.get(a)})};is[r.type](t.getParent(),i)}}}},is={RegExp:function(t,n){var r=t.node;r.body=n},Group:function(t,n){var r=t.node;r.capturing?r.expression=n:t.replace(n)}};function hu(e,t){if(!e)return!1;var n=e.type;if(n==="Disjunction"){var r=e.left,u=e.right;return hu(r,t)&&hu(u,t)}else if(n==="Char"){if(e.kind==="meta"&&e.symbol===".")return!1;var i=e.value;return t.set(i,e),!0}else if(n==="CharacterClass"&&!e.negative)return e.expressions.every(function(a){return hu(a,t)});return!1}var Xf={Group:function(t){var n=t.node,r=t.parent,u=t.getChild();n.capturing||u||(r.type==="Repetition"?t.getParent().replace(n):r.type!=="RegExp"&&t.remove())}};function gi(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var t0={Group:function(t){var n=t.node,r=t.parent,u=t.getChild();if(!(n.capturing||!u)&&e0(t)&&!(u.node.type==="Disjunction"&&r.type!=="RegExp")&&!(r.type==="Repetition"&&u.node.type!=="Char"&&u.node.type!=="CharacterClass"))if(u.node.type==="Alternative"){var i=t.getParent();i.node.type==="Alternative"&&i.replace({type:"Alternative",expressions:[].concat(gi(r.expressions.slice(0,t.index)),gi(u.node.expressions),gi(r.expressions.slice(t.index+1)))})}else t.replace(u.node)}};function e0(e){var t=e.parent,n=e.index;if(t.type!=="Alternative")return!0;var r=t.expressions[n-1];return r==null?!0:!(r.type==="Backreference"&&r.kind==="number"||r.type==="Char"&&r.kind==="decimal")}function Eu(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var Fu=ua,n0=aa,sc=n0.increaseQuantifierByOne,r0={Alternative:function(t){for(var n=t.node,r=1;r<n.expressions.length;){var u=t.getChild(r);if(r=Math.max(1,u0(t,u,r)),r>=n.expressions.length||(u=t.getChild(r),r=Math.max(1,i0(t,u,r)),r>=n.expressions.length))break;u=t.getChild(r),r=Math.max(1,a0(t,u,r)),r++}}};function u0(e,t,n){for(var r=e.node,u=Math.ceil(n/2),i=0;i<u;){var a=n-2*i-1,s=void 0,o=void 0;if(i===0?(s=t,o=e.getChild(a)):(s=Fu.getForNode({type:"Alternative",expressions:[].concat(Eu(r.expressions.slice(n-i,n)),[t.node])}),o=Fu.getForNode({type:"Alternative",expressions:[].concat(Eu(r.expressions.slice(a,n-i)))})),s.hasEqualSource(o)){for(var c=0;c<2*i+1;c++)e.getChild(a).remove();return t.replace({type:"Repetition",expression:i===0&&s.node.type!=="Repetition"?s.node:{type:"Group",capturing:!1,expression:s.node},quantifier:{type:"Quantifier",kind:"Range",from:2,to:2,greedy:!0}}),a}i++}return n}function i0(e,t,n){for(var r=e.node,u=0;u<n;){var i=e.getChild(u);if(i.node.type==="Repetition"&&i.node.quantifier.greedy){var a=i.getChild(),s=void 0;if(a.node.type==="Group"&&!a.node.capturing&&(a=a.getChild()),u+1===n?(s=t,s.node.type==="Group"&&!s.node.capturing&&(s=s.getChild())):s=Fu.getForNode({type:"Alternative",expressions:[].concat(Eu(r.expressions.slice(u+1,n+1)))}),a.hasEqualSource(s)){for(var o=u;o<n;o++)e.getChild(u+1).remove();return sc(i.node.quantifier),u}}u++}return n}function a0(e,t,n){var r=e.node;if(t.node.type==="Repetition"&&t.node.quantifier.greedy){var u=t.getChild(),i=void 0;u.node.type==="Group"&&!u.node.capturing&&(u=u.getChild());var a=void 0;if(u.node.type==="Alternative"?(a=u.node.expressions.length,i=Fu.getForNode({type:"Alternative",expressions:[].concat(Eu(r.expressions.slice(n-a,n)))})):(a=1,i=e.getChild(n-1),i.node.type==="Group"&&!i.node.capturing&&(i=i.getChild())),i.hasEqualSource(u)){for(var s=n-a;s<n;s++)e.getChild(n-a).remove();return sc(t.node.quantifier),n-a}}return n}var s0=new Map([["charSurrogatePairToSingleUnicode",tf],["charCodeToSimpleChar",ef],["charCaseInsensitiveLowerCaseTransform",af],["charClassRemoveDuplicates",df],["quantifiersMerge",gf],["quantifierRangeToSymbol",Df],["charClassClassrangesToChars",wf],["charClassToMeta",Ef],["charClassToSingleChar",Of],["charEscapeUnescape",_f],["charClassClassrangesMerge",Hf],["disjunctionRemoveDuplicates",Jf],["groupSingleCharsToCharClass",Qf],["removeEmptyGroup",Xf],["ungroup",t0],["combineRepeatingPatterns",r0]]),as=Xl,o0=qu,ss=ia,eu=s0,c0={optimize:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.whitelist,u=r===void 0?[]:r,i=n.blacklist,a=i===void 0?[]:i,s=u.length>0?u:Array.from(eu.keys()),o=s.filter(function(h){return!a.includes(h)}),c=t;t instanceof RegExp&&(t=""+t),typeof t=="string"&&(c=o0.parse(t));var d=new ss.TransformResult(c),l=void 0;do l=d.toString(),c=as(d.getAST()),o.forEach(function(h){if(!eu.has(h))throw new Error("Unknown optimization-transform: "+h+". Available transforms are: "+Array.from(eu.keys()).join(", "));var f=eu.get(h),m=ss.transform(c,f);m.toString()!==d.toString()&&(m.toString().length<=d.toString().length?d=m:c=as(d.getAST()))});while(d.toString()!==l);return d}},oc="ε",d0=oc+"*",Uu={EPSILON:oc,EPSILON_CLOSURE:d0},l0=function(){function e(t,n){var r=[],u=!0,i=!1,a=void 0;try{for(var s=t[Symbol.iterator](),o;!(u=(o=s.next()).done)&&(r.push(o.value),!(n&&r.length===n));u=!0);}catch(c){i=!0,a=c}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return r}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),f0=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function h0(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}function m0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var cc=Uu,p0=cc.EPSILON,os=cc.EPSILON_CLOSURE,g0=function(){function e(t,n){m0(this,e),this.in=t,this.out=n}return f0(e,[{key:"matches",value:function(n){return this.in.matches(n)}},{key:"getAlphabet",value:function(){if(!this._alphabet){this._alphabet=new Set;var n=this.getTransitionTable();for(var r in n){var u=n[r];for(var i in u)i!==os&&this._alphabet.add(i)}}return this._alphabet}},{key:"getAcceptingStates",value:function(){return this._acceptingStates||this.getTransitionTable(),this._acceptingStates}},{key:"getAcceptingStateNumbers",value:function(){if(!this._acceptingStateNumbers){this._acceptingStateNumbers=new Set;var n=!0,r=!1,u=void 0;try{for(var i=this.getAcceptingStates()[Symbol.iterator](),a;!(n=(a=i.next()).done);n=!0){var s=a.value;this._acceptingStateNumbers.add(s.number)}}catch(o){r=!0,u=o}finally{try{!n&&i.return&&i.return()}finally{if(r)throw u}}}return this._acceptingStateNumbers}},{key:"getTransitionTable",value:function(){var n=this;if(!this._transitionTable){this._transitionTable={},this._acceptingStates=new Set;var r=new Set,u=new Set,i=function a(s){if(!r.has(s)){r.add(s),s.number=r.size,n._transitionTable[s.number]={},s.accepting&&n._acceptingStates.add(s);var o=s.getTransitions(),c=!0,d=!1,l=void 0;try{for(var h=o[Symbol.iterator](),f;!(c=(f=h.next()).done);c=!0){var m=f.value,b=l0(m,2),g=b[0],y=b[1],C=[];u.add(g);var B=!0,E=!1,S=void 0;try{for(var T=y[Symbol.iterator](),F;!(B=(F=T.next()).done);B=!0){var v=F.value;a(v),C.push(v.number)}}catch(L){E=!0,S=L}finally{try{!B&&T.return&&T.return()}finally{if(E)throw S}}n._transitionTable[s.number][g]=C}}catch(L){d=!0,l=L}finally{try{!c&&h.return&&h.return()}finally{if(d)throw l}}}};i(this.in),r.forEach(function(a){delete n._transitionTable[a.number][p0],n._transitionTable[a.number][os]=[].concat(h0(a.getEpsilonClosure())).map(function(s){return s.number})})}return this._transitionTable}}]),e}(),dc=g0,D0=function(){function e(t,n){var r=[],u=!0,i=!1,a=void 0;try{for(var s=t[Symbol.iterator](),o;!(u=(o=s.next()).done)&&(r.push(o.value),!(n&&r.length===n));u=!0);}catch(c){i=!0,a=c}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return r}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();function b0(e){return Array.isArray(e)?e:Array.from(e)}function Ni(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var Ze=null;function y0(e){var t=e.getTransitionTable(),n=Object.keys(t),r=e.getAlphabet(),u=e.getAcceptingStateNumbers();Ze={};var i=new Set;n.forEach(function(it){it=Number(it);var St=u.has(it);St?Ze[it]=u:(i.add(it),Ze[it]=i)});var a=[[i,u].filter(function(it){return it.size>0})],s=void 0,o=void 0;s=a[a.length-1],o=a[a.length-2];for(var c=function(){var St={},_=!0,nt=!1,Dt=void 0;try{for(var bt=s[Symbol.iterator](),At;!(_=(At=bt.next()).done);_=!0){var Tt=At.value,lt={},vt=b0(Tt),kt=vt[0],Qt=vt.slice(1);lt[kt]=new Set([kt]);var yt=!0,ct=!1,zt=void 0;try{t:for(var R=Qt[Symbol.iterator](),G;!(yt=(G=R.next()).done);yt=!0){var et=G.value,dt=!0,Ht=!1,Ce=void 0;try{for(var pe=Object.keys(lt)[Symbol.iterator](),te;!(dt=(te=pe.next()).done);dt=!0){var ge=te.value;if(w0(et,ge,t,r)){lt[ge].add(et),lt[et]=lt[ge];continue t}}}catch(Nt){Ht=!0,Ce=Nt}finally{try{!dt&&pe.return&&pe.return()}finally{if(Ht)throw Ce}}lt[et]=new Set([et])}}catch(Nt){ct=!0,zt=Nt}finally{try{!yt&&R.return&&R.return()}finally{if(ct)throw zt}}Object.assign(St,lt)}}catch(Nt){nt=!0,Dt=Nt}finally{try{!_&&bt.return&&bt.return()}finally{if(nt)throw Dt}}Ze=St;var Ln=new Set(Object.keys(St).map(function(Nt){return St[Nt]}));a.push([].concat(Ni(Ln))),s=a[a.length-1],o=a[a.length-2]};!v0(s,o);)c();var d=new Map,l=1;s.forEach(function(it){return d.set(it,l++)});var h={},f=new Set,m=function(St,_){var nt=!0,Dt=!1,bt=void 0;try{for(var At=St[Symbol.iterator](),Tt;!(nt=(Tt=At.next()).done);nt=!0){var lt=Tt.value;u.has(lt)&&f.add(_)}}catch(vt){Dt=!0,bt=vt}finally{try{!nt&&At.return&&At.return()}finally{if(Dt)throw bt}}},b=!0,g=!1,y=void 0;try{for(var C=d.entries()[Symbol.iterator](),B;!(b=(B=C.next()).done);b=!0){var E=B.value,S=D0(E,2),T=S[0],F=S[1];h[F]={};var v=!0,L=!1,N=void 0;try{for(var M=r[Symbol.iterator](),W;!(v=(W=M.next()).done);v=!0){var $=W.value;m(T,F);var q=void 0,z=!0,gt=!1,ht=void 0;try{for(var pt=T[Symbol.iterator](),Ft;!(z=(Ft=pt.next()).done);z=!0){var Bt=Ft.value;if(q=t[Bt][$],q)break}}catch(it){gt=!0,ht=it}finally{try{!z&&pt.return&&pt.return()}finally{if(gt)throw ht}}q&&(h[F][$]=d.get(Ze[q]))}}catch(it){L=!0,N=it}finally{try{!v&&M.return&&M.return()}finally{if(L)throw N}}}}catch(it){g=!0,y=it}finally{try{!b&&C.return&&C.return()}finally{if(g)throw y}}return e.setTransitionTable(h),e.setAcceptingStateNumbers(f),e}function v0(e,t){if(!t||e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e[n],u=t[n];if(r.size!==u.size||[].concat(Ni(r)).sort().join(",")!==[].concat(Ni(u)).sort().join(","))return!1}return!0}function w0(e,t,n,r){var u=!0,i=!1,a=void 0;try{for(var s=r[Symbol.iterator](),o;!(u=(o=s.next()).done);u=!0){var c=o.value;if(!E0(e,t,n,c))return!1}}catch(d){i=!0,a=d}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return!0}function E0(e,t,n,r){if(!Ze[e]||!Ze[t])return!1;var u=n[e][r],i=n[t][r];return!u&&!i?!0:Ze[e].has(u)&&Ze[t].has(i)}var F0={minimize:y0},C0=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function cs(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}function k0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var x0=F0,S0=Uu,ds=S0.EPSILON_CLOSURE,T0=function(){function e(t){k0(this,e),this._nfa=t}return C0(e,[{key:"minimize",value:function(){this.getTransitionTable(),this._originalAcceptingStateNumbers=this._acceptingStateNumbers,this._originalTransitionTable=this._transitionTable,x0.minimize(this)}},{key:"getAlphabet",value:function(){return this._nfa.getAlphabet()}},{key:"getAcceptingStateNumbers",value:function(){return this._acceptingStateNumbers||this.getTransitionTable(),this._acceptingStateNumbers}},{key:"getOriginaAcceptingStateNumbers",value:function(){return this._originalAcceptingStateNumbers||this.getTransitionTable(),this._originalAcceptingStateNumbers}},{key:"setTransitionTable",value:function(n){this._transitionTable=n}},{key:"setAcceptingStateNumbers",value:function(n){this._acceptingStateNumbers=n}},{key:"getTransitionTable",value:function(){var n=this;if(this._transitionTable)return this._transitionTable;var r=this._nfa.getTransitionTable(),u=Object.keys(r);this._acceptingStateNumbers=new Set;for(var i=r[u[0]][ds],a=[i],s=this.getAlphabet(),o=this._nfa.getAcceptingStateNumbers(),c={},d=function(it){var St=!0,_=!1,nt=void 0;try{for(var Dt=o[Symbol.iterator](),bt;!(St=(bt=Dt.next()).done);St=!0){var At=bt.value;if(it.indexOf(At)!==-1){n._acceptingStateNumbers.add(it.join(","));break}}}catch(Tt){_=!0,nt=Tt}finally{try{!St&&Dt.return&&Dt.return()}finally{if(_)throw nt}}};a.length>0;){var l=a.shift(),h=l.join(",");c[h]={};var f=!0,m=!1,b=void 0;try{for(var g=s[Symbol.iterator](),y;!(f=(y=g.next()).done);f=!0){var C=y.value,B=[];d(l);var E=!0,S=!1,T=void 0;try{for(var F=l[Symbol.iterator](),v;!(E=(v=F.next()).done);E=!0){var L=v.value,N=r[L][C];if(N){var M=!0,W=!1,$=void 0;try{for(var q=N[Symbol.iterator](),z;!(M=(z=q.next()).done);M=!0){var gt=z.value;r[gt]&&B.push.apply(B,cs(r[gt][ds]))}}catch(Bt){W=!0,$=Bt}finally{try{!M&&q.return&&q.return()}finally{if(W)throw $}}}}}catch(Bt){S=!0,T=Bt}finally{try{!E&&F.return&&F.return()}finally{if(S)throw T}}var ht=new Set(B),pt=[].concat(cs(ht));if(pt.length>0){var Ft=pt.join(",");c[h][C]=Ft,c.hasOwnProperty(Ft)||a.unshift(pt)}}}catch(Bt){m=!0,b=Bt}finally{try{!f&&g.return&&g.return()}finally{if(m)throw b}}}return this._transitionTable=this._remapStateNumbers(c)}},{key:"_remapStateNumbers",value:function(n){var r={};this._originalTransitionTable=n;var u={};Object.keys(n).forEach(function(b,g){r[b]=g+1});for(var i in n){var a=n[i],s={};for(var o in a)s[o]=r[a[o]];u[r[i]]=s}this._originalAcceptingStateNumbers=this._acceptingStateNumbers,this._acceptingStateNumbers=new Set;var c=!0,d=!1,l=void 0;try{for(var h=this._originalAcceptingStateNumbers[Symbol.iterator](),f;!(c=(f=h.next()).done);c=!0){var m=f.value;this._acceptingStateNumbers.add(r[m])}}catch(b){d=!0,l=b}finally{try{!c&&h.return&&h.return()}finally{if(d)throw l}}return u}},{key:"getOriginalTransitionTable",value:function(){return this._originalTransitionTable||this.getTransitionTable(),this._originalTransitionTable}},{key:"matches",value:function(n){for(var r=1,u=0,i=this.getTransitionTable();n[u];)if(r=i[r][n[u++]],!r)return!1;return!!this.getAcceptingStateNumbers().has(r)}}]),e}(),A0=T0,O0=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function M0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var B0=function(){function e(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.accepting,r=n===void 0?!1:n;M0(this,e),this._transitions=new Map,this.accepting=r}return O0(e,[{key:"getTransitions",value:function(){return this._transitions}},{key:"addTransition",value:function(n,r){return this.getTransitionsOnSymbol(n).add(r),this}},{key:"getTransitionsOnSymbol",value:function(n){var r=this._transitions.get(n);return r||(r=new Set,this._transitions.set(n,r)),r}}]),e}(),L0=B0,P0=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function N0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _0(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function R0(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var $0=L0,I0=Uu,Di=I0.EPSILON,V0=function(e){R0(t,e);function t(){return N0(this,t),_0(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return P0(t,[{key:"matches",value:function(r){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;if(u.has(this))return!1;if(u.add(this),r.length===0){if(this.accepting)return!0;var i=!0,a=!1,s=void 0;try{for(var o=this.getTransitionsOnSymbol(Di)[Symbol.iterator](),c;!(i=(c=o.next()).done);i=!0){var d=c.value;if(d.matches("",u))return!0}}catch(N){a=!0,s=N}finally{try{!i&&o.return&&o.return()}finally{if(a)throw s}}return!1}var l=r[0],h=r.slice(1),f=this.getTransitionsOnSymbol(l),m=!0,b=!1,g=void 0;try{for(var y=f[Symbol.iterator](),C;!(m=(C=y.next()).done);m=!0){var B=C.value;if(B.matches(h))return!0}}catch(N){b=!0,g=N}finally{try{!m&&y.return&&y.return()}finally{if(b)throw g}}var E=!0,S=!1,T=void 0;try{for(var F=this.getTransitionsOnSymbol(Di)[Symbol.iterator](),v;!(E=(v=F.next()).done);E=!0){var L=v.value;if(L.matches(r,u))return!0}}catch(N){S=!0,T=N}finally{try{!E&&F.return&&F.return()}finally{if(S)throw T}}return!1}},{key:"getEpsilonClosure",value:function(){var r=this;return this._epsilonClosure||function(){var u=r.getTransitionsOnSymbol(Di),i=r._epsilonClosure=new Set;i.add(r);var a=!0,s=!1,o=void 0;try{for(var c=u[Symbol.iterator](),d;!(a=(d=c.next()).done);a=!0){var l=d.value;if(!i.has(l)){i.add(l);var h=l.getEpsilonClosure();h.forEach(function(f){return i.add(f)})}}}catch(f){s=!0,o=f}finally{try{!a&&c.return&&c.return()}finally{if(s)throw o}}}(),this._epsilonClosure}}]),t}($0),j0=V0,Yu=dc,tr=j0,H0=Uu,ce=H0.EPSILON;function lc(e){var t=new tr,n=new tr({accepting:!0});return new Yu(t.addTransition(e,n),n)}function q0(){return lc(ce)}function U0(e,t){return e.out.accepting=!1,t.out.accepting=!0,e.out.addTransition(ce,t.in),new Yu(e.in,t.out)}function Y0(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var u=!0,i=!1,a=void 0;try{for(var s=n[Symbol.iterator](),o;!(u=(o=s.next()).done);u=!0){var c=o.value;e=U0(e,c)}}catch(d){i=!0,a=d}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return e}function Z0(e,t){var n=new tr,r=new tr;return n.addTransition(ce,e.in),n.addTransition(ce,t.in),r.accepting=!0,e.out.accepting=!1,t.out.accepting=!1,e.out.addTransition(ce,r),t.out.addTransition(ce,r),new Yu(n,r)}function W0(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var u=!0,i=!1,a=void 0;try{for(var s=n[Symbol.iterator](),o;!(u=(o=s.next()).done);u=!0){var c=o.value;e=Z0(e,c)}}catch(d){i=!0,a=d}finally{try{!u&&s.return&&s.return()}finally{if(i)throw a}}return e}function z0(e){var t=new tr,n=new tr({accepting:!0});return t.addTransition(ce,e.in),t.addTransition(ce,n),e.out.accepting=!1,e.out.addTransition(ce,n),n.addTransition(ce,e.in),new Yu(t,n)}function G0(e){return e.in.addTransition(ce,e.out),e.out.addTransition(ce,e.in),e}function K0(e){return e.out.addTransition(ce,e.in),e}function J0(e){return e.in.addTransition(ce,e.out),e}var fc={alt:Y0,char:lc,e:q0,or:W0,rep:G0,repExplicit:z0,plusRep:K0,questionRep:J0};function Q0(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var X0=qu,sr=fc,th=sr.alt,eh=sr.char,nh=sr.or,rh=sr.rep,uh=sr.plusRep,ih=sr.questionRep;function Ue(e){if(e&&!ls[e.type])throw new Error(e.type+" is not supported in NFA/DFA interpreter.");return e?ls[e.type](e):""}var ls={RegExp:function(t){if(t.flags!=="")throw new Error("NFA/DFA: Flags are not supported yet.");return Ue(t.body)},Alternative:function(t){var n=(t.expressions||[]).map(Ue);return th.apply(void 0,Q0(n))},Disjunction:function(t){return nh(Ue(t.left),Ue(t.right))},Repetition:function(t){switch(t.quantifier.kind){case"*":return rh(Ue(t.expression));case"+":return uh(Ue(t.expression));case"?":return ih(Ue(t.expression));default:throw new Error("Unknown repeatition: "+t.quantifier.kind+".")}},Char:function(t){if(t.kind!=="simple")throw new Error("NFA/DFA: Only simple chars are supported yet.");return eh(t.value)},Group:function(t){return Ue(t.expression)}},ah={build:function(t){var n=t;return t instanceof RegExp&&(t=""+t),typeof t=="string"&&(n=X0.parse(t,{captureLocations:!0})),Ue(n)}},sh=dc,fs=A0,oh=ah,ch=fc,dh={NFA:sh,DFA:fs,builders:ch,toNFA:function(t){return oh.build(t)},toDFA:function(t){return new fs(this.toNFA(t))},test:function(t,n){return this.toDFA(t).matches(n)}},lh=function(){function e(t,n){for(var r=0;r<n.length;r++){var u=n[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,u.key,u)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function fh(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var hh=function(){function e(t,n){var r=n.flags,u=n.groups,i=n.source;fh(this,e),this._re=t,this._groups=u,this.flags=r,this.source=i||t.source,this.dotAll=r.includes("s"),this.global=t.global,this.ignoreCase=t.ignoreCase,this.multiline=t.multiline,this.sticky=t.sticky,this.unicode=t.unicode}return lh(e,[{key:"test",value:function(n){return this._re.test(n)}},{key:"compile",value:function(n){return this._re.compile(n)}},{key:"toString",value:function(){return this._toStringResult||(this._toStringResult="/"+this.source+"/"+this.flags),this._toStringResult}},{key:"exec",value:function(n){var r=this._re.exec(n);if(!this._groups||!r)return r;r.groups={};for(var u in this._groups){var i=this._groups[u];r.groups[u]=r[i]}return r}}]),e}(),mh={RegExpTree:hh},ph=Ql,gh=Go,Dh=c0,hs=qu,ms=ia,bh=rc,yh=dh,vh=mh,wh=vh.RegExpTree,Eh={parser:hs,fa:yh,TransformResult:ms.TransformResult,parse:function(t,n){return hs.parse(""+t,n)},traverse:function(t,n,r){return bh.traverse(t,n,r)},transform:function(t,n){return ms.transform(t,n)},generate:function(t){return gh.generate(t)},toRegExp:function(t){var n=this.compatTranspile(t);return new RegExp(n.getSource(),n.getFlags())},optimize:function(t,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},u=r.blacklist;return Dh.optimize(t,{whitelist:n,blacklist:u})},compatTranspile:function(t,n){return ph.transform(t,n)},exec:function(t,n){if(typeof t=="string"){var r=this.compatTranspile(t),u=r.getExtra();u.namedCapturingGroups?t=new wh(r.toRegExp(),{flags:r.getFlags(),source:r.getSource(),groups:u.namedCapturingGroups}):t=r.toRegExp()}return t.exec(n)}},Fh=Eh,Ch=Fh;const kh=zo,_i=Qi,hn=Ch,ps=new WeakMap;function xh(e){return kh.mode==="spec-compliant"?Th(this,e):Sh(this,e)}function Sh(e,t){const n=e.lastIndex,r=_i.call(e,t);if(r===null)return null;let u;return Object.defineProperty(r,"indices",{enumerable:!0,configurable:!0,get(){if(u===void 0){const{measurementRegExp:i,groupInfos:a}=hc(e);i.lastIndex=n;const s=_i.call(i,t);if(s===null)throw new TypeError;ve(r,"indices",u=mc(s,a))}return u},set(i){ve(r,"indices",i)}}),r}function Th(e,t){const{measurementRegExp:n,groupInfos:r}=hc(e);n.lastIndex=e.lastIndex;const u=_i.call(n,t);if(u===null)return null;e.lastIndex=n.lastIndex;const i=[];ve(i,0,u[0]);for(const a of r)ve(i,a.oldGroupNumber,u[a.newGroupNumber]);return ve(i,"index",u.index),ve(i,"input",u.input),ve(i,"groups",u.groups),ve(i,"indices",mc(u,r)),i}function hc(e){let t=ps.get(e);t||(t=Ph(hn.parse(`/${e.source}/${e.flags}`)),ps.set(e,t));const n=t.getExtra();return{measurementRegExp:t.toRegExp(),groupInfos:n}}function mc(e,t){const n=e.index,r=n+e[0].length,u=!!e.groups,i=[],a=u?Object.create(null):void 0;ve(i,0,[n,r]);for(const s of t){let o;if(e[s.newGroupNumber]!==void 0){let c=n;if(s.measurementGroups)for(const l of s.measurementGroups)c+=e[l].length;const d=c+e[s.newGroupNumber].length;o=[c,d]}ve(i,s.oldGroupNumber,o),a&&s.groupName!==void 0&&ve(a,s.groupName,o)}return ve(i,"groups",a),i}function ve(e,t,n){const r=Object.getOwnPropertyDescriptor(e,t);if(r?r.configurable:Object.isExtensible(e)){const u={enumerable:r?r.enumerable:!0,configurable:r?r.configurable:!0,writable:!0,value:n};Object.defineProperty(e,t,u)}}let Cu,Ri=!1,Br=new Set,$i=[],qn=!1,pc=1,ku=[],oa=new Map,ca=new Map;const Ah={init(){Ri=!1,Br.clear(),$i.length=0,qn=!1,pc=1,ku.length=0,oa.clear(),ca.clear(),Cu=[]},RegExp(e){return hn.traverse(e.node,Oh),Br.size>0&&(hn.transform(e.node,gc),hn.transform(e.node,Mh),Ri&&hn.transform(e.node,Bh)),!1}},pr={pre(e){$i.push(qn),qn=e.node.type==="Group"&&e.node.capturing},post(e){qn&&Br.add(e.node),qn=$i.pop()||qn}},Oh={Alternative:pr,Disjunction:pr,Assertion:pr,Group:pr,Repetition:pr,Backreference(e){Ri=!0}},gc={Alternative(e){if(Br.has(e.node)){let t=0,n=[];const r=[],u=[];for(let i=0;i<e.node.expressions.length;i++){const a=e.node.expressions[i];if(Br.has(a)){if(i>t){const s={type:"Group",capturing:!0,number:-1,expression:n.length>1?{type:"Alternative",expressions:n}:n.length===1?n[0]:null};u.push(s),r.push(s),t=i,n=[]}ku.push(r),hn.transform(a,gc),ku.pop(),n.push(a);continue}n.push(a)}e.update({expressions:u.concat(n)})}return!1},Group(e){e.node.capturing&&oa.set(e.node,Lh())}},Mh={Group(e){if(!Cu)throw new Error("Not initialized.");if(!e.node.capturing)return;const t=e.node.number,n=pc++,r=oa.get(e.node);t!==-1&&(Cu.push({oldGroupNumber:t,newGroupNumber:n,measurementGroups:r&&r.map(u=>u.number),groupName:e.node.name}),ca.set(t,n)),e.update({number:n})}},Bh={Backreference(e){const t=ca.get(e.node.number);t&&(e.node.kind==="number"?e.update({number:t,reference:t}):e.update({number:t}))}};function Lh(){const e=[];for(const t of ku)for(const n of t)e.push(n);return e}function Ph(e){const t=hn.transform(e,Ah);return new hn.TransformResult(t.getAST(),Cu)}var Dc=xh;const gs=Qi,Nh=Dc;function _h(){const e=new RegExp("a");return gs.call(e,"a").indices?gs:Nh}var bc=_h;const Rh=bc;function $h(){const e=Rh();RegExp.prototype.exec!==e&&(RegExp.prototype.exec=e)}var Ih=$h;const Vh=Dc,jh=Qi,yc=bc,Hh=Ih,qh=zo,Uh=yc();function Dn(e,t){return Uh.call(e,t)}Dn.implementation=Vh;Dn.native=jh;Dn.getPolyfill=yc;Dn.shim=Hh;Dn.config=qh;Dn||(Dn={});var Yh=Dn;const Zh=ll(Yh),Wh={from:-1,to:-1,match:/.*/.exec("")},zh="gm"+(/x/.unicode==null?"":"u");class Gh{constructor(t,n,r,u=0,i=t.length){this.to=i,this.curLine="",this.done=!1,this.value=Wh,this.re=new RegExp(n,zh+(r!=null&&r.ignoreCase?"i":"")),this.iter=t.iter();const a=t.lineAt(u);this.curLineStart=a.from,this.matchPos=u,this.getLine(this.curLineStart)}getLine(t){this.iter.next(t),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let t=this.matchPos-this.curLineStart;;){this.re.lastIndex=t;const n=this.matchPos<=this.to&&Zh(this.re,this.curLine);if(n){const r=this.curLineStart+n.index,u=r+n[0].length;if(this.matchPos=u+(r==u?1:0),r==this.curLine.length&&this.nextLine(),r<u||r>this.value.to)return this.value={from:r,to:u,match:n},this;t=this.matchPos-this.curLineStart}else if(this.curLineStart+this.curLine.length<this.to)this.nextLine(),t=0;else return this.done=!0,this}}}const vc=rt.StateEffect.define(),Ii=rt.StateEffect.define(),Kh=rt.StateField.define({create(){return J.Decoration.none},update(e,t){e=e.map(t.changes);for(const n of t.effects){if(n.is(Ii))return e=J.Decoration.none,e;n.is(vc)&&(e=e.update({add:[Jh.range(n.value.from,n.value.to)],sort:!0}))}return e},provide:e=>J.EditorView.decorations.from(e)}),Jh=J.Decoration.mark({class:"outliner-highlight"});function Qh(e){const t=[],n=e.iter();for(;n.value!==null;)t.push({from:n.from,to:n.to}),n.next();return t}function ze(e){const t=e.vault.getConfig("useTab")===void 0||window.app.vault.getConfig("useTab")===!0,n=t?1:e.vault.getConfig("tabSize");return(t?"	":" ").repeat(n)}function Xh(e,t){const n=e.state,r=new Gh(n.doc,t,{},0,n.doc.length),u=[];for(;!r.next().done;){const{from:i,to:a}=r.value,s=n.doc.lineAt(i);u.push({from:s.from,to:s.to}),e.dispatch({effects:vc.of({from:i,to:a})})}return u}function tm(e){const t=[];let n=0;for(;n<e;n++)t.push((16*Math.random()|0).toString(16));return t.join("")}function nu(e,t,n){const r=`o-${tm(4)}`,u=`%%${r}%%`,i=e.getSelection();if(!i)return;let a=!1;if(i.split(`
`).length>1&&e.getCursor("from").ch===0&&(a=!0),e.replaceSelection(`${u+(a?`
`:"")}${i}${u}`),!t.file)return;const s=(n==="embed"?"!":"")+t.app.fileManager.generateMarkdownLink(t.file,t.file.path,"",`${r}`);navigator.clipboard.writeText(s).then(()=>{new O.Notice("Copied to clipboard")})}const em=rt.StateField.define({create(){return J.Decoration.none},update(e,t){const n=t.state.field(O.editorInfoField),r=new rt.RangeSetBuilder,u=t.state.doc.line(t.state.doc.lines).to;return r.add(u,u,J.Decoration.widget({widget:new nm(n.app,n,u,u,t.state),side:2,block:!0})),r.finish()},provide:e=>J.EditorView.decorations.from(e)});class nm extends J.WidgetType{constructor(t,n,r,u,i){super(),this.app=t,this.editor=n,this.from=r,this.to=u,this.state=i}eq(t){return!0}toDOM(){const t=createEl("div",{cls:"cm-newline-button"});return new O.ExtraButtonComponent(t).setIcon("plus").onClick(()=>{var a,s,o,c,d,l,h,f,m;const n=(a=this.editor.editor)==null?void 0:a.lastLine(),r=(s=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:s.getZoomRange(this.editor.editor),u=ze(this.app);if(r){const b=r.from.line,g=r.to.line,y=(d=(c=(o=this.editor.editor)==null?void 0:o.getLine(b))==null?void 0:c.match(/^\s*/))==null?void 0:d[0],C=(l=this.editor.editor)==null?void 0:l.getLine(g);(h=this.editor.editor)==null||h.transaction({changes:[{text:`
${y}${u}- `,from:{line:g,ch:(C==null?void 0:C.length)||0}}],selection:{from:{line:g+1,ch:2+`${y}${u}`.length},to:{line:g+1,ch:2+`${y}${u}`.length}}});return}if(n===void 0)return;const i=(f=this.editor.editor)==null?void 0:f.getLine(n);(m=this.editor.editor)==null||m.transaction({changes:[{text:`
- `,from:{line:n,ch:(i==null?void 0:i.length)||0}}],selection:{from:{line:n+1,ch:2},to:{line:n+1,ch:2}}})}),t}ignoreEvent(t){var n;return t.type==="mousedown"||t.type==="mouseup"||t.type==="click"?(t.preventDefault(),(n=this.editor.editor)==null||n.focus(),!0):!1}}class Ds extends J.WidgetType{constructor(t,n,r,u){super(),this.app=t,this.view=n,this.from=r,this.to=u}eq(t){return t.from===this.from&&t.to===this.to&&t.view===this.view}toDOM(){return this.bulletSpanEl=createEl("span",{cls:"cm-bullet-menu-marker"}),new O.ExtraButtonComponent(this.bulletSpanEl).setIcon("menu").onClick(()=>{var o,c,d,l,h;const t=new O.Menu,n=this.view.field(O.editorInfoField).editor,r=this.view.doc.lineAt(this.from),u=((o=r.text.match(/^\s*/))==null?void 0:o[0].length)||0,i=((c=r.text.trimStart().match(/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/))==null?void 0:c[0])||"-",a=((l=(d=r.text.trimStart().match(/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/))==null?void 0:d[0])==null?void 0:l.length)||2;if(t.addItem(f=>{const m=f.setIcon("corner-up-right").setTitle("Turn into").setSubmenu();m.addItem(b=>{b.setIcon("list").setTitle("Bullet").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:"-"}],selection:{from:{line:r.number-1,ch:u+a+1},to:{line:r.number-1,ch:u+a+1}}})})}),m.addItem(b=>{b.setIcon("heading-1").setTitle("Heading 1").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:`${i} #`}],selection:{from:{line:r.number-1,ch:u+a+2},to:{line:r.number-1,ch:u+a+2}}})})}),m.addItem(b=>{b.setIcon("heading-2").setTitle("Heading 2").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:`${i} ##`}],selection:{from:{line:r.number-1,ch:u+a+3},to:{line:r.number-1,ch:u+a+3}}})})}),m.addItem(b=>{b.setIcon("list-checks").setTitle("To-do").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:`${i.replace(/\[.\]/g,"").trim()} [ ]`}],selection:{from:{line:r.number-1,ch:u+a+4},to:{line:r.number-1,ch:u+a+4}}})})}),m.addItem(b=>{b.setIcon("pilcrow").setTitle("Paragraph").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:`${i}`}],selection:{from:{line:r.number-1,ch:u+a+1},to:{line:r.number-1,ch:u+a+1}}})})}),m.addItem(b=>{b.setIcon("square-kanban").setTitle("Board").onClick(()=>{new O.Notice("Not yet available")})})}),t.addSeparator(),t.addItem(f=>{f.setIcon("check").setTitle("Complete").onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:u},to:{line:r.number-1,ch:u+a},text:`${i.replace(/\[.\]/g,"").trim()} [x]`}],selection:{from:{line:r.number-1,ch:u+a+4},to:{line:r.number-1,ch:u+a+4}}})})}),t.addItem(f=>{f.setIcon("pencil").setTitle("Add note").onClick(()=>{var m;/^(-|\*|\d+\.)(\s\[.\])?/g.test(r.text.trimStart())&&(n==null||n.focus(),n==null||n.transaction({selection:{from:{line:r.number-1,ch:r.length-1},to:{line:r.number-1,ch:r.length-1}}}),n&&n.editorComponent.options.onEnter(n.editorComponent,!1,!0)||(n==null||n.transaction({changes:[{from:{line:r.number-1,ch:r.length},to:{line:r.number-1,ch:r.length},text:`
${((m=r.text.match(/^\s*/))==null?void 0:m[0])||""}${" ".repeat(2)}`}]}),n==null||n.transaction({selection:{from:{line:r.number,ch:0},to:{line:r.number,ch:0}}})))})}),t.addItem(f=>{f.setIcon("calendar").setTitle("Add date").onClick(()=>{const b=`📅 ${O.moment().format("YYYY-MM-DD")}`;n==null||n.transaction({changes:[{from:{line:r.number-1,ch:r.length},text:` ${b} `}],selection:{from:{line:r.number,ch:r.length},to:{line:r.number,ch:r.length}}})})}),t.addItem(f=>{f.setIcon("play").setTitle("Present").onClick(()=>{new O.Notice("Not yet available")})}),t.addItem(f=>{f.setIcon("move-right").setTitle("Move to").onClick(()=>{new O.Notice("Not yet available")})}),t.addSeparator(),t.addItem(f=>{f.setIcon("trash").setTitle("Delete").setWarning(!0).onClick(()=>{n==null||n.transaction({changes:[{from:{line:r.number-1,ch:0},to:{line:r.number,ch:0},text:""}],selection:{from:{line:r.number-1,ch:0},to:{line:r.number-1,ch:0}}})})}),!this.bulletSpanEl)return;const s=(h=this.bulletSpanEl)==null?void 0:h.getBoundingClientRect();t.showAtPosition({x:s.left-42,y:s.bottom})}),this.bulletSpanEl}ignoreEvent(t){return t.type==="mousedown"||t.type==="mouseup"||t.type==="click"?(t.preventDefault(),!0):!1}}const rm=rt.StateField.define({create(){return J.Decoration.none},update(e,t){const n=new rt.RangeSetBuilder,r=t.state.field(O.editorInfoField);for(let i=1;i<=t.state.doc.lines;i++){const a=t.state.doc.line(i);if(!/^(-|\*|\d+\.)(\s(\[.\]))?/g.test(a.text.trimStart()))continue;const s=a.text.match(/^\s*/)[0].length;s>0?n.add(a.from+s,a.from+s,J.Decoration.widget({widget:new Ds(r.app,t.state,a.from+s,a.from+s),side:-1})):n.add(a.from,a.from,J.Decoration.widget({widget:new Ds(r.app,t.state,a.from,a.from),side:-1}))}return n.finish()},provide:e=>J.EditorView.decorations.from(e)}),xu=rt.StateEffect.define(),wc=rt.StateEffect.define(),Be=rt.StateEffect.define(),er=rt.StateEffect.define(),pn=rt.StateEffect.define(),Ec=rt.StateEffect.define(),$n=J.Decoration.replace({block:!0});class um extends J.WidgetType{constructor(t,n){super(),this.from=t,this.to=n}eq(t){return t.from===this.from&&t.to===this.to}toDOM(){const t=createEl("span",{cls:"cm-edit-button-container"});return new O.ExtraButtonComponent(t).setIcon("pencil").onClick(()=>{const n=t.closest(".internal-embed.is-loaded");if(n&&"cmView"in n){const r=n.cmView.widget,u=r.editor.cm;u.focus(),u.dispatch({selection:{head:r.start,anchor:r.end},scrollIntoView:!0})}}),t}}const Lr=rt.StateField.define({create:()=>J.Decoration.none,update:(e,t)=>{e=e.map(t.changes);for(const n of t.effects){if(n.is(Be)&&(e=e.update({filter:()=>!1}),n.value.type==="part"&&n.value.container&&(e=e.update({add:[J.Decoration.widget({widget:new um(n.value.to+1,n.value.to+1),side:1}).range(n.value.to+1,n.value.to+1)]})),n.value.from>0&&(e=e.update({add:[n.value.type==="part"?J.Decoration.replace({block:!0,inclusiveEnd:!1}).range(0,n.value.from-1):$n.range(0,n.value.from-1)]})),n.value.to<t.newDoc.length&&(e=e.update({add:[n.value.type==="part"?J.Decoration.replace({block:!0,inclusiveStart:!1}).range(n.value.to+1,t.newDoc.length):$n.range(n.value.to+1,t.newDoc.length)]}))),n.is(wc)&&(e=e.update({filter:()=>!1}),n.value.ranges.forEach(r=>{e=e.update({add:[$n.range(r.from,r.to)]})})),n.is(er)&&(e=e.update({filter:()=>!1})),n.is(xu)){e=e.update({filter:()=>!1});const r=t.state.doc.length,u=n.value.ranges;u.sort((s,o)=>s.from-o.from);const i=[];let a=0;u.forEach(s=>{s.from>a+1&&i.push({from:a,to:s.from}),a=Math.max(a,s.to)}),a<r&&i.push({from:a,to:r}),i.forEach(s=>{e=e.update({add:[$n.range(s.from===0?s.from:s.from+1,s.to===r?s.to:s.to-1)]})})}if(n.is(pn)){const{range:r,indent:u}=n.value,i=t.state.doc.lineAt(r.from),a=t.state.doc.lineAt(r.to);for(let s=i.number;s<=a.number;s++){const o=t.state.doc.line(s);e=e.update({add:[$n.range(o.from,o.from+u.length)]})}}if(n.is(Ec)){e=e.update({filter:()=>!1});const{range:r}=n.value;e=e.update({add:[$n.range(r.from,r.to)]})}}return e},provide:e=>J.EditorView.decorations.from(e)});var tn={};Object.defineProperty(tn,"__esModule",{value:!0});class Bn extends Error{}class im extends Bn{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}}class am extends Bn{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}}class sm extends Bn{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}}class wr extends Bn{}class Fc extends Bn{constructor(t){super(`Invalid unit ${t}`)}}class we extends Bn{}class on extends Bn{constructor(){super("Zone is an abstract class")}}const Z="numeric",Le="short",fe="long",Su={year:Z,month:Z,day:Z},Cc={year:Z,month:Le,day:Z},om={year:Z,month:Le,day:Z,weekday:Le},kc={year:Z,month:fe,day:Z},xc={year:Z,month:fe,day:Z,weekday:fe},Sc={hour:Z,minute:Z},Tc={hour:Z,minute:Z,second:Z},Ac={hour:Z,minute:Z,second:Z,timeZoneName:Le},Oc={hour:Z,minute:Z,second:Z,timeZoneName:fe},Mc={hour:Z,minute:Z,hourCycle:"h23"},Bc={hour:Z,minute:Z,second:Z,hourCycle:"h23"},Lc={hour:Z,minute:Z,second:Z,hourCycle:"h23",timeZoneName:Le},Pc={hour:Z,minute:Z,second:Z,hourCycle:"h23",timeZoneName:fe},Nc={year:Z,month:Z,day:Z,hour:Z,minute:Z},_c={year:Z,month:Z,day:Z,hour:Z,minute:Z,second:Z},Rc={year:Z,month:Le,day:Z,hour:Z,minute:Z},$c={year:Z,month:Le,day:Z,hour:Z,minute:Z,second:Z},cm={year:Z,month:Le,day:Z,weekday:Le,hour:Z,minute:Z},Ic={year:Z,month:fe,day:Z,hour:Z,minute:Z,timeZoneName:Le},Vc={year:Z,month:fe,day:Z,hour:Z,minute:Z,second:Z,timeZoneName:Le},jc={year:Z,month:fe,day:Z,weekday:fe,hour:Z,minute:Z,timeZoneName:fe},Hc={year:Z,month:fe,day:Z,weekday:fe,hour:Z,minute:Z,second:Z,timeZoneName:fe};class Ir{get type(){throw new on}get name(){throw new on}get ianaName(){return this.name}get isUniversal(){throw new on}offsetName(t,n){throw new on}formatOffset(t,n){throw new on}offset(t){throw new on}equals(t){throw new on}get isValid(){throw new on}}let bi=null;class Zu extends Ir{static get instance(){return bi===null&&(bi=new Zu),bi}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:n,locale:r}){return Uc(t,n,r)}formatOffset(t,n){return Sr(this.offset(t),n)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return t.type==="system"}get isValid(){return!0}}let mu={};function dm(e){return mu[e]||(mu[e]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),mu[e]}const lm={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function fm(e,t){const n=e.format(t).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,u,i,a,s,o,c,d]=r;return[a,u,i,s,o,c,d]}function hm(e,t){const n=e.formatToParts(t),r=[];for(let u=0;u<n.length;u++){const{type:i,value:a}=n[u],s=lm[i];i==="era"?r[s]=a:mt(s)||(r[s]=parseInt(a,10))}return r}let ru={};class Je extends Ir{static create(t){return ru[t]||(ru[t]=new Je(t)),ru[t]}static resetCache(){ru={},mu={}}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch{return!1}}constructor(t){super(),this.zoneName=t,this.valid=Je.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:n,locale:r}){return Uc(t,n,r,this.name)}formatOffset(t,n){return Sr(this.offset(t),n)}offset(t){const n=new Date(t);if(isNaN(n))return NaN;const r=dm(this.name);let[u,i,a,s,o,c,d]=r.formatToParts?hm(r,n):fm(r,n);s==="BC"&&(u=-Math.abs(u)+1);const h=zu({year:u,month:i,day:a,hour:o===24?0:o,minute:c,second:d,millisecond:0});let f=+n;const m=f%1e3;return f-=m>=0?m:1e3+m,(h-f)/(60*1e3)}equals(t){return t.type==="iana"&&t.name===this.name}get isValid(){return this.valid}}let bs={};function mm(e,t={}){const n=JSON.stringify([e,t]);let r=bs[n];return r||(r=new Intl.ListFormat(e,t),bs[n]=r),r}let Vi={};function ji(e,t={}){const n=JSON.stringify([e,t]);let r=Vi[n];return r||(r=new Intl.DateTimeFormat(e,t),Vi[n]=r),r}let Hi={};function pm(e,t={}){const n=JSON.stringify([e,t]);let r=Hi[n];return r||(r=new Intl.NumberFormat(e,t),Hi[n]=r),r}let qi={};function gm(e,t={}){const{base:n,...r}=t,u=JSON.stringify([e,r]);let i=qi[u];return i||(i=new Intl.RelativeTimeFormat(e,t),qi[u]=i),i}let Er=null;function Dm(){return Er||(Er=new Intl.DateTimeFormat().resolvedOptions().locale,Er)}function bm(e){const t=e.indexOf("-x-");t!==-1&&(e=e.substring(0,t));const n=e.indexOf("-u-");if(n===-1)return[e];{let r,u;try{r=ji(e).resolvedOptions(),u=e}catch{const o=e.substring(0,n);r=ji(o).resolvedOptions(),u=o}const{numberingSystem:i,calendar:a}=r;return[u,i,a]}}function ym(e,t,n){return(n||t)&&(e.includes("-u-")||(e+="-u"),n&&(e+=`-ca-${n}`),t&&(e+=`-nu-${t}`)),e}function vm(e){const t=[];for(let n=1;n<=12;n++){const r=tt.utc(2009,n,1);t.push(e(r))}return t}function wm(e){const t=[];for(let n=1;n<=7;n++){const r=tt.utc(2016,11,13+n);t.push(e(r))}return t}function uu(e,t,n,r){const u=e.listingMode();return u==="error"?null:u==="en"?n(t):r(t)}function Em(e){return e.numberingSystem&&e.numberingSystem!=="latn"?!1:e.numberingSystem==="latn"||!e.locale||e.locale.startsWith("en")||new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem==="latn"}class Fm{constructor(t,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;const{padTo:u,floor:i,...a}=r;if(!n||Object.keys(a).length>0){const s={useGrouping:!1,...r};r.padTo>0&&(s.minimumIntegerDigits=r.padTo),this.inf=pm(t,s)}}format(t){if(this.inf){const n=this.floor?Math.floor(t):t;return this.inf.format(n)}else{const n=this.floor?Math.floor(t):la(t,3);return Zt(n,this.padTo)}}}class Cm{constructor(t,n,r){this.opts=r,this.originalZone=void 0;let u;if(this.opts.timeZone)this.dt=t;else if(t.zone.type==="fixed"){const a=-1*(t.offset/60),s=a>=0?`Etc/GMT+${a}`:`Etc/GMT${a}`;t.offset!==0&&Je.create(s).valid?(u=s,this.dt=t):(u="UTC",this.dt=t.offset===0?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)}else t.zone.type==="system"?this.dt=t:t.zone.type==="iana"?(this.dt=t,u=t.zone.name):(u="UTC",this.dt=t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone);const i={...this.opts};i.timeZone=i.timeZone||u,this.dtf=ji(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:t})=>t).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(n=>{if(n.type==="timeZoneName"){const r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):t}resolvedOptions(){return this.dtf.resolvedOptions()}}class km{constructor(t,n,r){this.opts={style:"long",...r},!n&&qc()&&(this.rtf=gm(t,r))}format(t,n){return this.rtf?this.rtf.format(t,n):Hm(n,t,this.opts.numeric,this.opts.style!=="long")}formatToParts(t,n){return this.rtf?this.rtf.formatToParts(t,n):[]}}class Ot{static fromOpts(t){return Ot.create(t.locale,t.numberingSystem,t.outputCalendar,t.defaultToEN)}static create(t,n,r,u=!1){const i=t||Yt.defaultLocale,a=i||(u?"en-US":Dm()),s=n||Yt.defaultNumberingSystem,o=r||Yt.defaultOutputCalendar;return new Ot(a,s,o,i)}static resetCache(){Er=null,Vi={},Hi={},qi={}}static fromObject({locale:t,numberingSystem:n,outputCalendar:r}={}){return Ot.create(t,n,r)}constructor(t,n,r,u){const[i,a,s]=bm(t);this.locale=i,this.numberingSystem=n||a||null,this.outputCalendar=r||s||null,this.intl=ym(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=u,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=Em(this)),this.fastNumbersCached}listingMode(){const t=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return t&&n?"en":"intl"}clone(t){return!t||Object.getOwnPropertyNames(t).length===0?this:Ot.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,t.defaultToEN||!1)}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,n=!1){return uu(this,t,Wc,()=>{const r=n?{month:t,day:"numeric"}:{month:t},u=n?"format":"standalone";return this.monthsCache[u][t]||(this.monthsCache[u][t]=vm(i=>this.extract(i,r,"month"))),this.monthsCache[u][t]})}weekdays(t,n=!1){return uu(this,t,Kc,()=>{const r=n?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},u=n?"format":"standalone";return this.weekdaysCache[u][t]||(this.weekdaysCache[u][t]=wm(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[u][t]})}meridiems(){return uu(this,void 0,()=>Jc,()=>{if(!this.meridiemCache){const t={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[tt.utc(2016,11,13,9),tt.utc(2016,11,13,19)].map(n=>this.extract(n,t,"dayperiod"))}return this.meridiemCache})}eras(t){return uu(this,t,Qc,()=>{const n={era:t};return this.eraCache[t]||(this.eraCache[t]=[tt.utc(-40,1,1),tt.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[t]})}extract(t,n,r){const u=this.dtFormatter(t,n),i=u.formatToParts(),a=i.find(s=>s.type.toLowerCase()===r);return a?a.value:null}numberFormatter(t={}){return new Fm(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,n={}){return new Cm(t,this.intl,n)}relFormatter(t={}){return new km(this.intl,this.isEnglish(),t)}listFormatter(t={}){return mm(this.intl,t)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}}let yi=null;class oe extends Ir{static get utcInstance(){return yi===null&&(yi=new oe(0)),yi}static instance(t){return t===0?oe.utcInstance:new oe(t)}static parseSpecifier(t){if(t){const n=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new oe(Gu(n[1],n[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${Sr(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${Sr(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,n){return Sr(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return t.type==="fixed"&&t.fixed===this.fixed}get isValid(){return!0}}class xm extends Ir{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function fn(e,t){if(mt(e)||e===null)return t;if(e instanceof Ir)return e;if(Sm(e)){const n=e.toLowerCase();return n==="default"?t:n==="local"||n==="system"?Zu.instance:n==="utc"||n==="gmt"?oe.utcInstance:oe.parseSpecifier(n)||Je.create(e)}else return Sn(e)?oe.instance(e):typeof e=="object"&&"offset"in e&&typeof e.offset=="function"?e:new xm(e)}let ys=()=>Date.now(),vs="system",ws=null,Es=null,Fs=null,Cs=60,ks;class Yt{static get now(){return ys}static set now(t){ys=t}static set defaultZone(t){vs=t}static get defaultZone(){return fn(vs,Zu.instance)}static get defaultLocale(){return ws}static set defaultLocale(t){ws=t}static get defaultNumberingSystem(){return Es}static set defaultNumberingSystem(t){Es=t}static get defaultOutputCalendar(){return Fs}static set defaultOutputCalendar(t){Fs=t}static get twoDigitCutoffYear(){return Cs}static set twoDigitCutoffYear(t){Cs=t%100}static get throwOnInvalid(){return ks}static set throwOnInvalid(t){ks=t}static resetCaches(){Ot.resetCache(),Je.resetCache()}}function mt(e){return typeof e>"u"}function Sn(e){return typeof e=="number"}function Wu(e){return typeof e=="number"&&e%1===0}function Sm(e){return typeof e=="string"}function Tm(e){return Object.prototype.toString.call(e)==="[object Date]"}function qc(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Am(e){return Array.isArray(e)?e:[e]}function xs(e,t,n){if(e.length!==0)return e.reduce((r,u)=>{const i=[t(u),u];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function Om(e,t){return t.reduce((n,r)=>(n[r]=e[r],n),{})}function nr(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function We(e,t,n){return Wu(e)&&e>=t&&e<=n}function Mm(e,t){return e-t*Math.floor(e/t)}function Zt(e,t=2){const n=e<0;let r;return n?r="-"+(""+-e).padStart(t,"0"):r=(""+e).padStart(t,"0"),r}function ln(e){if(!(mt(e)||e===null||e===""))return parseInt(e,10)}function En(e){if(!(mt(e)||e===null||e===""))return parseFloat(e)}function da(e){if(!(mt(e)||e===null||e==="")){const t=parseFloat("0."+e)*1e3;return Math.floor(t)}}function la(e,t,n=!1){const r=10**t;return(n?Math.trunc:Math.round)(e*r)/r}function Vr(e){return e%4===0&&(e%100!==0||e%400===0)}function xr(e){return Vr(e)?366:365}function Tu(e,t){const n=Mm(t-1,12)+1,r=e+(t-n)/12;return n===2?Vr(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function zu(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(e.year,e.month-1,e.day)),+t}function Au(e){const t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,n=e-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return t===4||r===3?53:52}function Ui(e){return e>99?e:e>Yt.twoDigitCutoffYear?1900+e:2e3+e}function Uc(e,t,n,r=null){const u=new Date(e),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);const a={timeZoneName:t,...i},s=new Intl.DateTimeFormat(n,a).formatToParts(u).find(o=>o.type.toLowerCase()==="timezonename");return s?s.value:null}function Gu(e,t){let n=parseInt(e,10);Number.isNaN(n)&&(n=0);const r=parseInt(t,10)||0,u=n<0||Object.is(n,-0)?-r:r;return n*60+u}function Yc(e){const t=Number(e);if(typeof e=="boolean"||e===""||Number.isNaN(t))throw new we(`Invalid unit value ${e}`);return t}function Ou(e,t){const n={};for(const r in e)if(nr(e,r)){const u=e[r];if(u==null)continue;n[t(r)]=Yc(u)}return n}function Sr(e,t){const n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),u=e>=0?"+":"-";switch(t){case"short":return`${u}${Zt(n,2)}:${Zt(r,2)}`;case"narrow":return`${u}${n}${r>0?`:${r}`:""}`;case"techie":return`${u}${Zt(n,2)}${Zt(r,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function Ku(e){return Om(e,["hour","minute","second","millisecond"])}const Bm=["January","February","March","April","May","June","July","August","September","October","November","December"],Zc=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Lm=["J","F","M","A","M","J","J","A","S","O","N","D"];function Wc(e){switch(e){case"narrow":return[...Lm];case"short":return[...Zc];case"long":return[...Bm];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const zc=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Gc=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Pm=["M","T","W","T","F","S","S"];function Kc(e){switch(e){case"narrow":return[...Pm];case"short":return[...Gc];case"long":return[...zc];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Jc=["AM","PM"],Nm=["Before Christ","Anno Domini"],_m=["BC","AD"],Rm=["B","A"];function Qc(e){switch(e){case"narrow":return[...Rm];case"short":return[..._m];case"long":return[...Nm];default:return null}}function $m(e){return Jc[e.hour<12?0:1]}function Im(e,t){return Kc(t)[e.weekday-1]}function Vm(e,t){return Wc(t)[e.month-1]}function jm(e,t){return Qc(t)[e.year<0?0:1]}function Hm(e,t,n="always",r=!1){const u={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(e)===-1;if(n==="auto"&&i){const l=e==="days";switch(t){case 1:return l?"tomorrow":`next ${u[e][0]}`;case-1:return l?"yesterday":`last ${u[e][0]}`;case 0:return l?"today":`this ${u[e][0]}`}}const a=Object.is(t,-0)||t<0,s=Math.abs(t),o=s===1,c=u[e],d=r?o?c[1]:c[2]||c[1]:o?u[e][0]:e;return a?`${s} ${d} ago`:`in ${s} ${d}`}function Ss(e,t){let n="";for(const r of e)r.literal?n+=r.val:n+=t(r.val);return n}const qm={D:Su,DD:Cc,DDD:kc,DDDD:xc,t:Sc,tt:Tc,ttt:Ac,tttt:Oc,T:Mc,TT:Bc,TTT:Lc,TTTT:Pc,f:Nc,ff:Rc,fff:Ic,ffff:jc,F:_c,FF:$c,FFF:Vc,FFFF:Hc};class re{static create(t,n={}){return new re(t,n)}static parseFormat(t){let n=null,r="",u=!1;const i=[];for(let a=0;a<t.length;a++){const s=t.charAt(a);s==="'"?(r.length>0&&i.push({literal:u||/^\s+$/.test(r),val:r}),n=null,r="",u=!u):u||s===n?r+=s:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=s,n=s)}return r.length>0&&i.push({literal:u||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(t){return qm[t]}constructor(t,n){this.opts=n,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...n}).format()}dtFormatter(t,n={}){return this.loc.dtFormatter(t,{...this.opts,...n})}formatDateTime(t,n){return this.dtFormatter(t,n).format()}formatDateTimeParts(t,n){return this.dtFormatter(t,n).formatToParts()}formatInterval(t,n){return this.dtFormatter(t.start,n).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,n){return this.dtFormatter(t,n).resolvedOptions()}num(t,n=0){if(this.opts.forceSimple)return Zt(t,n);const r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(t)}formatDateTimeFromString(t,n){const r=this.loc.listingMode()==="en",u=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(f,m)=>this.loc.extract(t,f,m),a=f=>t.isOffsetFixed&&t.offset===0&&f.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,f.format):"",s=()=>r?$m(t):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),o=(f,m)=>r?Vm(t,f):i(m?{month:f}:{month:f,day:"numeric"},"month"),c=(f,m)=>r?Im(t,f):i(m?{weekday:f}:{weekday:f,month:"long",day:"numeric"},"weekday"),d=f=>{const m=re.macroTokenToFormatOpts(f);return m?this.formatWithSystemDefault(t,m):f},l=f=>r?jm(t,f):i({era:f},"era"),h=f=>{switch(f){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12===0?12:t.hour%12);case"hh":return this.num(t.hour%12===0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return a({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return a({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return s();case"d":return u?i({day:"numeric"},"day"):this.num(t.day);case"dd":return u?i({day:"2-digit"},"day"):this.num(t.day,2);case"c":return this.num(t.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return this.num(t.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return u?i({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return u?i({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return o("short",!0);case"LLLL":return o("long",!0);case"LLLLL":return o("narrow",!0);case"M":return u?i({month:"numeric"},"month"):this.num(t.month);case"MM":return u?i({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return o("short",!1);case"MMMM":return o("long",!1);case"MMMMM":return o("narrow",!1);case"y":return u?i({year:"numeric"},"year"):this.num(t.year);case"yy":return u?i({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return u?i({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return u?i({year:"numeric"},"year"):this.num(t.year,6);case"G":return l("short");case"GG":return l("long");case"GGGGG":return l("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return d(f)}};return Ss(re.parseFormat(n),h)}formatDurationFromString(t,n){const r=o=>{switch(o[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},u=o=>c=>{const d=r(c);return d?this.num(o.get(d),c.length):c},i=re.parseFormat(n),a=i.reduce((o,{literal:c,val:d})=>c?o:o.concat(d),[]),s=t.shiftTo(...a.map(r).filter(o=>o));return Ss(i,u(s))}}class Me{constructor(t,n){this.reason=t,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Xc=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function or(...e){const t=e.reduce((n,r)=>n+r.source,"");return RegExp(`^${t}$`)}function cr(...e){return t=>e.reduce(([n,r,u],i)=>{const[a,s,o]=i(t,u);return[{...n,...a},s||r,o]},[{},null,1]).slice(0,2)}function dr(e,...t){if(e==null)return[null,null];for(const[n,r]of t){const u=n.exec(e);if(u)return r(u)}return[null,null]}function td(...e){return(t,n)=>{const r={};let u;for(u=0;u<e.length;u++)r[e[u]]=ln(t[n+u]);return[r,null,n+u]}}const ed=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Um=`(?:${ed.source}?(?:\\[(${Xc.source})\\])?)?`,fa=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,nd=RegExp(`${fa.source}${Um}`),ha=RegExp(`(?:T${nd.source})?`),Ym=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Zm=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Wm=/(\d{4})-?(\d{3})/,zm=td("weekYear","weekNumber","weekDay"),Gm=td("year","ordinal"),Km=/(\d{4})-(\d\d)-(\d\d)/,rd=RegExp(`${fa.source} ?(?:${ed.source}|(${Xc.source}))?`),Jm=RegExp(`(?: ${rd.source})?`);function zn(e,t,n){const r=e[t];return mt(r)?n:ln(r)}function Qm(e,t){return[{year:zn(e,t),month:zn(e,t+1,1),day:zn(e,t+2,1)},null,t+3]}function lr(e,t){return[{hours:zn(e,t,0),minutes:zn(e,t+1,0),seconds:zn(e,t+2,0),milliseconds:da(e[t+3])},null,t+4]}function jr(e,t){const n=!e[t]&&!e[t+1],r=Gu(e[t+1],e[t+2]),u=n?null:oe.instance(r);return[{},u,t+3]}function Hr(e,t){const n=e[t]?Je.create(e[t]):null;return[{},n,t+1]}const Xm=RegExp(`^T?${fa.source}$`),t1=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function e1(e){const[t,n,r,u,i,a,s,o,c]=e,d=t[0]==="-",l=o&&o[0]==="-",h=(f,m=!1)=>f!==void 0&&(m||f&&d)?-f:f;return[{years:h(En(n)),months:h(En(r)),weeks:h(En(u)),days:h(En(i)),hours:h(En(a)),minutes:h(En(s)),seconds:h(En(o),o==="-0"),milliseconds:h(da(c),l)}]}const n1={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function ma(e,t,n,r,u,i,a){const s={year:t.length===2?Ui(ln(t)):ln(t),month:Zc.indexOf(n)+1,day:ln(r),hour:ln(u),minute:ln(i)};return a&&(s.second=ln(a)),e&&(s.weekday=e.length>3?zc.indexOf(e)+1:Gc.indexOf(e)+1),s}const r1=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function u1(e){const[,t,n,r,u,i,a,s,o,c,d,l]=e,h=ma(t,u,r,n,i,a,s);let f;return o?f=n1[o]:c?f=0:f=Gu(d,l),[h,new oe(f)]}function i1(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const a1=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,s1=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,o1=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Ts(e){const[,t,n,r,u,i,a,s]=e;return[ma(t,u,r,n,i,a,s),oe.utcInstance]}function c1(e){const[,t,n,r,u,i,a,s]=e;return[ma(t,s,n,r,u,i,a),oe.utcInstance]}const d1=or(Ym,ha),l1=or(Zm,ha),f1=or(Wm,ha),h1=or(nd),ud=cr(Qm,lr,jr,Hr),m1=cr(zm,lr,jr,Hr),p1=cr(Gm,lr,jr,Hr),g1=cr(lr,jr,Hr);function D1(e){return dr(e,[d1,ud],[l1,m1],[f1,p1],[h1,g1])}function b1(e){return dr(i1(e),[r1,u1])}function y1(e){return dr(e,[a1,Ts],[s1,Ts],[o1,c1])}function v1(e){return dr(e,[t1,e1])}const w1=cr(lr);function E1(e){return dr(e,[Xm,w1])}const F1=or(Km,Jm),C1=or(rd),k1=cr(lr,jr,Hr);function x1(e){return dr(e,[F1,ud],[C1,k1])}const As="Invalid Duration",id={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},S1={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...id},De=146097/400,In=146097/4800,T1={years:{quarters:4,months:12,weeks:De/7,days:De,hours:De*24,minutes:De*24*60,seconds:De*24*60*60,milliseconds:De*24*60*60*1e3},quarters:{months:3,weeks:De/28,days:De/4,hours:De*24/4,minutes:De*24*60/4,seconds:De*24*60*60/4,milliseconds:De*24*60*60*1e3/4},months:{weeks:In/7,days:In,hours:In*24,minutes:In*24*60,seconds:In*24*60*60,milliseconds:In*24*60*60*1e3},...id},kn=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],A1=kn.slice(0).reverse();function cn(e,t,n=!1){const r={values:n?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new K(r)}function ad(e,t){let n=t.milliseconds??0;for(const r of A1.slice(1))t[r]&&(n+=t[r]*e[r].milliseconds);return n}function Os(e,t){const n=ad(e,t)<0?-1:1;kn.reduceRight((r,u)=>{if(mt(t[u]))return r;if(r){const i=t[r]*n,a=e[u][r],s=Math.floor(i/a);t[u]+=s*n,t[r]-=s*a*n}return u},null),kn.reduce((r,u)=>{if(mt(t[u]))return r;if(r){const i=t[r]%1;t[r]-=i,t[u]+=i*e[r][u]}return u},null)}function O1(e){const t={};for(const[n,r]of Object.entries(e))r!==0&&(t[n]=r);return t}class K{constructor(t){const n=t.conversionAccuracy==="longterm"||!1;let r=n?T1:S1;t.matrix&&(r=t.matrix),this.values=t.values,this.loc=t.loc||Ot.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(t,n){return K.fromObject({milliseconds:t},n)}static fromObject(t,n={}){if(t==null||typeof t!="object")throw new we(`Duration.fromObject: argument expected to be an object, got ${t===null?"null":typeof t}`);return new K({values:Ou(t,K.normalizeUnit),loc:Ot.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(t){if(Sn(t))return K.fromMillis(t);if(K.isDuration(t))return t;if(typeof t=="object")return K.fromObject(t);throw new we(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,n){const[r]=v1(t);return r?K.fromObject(r,n):K.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,n){const[r]=E1(t);return r?K.fromObject(r,n):K.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,n=null){if(!t)throw new we("need to specify a reason the Duration is invalid");const r=t instanceof Me?t:new Me(t,n);if(Yt.throwOnInvalid)throw new sm(r);return new K({invalid:r})}static normalizeUnit(t){const n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(!n)throw new Fc(t);return n}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,n={}){const r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?re.create(this.loc,r).formatDurationFromString(this,t):As}toHuman(t={}){if(!this.isValid)return As;const n=kn.map(r=>{const u=this.values[r];return mt(u)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:r.slice(0,-1)}).format(u)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return this.years!==0&&(t+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(t+=this.months+this.quarters*3+"M"),this.weeks!==0&&(t+=this.weeks+"W"),this.days!==0&&(t+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(t+="T"),this.hours!==0&&(t+=this.hours+"H"),this.minutes!==0&&(t+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(t+=la(this.seconds+this.milliseconds/1e3,3)+"S"),t==="P"&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;const n=this.toMillis();return n<0||n>=864e5?null:(t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t,includeOffset:!1},tt.fromMillis(n,{zone:"UTC"}).toISOTime(t))}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.isValid?ad(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;const n=K.fromDurationLike(t),r={};for(const u of kn)(nr(n.values,u)||nr(this.values,u))&&(r[u]=n.get(u)+this.get(u));return cn(this,{values:r},!0)}minus(t){if(!this.isValid)return this;const n=K.fromDurationLike(t);return this.plus(n.negate())}mapUnits(t){if(!this.isValid)return this;const n={};for(const r of Object.keys(this.values))n[r]=Yc(t(this.values[r],r));return cn(this,{values:n},!0)}get(t){return this[K.normalizeUnit(t)]}set(t){if(!this.isValid)return this;const n={...this.values,...Ou(t,K.normalizeUnit)};return cn(this,{values:n})}reconfigure({locale:t,numberingSystem:n,conversionAccuracy:r,matrix:u}={}){const a={loc:this.loc.clone({locale:t,numberingSystem:n}),matrix:u,conversionAccuracy:r};return cn(this,a)}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;const t=this.toObject();return Os(this.matrix,t),cn(this,{values:t},!0)}rescale(){if(!this.isValid)return this;const t=O1(this.normalize().shiftToAll().toObject());return cn(this,{values:t},!0)}shiftTo(...t){if(!this.isValid)return this;if(t.length===0)return this;t=t.map(a=>K.normalizeUnit(a));const n={},r={},u=this.toObject();let i;for(const a of kn)if(t.indexOf(a)>=0){i=a;let s=0;for(const c in r)s+=this.matrix[c][a]*r[c],r[c]=0;Sn(u[a])&&(s+=u[a]);const o=Math.trunc(s);n[a]=o,r[a]=(s*1e3-o*1e3)/1e3}else Sn(u[a])&&(r[a]=u[a]);for(const a in r)r[a]!==0&&(n[i]+=a===i?r[a]:r[a]/this.matrix[i][a]);return Os(this.matrix,n),cn(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const t={};for(const n of Object.keys(this.values))t[n]=this.values[n]===0?0:-this.values[n];return cn(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid||!this.loc.equals(t.loc))return!1;function n(r,u){return r===void 0||r===0?u===void 0||u===0:r===u}for(const r of kn)if(!n(this.values[r],t.values[r]))return!1;return!0}}const Vn="Invalid Interval";function M1(e,t){return!e||!e.isValid?Rt.invalid("missing or invalid start"):!t||!t.isValid?Rt.invalid("missing or invalid end"):t<e?Rt.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null}class Rt{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,n=null){if(!t)throw new we("need to specify a reason the Interval is invalid");const r=t instanceof Me?t:new Me(t,n);if(Yt.throwOnInvalid)throw new am(r);return new Rt({invalid:r})}static fromDateTimes(t,n){const r=Dr(t),u=Dr(n),i=M1(r,u);return i??new Rt({start:r,end:u})}static after(t,n){const r=K.fromDurationLike(n),u=Dr(t);return Rt.fromDateTimes(u,u.plus(r))}static before(t,n){const r=K.fromDurationLike(n),u=Dr(t);return Rt.fromDateTimes(u.minus(r),u)}static fromISO(t,n){const[r,u]=(t||"").split("/",2);if(r&&u){let i,a;try{i=tt.fromISO(r,n),a=i.isValid}catch{a=!1}let s,o;try{s=tt.fromISO(u,n),o=s.isValid}catch{o=!1}if(a&&o)return Rt.fromDateTimes(i,s);if(a){const c=K.fromISO(u,n);if(c.isValid)return Rt.after(i,c)}else if(o){const c=K.fromISO(r,n);if(c.isValid)return Rt.before(s,c)}}return Rt.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds"){if(!this.isValid)return NaN;const n=this.start.startOf(t),r=this.end.startOf(t);return Math.floor(r.diff(n,t).get(t))+(r.valueOf()!==this.end.valueOf())}hasSame(t){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,t):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return this.isValid?this.s>t:!1}isBefore(t){return this.isValid?this.e<=t:!1}contains(t){return this.isValid?this.s<=t&&this.e>t:!1}set({start:t,end:n}={}){return this.isValid?Rt.fromDateTimes(t||this.s,n||this.e):this}splitAt(...t){if(!this.isValid)return[];const n=t.map(Dr).filter(a=>this.contains(a)).sort(),r=[];let{s:u}=this,i=0;for(;u<this.e;){const a=n[i]||this.e,s=+a>+this.e?this.e:a;r.push(Rt.fromDateTimes(u,s)),u=s,i+=1}return r}splitBy(t){const n=K.fromDurationLike(t);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,u=1,i;const a=[];for(;r<this.e;){const s=this.start.plus(n.mapUnits(o=>o*u));i=+s>+this.e?this.e:s,a.push(Rt.fromDateTimes(r,i)),r=i,u+=1}return a}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return this.isValid?+this.e==+t.s:!1}abutsEnd(t){return this.isValid?+t.e==+this.s:!1}engulfs(t){return this.isValid?this.s<=t.s&&this.e>=t.e:!1}equals(t){return!this.isValid||!t.isValid?!1:this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;const n=this.s>t.s?this.s:t.s,r=this.e<t.e?this.e:t.e;return n>=r?null:Rt.fromDateTimes(n,r)}union(t){if(!this.isValid)return this;const n=this.s<t.s?this.s:t.s,r=this.e>t.e?this.e:t.e;return Rt.fromDateTimes(n,r)}static merge(t){const[n,r]=t.sort((u,i)=>u.s-i.s).reduce(([u,i],a)=>i?i.overlaps(a)||i.abutsStart(a)?[u,i.union(a)]:[u.concat([i]),a]:[u,a],[[],null]);return r&&n.push(r),n}static xor(t){let n=null,r=0;const u=[],i=t.map(o=>[{time:o.s,type:"s"},{time:o.e,type:"e"}]),a=Array.prototype.concat(...i),s=a.sort((o,c)=>o.time-c.time);for(const o of s)r+=o.type==="s"?1:-1,r===1?n=o.time:(n&&+n!=+o.time&&u.push(Rt.fromDateTimes(n,o.time)),n=null);return Rt.merge(u)}difference(...t){return Rt.xor([this].concat(t)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Vn}toLocaleString(t=Su,n={}){return this.isValid?re.create(this.s.loc.clone(n),t).formatInterval(this):Vn}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:Vn}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Vn}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:Vn}toFormat(t,{separator:n=" – "}={}){return this.isValid?`${this.s.toFormat(t)}${n}${this.e.toFormat(t)}`:Vn}toDuration(t,n){return this.isValid?this.e.diff(this.s,t,n):K.invalid(this.invalidReason)}mapEndpoints(t){return Rt.fromDateTimes(t(this.s),t(this.e))}}class iu{static hasDST(t=Yt.defaultZone){const n=tt.now().setZone(t).set({month:12});return!t.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(t){return Je.isValidZone(t)}static normalizeZone(t){return fn(t,Yt.defaultZone)}static months(t="long",{locale:n=null,numberingSystem:r=null,locObj:u=null,outputCalendar:i="gregory"}={}){return(u||Ot.create(n,r,i)).months(t)}static monthsFormat(t="long",{locale:n=null,numberingSystem:r=null,locObj:u=null,outputCalendar:i="gregory"}={}){return(u||Ot.create(n,r,i)).months(t,!0)}static weekdays(t="long",{locale:n=null,numberingSystem:r=null,locObj:u=null}={}){return(u||Ot.create(n,r,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:n=null,numberingSystem:r=null,locObj:u=null}={}){return(u||Ot.create(n,r,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return Ot.create(t).meridiems()}static eras(t="short",{locale:n=null}={}){return Ot.create(n,null,"gregory").eras(t)}static features(){return{relative:qc()}}}function Ms(e,t){const n=u=>u.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(t)-n(e);return Math.floor(K.fromMillis(r).as("days"))}function B1(e,t,n){const r=[["years",(o,c)=>c.year-o.year],["quarters",(o,c)=>c.quarter-o.quarter+(c.year-o.year)*4],["months",(o,c)=>c.month-o.month+(c.year-o.year)*12],["weeks",(o,c)=>{const d=Ms(o,c);return(d-d%7)/7}],["days",Ms]],u={},i=e;let a,s;for(const[o,c]of r)n.indexOf(o)>=0&&(a=o,u[o]=c(e,t),s=i.plus(u),s>t?(u[o]--,e=i.plus(u),e>t&&(s=e,u[o]--,e=i.plus(u))):e=s);return[e,u,s,a]}function L1(e,t,n,r){let[u,i,a,s]=B1(e,t,n);const o=t-u,c=n.filter(l=>["hours","minutes","seconds","milliseconds"].indexOf(l)>=0);c.length===0&&(a<t&&(a=u.plus({[s]:1})),a!==u&&(i[s]=(i[s]||0)+o/(a-u)));const d=K.fromObject(i,r);return c.length>0?K.fromMillis(o,r).shiftTo(...c).plus(d):d}const pa={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},Bs={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},P1=pa.hanidec.replace(/[\[|\]]/g,"").split("");function N1(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(e[n].search(pa.hanidec)!==-1)t+=P1.indexOf(e[n]);else for(const u in Bs){const[i,a]=Bs[u];r>=i&&r<=a&&(t+=r-i)}}return parseInt(t,10)}else return t}function xe({numberingSystem:e},t=""){return new RegExp(`${pa[e||"latn"]}${t}`)}const _1="missing Intl.DateTimeFormat.formatToParts support";function Et(e,t=n=>n){return{regex:e,deser:([n])=>t(N1(n))}}const R1=" ",sd=`[ ${R1}]`,od=new RegExp(sd,"g");function $1(e){return e.replace(/\./g,"\\.?").replace(od,sd)}function Ls(e){return e.replace(/\./g,"").replace(od," ").toLowerCase()}function Se(e,t){return e===null?null:{regex:RegExp(e.map($1).join("|")),deser:([n])=>e.findIndex(r=>Ls(n)===Ls(r))+t}}function Ps(e,t){return{regex:e,deser:([,n,r])=>Gu(n,r),groups:t}}function au(e){return{regex:e,deser:([t])=>t}}function I1(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function V1(e,t){const n=xe(t),r=xe(t,"{2}"),u=xe(t,"{3}"),i=xe(t,"{4}"),a=xe(t,"{6}"),s=xe(t,"{1,2}"),o=xe(t,"{1,3}"),c=xe(t,"{1,6}"),d=xe(t,"{1,9}"),l=xe(t,"{2,4}"),h=xe(t,"{4,6}"),f=g=>({regex:RegExp(I1(g.val)),deser:([y])=>y,literal:!0}),b=(g=>{if(e.literal)return f(g);switch(g.val){case"G":return Se(t.eras("short"),0);case"GG":return Se(t.eras("long"),0);case"y":return Et(c);case"yy":return Et(l,Ui);case"yyyy":return Et(i);case"yyyyy":return Et(h);case"yyyyyy":return Et(a);case"M":return Et(s);case"MM":return Et(r);case"MMM":return Se(t.months("short",!0),1);case"MMMM":return Se(t.months("long",!0),1);case"L":return Et(s);case"LL":return Et(r);case"LLL":return Se(t.months("short",!1),1);case"LLLL":return Se(t.months("long",!1),1);case"d":return Et(s);case"dd":return Et(r);case"o":return Et(o);case"ooo":return Et(u);case"HH":return Et(r);case"H":return Et(s);case"hh":return Et(r);case"h":return Et(s);case"mm":return Et(r);case"m":return Et(s);case"q":return Et(s);case"qq":return Et(r);case"s":return Et(s);case"ss":return Et(r);case"S":return Et(o);case"SSS":return Et(u);case"u":return au(d);case"uu":return au(s);case"uuu":return Et(n);case"a":return Se(t.meridiems(),0);case"kkkk":return Et(i);case"kk":return Et(l,Ui);case"W":return Et(s);case"WW":return Et(r);case"E":case"c":return Et(n);case"EEE":return Se(t.weekdays("short",!1),1);case"EEEE":return Se(t.weekdays("long",!1),1);case"ccc":return Se(t.weekdays("short",!0),1);case"cccc":return Se(t.weekdays("long",!0),1);case"Z":case"ZZ":return Ps(new RegExp(`([+-]${s.source})(?::(${r.source}))?`),2);case"ZZZ":return Ps(new RegExp(`([+-]${s.source})(${r.source})?`),2);case"z":return au(/[a-z_+-/]{1,256}?/i);case" ":return au(/[^\S\n\r]/);default:return f(g)}})(e)||{invalidReason:_1};return b.token=e,b}const j1={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function H1(e,t,n){const{type:r,value:u}=e;if(r==="literal"){const o=/^\s+$/.test(u);return{literal:!o,val:o?" ":u}}const i=t[r];let a=r;r==="hour"&&(t.hour12!=null?a=t.hour12?"hour12":"hour24":t.hourCycle!=null?t.hourCycle==="h11"||t.hourCycle==="h12"?a="hour12":a="hour24":a=n.hour12?"hour12":"hour24");let s=j1[a];if(typeof s=="object"&&(s=s[i]),s)return{literal:!1,val:s}}function q1(e){return[`^${e.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,e]}function U1(e,t,n){const r=e.match(t);if(r){const u={};let i=1;for(const a in n)if(nr(n,a)){const s=n[a],o=s.groups?s.groups+1:1;!s.literal&&s.token&&(u[s.token.val[0]]=s.deser(r.slice(i,i+o))),i+=o}return[r,u]}else return[r,{}]}function Y1(e){const t=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let n=null,r;return mt(e.z)||(n=Je.create(e.z)),mt(e.Z)||(n||(n=new oe(e.Z)),r=e.Z),mt(e.q)||(e.M=(e.q-1)*3+1),mt(e.h)||(e.h<12&&e.a===1?e.h+=12:e.h===12&&e.a===0&&(e.h=0)),e.G===0&&e.y&&(e.y=-e.y),mt(e.u)||(e.S=da(e.u)),[Object.keys(e).reduce((i,a)=>{const s=t(a);return s&&(i[s]=e[a]),i},{}),n,r]}let vi=null;function Z1(){return vi||(vi=tt.fromMillis(1555555555555)),vi}function W1(e,t){if(e.literal)return e;const n=re.macroTokenToFormatOpts(e.val),r=ld(n,t);return r==null||r.includes(void 0)?e:r}function cd(e,t){return Array.prototype.concat(...e.map(n=>W1(n,t)))}function dd(e,t,n){const r=cd(re.parseFormat(n),e),u=r.map(a=>V1(a,e)),i=u.find(a=>a.invalidReason);if(i)return{input:t,tokens:r,invalidReason:i.invalidReason};{const[a,s]=q1(u),o=RegExp(a,"i"),[c,d]=U1(t,o,s),[l,h,f]=d?Y1(d):[null,null,void 0];if(nr(d,"a")&&nr(d,"H"))throw new wr("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:r,regex:o,rawMatches:c,matches:d,result:l,zone:h,specificOffset:f}}}function z1(e,t,n){const{result:r,zone:u,specificOffset:i,invalidReason:a}=dd(e,t,n);return[r,u,i,a]}function ld(e,t){if(!e)return null;const r=re.create(t,e).dtFormatter(Z1()),u=r.formatToParts(),i=r.resolvedOptions();return u.map(a=>H1(a,e,i))}const fd=[0,31,59,90,120,151,181,212,243,273,304,334],hd=[0,31,60,91,121,152,182,213,244,274,305,335];function Ee(e,t){return new Me("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function md(e,t,n){const r=new Date(Date.UTC(e,t-1,n));e<100&&e>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const u=r.getUTCDay();return u===0?7:u}function pd(e,t,n){return n+(Vr(e)?hd:fd)[t-1]}function gd(e,t){const n=Vr(e)?hd:fd,r=n.findIndex(i=>i<t),u=t-n[r];return{month:r+1,day:u}}function Yi(e){const{year:t,month:n,day:r}=e,u=pd(t,n,r),i=md(t,n,r);let a=Math.floor((u-i+10)/7),s;return a<1?(s=t-1,a=Au(s)):a>Au(t)?(s=t+1,a=1):s=t,{weekYear:s,weekNumber:a,weekday:i,...Ku(e)}}function Ns(e){const{weekYear:t,weekNumber:n,weekday:r}=e,u=md(t,1,4),i=xr(t);let a=n*7+r-u-3,s;a<1?(s=t-1,a+=xr(s)):a>i?(s=t+1,a-=xr(t)):s=t;const{month:o,day:c}=gd(s,a);return{year:s,month:o,day:c,...Ku(e)}}function wi(e){const{year:t,month:n,day:r}=e,u=pd(t,n,r);return{year:t,ordinal:u,...Ku(e)}}function _s(e){const{year:t,ordinal:n}=e,{month:r,day:u}=gd(t,n);return{year:t,month:r,day:u,...Ku(e)}}function G1(e){const t=Wu(e.weekYear),n=We(e.weekNumber,1,Au(e.weekYear)),r=We(e.weekday,1,7);return t?n?r?!1:Ee("weekday",e.weekday):Ee("week",e.week):Ee("weekYear",e.weekYear)}function K1(e){const t=Wu(e.year),n=We(e.ordinal,1,xr(e.year));return t?n?!1:Ee("ordinal",e.ordinal):Ee("year",e.year)}function Dd(e){const t=Wu(e.year),n=We(e.month,1,12),r=We(e.day,1,Tu(e.year,e.month));return t?n?r?!1:Ee("day",e.day):Ee("month",e.month):Ee("year",e.year)}function bd(e){const{hour:t,minute:n,second:r,millisecond:u}=e,i=We(t,0,23)||t===24&&n===0&&r===0&&u===0,a=We(n,0,59),s=We(r,0,59),o=We(u,0,999);return i?a?s?o?!1:Ee("millisecond",u):Ee("second",r):Ee("minute",n):Ee("hour",t)}const Ei="Invalid DateTime",Rs=864e13;function su(e){return new Me("unsupported zone",`the zone "${e.name}" is not supported`)}function Fi(e){return e.weekData===null&&(e.weekData=Yi(e.c)),e.weekData}function Fn(e,t){const n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new tt({...n,...t,old:n})}function yd(e,t,n){let r=e-t*60*1e3;const u=n.offset(r);if(t===u)return[r,t];r-=(u-t)*60*1e3;const i=n.offset(r);return u===i?[r,u]:[e-Math.min(u,i)*60*1e3,Math.max(u,i)]}function ou(e,t){e+=t*60*1e3;const n=new Date(e);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function pu(e,t,n){return yd(zu(e),t,n)}function $s(e,t){const n=e.o,r=e.c.year+Math.trunc(t.years),u=e.c.month+Math.trunc(t.months)+Math.trunc(t.quarters)*3,i={...e.c,year:r,month:u,day:Math.min(e.c.day,Tu(r,u))+Math.trunc(t.days)+Math.trunc(t.weeks)*7},a=K.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),s=zu(i);let[o,c]=yd(s,n,e.zone);return a!==0&&(o+=a,c=e.zone.offset(o)),{ts:o,o:c}}function gr(e,t,n,r,u,i){const{setZone:a,zone:s}=n;if(e&&Object.keys(e).length!==0||t){const o=t||s,c=tt.fromObject(e,{...n,zone:o,specificOffset:i});return a?c:c.setZone(s)}else return tt.invalid(new Me("unparsable",`the input "${u}" can't be parsed as ${r}`))}function cu(e,t,n=!0){return e.isValid?re.create(Ot.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function Ci(e,t){const n=e.c.year>9999||e.c.year<0;let r="";return n&&e.c.year>=0&&(r+="+"),r+=Zt(e.c.year,n?6:4),t?(r+="-",r+=Zt(e.c.month),r+="-",r+=Zt(e.c.day)):(r+=Zt(e.c.month),r+=Zt(e.c.day)),r}function Is(e,t,n,r,u,i){let a=Zt(e.c.hour);return t?(a+=":",a+=Zt(e.c.minute),(e.c.millisecond!==0||e.c.second!==0||!n)&&(a+=":")):a+=Zt(e.c.minute),(e.c.millisecond!==0||e.c.second!==0||!n)&&(a+=Zt(e.c.second),(e.c.millisecond!==0||!r)&&(a+=".",a+=Zt(e.c.millisecond,3))),u&&(e.isOffsetFixed&&e.offset===0&&!i?a+="Z":e.o<0?(a+="-",a+=Zt(Math.trunc(-e.o/60)),a+=":",a+=Zt(Math.trunc(-e.o%60))):(a+="+",a+=Zt(Math.trunc(e.o/60)),a+=":",a+=Zt(Math.trunc(e.o%60)))),i&&(a+="["+e.zone.ianaName+"]"),a}const vd={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},J1={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Q1={ordinal:1,hour:0,minute:0,second:0,millisecond:0},wd=["year","month","day","hour","minute","second","millisecond"],X1=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],tp=["year","ordinal","hour","minute","second","millisecond"];function Vs(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new Fc(e);return t}function js(e,t){const n=fn(t.zone,Yt.defaultZone),r=Ot.fromObject(t),u=Yt.now();let i,a;if(mt(e.year))i=u;else{for(const c of wd)mt(e[c])&&(e[c]=vd[c]);const s=Dd(e)||bd(e);if(s)return tt.invalid(s);const o=n.offset(u);[i,a]=pu(e,o,n)}return new tt({ts:i,zone:n,loc:r,o:a})}function Hs(e,t,n){const r=mt(n.round)?!0:n.round,u=(a,s)=>(a=la(a,r||n.calendary?0:2,!0),t.loc.clone(n).relFormatter(n).format(a,s)),i=a=>n.calendary?t.hasSame(e,a)?0:t.startOf(a).diff(e.startOf(a),a).get(a):t.diff(e,a).get(a);if(n.unit)return u(i(n.unit),n.unit);for(const a of n.units){const s=i(a);if(Math.abs(s)>=1)return u(s,a)}return u(e>t?-0:0,n.units[n.units.length-1])}function qs(e){let t={},n;return e.length>0&&typeof e[e.length-1]=="object"?(t=e[e.length-1],n=Array.from(e).slice(0,e.length-1)):n=Array.from(e),[t,n]}class tt{constructor(t){const n=t.zone||Yt.defaultZone;let r=t.invalid||(Number.isNaN(t.ts)?new Me("invalid input"):null)||(n.isValid?null:su(n));this.ts=mt(t.ts)?Yt.now():t.ts;let u=null,i=null;if(!r)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(n))[u,i]=[t.old.c,t.old.o];else{const s=n.offset(this.ts);u=ou(this.ts,s),r=Number.isNaN(u.year)?new Me("invalid input"):null,u=r?null:u,i=r?null:s}this._zone=n,this.loc=t.loc||Ot.create(),this.invalid=r,this.weekData=null,this.c=u,this.o=i,this.isLuxonDateTime=!0}static now(){return new tt({})}static local(){const[t,n]=qs(arguments),[r,u,i,a,s,o,c]=n;return js({year:r,month:u,day:i,hour:a,minute:s,second:o,millisecond:c},t)}static utc(){const[t,n]=qs(arguments),[r,u,i,a,s,o,c]=n;return t.zone=oe.utcInstance,js({year:r,month:u,day:i,hour:a,minute:s,second:o,millisecond:c},t)}static fromJSDate(t,n={}){const r=Tm(t)?t.valueOf():NaN;if(Number.isNaN(r))return tt.invalid("invalid input");const u=fn(n.zone,Yt.defaultZone);return u.isValid?new tt({ts:r,zone:u,loc:Ot.fromObject(n)}):tt.invalid(su(u))}static fromMillis(t,n={}){if(Sn(t))return t<-Rs||t>Rs?tt.invalid("Timestamp out of range"):new tt({ts:t,zone:fn(n.zone,Yt.defaultZone),loc:Ot.fromObject(n)});throw new we(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,n={}){if(Sn(t))return new tt({ts:t*1e3,zone:fn(n.zone,Yt.defaultZone),loc:Ot.fromObject(n)});throw new we("fromSeconds requires a numerical input")}static fromObject(t,n={}){t=t||{};const r=fn(n.zone,Yt.defaultZone);if(!r.isValid)return tt.invalid(su(r));const u=Yt.now(),i=mt(n.specificOffset)?r.offset(u):n.specificOffset,a=Ou(t,Vs),s=!mt(a.ordinal),o=!mt(a.year),c=!mt(a.month)||!mt(a.day),d=o||c,l=a.weekYear||a.weekNumber,h=Ot.fromObject(n);if((d||s)&&l)throw new wr("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(c&&s)throw new wr("Can't mix ordinal dates with month/day");const f=l||a.weekday&&!d;let m,b,g=ou(u,i);f?(m=X1,b=J1,g=Yi(g)):s?(m=tp,b=Q1,g=wi(g)):(m=wd,b=vd);let y=!1;for(const v of m){const L=a[v];mt(L)?y?a[v]=b[v]:a[v]=g[v]:y=!0}const C=f?G1(a):s?K1(a):Dd(a),B=C||bd(a);if(B)return tt.invalid(B);const E=f?Ns(a):s?_s(a):a,[S,T]=pu(E,i,r),F=new tt({ts:S,zone:r,o:T,loc:h});return a.weekday&&d&&t.weekday!==F.weekday?tt.invalid("mismatched weekday",`you can't specify both a weekday of ${a.weekday} and a date of ${F.toISO()}`):F}static fromISO(t,n={}){const[r,u]=D1(t);return gr(r,u,n,"ISO 8601",t)}static fromRFC2822(t,n={}){const[r,u]=b1(t);return gr(r,u,n,"RFC 2822",t)}static fromHTTP(t,n={}){const[r,u]=y1(t);return gr(r,u,n,"HTTP",n)}static fromFormat(t,n,r={}){if(mt(t)||mt(n))throw new we("fromFormat requires an input string and a format");const{locale:u=null,numberingSystem:i=null}=r,a=Ot.fromOpts({locale:u,numberingSystem:i,defaultToEN:!0}),[s,o,c,d]=z1(a,t,n);return d?tt.invalid(d):gr(s,o,r,`format ${n}`,t,c)}static fromString(t,n,r={}){return tt.fromFormat(t,n,r)}static fromSQL(t,n={}){const[r,u]=x1(t);return gr(r,u,n,"SQL",t)}static invalid(t,n=null){if(!t)throw new we("need to specify a reason the DateTime is invalid");const r=t instanceof Me?t:new Me(t,n);if(Yt.throwOnInvalid)throw new im(r);return new tt({invalid:r})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,n={}){const r=ld(t,Ot.fromObject(n));return r?r.map(u=>u?u.val:null).join(""):null}static expandFormat(t,n={}){return cd(re.parseFormat(t),Ot.fromObject(n)).map(u=>u.val).join("")}get(t){return this[t]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Fi(this).weekYear:NaN}get weekNumber(){return this.isValid?Fi(this).weekNumber:NaN}get weekday(){return this.isValid?Fi(this).weekday:NaN}get ordinal(){return this.isValid?wi(this.c).ordinal:NaN}get monthShort(){return this.isValid?iu.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?iu.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?iu.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?iu.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const t=864e5,n=6e4,r=zu(this.c),u=this.zone.offset(r-t),i=this.zone.offset(r+t),a=this.zone.offset(r-u*n),s=this.zone.offset(r-i*n);if(a===s)return[this];const o=r-a*n,c=r-s*n,d=ou(o,a),l=ou(c,s);return d.hour===l.hour&&d.minute===l.minute&&d.second===l.second&&d.millisecond===l.millisecond?[Fn(this,{ts:o}),Fn(this,{ts:c})]:[this]}get isInLeapYear(){return Vr(this.year)}get daysInMonth(){return Tu(this.year,this.month)}get daysInYear(){return this.isValid?xr(this.year):NaN}get weeksInWeekYear(){return this.isValid?Au(this.weekYear):NaN}resolvedLocaleOptions(t={}){const{locale:n,numberingSystem:r,calendar:u}=re.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:u}}toUTC(t=0,n={}){return this.setZone(oe.instance(t),n)}toLocal(){return this.setZone(Yt.defaultZone)}setZone(t,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(t=fn(t,Yt.defaultZone),t.equals(this.zone))return this;if(t.isValid){let u=this.ts;if(n||r){const i=t.offset(this.ts),a=this.toObject();[u]=pu(a,i,t)}return Fn(this,{ts:u,zone:t})}else return tt.invalid(su(t))}reconfigure({locale:t,numberingSystem:n,outputCalendar:r}={}){const u=this.loc.clone({locale:t,numberingSystem:n,outputCalendar:r});return Fn(this,{loc:u})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;const n=Ou(t,Vs),r=!mt(n.weekYear)||!mt(n.weekNumber)||!mt(n.weekday),u=!mt(n.ordinal),i=!mt(n.year),a=!mt(n.month)||!mt(n.day),s=i||a,o=n.weekYear||n.weekNumber;if((s||u)&&o)throw new wr("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(a&&u)throw new wr("Can't mix ordinal dates with month/day");let c;r?c=Ns({...Yi(this.c),...n}):mt(n.ordinal)?(c={...this.toObject(),...n},mt(n.day)&&(c.day=Math.min(Tu(c.year,c.month),c.day))):c=_s({...wi(this.c),...n});const[d,l]=pu(c,this.o,this.zone);return Fn(this,{ts:d,o:l})}plus(t){if(!this.isValid)return this;const n=K.fromDurationLike(t);return Fn(this,$s(this,n))}minus(t){if(!this.isValid)return this;const n=K.fromDurationLike(t).negate();return Fn(this,$s(this,n))}startOf(t){if(!this.isValid)return this;const n={},r=K.normalizeUnit(t);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break}if(r==="weeks"&&(n.weekday=1),r==="quarters"){const u=Math.ceil(this.month/3);n.month=(u-1)*3+1}return this.set(n)}endOf(t){return this.isValid?this.plus({[t]:1}).startOf(t).minus(1):this}toFormat(t,n={}){return this.isValid?re.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,t):Ei}toLocaleString(t=Su,n={}){return this.isValid?re.create(this.loc.clone(n),t).formatDateTime(this):Ei}toLocaleParts(t={}){return this.isValid?re.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:u=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;const a=t==="extended";let s=Ci(this,a);return s+="T",s+=Is(this,a,n,r,u,i),s}toISODate({format:t="extended"}={}){return this.isValid?Ci(this,t==="extended"):null}toISOWeekDate(){return cu(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:u=!1,extendedZone:i=!1,format:a="extended"}={}){return this.isValid?(u?"T":"")+Is(this,a==="extended",n,t,r,i):null}toRFC2822(){return cu(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return cu(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Ci(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let u="HH:mm:ss.SSS";return(n||t)&&(r&&(u+=" "),n?u+="z":t&&(u+="ZZ")),cu(this,u,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():Ei}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};const n={...this.c};return t.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,n="milliseconds",r={}){if(!this.isValid||!t.isValid)return K.invalid("created by diffing an invalid DateTime");const u={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=Am(n).map(K.normalizeUnit),a=t.valueOf()>this.valueOf(),s=a?this:t,o=a?t:this,c=L1(s,o,i,u);return a?c.negate():c}diffNow(t="milliseconds",n={}){return this.diff(tt.now(),t,n)}until(t){return this.isValid?Rt.fromDateTimes(this,t):this}hasSame(t,n){if(!this.isValid)return!1;const r=t.valueOf(),u=this.setZone(t.zone,{keepLocalTime:!0});return u.startOf(n)<=r&&r<=u.endOf(n)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;const n=t.base||tt.fromObject({},{zone:this.zone}),r=t.padding?this<n?-t.padding:t.padding:0;let u=["years","months","days","hours","minutes","seconds"],i=t.unit;return Array.isArray(t.unit)&&(u=t.unit,i=void 0),Hs(n,this.plus(r),{...t,numeric:"always",units:u,unit:i})}toRelativeCalendar(t={}){return this.isValid?Hs(t.base||tt.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(tt.isDateTime))throw new we("min requires all arguments be DateTimes");return xs(t,n=>n.valueOf(),Math.min)}static max(...t){if(!t.every(tt.isDateTime))throw new we("max requires all arguments be DateTimes");return xs(t,n=>n.valueOf(),Math.max)}static fromFormatExplain(t,n,r={}){const{locale:u=null,numberingSystem:i=null}=r,a=Ot.fromOpts({locale:u,numberingSystem:i,defaultToEN:!0});return dd(a,t,n)}static fromStringExplain(t,n,r={}){return tt.fromFormatExplain(t,n,r)}static get DATE_SHORT(){return Su}static get DATE_MED(){return Cc}static get DATE_MED_WITH_WEEKDAY(){return om}static get DATE_FULL(){return kc}static get DATE_HUGE(){return xc}static get TIME_SIMPLE(){return Sc}static get TIME_WITH_SECONDS(){return Tc}static get TIME_WITH_SHORT_OFFSET(){return Ac}static get TIME_WITH_LONG_OFFSET(){return Oc}static get TIME_24_SIMPLE(){return Mc}static get TIME_24_WITH_SECONDS(){return Bc}static get TIME_24_WITH_SHORT_OFFSET(){return Lc}static get TIME_24_WITH_LONG_OFFSET(){return Pc}static get DATETIME_SHORT(){return Nc}static get DATETIME_SHORT_WITH_SECONDS(){return _c}static get DATETIME_MED(){return Rc}static get DATETIME_MED_WITH_SECONDS(){return $c}static get DATETIME_MED_WITH_WEEKDAY(){return cm}static get DATETIME_FULL(){return Ic}static get DATETIME_FULL_WITH_SECONDS(){return Vc}static get DATETIME_HUGE(){return jc}static get DATETIME_HUGE_WITH_SECONDS(){return Hc}}function Dr(e){if(tt.isDateTime(e))return e;if(e&&e.valueOf&&Sn(e.valueOf()))return tt.fromJSDate(e);if(e&&typeof e=="object")return tt.fromObject(e);throw new we(`Unknown datetime argument: ${e}, of type ${typeof e}`)}const ga={renderNullAs:"\\-",taskCompletionTracking:!1,taskCompletionUseEmojiShorthand:!1,taskCompletionText:"completion",taskCompletionDateFormat:"yyyy-MM-dd",recursiveSubTaskCompletion:!1,warnOnEmptyResult:!0,refreshEnabled:!0,refreshInterval:2500,defaultDateFormat:"MMMM dd, yyyy",defaultDateTimeFormat:"h:mm a - MMMM dd, yyyy",maxRecursiveRenderDepth:4,tableIdColumnName:"File",tableGroupColumnName:"Group",showResultCount:!0},ep={allowHtml:!0};({...ga,...ep});class Da{constructor(t){Xt(this,"value");Xt(this,"successful");this.value=t,this.successful=!0}map(t){return new Da(t(this.value))}flatMap(t){return t(this.value)}mapErr(t){return this}bimap(t,n){return this.map(t)}orElse(t){return this.value}cast(){return this}orElseThrow(t){return this.value}}class ba{constructor(t){Xt(this,"error");Xt(this,"successful");this.error=t,this.successful=!1}map(t){return this}flatMap(t){return this}mapErr(t){return new ba(t(this.error))}bimap(t,n){return this.mapErr(n)}orElse(t){return t}cast(){return this}orElseThrow(t){throw t?new Error(t(this.error)):new Error(""+this.error)}}var Mu;(function(e){function t(i){return new Da(i)}e.success=t;function n(i){return new ba(i)}e.failure=n;function r(i,a,s){return i.successful?a.successful?s(i.value,a.value):n(a.error):n(i.error)}e.flatMap2=r;function u(i,a,s){return r(i,a,(o,c)=>t(s(o,c)))}e.map2=u})(Mu||(Mu={}));var np=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof $a<"u"?$a:typeof self<"u"?self:{},Ed={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(typeof self<"u"?self:np,function(){return function(n){var r={};function u(i){if(r[i])return r[i].exports;var a=r[i]={i,l:!1,exports:{}};return n[i].call(a.exports,a,a.exports,u),a.l=!0,a.exports}return u.m=n,u.c=r,u.d=function(i,a,s){u.o(i,a)||Object.defineProperty(i,a,{configurable:!1,enumerable:!0,get:s})},u.r=function(i){Object.defineProperty(i,"__esModule",{value:!0})},u.n=function(i){var a=i&&i.__esModule?function(){return i.default}:function(){return i};return u.d(a,"a",a),a},u.o=function(i,a){return Object.prototype.hasOwnProperty.call(i,a)},u.p="",u(u.s=0)}([function(n,r,u){function i(p){if(!(this instanceof i))return new i(p);this._=p}var a=i.prototype;function s(p,w){for(var x=0;x<p;x++)w(x)}function o(p,w,x){return function(P,V){s(V.length,function(H){P(V[H],H,V)})}(function(P,V,H){w=p(w,P,V,H)},x),w}function c(p,w){return o(function(x,P,V,H){return x.concat([p(P,V,H)])},[],w)}function d(p,w){var x={v:0,buf:w};return s(p,function(){var P;x={v:x.v<<1|(P=x.buf,P[0]>>7),buf:function(V){var H=o(function(j,st,wt,Gt){return j.concat(wt===Gt.length-1?Buffer.from([st,0]).readUInt16BE(0):Gt.readUInt16BE(wt))},[],V);return Buffer.from(c(function(j){return(j<<1&65535)>>8},H))}(x.buf)}}),x}function l(){return typeof Buffer<"u"}function h(){if(!l())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function f(p){h();var w=o(function(H,j){return H+j},0,p);if(w%8!=0)throw new Error("The bits ["+p.join(", ")+"] add up to "+w+" which is not an even number of bytes; the total should be divisible by 8");var x,P=w/8,V=(x=function(H){return H>48},o(function(H,j){return H||(x(j)?j:H)},null,p));if(V)throw new Error(V+" bit range requested exceeds 48 bit (6 byte) Number max.");return new i(function(H,j){var st=P+j;return st>H.length?v(j,P.toString()+" bytes"):F(st,o(function(wt,Gt){var qt=d(Gt,wt.buf);return{coll:wt.coll.concat(qt.v),buf:qt.buf}},{coll:[],buf:H.slice(j,st)},p).coll)})}function m(p,w){return new i(function(x,P){return h(),P+w>x.length?v(P,w+" bytes for "+p):F(P+w,x.slice(P,P+w))})}function b(p,w){if(typeof(x=w)!="number"||Math.floor(x)!==x||w<0||w>6)throw new Error(p+" requires integer length in range [0, 6].");var x}function g(p){return b("uintBE",p),m("uintBE("+p+")",p).map(function(w){return w.readUIntBE(0,p)})}function y(p){return b("uintLE",p),m("uintLE("+p+")",p).map(function(w){return w.readUIntLE(0,p)})}function C(p){return b("intBE",p),m("intBE("+p+")",p).map(function(w){return w.readIntBE(0,p)})}function B(p){return b("intLE",p),m("intLE("+p+")",p).map(function(w){return w.readIntLE(0,p)})}function E(p){return p instanceof i}function S(p){return{}.toString.call(p)==="[object Array]"}function T(p){return l()&&Buffer.isBuffer(p)}function F(p,w){return{status:!0,index:p,value:w,furthest:-1,expected:[]}}function v(p,w){return S(w)||(w=[w]),{status:!1,index:-1,value:null,furthest:p,expected:w}}function L(p,w){if(!w||p.furthest>w.furthest)return p;var x=p.furthest===w.furthest?function(P,V){if(function(){if(i._supportsSet!==void 0)return i._supportsSet;var ie=typeof Set<"u";return i._supportsSet=ie,ie}()&&Array.from){for(var H=new Set(P),j=0;j<V.length;j++)H.add(V[j]);var st=Array.from(H);return st.sort(),st}for(var wt={},Gt=0;Gt<P.length;Gt++)wt[P[Gt]]=!0;for(var qt=0;qt<V.length;qt++)wt[V[qt]]=!0;var ke=[];for(var ue in wt)({}).hasOwnProperty.call(wt,ue)&&ke.push(ue);return ke.sort(),ke}(p.expected,w.expected):w.expected;return{status:p.status,index:p.index,value:p.value,furthest:w.furthest,expected:x}}var N={};function M(p,w){if(T(p))return{offset:w,line:-1,column:-1};p in N||(N[p]={});for(var x=N[p],P=0,V=0,H=0,j=w;j>=0;){if(j in x){P=x[j].line,H===0&&(H=x[j].lineStart);break}(p.charAt(j)===`
`||p.charAt(j)==="\r"&&p.charAt(j+1)!==`
`)&&(V++,H===0&&(H=j+1)),j--}var st=P+V,wt=w-H;return x[w]={line:st,lineStart:H},{offset:w,line:st+1,column:wt+1}}function W(p){if(!E(p))throw new Error("not a parser: "+p)}function $(p,w){return typeof p=="string"?p.charAt(w):p[w]}function q(p){if(typeof p!="number")throw new Error("not a number: "+p)}function z(p){if(typeof p!="function")throw new Error("not a function: "+p)}function gt(p){if(typeof p!="string")throw new Error("not a string: "+p)}var ht=2,pt=3,Ft=8,Bt=5*Ft,it=4*Ft,St="  ";function _(p,w){return new Array(w+1).join(p)}function nt(p,w,x){var P=w-p.length;return P<=0?p:_(x,P)+p}function Dt(p,w,x,P){return{from:p-w>0?p-w:0,to:p+x>P?P:p+x}}function bt(p,w){var x,P,V,H,j,st=w.index,wt=st.offset,Gt=1;if(wt===p.length)return"Got the end of the input";if(T(p)){var qt=wt-wt%Ft,ke=wt-qt,ue=Dt(qt,Bt,it+Ft,p.length),ie=c(function(It){return c(function(rn){return nt(rn.toString(16),2,"0")},It)},function(It,rn){var un=It.length,qe=[],an=0;if(un<=rn)return[It.slice()];for(var sn=0;sn<un;sn++)qe[an]||qe.push([]),qe[an].push(It[sn]),(sn+1)%rn==0&&an++;return qe}(p.slice(ue.from,ue.to).toJSON().data,Ft));H=function(It){return It.from===0&&It.to===1?{from:It.from,to:It.to}:{from:It.from/Ft,to:Math.floor(It.to/Ft)}}(ue),P=qt/Ft,x=3*ke,ke>=4&&(x+=1),Gt=2,V=c(function(It){return It.length<=4?It.join(" "):It.slice(0,4).join(" ")+"  "+It.slice(4).join(" ")},ie),(j=(8*(H.to>0?H.to-1:H.to)).toString(16).length)<2&&(j=2)}else{var He=p.split(/\r\n|[\n\r\u2028\u2029]/);x=st.column-1,P=st.line-1,H=Dt(P,ht,pt,He.length),V=He.slice(H.from,H.to),j=H.to.toString().length}var li=P-H.from;return T(p)&&(j=(8*(H.to>0?H.to-1:H.to)).toString(16).length)<2&&(j=2),o(function(It,rn,un){var qe,an=un===li,sn=an?"> ":St;return qe=T(p)?nt((8*(H.from+un)).toString(16),j,"0"):nt((H.from+un+1).toString(),j," "),[].concat(It,[sn+qe+" | "+rn],an?[St+_(" ",j)+" | "+nt("",x," ")+_("^",Gt)]:[])},[],V).join(`
`)}function At(p,w){return[`
`,"-- PARSING FAILED "+_("-",50),`

`,bt(p,w),`

`,(x=w.expected,x.length===1?`Expected:

`+x[0]:`Expected one of the following: 

`+x.join(", ")),`
`].join("");var x}function Tt(p){return p.flags!==void 0?p.flags:[p.global?"g":"",p.ignoreCase?"i":"",p.multiline?"m":"",p.unicode?"u":"",p.sticky?"y":""].join("")}function lt(){for(var p=[].slice.call(arguments),w=p.length,x=0;x<w;x+=1)W(p[x]);return i(function(P,V){for(var H,j=new Array(w),st=0;st<w;st+=1){if(!(H=L(p[st]._(P,V),H)).status)return H;j[st]=H.value,V=H.index}return L(F(V,j),H)})}function vt(){var p=[].slice.call(arguments);if(p.length===0)throw new Error("seqMap needs at least one argument");var w=p.pop();return z(w),lt.apply(null,p).map(function(x){return w.apply(null,x)})}function kt(){var p=[].slice.call(arguments),w=p.length;if(w===0)return G("zero alternates");for(var x=0;x<w;x+=1)W(p[x]);return i(function(P,V){for(var H,j=0;j<p.length;j+=1)if((H=L(p[j]._(P,V),H)).status)return H;return H})}function Qt(p,w){return yt(p,w).or(R([]))}function yt(p,w){return W(p),W(w),vt(p,w.then(p).many(),function(x,P){return[x].concat(P)})}function ct(p){gt(p);var w="'"+p+"'";return i(function(x,P){var V=P+p.length,H=x.slice(P,V);return H===p?F(V,H):v(P,w)})}function zt(p,w){(function(V){if(!(V instanceof RegExp))throw new Error("not a regexp: "+V);for(var H=Tt(V),j=0;j<H.length;j++){var st=H.charAt(j);if(st!=="i"&&st!=="m"&&st!=="u"&&st!=="s")throw new Error('unsupported regexp flag "'+st+'": '+V)}})(p),arguments.length>=2?q(w):w=0;var x=function(V){return RegExp("^(?:"+V.source+")",Tt(V))}(p),P=""+p;return i(function(V,H){var j=x.exec(V.slice(H));if(j){if(0<=w&&w<=j.length){var st=j[0],wt=j[w];return F(H+st.length,wt)}return v(H,"valid match group (0 to "+j.length+") in "+P)}return v(H,P)})}function R(p){return i(function(w,x){return F(x,p)})}function G(p){return i(function(w,x){return v(x,p)})}function et(p){if(E(p))return i(function(w,x){var P=p._(w,x);return P.index=x,P.value="",P});if(typeof p=="string")return et(ct(p));if(p instanceof RegExp)return et(zt(p));throw new Error("not a string, regexp, or parser: "+p)}function dt(p){return W(p),i(function(w,x){var P=p._(w,x),V=w.slice(x,P.index);return P.status?v(x,'not "'+V+'"'):F(x,null)})}function Ht(p){return z(p),i(function(w,x){var P=$(w,x);return x<w.length&&p(P)?F(x+1,P):v(x,"a character/byte matching "+p)})}function Ce(p,w){arguments.length<2&&(w=p,p=void 0);var x=i(function(P,V){return x._=w()._,x._(P,V)});return p?x.desc(p):x}function pe(){return G("fantasy-land/empty")}a.parse=function(p){if(typeof p!="string"&&!T(p))throw new Error(".parse must be called with a string or Buffer as its argument");var w,x=this.skip(Nt)._(p,0);return w=x.status?{status:!0,value:x.value}:{status:!1,index:M(p,x.furthest),expected:x.expected},delete N[p],w},a.tryParse=function(p){var w=this.parse(p);if(w.status)return w.value;var x=At(p,w),P=new Error(x);throw P.type="ParsimmonError",P.result=w,P},a.assert=function(p,w){return this.chain(function(x){return p(x)?R(x):G(w)})},a.or=function(p){return kt(this,p)},a.trim=function(p){return this.wrap(p,p)},a.wrap=function(p,w){return vt(p,this,w,function(x,P){return P})},a.thru=function(p){return p(this)},a.then=function(p){return W(p),lt(this,p).map(function(w){return w[1]})},a.many=function(){var p=this;return i(function(w,x){for(var P=[],V=void 0;;){if(!(V=L(p._(w,x),V)).status)return L(F(x,P),V);if(x===V.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");x=V.index,P.push(V.value)}})},a.tieWith=function(p){return gt(p),this.map(function(w){if(function(V){if(!S(V))throw new Error("not an array: "+V)}(w),w.length){gt(w[0]);for(var x=w[0],P=1;P<w.length;P++)gt(w[P]),x+=p+w[P];return x}return""})},a.tie=function(){return this.tieWith("")},a.times=function(p,w){var x=this;return arguments.length<2&&(w=p),q(p),q(w),i(function(P,V){for(var H=[],j=void 0,st=void 0,wt=0;wt<p;wt+=1){if(st=L(j=x._(P,V),st),!j.status)return st;V=j.index,H.push(j.value)}for(;wt<w&&(st=L(j=x._(P,V),st),j.status);wt+=1)V=j.index,H.push(j.value);return L(F(V,H),st)})},a.result=function(p){return this.map(function(){return p})},a.atMost=function(p){return this.times(0,p)},a.atLeast=function(p){return vt(this.times(p),this.many(),function(w,x){return w.concat(x)})},a.map=function(p){z(p);var w=this;return i(function(x,P){var V=w._(x,P);return V.status?L(F(V.index,p(V.value)),V):V})},a.contramap=function(p){z(p);var w=this;return i(function(x,P){var V=w.parse(p(x.slice(P)));return V.status?F(P+x.length,V.value):V})},a.promap=function(p,w){return z(p),z(w),this.contramap(p).map(w)},a.skip=function(p){return lt(this,p).map(function(w){return w[0]})},a.mark=function(){return vt(te,this,te,function(p,w,x){return{start:p,value:w,end:x}})},a.node=function(p){return vt(te,this,te,function(w,x,P){return{name:p,value:x,start:w,end:P}})},a.sepBy=function(p){return Qt(this,p)},a.sepBy1=function(p){return yt(this,p)},a.lookahead=function(p){return this.skip(et(p))},a.notFollowedBy=function(p){return this.skip(dt(p))},a.desc=function(p){S(p)||(p=[p]);var w=this;return i(function(x,P){var V=w._(x,P);return V.status||(V.expected=p),V})},a.fallback=function(p){return this.or(R(p))},a.ap=function(p){return vt(p,this,function(w,x){return w(x)})},a.chain=function(p){var w=this;return i(function(x,P){var V=w._(x,P);return V.status?L(p(V.value)._(x,V.index),V):V})},a.concat=a.or,a.empty=pe,a.of=R,a["fantasy-land/ap"]=a.ap,a["fantasy-land/chain"]=a.chain,a["fantasy-land/concat"]=a.concat,a["fantasy-land/empty"]=a.empty,a["fantasy-land/of"]=a.of,a["fantasy-land/map"]=a.map;var te=i(function(p,w){return F(w,M(p,w))}),ge=i(function(p,w){return w>=p.length?v(w,"any character/byte"):F(w+1,$(p,w))}),Ln=i(function(p,w){return F(p.length,p.slice(w))}),Nt=i(function(p,w){return w<p.length?v(w,"EOF"):F(w,null)}),vn=zt(/[0-9]/).desc("a digit"),fr=zt(/[0-9]*/).desc("optional digits"),ci=zt(/[a-z]/i).desc("a letter"),di=zt(/[a-z]*/i).desc("optional letters"),Pn=zt(/\s*/).desc("optional whitespace"),hr=zt(/\s+/).desc("whitespace"),Zr=ct("\r"),Wr=ct(`
`),zr=ct(`\r
`),Gr=kt(zr,Wr,Zr).desc("newline"),Nn=kt(Gr,Nt);i.all=Ln,i.alt=kt,i.any=ge,i.cr=Zr,i.createLanguage=function(p){var w={};for(var x in p)({}).hasOwnProperty.call(p,x)&&function(P){w[P]=Ce(function(){return p[P](w)})}(x);return w},i.crlf=zr,i.custom=function(p){return i(p(F,v))},i.digit=vn,i.digits=fr,i.empty=pe,i.end=Nn,i.eof=Nt,i.fail=G,i.formatError=At,i.index=te,i.isParser=E,i.lazy=Ce,i.letter=ci,i.letters=di,i.lf=Wr,i.lookahead=et,i.makeFailure=v,i.makeSuccess=F,i.newline=Gr,i.noneOf=function(p){return Ht(function(w){return p.indexOf(w)<0}).desc("none of '"+p+"'")},i.notFollowedBy=dt,i.of=R,i.oneOf=function(p){for(var w=p.split(""),x=0;x<w.length;x++)w[x]="'"+w[x]+"'";return Ht(function(P){return p.indexOf(P)>=0}).desc(w)},i.optWhitespace=Pn,i.Parser=i,i.range=function(p,w){return Ht(function(x){return p<=x&&x<=w}).desc(p+"-"+w)},i.regex=zt,i.regexp=zt,i.sepBy=Qt,i.sepBy1=yt,i.seq=lt,i.seqMap=vt,i.seqObj=function(){for(var p,w={},x=0,P=(p=arguments,Array.prototype.slice.call(p)),V=P.length,H=0;H<V;H+=1){var j=P[H];if(!E(j)){if(S(j)&&j.length===2&&typeof j[0]=="string"&&E(j[1])){var st=j[0];if(Object.prototype.hasOwnProperty.call(w,st))throw new Error("seqObj: duplicate key "+st);w[st]=!0,x++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(x===0)throw new Error("seqObj expects at least one named parser, found zero");return i(function(wt,Gt){for(var qt,ke={},ue=0;ue<V;ue+=1){var ie,He;if(S(P[ue])?(ie=P[ue][0],He=P[ue][1]):(ie=null,He=P[ue]),!(qt=L(He._(wt,Gt),qt)).status)return qt;ie&&(ke[ie]=qt.value),Gt=qt.index}return L(F(Gt,ke),qt)})},i.string=ct,i.succeed=R,i.takeWhile=function(p){return z(p),i(function(w,x){for(var P=x;P<w.length&&p($(w,P));)P++;return F(P,w.slice(x,P))})},i.test=Ht,i.whitespace=hr,i["fantasy-land/empty"]=pe,i["fantasy-land/of"]=R,i.Binary={bitSeq:f,bitSeqObj:function(p){h();var w={},x=0,P=c(function(H){if(S(H)){var j=H;if(j.length!==2)throw new Error("["+j.join(", ")+"] should be length 2, got length "+j.length);if(gt(j[0]),q(j[1]),Object.prototype.hasOwnProperty.call(w,j[0]))throw new Error("duplicate key in bitSeqObj: "+j[0]);return w[j[0]]=!0,x++,j}return q(H),[null,H]},p);if(x<1)throw new Error("bitSeqObj expects at least one named pair, got ["+p.join(", ")+"]");var V=c(function(H){return H[0]},P);return f(c(function(H){return H[1]},P)).map(function(H){return o(function(j,st){return st[0]!==null&&(j[st[0]]=st[1]),j},{},c(function(j,st){return[j,H[st]]},V))})},byte:function(p){if(h(),q(p),p>255)throw new Error("Value specified to byte constructor ("+p+"=0x"+p.toString(16)+") is larger in value than a single byte.");var w=(p>15?"0x":"0x0")+p.toString(16);return i(function(x,P){var V=$(x,P);return V===p?F(P+1,V):v(P,w)})},buffer:function(p){return m("buffer",p).map(function(w){return Buffer.from(w)})},encodedString:function(p,w){return m("string",w).map(function(x){return x.toString(p)})},uintBE:g,uint8BE:g(1),uint16BE:g(2),uint32BE:g(4),uintLE:y,uint8LE:y(1),uint16LE:y(2),uint32LE:y(4),intBE:C,int8BE:C(1),int16BE:C(2),int32BE:C(4),intLE:B,int8LE:B(1),int16LE:B(2),int32LE:B(4),floatBE:m("floatBE",4).map(function(p){return p.readFloatBE(0)}),floatLE:m("floatLE",4).map(function(p){return p.readFloatLE(0)}),doubleBE:m("doubleBE",8).map(function(p){return p.readDoubleBE(0)}),doubleLE:m("doubleLE",8).map(function(p){return p.readDoubleLE(0)})},n.exports=i}])})})(Ed);var D=Ed.exports,ya=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function Fd(e){return e==null?e:e.shiftToAll().normalize()}function Us(e){return e.includes("/")&&(e=e.substring(e.lastIndexOf("/")+1)),e.endsWith(".md")&&(e=e.substring(0,e.length-3)),e}D.alt(D.regex(new RegExp(ya(),"")),D.regex(/[0-9\p{Letter}_-]+/u).map(e=>e.toLocaleLowerCase()),D.whitespace.map(e=>"-"),D.any.map(e=>"")).many().map(e=>e.join(""));const rp=D.alt(D.regex(new RegExp(ya(),"")),D.regex(/[0-9\p{Letter}_-]+/u),D.whitespace.map(e=>" "),D.any.map(e=>" ")).many().map(e=>e.join("").split(/\s+/).join(" ").trim());function up(e){return rp.tryParse(e)}function ip(e){return e=Fd(e),e=K.fromObject(Object.fromEntries(Object.entries(e.toObject()).filter(([,t])=>t!=0))),e.toHuman()}var Pr;(function(e){function t(E,S=ga,T=!1){let F=n(E);if(!F)return S.renderNullAs;switch(F.type){case"null":return S.renderNullAs;case"string":return F.value;case"number":case"boolean":return""+F.value;case"html":return F.value.outerHTML;case"widget":return F.value.markdown();case"link":return F.value.markdown();case"function":return"<function>";case"array":let v="";return T&&(v+="["),v+=F.value.map(L=>t(L,S,!0)).join(", "),T&&(v+="]"),v;case"object":return"{ "+Object.entries(F.value).map(L=>L[0]+": "+t(L[1],S,!0)).join(", ")+" }";case"date":return F.value.second==0&&F.value.hour==0&&F.value.minute==0?F.value.toFormat(S.defaultDateFormat):F.value.toFormat(S.defaultDateTimeFormat);case"duration":return ip(F.value)}}e.toString=t;function n(E){return h(E)?{type:"null",value:E}:c(E)?{type:"number",value:E}:o(E)?{type:"string",value:E}:m(E)?{type:"boolean",value:E}:l(E)?{type:"duration",value:E}:d(E)?{type:"date",value:E}:g(E)?{type:"widget",value:E}:f(E)?{type:"array",value:E}:b(E)?{type:"link",value:E}:B(E)?{type:"function",value:E}:y(E)?{type:"html",value:E}:C(E)?{type:"object",value:E}:void 0}e.wrapValue=n;function r(E,S){if(C(E)){let T={};for(let[F,v]of Object.entries(E))T[F]=r(v,S);return T}else if(f(E)){let T=[];for(let F of E)T.push(r(F,S));return T}else return S(E)}e.mapLeaves=r;function u(E,S,T){if(E===void 0&&(E=null),S===void 0&&(S=null),E===null&&S===null)return 0;if(E===null)return-1;if(S===null)return 1;let F=n(E),v=n(S);if(F===void 0&&v===void 0)return 0;if(F===void 0)return-1;if(v===void 0)return 1;if(F.type!=v.type)return F.type.localeCompare(v.type);if(F.value===v.value)return 0;switch(F.type){case"string":return F.value.localeCompare(v.value);case"number":return F.value<v.value?-1:F.value==v.value?0:1;case"null":return 0;case"boolean":return F.value==v.value?0:F.value?1:-1;case"link":let L=F.value,N=v.value,M=T??(it=>it),W=M(L.path).localeCompare(M(N.path));if(W!=0)return W;let $=L.type.localeCompare(N.type);return $!=0?$:L.subpath&&!N.subpath?1:!L.subpath&&N.subpath?-1:!L.subpath&&!N.subpath?0:(L.subpath??"").localeCompare(N.subpath??"");case"date":return F.value<v.value?-1:F.value.equals(v.value)?0:1;case"duration":return F.value<v.value?-1:F.value.equals(v.value)?0:1;case"array":let q=F.value,z=v.value;for(let it=0;it<Math.min(q.length,z.length);it++){let St=u(q[it],z[it]);if(St!=0)return St}return q.length-z.length;case"object":let gt=F.value,ht=v.value,pt=Array.from(Object.keys(gt)),Ft=Array.from(Object.keys(ht));pt.sort(),Ft.sort();let Bt=u(pt,Ft);if(Bt!=0)return Bt;for(let it of pt){let St=u(gt[it],ht[it]);if(St!=0)return St}return 0;case"widget":case"html":case"function":return 0}}e.compareValue=u;function i(E){var S;return(S=n(E))==null?void 0:S.type}e.typeOf=i;function a(E){let S=n(E);if(!S)return!1;switch(S.type){case"number":return S.value!=0;case"string":return S.value.length>0;case"boolean":return S.value;case"link":return!!S.value.path;case"date":return S.value.toMillis()!=0;case"duration":return S.value.as("seconds")!=0;case"object":return Object.keys(S.value).length>0;case"array":return S.value.length>0;case"null":return!1;case"html":case"widget":case"function":return!0}}e.isTruthy=a;function s(E){if(E==null)return E;if(e.isArray(E))return[].concat(E.map(S=>s(S)));if(e.isObject(E)){let S={};for(let[T,F]of Object.entries(E))S[T]=s(F);return S}else return E}e.deepCopy=s;function o(E){return typeof E=="string"}e.isString=o;function c(E){return typeof E=="number"}e.isNumber=c;function d(E){return E instanceof tt}e.isDate=d;function l(E){return E instanceof K}e.isDuration=l;function h(E){return E==null}e.isNull=h;function f(E){return Array.isArray(E)}e.isArray=f;function m(E){return typeof E=="boolean"}e.isBoolean=m;function b(E){return E instanceof ne}e.isLink=b;function g(E){return E instanceof va}e.isWidget=g;function y(E){return typeof HTMLElement<"u"?E instanceof HTMLElement:!1}e.isHtml=y;function C(E){return typeof E=="object"&&!y(E)&&!g(E)&&!f(E)&&!l(E)&&!d(E)&&!b(E)&&E!==void 0&&!h(E)}e.isObject=C;function B(E){return typeof E=="function"}e.isFunction=B})(Pr||(Pr={}));var Ys;(function(e){function t(u){return Pr.isObject(u)&&Object.keys(u).length==2&&"key"in u&&"rows"in u}e.isElementGroup=t;function n(u){for(let i of u)if(!t(i))return!1;return!0}e.isGrouping=n;function r(u){if(n(u)){let i=0;for(let a of u)i+=r(a.rows);return i}else return u.length}e.count=r})(Ys||(Ys={}));class ne{constructor(t){Xt(this,"path");Xt(this,"display");Xt(this,"subpath");Xt(this,"embed");Xt(this,"type");Object.assign(this,t)}static file(t,n=!1,r){return new ne({path:t,embed:n,display:r,subpath:void 0,type:"file"})}static infer(t,n=!1,r){if(t.includes("#^")){let u=t.split("#^");return ne.block(u[0],u[1],n,r)}else if(t.includes("#")){let u=t.split("#");return ne.header(u[0],u[1],n,r)}else return ne.file(t,n,r)}static header(t,n,r,u){return new ne({path:t,embed:r,display:u,subpath:up(n),type:"header"})}static block(t,n,r,u){return new ne({path:t,embed:r,display:u,subpath:n,type:"block"})}static fromObject(t){return new ne(t)}equals(t){return t==null||t==null?!1:this.path==t.path&&this.type==t.type&&this.subpath==t.subpath}toString(){return this.markdown()}toObject(){return{path:this.path,type:this.type,subpath:this.subpath,display:this.display,embed:this.embed}}withPath(t){return new ne(Object.assign({},this,{path:t}))}withDisplay(t){return new ne(Object.assign({},this,{display:t}))}withHeader(t){return ne.header(this.path,t,this.embed,this.display)}toFile(){return ne.file(this.path,this.embed,this.display)}toEmbed(){if(this.embed)return this;{let t=new ne(this);return t.embed=!0,t}}fromEmbed(){if(this.embed){let t=new ne(this);return t.embed=!1,t}else return this}markdown(){let t=(this.embed?"!":"")+"[["+this.obsidianLink();return this.display?t+="|"+this.display:(t+="|"+Us(this.path),(this.type=="header"||this.type=="block")&&(t+=" > "+this.subpath)),t+="]]",t}obsidianLink(){var n,r;const t=this.path.replaceAll("|","\\|");return this.type=="header"?t+"#"+((n=this.subpath)==null?void 0:n.replaceAll("|","\\|")):this.type=="block"?t+"#^"+((r=this.subpath)==null?void 0:r.replaceAll("|","\\|")):t}fileName(){return Us(this.path).replace(".md","")}}class va{constructor(t){Xt(this,"$widget");this.$widget=t}}class ap extends va{constructor(n,r){super("dataview:list-pair");Xt(this,"key");Xt(this,"value");this.key=n,this.value=r}markdown(){return`${Pr.toString(this.key)}: ${Pr.toString(this.value)}`}}class sp extends va{constructor(n,r){super("dataview:external-link");Xt(this,"url");Xt(this,"display");this.url=n,this.display=r}markdown(){return`[${this.display??this.url}](${this.url})`}}var Zs;(function(e){function t(a,s){return new ap(a,s)}e.listPair=t;function n(a,s){return new sp(a,s)}e.externalLink=n;function r(a){return a.$widget==="dataview:list-pair"}e.isListPair=r;function u(a){return a.$widget==="dataview:external-link"}e.isExternalLink=u;function i(a){return r(a)||u(a)}e.isBuiltin=i})(Zs||(Zs={}));var _t;(function(e){function t(h){return{type:"variable",name:h}}e.variable=t;function n(h){return{type:"literal",value:h}}e.literal=n;function r(h,f,m){return{type:"binaryop",left:h,op:f,right:m}}e.binaryOp=r;function u(h,f){return{type:"index",object:h,index:f}}e.index=u;function i(h){let f=h.split("."),m=e.variable(f[0]);for(let b=1;b<f.length;b++)m=e.index(m,e.literal(f[b]));return m}e.indexVariable=i;function a(h,f){return{type:"lambda",arguments:h,value:f}}e.lambda=a;function s(h,f){return{type:"function",func:h,arguments:f}}e.func=s;function o(h){return{type:"list",values:h}}e.list=o;function c(h){return{type:"object",values:h}}e.object=c;function d(h){return{type:"negated",child:h}}e.negate=d;function l(h){return h=="<="||h=="<"||h==">"||h==">="||h=="!="||h=="="}e.isCompareOp=l,e.NULL=e.literal(null)})(_t||(_t={}));var Ne;(function(e){function t(d){return{type:"tag",tag:d}}e.tag=t;function n(d){return{type:"csv",path:d}}e.csv=n;function r(d){return{type:"folder",folder:d}}e.folder=r;function u(d,l){return{type:"link",file:d,direction:l?"incoming":"outgoing"}}e.link=u;function i(d,l,h){return{type:"binaryop",left:d,op:l,right:h}}e.binaryOp=i;function a(d,l){return{type:"binaryop",left:d,op:"&",right:l}}e.and=a;function s(d,l){return{type:"binaryop",left:d,op:"|",right:l}}e.or=s;function o(d){return{type:"negate",child:d}}e.negate=o;function c(){return{type:"empty"}}e.empty=c})(Ne||(Ne={}));const Ws=new RegExp(ya(),""),Zi={year:K.fromObject({years:1}),years:K.fromObject({years:1}),yr:K.fromObject({years:1}),yrs:K.fromObject({years:1}),month:K.fromObject({months:1}),months:K.fromObject({months:1}),mo:K.fromObject({months:1}),mos:K.fromObject({months:1}),week:K.fromObject({weeks:1}),weeks:K.fromObject({weeks:1}),wk:K.fromObject({weeks:1}),wks:K.fromObject({weeks:1}),w:K.fromObject({weeks:1}),day:K.fromObject({days:1}),days:K.fromObject({days:1}),d:K.fromObject({days:1}),hour:K.fromObject({hours:1}),hours:K.fromObject({hours:1}),hr:K.fromObject({hours:1}),hrs:K.fromObject({hours:1}),h:K.fromObject({hours:1}),minute:K.fromObject({minutes:1}),minutes:K.fromObject({minutes:1}),min:K.fromObject({minutes:1}),mins:K.fromObject({minutes:1}),m:K.fromObject({minutes:1}),second:K.fromObject({seconds:1}),seconds:K.fromObject({seconds:1}),sec:K.fromObject({seconds:1}),secs:K.fromObject({seconds:1}),s:K.fromObject({seconds:1})},Wi={now:()=>tt.local(),today:()=>tt.local().startOf("day"),yesterday:()=>tt.local().startOf("day").minus(K.fromObject({days:1})),tomorrow:()=>tt.local().startOf("day").plus(K.fromObject({days:1})),sow:()=>tt.local().startOf("week"),"start-of-week":()=>tt.local().startOf("week"),eow:()=>tt.local().endOf("week"),"end-of-week":()=>tt.local().endOf("week"),soy:()=>tt.local().startOf("year"),"start-of-year":()=>tt.local().startOf("year"),eoy:()=>tt.local().endOf("year"),"end-of-year":()=>tt.local().endOf("year"),som:()=>tt.local().startOf("month"),"start-of-month":()=>tt.local().startOf("month"),eom:()=>tt.local().endOf("month"),"end-of-month":()=>tt.local().endOf("month")},zi=["FROM","WHERE","LIMIT","GROUP","FLATTEN"];function op(e){let t=-1;for(;(t=e.indexOf("|",t+1))>=0;)if(!(t>0&&e[t-1]=="\\"))return[e.substring(0,t).replace(/\\\|/g,"|"),e.substring(t+1)];return[e.replace(/\\\|/g,"|"),void 0]}function cp(e){let[t,n]=op(e);return ne.infer(t,!1,n)}function br(e,t,n){return D.seqMap(e,D.seq(D.optWhitespace,t,D.optWhitespace,e).many(),(r,u)=>{if(u.length==0)return r;let i=n(r,u[0][1],u[0][3]);for(let a=1;a<u.length;a++)i=n(i,u[a][1],u[a][3]);return i})}function dp(e,...t){return D.custom((n,r)=>(u,i)=>{let a=e._(u,i);if(!a.status)return a;for(let s of t){let o=s(a.value)._(u,a.index);if(!o.status)return a;a=o}return a})}const Ae=D.createLanguage({number:e=>D.regexp(/-?[0-9]+(\.[0-9]+)?/).map(t=>Number.parseFloat(t)).desc("number"),string:e=>D.string('"').then(D.alt(e.escapeCharacter,D.noneOf('"\\')).atLeast(0).map(t=>t.join(""))).skip(D.string('"')).desc("string"),escapeCharacter:e=>D.string("\\").then(D.any).map(t=>t==='"'?'"':t==="\\"?"\\":"\\"+t),bool:e=>D.regexp(/true|false|True|False/).map(t=>t.toLowerCase()=="true").desc("boolean ('true' or 'false')"),tag:e=>D.seqMap(D.string("#"),D.alt(D.regexp(/[^\u2000-\u206F\u2E00-\u2E7F'!"#$%&()*+,.:;<=>?@^`{|}~\[\]\\\s]/).desc("text")).many(),(t,n)=>t+n.join("")).desc("tag ('#hello/stuff')"),identifier:e=>D.seqMap(D.alt(D.regexp(new RegExp("\\p{Letter}","u")),D.regexp(Ws).desc("text")),D.alt(D.regexp(/[0-9\p{Letter}_-]/u),D.regexp(Ws).desc("text")).many(),(t,n)=>t+n.join("")).desc("variable identifier"),link:e=>D.regexp(/\[\[([^\[\]]*?)\]\]/u,1).map(t=>cp(t)).desc("file link"),embedLink:e=>D.seqMap(D.string("!").atMost(1),e.link,(t,n)=>(t.length>0&&(n.embed=!0),n)).desc("file link"),binaryPlusMinus:e=>D.regexp(/\+|-/).map(t=>t).desc("'+' or '-'"),binaryMulDiv:e=>D.regexp(/\*|\/|%/).map(t=>t).desc("'*' or '/' or '%'"),binaryCompareOp:e=>D.regexp(/>=|<=|!=|>|<|=/).map(t=>t).desc("'>=' or '<=' or '!=' or '=' or '>' or '<'"),binaryBooleanOp:e=>D.regexp(/and|or|&|\|/i).map(t=>t.toLowerCase()=="and"?"&":t.toLowerCase()=="or"?"|":t).desc("'and' or 'or'"),rootDate:e=>D.seqMap(D.regexp(/\d{4}/),D.string("-"),D.regexp(/\d{2}/),(t,n,r)=>tt.fromObject({year:Number.parseInt(t),month:Number.parseInt(r)})).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),dateShorthand:e=>D.alt(...Object.keys(Wi).sort((t,n)=>n.length-t.length).map(D.string)),date:e=>dp(e.rootDate,t=>D.seqMap(D.string("-"),D.regexp(/\d{2}/),(n,r)=>t.set({day:Number.parseInt(r)})),t=>D.seqMap(D.string("T"),D.regexp(/\d{2}/),(n,r)=>t.set({hour:Number.parseInt(r)})),t=>D.seqMap(D.string(":"),D.regexp(/\d{2}/),(n,r)=>t.set({minute:Number.parseInt(r)})),t=>D.seqMap(D.string(":"),D.regexp(/\d{2}/),(n,r)=>t.set({second:Number.parseInt(r)})),t=>D.alt(D.seqMap(D.string("."),D.regexp(/\d{3}/),(n,r)=>t.set({millisecond:Number.parseInt(r)})),D.succeed(t)),t=>D.alt(D.seqMap(D.string("+").or(D.string("-")),D.regexp(/\d{1,2}(:\d{2})?/),(n,r)=>t.setZone("UTC"+n+r,{keepLocalTime:!0})),D.seqMap(D.string("Z"),()=>t.setZone("utc",{keepLocalTime:!0})),D.seqMap(D.string("["),D.regexp(/[0-9A-Za-z+-\/]+/u),D.string("]"),(n,r,u)=>t.setZone(r,{keepLocalTime:!0})))).assert(t=>t.isValid,"valid date").desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),datePlus:e=>D.alt(e.dateShorthand.map(t=>Wi[t]()),e.date).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS] or in shorthand"),durationType:e=>D.alt(...Object.keys(Zi).sort((t,n)=>n.length-t.length).map(D.string)),duration:e=>D.seqMap(e.number,D.optWhitespace,e.durationType,(t,n,r)=>Zi[r].mapUnits(u=>u*t)).sepBy1(D.string(",").trim(D.optWhitespace).or(D.optWhitespace)).map(t=>t.reduce((n,r)=>n.plus(r))).desc("duration like 4hr2min"),rawNull:e=>D.string("null"),tagSource:e=>e.tag.map(t=>Ne.tag(t)),csvSource:e=>D.seqMap(D.string("csv(").skip(D.optWhitespace),e.string,D.string(")"),(t,n,r)=>Ne.csv(n)),linkIncomingSource:e=>e.link.map(t=>Ne.link(t.path,!0)),linkOutgoingSource:e=>D.seqMap(D.string("outgoing(").skip(D.optWhitespace),e.link,D.string(")"),(t,n,r)=>Ne.link(n.path,!1)),folderSource:e=>e.string.map(t=>Ne.folder(t)),parensSource:e=>D.seqMap(D.string("("),D.optWhitespace,e.source,D.optWhitespace,D.string(")"),(t,n,r,u,i)=>r),negateSource:e=>D.seqMap(D.alt(D.string("-"),D.string("!")),e.atomSource,(t,n)=>Ne.negate(n)),atomSource:e=>D.alt(e.parensSource,e.negateSource,e.linkOutgoingSource,e.linkIncomingSource,e.folderSource,e.tagSource,e.csvSource),binaryOpSource:e=>br(e.atomSource,e.binaryBooleanOp.map(t=>t),Ne.binaryOp),source:e=>e.binaryOpSource,variableField:e=>e.identifier.chain(t=>zi.includes(t.toUpperCase())?D.fail("Variable fields cannot be a keyword ("+zi.join(" or ")+")"):D.succeed(_t.variable(t))).desc("variable"),numberField:e=>e.number.map(t=>_t.literal(t)).desc("number"),stringField:e=>e.string.map(t=>_t.literal(t)).desc("string"),boolField:e=>e.bool.map(t=>_t.literal(t)).desc("boolean"),dateField:e=>D.seqMap(D.string("date("),D.optWhitespace,e.datePlus,D.optWhitespace,D.string(")"),(t,n,r,u,i)=>_t.literal(r)).desc("date"),durationField:e=>D.seqMap(D.string("dur("),D.optWhitespace,e.duration,D.optWhitespace,D.string(")"),(t,n,r,u,i)=>_t.literal(r)).desc("duration"),nullField:e=>e.rawNull.map(t=>_t.NULL),linkField:e=>e.link.map(t=>_t.literal(t)),listField:e=>e.field.sepBy(D.string(",").trim(D.optWhitespace)).wrap(D.string("[").skip(D.optWhitespace),D.optWhitespace.then(D.string("]"))).map(t=>_t.list(t)).desc("list ('[1, 2, 3]')"),objectField:e=>D.seqMap(e.identifier.or(e.string),D.string(":").trim(D.optWhitespace),e.field,(t,n,r)=>({name:t,value:r})).sepBy(D.string(",").trim(D.optWhitespace)).wrap(D.string("{").skip(D.optWhitespace),D.optWhitespace.then(D.string("}"))).map(t=>{let n={};for(let r of t)n[r.name]=r.value;return _t.object(n)}).desc("object ('{ a: 1, b: 2 }')"),atomInlineField:e=>D.alt(e.date,e.duration.map(t=>Fd(t)),e.string,e.tag,e.embedLink,e.bool,e.number,e.rawNull),inlineFieldList:e=>e.atomInlineField.sepBy(D.string(",").trim(D.optWhitespace).lookahead(e.atomInlineField)),inlineField:e=>D.alt(D.seqMap(e.atomInlineField,D.string(",").trim(D.optWhitespace),e.inlineFieldList,(t,n,r)=>[t].concat(r)),e.atomInlineField),atomField:e=>D.alt(e.embedLink.map(t=>_t.literal(t)),e.negatedField,e.linkField,e.listField,e.objectField,e.lambdaField,e.parensField,e.boolField,e.numberField,e.stringField,e.dateField,e.durationField,e.nullField,e.variableField),indexField:e=>D.seqMap(e.atomField,D.alt(e.dotPostfix,e.indexPostfix,e.functionPostfix).many(),(t,n)=>{let r=t;for(let u of n)switch(u.type){case"dot":r=_t.index(r,_t.literal(u.field));break;case"index":r=_t.index(r,u.field);break;case"function":r=_t.func(r,u.fields);break}return r}),negatedField:e=>D.seqMap(D.string("!"),e.indexField,(t,n)=>_t.negate(n)).desc("negated field"),parensField:e=>D.seqMap(D.string("("),D.optWhitespace,e.field,D.optWhitespace,D.string(")"),(t,n,r,u,i)=>r),lambdaField:e=>D.seqMap(e.identifier.sepBy(D.string(",").trim(D.optWhitespace)).wrap(D.string("(").trim(D.optWhitespace),D.string(")").trim(D.optWhitespace)),D.string("=>").trim(D.optWhitespace),e.field,(t,n,r)=>({type:"lambda",arguments:t,value:r})),dotPostfix:e=>D.seqMap(D.string("."),e.identifier,(t,n)=>({type:"dot",field:n})),indexPostfix:e=>D.seqMap(D.string("["),D.optWhitespace,e.field,D.optWhitespace,D.string("]"),(t,n,r,u,i)=>({type:"index",field:r})),functionPostfix:e=>D.seqMap(D.string("("),D.optWhitespace,e.field.sepBy(D.string(",").trim(D.optWhitespace)),D.optWhitespace,D.string(")"),(t,n,r,u,i)=>({type:"function",fields:r})),binaryMulDivField:e=>br(e.indexField,e.binaryMulDiv,_t.binaryOp),binaryPlusMinusField:e=>br(e.binaryMulDivField,e.binaryPlusMinus,_t.binaryOp),binaryCompareField:e=>br(e.binaryPlusMinusField,e.binaryCompareOp,_t.binaryOp),binaryBooleanField:e=>br(e.binaryCompareField,e.binaryBooleanOp,_t.binaryOp),binaryOpField:e=>e.binaryBooleanField,field:e=>e.binaryOpField});function lp(e){try{return Mu.success(Ae.field.tryParse(e))}catch(t){return Mu.failure(""+t)}}var Bu;(function(e){function t(r,u){return{name:r,field:u}}e.named=t;function n(r,u){return{field:r,direction:u}}e.sortBy=n})(Bu||(Bu={}));function fp(e){return D.custom((t,n)=>(r,u)=>{let i=e._(r,u);return i.status?Object.assign({},i,{value:[i.value,r.substring(u,i.index)]}):i})}function hp(e){return e.split(/[\r\n]+/).map(t=>t.trim()).join("")}function zs(e,t){return D.eof.map(e).or(D.whitespace.then(t))}const Cd=D.createLanguage({queryType:e=>D.alt(D.regexp(/TABLE|LIST|TASK|CALENDAR/i)).map(t=>t.toLowerCase()).desc("query type ('TABLE', 'LIST', 'TASK', or 'CALENDAR')"),explicitNamedField:e=>D.seqMap(Ae.field.skip(D.whitespace),D.regexp(/AS/i).skip(D.whitespace),Ae.identifier.or(Ae.string),(t,n,r)=>Bu.named(r,t)),comment:()=>D.Parser((e,t)=>{let n=e.substring(t);if(!n.startsWith("//"))return D.makeFailure(t,"Not a comment");n=n.split(`
`)[0];let r=n.substring(2).trim();return D.makeSuccess(t+n.length,r)}),namedField:e=>D.alt(e.explicitNamedField,fp(Ae.field).map(([t,n])=>Bu.named(hp(n),t))),sortField:e=>D.seqMap(Ae.field.skip(D.optWhitespace),D.regexp(/ASCENDING|DESCENDING|ASC|DESC/i).atMost(1),(t,n)=>{let r=n.length==0?"ascending":n[0].toLowerCase();return r=="desc"&&(r="descending"),r=="asc"&&(r="ascending"),{field:t,direction:r}}),headerClause:e=>e.queryType.chain(t=>{switch(t){case"table":return zs(()=>({type:t,fields:[],showId:!0}),D.seqMap(D.regexp(/WITHOUT\s+ID/i).skip(D.optWhitespace).atMost(1),D.sepBy(e.namedField,D.string(",").trim(D.optWhitespace)),(n,r)=>({type:t,fields:r,showId:n.length==0})));case"list":return zs(()=>({type:t,format:void 0,showId:!0}),D.seqMap(D.regexp(/WITHOUT\s+ID/i).skip(D.optWhitespace).atMost(1),Ae.field.atMost(1),(n,r)=>({type:t,format:r.length==1?r[0]:void 0,showId:n.length==0})));case"task":return D.succeed({type:t});case"calendar":return D.whitespace.then(D.seqMap(e.namedField,n=>({type:t,showId:!0,field:n})));default:return D.fail(`Unrecognized query type '${t}'`)}}).desc("TABLE or LIST or TASK or CALENDAR"),fromClause:e=>D.seqMap(D.regexp(/FROM/i),D.whitespace,Ae.source,(t,n,r)=>r),whereClause:e=>D.seqMap(D.regexp(/WHERE/i),D.whitespace,Ae.field,(t,n,r)=>({type:"where",clause:r})).desc("WHERE <expression>"),sortByClause:e=>D.seqMap(D.regexp(/SORT/i),D.whitespace,e.sortField.sepBy1(D.string(",").trim(D.optWhitespace)),(t,n,r)=>({type:"sort",fields:r})).desc("SORT field [ASC/DESC]"),limitClause:e=>D.seqMap(D.regexp(/LIMIT/i),D.whitespace,Ae.field,(t,n,r)=>({type:"limit",amount:r})).desc("LIMIT <value>"),flattenClause:e=>D.seqMap(D.regexp(/FLATTEN/i).skip(D.whitespace),e.namedField,(t,n)=>({type:"flatten",field:n})).desc("FLATTEN <value> [AS <name>]"),groupByClause:e=>D.seqMap(D.regexp(/GROUP BY/i).skip(D.whitespace),e.namedField,(t,n)=>({type:"group",field:n})).desc("GROUP BY <value> [AS <name>]"),clause:e=>D.alt(e.fromClause,e.whereClause,e.sortByClause,e.limitClause,e.groupByClause,e.flattenClause),query:e=>D.seqMap(e.headerClause.trim(ki),e.fromClause.trim(ki).atMost(1),e.clause.trim(ki).many(),(t,n,r)=>({header:t,source:n.length==0?Ne.folder(""):n[0],operations:r,settings:ga}))}),ki=D.alt(D.whitespace,Cd.comment).many().map(e=>e.join("")),mp=e=>{var t;return e?(t=e.plugins.plugins.dataview)==null?void 0:t.api:window.DataviewAPI},pp=e=>e.plugins.enabledPlugins.has("dataview");tn.DATE_SHORTHANDS=Wi;tn.DURATION_TYPES=Zi;tn.EXPRESSION=Ae;tn.KEYWORDS=zi;tn.QUERY_LANGUAGE=Cd;var Gs=tn.getAPI=mp;tn.isPluginEnabled=pp;tn.parseField=lp;class qr{constructor(){}getExtension(){return Lr}calculateHiddenContentRanges(t){return Qh(t.field(Lr))}calculateVisibleContentRange(t){const n=this.calculateHiddenContentRanges(t);if(n.length===1){const[r]=n;return r.from===0?{from:r.to+1,to:t.doc.length}:{from:0,to:r.from-1}}if(n.length===2){const[r,u]=n;return{from:r.to+1,to:u.from-1}}return null}keepOnlyZoomedContentVisible(t,n,r,u={}){const{scrollIntoView:i}={scrollIntoView:!0,...u},a=Be.of({from:n,to:r});t.dispatch({effects:[a]}),i&&t.dispatch({effects:[J.EditorView.scrollIntoView(t.state.selection.main,{y:"start"})]})}hideRanges(t,n){}keepRangesVisible(t,n){t.dispatch({effects:[xu.of({ranges:n})]})}showAllContent(t){t.dispatch({effects:[er.of()]}),t.dispatch({effects:[J.EditorView.scrollIntoView(t.state.selection.main,{y:"center"})]})}}class wa{calculateRangeForZooming(t,n){const r=t.doc.lineAt(n),u=Vt.foldable(t,r.from,r.to);return!u&&/^\s*([-*+]|\d+\.)\s+/.test(r.text)?{from:r.from,to:r.to}:u?{from:r.from,to:u.to}:null}calculateRangesBasedOnType(t,n){const r=t.state,u=[];for(let a=1;a<=r.doc.lines;a++){const s=r.doc.line(a);n==="completed"&&s.text.includes("[x]")&&u.push({from:s.from,to:s.to}),n==="incompleted"&&!s.text.includes("[x]")&&u.push({from:s.from,to:s.to})}return u.map(a=>this.calculateRangeForZooming(r,a.from)||a)}calculateRangesBasedOnSearch(t,n,r){const u=[],i=Xh(t,r);for(const a of i){const s=n.find(c=>c.from===a.to),o=n.filter(c=>c.from<a.from&&c.to>=a.to);if(o.length>0){const d=o.map(l=>t.state.doc.lineAt(l.from)).map(l=>({from:l.from,to:l.to}));u.push(...d)}if(s){const c=t.state.doc.lineAt(a.from);u.push({from:c.from,to:s.to})}}return i.forEach(a=>{u.some(s=>s.from===a.from&&s.to===a.to)||u.push(a)}),u}}const $e=rt.Annotation.define(),gp=()=>rt.EditorState.transactionFilter.of(e=>{const t=e.state.selection.main,n=e.state.doc.lineAt(t.from);if(e.annotation($e)==="arrow.up.selection"||!/^\s*(-|\*|\d{1,}\.)(\s\[.\])?\s/.test(n.text))return e;const r=n.text.match(/^\s*(-|\*|\d{1,}\.)(\s\[.\])?\s/)[0],u=e.state.doc.lineAt(t.to),i=n.from+r.length,a=u.number!==n.number?u.to:n.to,s=e.newSelection,o=Vt.foldable(e.state,n.from,n.to);if(o&&(n==null?void 0:n.number)!==u.number)return[e,{selection:rt.EditorSelection.create(s.ranges.map(d=>rt.EditorSelection.range(o.to,o.from-n.length)),s.mainIndex)}];if(!s.ranges.some(({from:d,to:l})=>d<i||l>a))return e;const c=d=>Math.min(Math.max(d,i),a);return[e,{selection:rt.EditorSelection.create(s.ranges.map(d=>rt.EditorSelection.range(c(d.anchor),c(d.head))),s.mainIndex)}]});class Dp extends O.Component{constructor(t,n){super(),this.containerEl=n,this.calculateRangeForZooming=new wa,this.KeepOnlyZoomedContentVisible=new qr,this.contentMap=new Map,this.editorMap=new Map,this.indexed=!1,this.changedBySelf=!1,this.editing=!1,this.requestSave=O.debounce(async(r,u)=>{const i=this.app.vault.getFileByPath(r);i&&(this.editing=!1,await this.app.vault.modify(i,u))},3e3),this.debounceUpdateEditor=O.debounce((r,u)=>{this.updateEditor(r,u)},1e3),this.app=t}async onload(){super.onload();try{this.settings=(this.app.plugins.getPlugin("outliner-md")||this.app.plugins.getPlugin("outliner-md-beta")).settings}catch(t){console.error(t)}this.initEditor(),this.registerEvent(this.app.metadataCache.on("dataview:index-ready",()=>{this.initEditor(),this.indexed=!0})),this.registerEvent(this.app.metadataCache.on("dataview:metadata-change",(t,n,r)=>{this.indexed&&this.debounceUpdateEditor(n,r)}))}getDisplayText(){var t;return((t=this.file)==null?void 0:t.basename)||"Untitled"}getViewType(){return"outliner"}async onunload(){super.onunload()}createEditor(t,n,r,u){const i=new _a(this.app,t,{onEnter:(a,s,o)=>{var c,d,l,h,f,m,b;if(!o){const{line:g,ch:y}=a.editor.getCursor(),C=a.editor.getLine(g),B=(c=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:c.getZoomRange(a.editor),E=ze(this.app);if(B){const T=B.from.line,F=B.to.line,v=((l=(d=a.editor.getLine(T))==null?void 0:d.match(/^\s*/))==null?void 0:l[0])||"",L=a.editor.getLine(F),N=a.editor.getCursor(),M=a.editor.getLine(N.line);if(/^((-|\*|\d+\.)(\s\[.\])?)/g.test(M.trim())){const $=N.line,z=((h=a.editor.getLine($).match(/^\s*/))==null?void 0:h[0])||"";return a.editor.transaction({changes:[{text:`
${z}${z.length>v.length?"":E}- `,from:{line:N.line,ch:N.ch||0}}],selection:{from:{line:N.line+1,ch:2+`${z}${z.length>v.length?"":E}`.length},to:{line:N.line+1,ch:2+`${z}${z.length>v.length?"":E}`.length}}}),!0}const W=(f=L==null?void 0:L.match(/^\s*/))==null?void 0:f[0];if(/^((-|\*|\d+\.)(\s\[.\])?)$/g.test(L.trim())&&W===v+E)return a.editor.transaction({changes:[{text:`
${v}${E}- `,from:{line:F,ch:L.length||0}}],selection:{from:{line:F+1,ch:2+`${v}${E}`.length},to:{line:F+1,ch:2+`${v}${E}`.length}}}),!0}const S=g>0?a.editor.getLine(g-1):"";if(C.startsWith("- "))return a.editor.transaction({changes:[{text:`
- `,from:{line:g,ch:y}}],selection:{from:{line:g,ch:y+3},to:{line:g,ch:y+3}}}),!0;if(!C.trim()&&/^(-|\*|\d+\.)(\s\[.\])?/g.test(S.trim()))return a.editor.transaction({changes:[{text:"- ",from:{line:g,ch:0}}],selection:{from:{line:g,ch:2},to:{line:g,ch:2}}}),!0;if(/^\s+/g.test(C)&&!/^(-|\*|\d+\.)(\s\[.\])?/g.test(C.trim())){const T=((m=C.match(/^\s+/))==null?void 0:m[0])||"";return a.editor.transaction({changes:[{text:`
${T}`,from:{line:g,ch:y}}],selection:{from:{line:g+1,ch:T.length},to:{line:g+1,ch:T.length}}}),!0}if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(C.trim())){const T=Vt.foldable(a.editor.cm.state,a.editor.posToOffset({line:g,ch:0}),a.editor.posToOffset({line:g+1,ch:0})-1),F=ze(this.app),v=((b=C.match(/^\s+/))==null?void 0:b[0])||"";if(T){let L=!1;const N=a.editor.cm.state.doc.lineAt(T.from).number;for(let M=N+1;M<a.editor.cm.state.doc.lines;M++){const W=a.editor.cm.state.doc.line(M),$=W.text;if(/^\s+/.test($)&&!/^(-|\*|\d+\.)\s/.test($.trimStart()))L=!0;else return L?a.editor.cm.state.doc.line(M-1).to===T.to?(a.editor.transaction({changes:[{text:`${v}- 
`,from:{line:M-1,ch:0}}]}),a.editor.cm.dispatch({selection:{head:W.from,anchor:W.from}}),!0):(a.editor.cm.dispatch({changes:{insert:`${v}${F}- 
`,from:W.from}}),a.editor.cm.dispatch({selection:{head:W.from,anchor:W.from}}),!0):!1}}return!1}}if(o){const{line:g,ch:y}=a.editor.getCursor(),C=a.editor.posToOffset({line:g,ch:y}),B=a.editor.cm.state.doc.lineAt(C);if(/^\s+/g.test(B.text)&&!/^(-|\*|\d+\.)(\s\[.\])?/g.test(B.text.trimStart())){const E=B.number;for(let S=E;S>=1;S--){const F=a.editor.cm.state.doc.line(S).text;if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(F.trimStart())){const v=a.editor.cm.state.doc.line(S);return a.editor.cm.dispatch({selection:{head:v.to,anchor:v.to},annotations:$e.of("arrow.up.selection")}),!0}}}else if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(B.text.trimStart())){const E=B.number;let S=!1;for(let T=E+1;T<a.editor.cm.state.doc.lines;T++){const v=a.editor.cm.state.doc.line(T).text;if(/^\s+/.test(v)&&!/^(-|\*|\d+\.)\s/.test(v.trimStart()))S=!0;else{if(S){const L=a.editor.cm.state.doc.line(T-1);return a.editor.cm.dispatch({selection:{head:L.to,anchor:L.to},annotations:$e.of("arrow.up.selection")}),!0}return!1}}if(S){const T=a.editor.cm.state.doc.line(a.editor.cm.state.doc.lines-1);return a.editor.cm.dispatch({selection:{head:T.to,anchor:T.to}}),!0}return!1}}return!1},onDelete:a=>{var f;const{line:s,ch:o}=a.editor.getCursor(),c=a.editor.getLine(s),d=a.editor.posToOffset({line:s,ch:0}),l=a.editor.posToOffset({line:s+1,ch:0})-1,h=Vt.foldable(a.editor.cm.state,d,l);if(/^(\s*?)((-|\*|\d+\.)(\s\[.\])?)\s/g.test(c)&&/^((-|\*|\d+\.)(\s\[.\])?)$/g.test(c.trim())){if(s===0||h)return!0;const m=(f=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:f.getZoomRange(a.editor);return m&&m.from.line===s||a.editor.transaction({changes:[{text:"",from:{line:s-1,ch:a.editor.getLine(s-1).length},to:{line:s,ch:o}}]}),!0}else if(/^\s+$/g.test(c))return a.editor.transaction({changes:[{text:"",from:{line:s-1,ch:a.editor.getLine(s-1).length},to:{line:s,ch:o}}]}),!0;return!1},onIndent:(a,s,o)=>{var c,d,l,h;if(o){const f=(c=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:c.getZoomRange(a.editor);if(f){const m=f.from.line,b=f.to.line,g=(l=(d=a.editor.getLine(m))==null?void 0:d.match(/^\s*/))==null?void 0:l[0],y=a.editor.getLine(b),C=(h=y==null?void 0:y.match(/^\s*/))==null?void 0:h[0],B=ze(this.app);if(m===b||g===C||C===g+B)return!0}}return!1},getDisplayText:()=>this.getDisplayText(),getViewType:()=>this.getViewType(),onChange:(a,s)=>{if(s){if(!a.docChanged||this.contentMap.get(s)===a.state.doc.toString())return;if(this.changedBySelf){this.changedBySelf=!1;return}this.editing=!0,this.requestSave(s,a.state.doc.toString())}},type:"embed",value:r||"",path:n,foldByDefault:!0,disableTimeFormat:!this.settings.timeFormatWidget});return this.editorMap.set(n,i.editor),i.editor.getValue=()=>r||"",this.updateVisibleRange(i.editor,u),this.addChild(i)}zoomIn(){if(!this.editor||!this.range)return;const t=this.calculateRangeForZooming.calculateRangeForZooming(this.editor.cm.state,this.range.from);t&&this.editor.cm.dispatch({effects:Be.of(t)})}updateVisibleRange(t,n){var r,u;if(Array.isArray(n)){const i=n.map(a=>this.calculateRangeForZooming.calculateRangeForZooming(t.cm.state,a.from)||{from:a.from,to:a.to});this.KeepOnlyZoomedContentVisible.keepRangesVisible(t.cm,i);for(const a of i){const o=(r=t.cm.state.doc.lineAt(a.from).text.match(/^\s*/))==null?void 0:r[0];o&&t.cm.dispatch({effects:pn.of({range:{from:a.from,to:a.to},indent:o})})}}else{const i=this.calculateRangeForZooming.calculateRangeForZooming(t.cm.state,n.from);if(i){t.cm.dispatch({effects:[Be.of(i)]});const s=(u=t.cm.state.doc.lineAt(i.from).text.match(/^\s*/))==null?void 0:u[0];s&&t.cm.dispatch({effects:pn.of({range:{from:i.from,to:i.to},indent:s})})}}}updateResultEl(t){this.containerEl.toggleClass("cm-task-group-result-empty",t===0),this.containerEl.find(".cm-task-group-result").setText(t.toString()+" results")}async initEditor(){const t=Gs(this.app);if(!t)return;const n=await t.query(`TASK  
FROM ""  
WHERE contains(text, "${this.settings.taskGroupQuery}")
GROUP BY file.path`);if(!n.successful)return;const r=n.value.values;let u=0;for(const i of r){if(!i.key||this.editorMap.has(i.key))continue;const a=this.containerEl.createEl("div",{cls:"cm-task-group-header"}),s=this.containerEl.createEl("div",{cls:"cm-task-container"}),o=a.createEl("span",{cls:"cm-group-collapse-button"});O.setIcon(o,"chevron-right");const c=a.createEl("span",{cls:"cm-group-title",text:i.key});if(o.onclick=()=>{s.isShown()?s.hide():s.show(),o.toggleClass("cm-task-container-collapsed",!s.isShown())},c.onclick=()=>{this.app.workspace.openLinkText(i.key,"",!0,{active:!0})},this.settings.foldTaskGroup&&s.hide(),o.toggleClass("cm-task-container-collapsed",!s.isShown()),this.app.vault.getFileByPath(i.key)){const d=this.app.vault.getFileByPath(i.key),l=i.rows.map(h=>(u++,{from:h.position.start.offset,to:h.position.end.offset}));if(d){const h=await this.app.vault.read(d);this.createEditor(s,i.key,h,l)}}}this.updateResultEl(u)}async updateEditor(t,n){if(this.editing)return;const r=Gs(this.app);if(!r)return;const u=await r.query(`TASK  
FROM ""  
WHERE contains(text, "${this.settings.taskGroupQuery}")
GROUP BY file.path`);if(!u.successful)return;const i=u.value.values;let a=0;for(const s of i){const o=s.rows.map(c=>(a++,{from:c.position.start.offset,to:c.position.end.offset}));if(this.editorMap.has(s.key)){const c=this.app.vault.getFileByPath(s.key);if(!c)continue;const d=this.editorMap.get(c==null?void 0:c.path);if(d){const l=await this.app.vault.read(c);if(this.contentMap.get(c.path)!==l){this.contentMap.has(c.path)&&this.contentMap.set(c.path,l),this.changedBySelf=!0;const f=d.cm.state.doc.lineAt(d.cm.state.doc.length-1);d.replaceRange(l,{line:0,ch:0},{line:f.number,ch:f.length-1}),this.contentMap.set(c.path,l),this.updateVisibleRange(d,o)}}}else{const c=this.app.vault.getFileByPath(s.key),d=this.containerEl.createEl("div",{cls:"cm-task-group-header",text:s.key});if(d.onclick=()=>{this.app.workspace.openLinkText(s.key,"",!0,{active:!0})},c){const l=await this.app.vault.read(c);this.createEditor(this.containerEl,s.key,l,o),this.contentMap.set(c.path,l)}}}this.updateResultEl(a)}}const bp=rt.StateField.define({create(){return J.Decoration.none},update(e,t){const n=t.state.field(O.editorInfoField),r=n.app,u=new rt.RangeSetBuilder,i=t.state.doc.line(t.state.doc.lines).to;return u.add(i,i,J.Decoration.widget({widget:new yp(r,n,i,i,t.state),side:3,block:!0})),u.finish()},provide:e=>J.EditorView.decorations.from(e)});class yp extends J.WidgetType{constructor(t,n,r,u,i){super(),this.app=t,this.editor=n,this.from=r,this.to=u,this.state=i}eq(t){return!0}toDOM(){const t=createEl("div",{cls:"cm-task-group-container"});t.createEl("span",{cls:"cm-task-group-margin"});const n=t.createEl("span",{cls:"cm-task-group"}),r=n.createEl("span",{cls:"cm-task-group-title"});return r.createEl("span",{cls:"cm-task-group-title-inner",text:"⛏ Task Group"}),r.createEl("span",{cls:"cm-task-group-result"}),new Dp(this.app,n).onload(),t}ignoreEvent(t){return t.type==="mousedown"||t.type==="mouseup"||t.type==="click"?(t.preventDefault(),!0):!1}}const vp=J.Decoration.mark({attributes:{class:"cm-blank-bullet-line"}}),wp=rt.StateField.define({create(){return J.Decoration.none},update(e,t){const n=new rt.RangeSetBuilder;for(let u=1;u<=t.state.doc.lines;u++){const i=t.state.doc.line(u);/^(-|\*|(\d{1,}\.))(\s(\[.\]))?$/g.test(i.text.trim())&&n.add(i.from,i.to,vp)}return n.finish()},provide:e=>J.EditorView.decorations.from(e)});function Qe(){}const Ju=e=>e;function Ep(e,t){for(const n in t)e[n]=t[n];return e}function kd(e){return e()}function Ks(){return Object.create(null)}function Jt(e){e.forEach(kd)}function en(e){return typeof e=="function"}function Qu(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Fp(e){return Object.keys(e).length===0}function xd(e,t,n,r){if(e){const u=Sd(e,t,n,r);return e[0](u)}}function Sd(e,t,n,r){return e[1]&&r?Ep(n.ctx.slice(),e[1](r(t))):n.ctx}function Td(e,t,n,r){if(e[2]&&r){const u=e[2](r(n));if(t.dirty===void 0)return u;if(typeof u=="object"){const i=[],a=Math.max(t.dirty.length,u.length);for(let s=0;s<a;s+=1)i[s]=t.dirty[s]|u[s];return i}return t.dirty|u}return t.dirty}function Ad(e,t,n,r,u,i){if(u){const a=Sd(t,n,r,i);e.p(a,u)}}function Od(e){if(e.ctx.length>32){const t=[],n=e.ctx.length/32;for(let r=0;r<n;r++)t[r]=-1;return t}return-1}function Ie(e){return e??""}function Md(e){return e&&en(e.destroy)?e.destroy:Qe}const Bd=typeof window<"u";let Ea=Bd?()=>window.performance.now():()=>Date.now(),Fa=Bd?e=>requestAnimationFrame(e):Qe;const Gn=new Set;function Ld(e){Gn.forEach(t=>{t.c(e)||(Gn.delete(t),t.f())}),Gn.size!==0&&Fa(Ld)}function Ca(e){let t;return Gn.size===0&&Fa(Ld),{promise:new Promise(n=>{Gn.add(t={c:e,f:n})}),abort(){Gn.delete(t)}}}function U(e,t){e.appendChild(t)}function Pd(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function Cp(e){const t=Q("style");return t.textContent="/* empty */",kp(Pd(e),t),t.sheet}function kp(e,t){return U(e.head||e,t),t.sheet}function at(e,t,n){e.insertBefore(t,n||null)}function ut(e){e.parentNode&&e.parentNode.removeChild(e)}function Nr(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function Q(e){return document.createElement(e)}function mn(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function jt(e){return document.createTextNode(e)}function Ct(){return jt(" ")}function Xu(){return jt("")}function ft(e,t,n,r){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)}function Nd(e){return function(t){return t.preventDefault(),e.call(this,t)}}function A(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}function xp(e){return Array.from(e.childNodes)}function Kt(e,t){t=""+t,e.data!==t&&(e.data=t)}function Js(e,t,n,r){e.style.setProperty(t,n,"")}function ot(e,t,n){e.classList.toggle(t,!!n)}function _d(e,t,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(e,{detail:t,bubbles:n,cancelable:r})}const Lu=new Map;let Pu=0;function Sp(e){let t=5381,n=e.length;for(;n--;)t=(t<<5)-t^e.charCodeAt(n);return t>>>0}function Tp(e,t){const n={stylesheet:Cp(t),rules:{}};return Lu.set(e,n),n}function Nu(e,t,n,r,u,i,a,s=0){const o=16.666/r;let c=`{
`;for(let g=0;g<=1;g+=o){const y=t+(n-t)*i(g);c+=g*100+`%{${a(y,1-y)}}
`}const d=c+`100% {${a(n,1-n)}}
}`,l=`__svelte_${Sp(d)}_${s}`,h=Pd(e),{stylesheet:f,rules:m}=Lu.get(h)||Tp(h,e);m[l]||(m[l]=!0,f.insertRule(`@keyframes ${l} ${d}`,f.cssRules.length));const b=e.style.animation||"";return e.style.animation=`${b?`${b}, `:""}${l} ${r}ms linear ${u}ms 1 both`,Pu+=1,l}function _u(e,t){const n=(e.style.animation||"").split(", "),r=n.filter(t?i=>i.indexOf(t)<0:i=>i.indexOf("__svelte")===-1),u=n.length-r.length;u&&(e.style.animation=r.join(", "),Pu-=u,Pu||Ap())}function Ap(){Fa(()=>{Pu||(Lu.forEach(e=>{const{ownerNode:t}=e.stylesheet;t&&ut(t)}),Lu.clear())})}let _r;function Tr(e){_r=e}function Rd(){if(!_r)throw new Error("Function called outside component initialization");return _r}function Op(e){Rd().$$.on_mount.push(e)}function ti(){const e=Rd();return(t,n,{cancelable:r=!1}={})=>{const u=e.$$.callbacks[t];if(u){const i=_d(t,n,{cancelable:r});return u.slice().forEach(a=>{a.call(e,i)}),!i.defaultPrevented}return!0}}function xi(e,t){const n=e.$$.callbacks[t.type];n&&n.slice().forEach(r=>r.call(this,t))}const Un=[],Kn=[];let Jn=[];const Qs=[],$d=Promise.resolve();let Gi=!1;function Id(){Gi||(Gi=!0,$d.then(jd))}function Vd(){return Id(),$d}function me(e){Jn.push(e)}const Si=new Set;let jn=0;function jd(){if(jn!==0)return;const e=_r;do{try{for(;jn<Un.length;){const t=Un[jn];jn++,Tr(t),Mp(t.$$)}}catch(t){throw Un.length=0,jn=0,t}for(Tr(null),Un.length=0,jn=0;Kn.length;)Kn.pop()();for(let t=0;t<Jn.length;t+=1){const n=Jn[t];Si.has(n)||(Si.add(n),n())}Jn.length=0}while(Un.length);for(;Qs.length;)Qs.pop()();Gi=!1,Si.clear(),Tr(e)}function Mp(e){if(e.fragment!==null){e.update(),Jt(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(me)}}function Bp(e){const t=[],n=[];Jn.forEach(r=>e.indexOf(r)===-1?t.push(r):n.push(r)),n.forEach(r=>r()),Jn=t}let yr;function ka(){return yr||(yr=Promise.resolve(),yr.then(()=>{yr=null})),yr}function Tn(e,t,n){e.dispatchEvent(_d(`${t?"intro":"outro"}${n}`))}const gu=new Set;let Ve;function Ge(){Ve={r:0,c:[],p:Ve}}function Ke(){Ve.r||Jt(Ve.c),Ve=Ve.p}function xt(e,t){e&&e.i&&(gu.delete(e),e.i(t))}function $t(e,t,n,r){if(e&&e.o){if(gu.has(e))return;gu.add(e),Ve.c.push(()=>{gu.delete(e),r&&(n&&e.d(1),r())}),e.o(t)}else r&&r()}const xa={duration:0};function ei(e,t,n){const r={direction:"in"};let u=t(e,n,r),i=!1,a,s,o=0;function c(){a&&_u(e,a)}function d(){const{delay:h=0,duration:f=300,easing:m=Ju,tick:b=Qe,css:g}=u||xa;g&&(a=Nu(e,0,1,f,h,m,g,o++)),b(0,1);const y=Ea()+h,C=y+f;s&&s.abort(),i=!0,me(()=>Tn(e,!0,"start")),s=Ca(B=>{if(i){if(B>=C)return b(1,0),Tn(e,!0,"end"),c(),i=!1;if(B>=y){const E=m((B-y)/f);b(E,1-E)}}return i})}let l=!1;return{start(){l||(l=!0,_u(e),en(u)?(u=u(r),ka().then(d)):d())},invalidate(){l=!1},end(){i&&(c(),i=!1)}}}function Sa(e,t,n){const r={direction:"out"};let u=t(e,n,r),i=!0,a;const s=Ve;s.r+=1;let o;function c(){const{delay:d=0,duration:l=300,easing:h=Ju,tick:f=Qe,css:m}=u||xa;m&&(a=Nu(e,1,0,l,d,h,m));const b=Ea()+d,g=b+l;me(()=>Tn(e,!1,"start")),"inert"in e&&(o=e.inert,e.inert=!0),Ca(y=>{if(i){if(y>=g)return f(0,1),Tn(e,!1,"end"),--s.r||Jt(s.c),!1;if(y>=b){const C=h((y-b)/l);f(1-C,C)}}return i})}return en(u)?ka().then(()=>{u=u(r),c()}):c(),{end(d){d&&"inert"in e&&(e.inert=o),d&&u.tick&&u.tick(1,0),i&&(a&&_u(e,a),i=!1)}}}function rr(e,t,n,r){let i=t(e,n,{direction:"both"}),a=r?0:1,s=null,o=null,c=null,d;function l(){c&&_u(e,c)}function h(m,b){const g=m.b-a;return b*=Math.abs(g),{a,b:m.b,d:g,duration:b,start:m.start,end:m.start+b,group:m.group}}function f(m){const{delay:b=0,duration:g=300,easing:y=Ju,tick:C=Qe,css:B}=i||xa,E={start:Ea()+b,b:m};m||(E.group=Ve,Ve.r+=1),"inert"in e&&(m?d!==void 0&&(e.inert=d):(d=e.inert,e.inert=!0)),s||o?o=E:(B&&(l(),c=Nu(e,a,m,g,b,y,B)),m&&C(0,1),s=h(E,g),me(()=>Tn(e,m,"start")),Ca(S=>{if(o&&S>o.start&&(s=h(o,g),o=null,Tn(e,s.b,"start"),B&&(l(),c=Nu(e,a,s.b,s.duration,0,y,i.css))),s){if(S>=s.end)C(a=s.b,1-a),Tn(e,s.b,"end"),o||(s.b?l():--s.group.r||Jt(s.group.c)),s=null;else if(S>=s.start){const T=S-s.start;a=s.a+s.d*y(T/s.duration),C(a,1-a)}}return!!(s||o)}))}return{run(m){en(i)?ka().then(()=>{i=i({direction:m?"in":"out"}),f(m)}):f(m)},end(){l(),s=o=null}}}function Wt(e){return(e==null?void 0:e.length)!==void 0?e:Array.from(e)}function Ta(e,t){e.d(1),t.delete(e.key)}function Hd(e,t){$t(e,1,1,()=>{t.delete(e.key)})}function Ur(e,t,n,r,u,i,a,s,o,c,d,l){let h=e.length,f=i.length,m=h;const b={};for(;m--;)b[e[m].key]=m;const g=[],y=new Map,C=new Map,B=[];for(m=f;m--;){const F=l(u,i,m),v=n(F);let L=a.get(v);L?B.push(()=>L.p(F,t)):(L=c(v,F),L.c()),y.set(v,g[m]=L),v in b&&C.set(v,Math.abs(m-b[v]))}const E=new Set,S=new Set;function T(F){xt(F,1),F.m(s,d),a.set(F.key,F),d=F.first,f--}for(;h&&f;){const F=g[f-1],v=e[h-1],L=F.key,N=v.key;F===v?(d=F.first,h--,f--):y.has(N)?!a.has(L)||E.has(L)?T(F):S.has(N)?h--:C.get(L)>C.get(N)?(S.add(L),T(F)):(E.add(N),h--):(o(v,a),h--)}for(;h--;){const F=e[h];y.has(F.key)||o(F,a)}for(;f;)T(g[f-1]);return Jt(B),g}function Aa(e){e&&e.c()}function ni(e,t,n){const{fragment:r,after_update:u}=e.$$;r&&r.m(t,n),me(()=>{const i=e.$$.on_mount.map(kd).filter(en);e.$$.on_destroy?e.$$.on_destroy.push(...i):Jt(i),e.$$.on_mount=[]}),u.forEach(me)}function ri(e,t){const n=e.$$;n.fragment!==null&&(Bp(n.after_update),Jt(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function Lp(e,t){e.$$.dirty[0]===-1&&(Un.push(e),Id(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function ui(e,t,n,r,u,i,a=null,s=[-1]){const o=_r;Tr(e);const c=e.$$={fragment:null,ctx:[],props:i,update:Qe,not_equal:u,bound:Ks(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(o?o.$$.context:[])),callbacks:Ks(),dirty:s,skip_bound:!1,root:t.target||o.$$.root};a&&a(c.root);let d=!1;if(c.ctx=n?n(e,t.props||{},(l,h,...f)=>{const m=f.length?f[0]:h;return c.ctx&&u(c.ctx[l],c.ctx[l]=m)&&(!c.skip_bound&&c.bound[l]&&c.bound[l](m),d&&Lp(e,l)),h}):[],c.update(),d=!0,Jt(c.before_update),c.fragment=r?r(c.ctx):!1,t.target){if(t.hydrate){const l=xp(t.target);c.fragment&&c.fragment.l(l),l.forEach(ut)}else c.fragment&&c.fragment.c();t.intro&&xt(e.$$.fragment),ni(e,t.target,t.anchor),jd()}Tr(o)}class ii{constructor(){Xt(this,"$$");Xt(this,"$$set")}$destroy(){ri(this,1),this.$destroy=Qe}$on(t,n){if(!en(n))return Qe;const r=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return r.push(n),()=>{const u=r.indexOf(n);u!==-1&&r.splice(u,1)}}$set(t){this.$$set&&!Fp(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const Pp="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(Pp);const Np={days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["am","pm"],suffix:["st","nd","rd","th"],todayBtn:"Today",clearBtn:"Clear",okBtn:"Ok",cancelBtn:"Cancel",timeView:"Show time view",backToDate:"Back to calendar view"},_p={theme:"sdt-calendar-colors",format:"yyyy-mm-dd",formatType:"standard",displayFormat:null,displayFormatType:null,minuteIncrement:1,weekStart:1,inputClasses:"",todayBtnClasses:"sdt-action-btn sdt-today-btn",clearBtnClasses:"sdt-action-btn sdt-clear-btn",hourOnly:!1,todayBtn:!0,clearBtn:!0,clearToggle:!0,autocommit:!0,i18n:Np};function Rp(e){const t=e-1;return t*t*t+1}function On(e,{delay:t=0,duration:n=400,easing:r=Ju}={}){const u=+getComputedStyle(e).opacity;return{delay:t,duration:n,easing:r,css:i=>`opacity: ${i*u}`}}const _e=0,xn=1,be=2,$p=3;function Fr(e,t,n,r){var g,y;if(e instanceof Date)return e;const u=r==="php"?{date:"Y-m-d",datetime:"Y-m-d H:i",datetime_s:"Y-m-d H:i:s"}:{date:"yyyy-mm-dd",datetime:"yyyy-mm-dd hh:ii",datetime_s:"yyyy-mm-dd hh:ii:ss"};let i,a;/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(e)?i=dn.parseFormat(u.date,r):/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}$/.test(e)?i=dn.parseFormat(u.datetime,r):/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}\:\d{1,2}[Z]{0,1}$/.test(e)?i=dn.parseFormat(u.datetime_s,r):(/^([01]*\d|2[0-3])([0-5]\d)(?:[ ]([ap][m]|[AP][M]))?$/.test(e)&&(a=(g=e.match(/^([01]*\d|2[0-3])([0-5]\d)(?:[ ]([ap][m]|[AP][M]))?$/))==null?void 0:g.slice(1).filter(C=>C)),i=dn.parseFormat(t,r));const s=a||e&&e.toString().match(dn.nonpunctuation)||[];e=new Date,e.setHours(0,0,0,0);const o={},{setters_order:c,setters_map:d}=dn.setters(r);let l,h;if(s.length!==i.parts.length&&i.parts.includes("S")){const C=(y=s[i.parts.indexOf("S")-1].match(/(\d+)([a-zA-Z]+)/))==null?void 0:y.slice(1,3);s.splice(i.parts.indexOf("S")-1,1,...C)}if(s.length===i.parts.length){for(var f=0,m=i.parts.length;f<m;f++){if(l=parseInt(s[f],10),h=i.parts[f],isNaN(l))if(r==="standard")switch(h){case"MM":l=n.months.indexOf(s[f])+1;break;case"M":l=n.monthsShort.indexOf(s[f])+1;break;case"p":case"P":l=n.meridiem.indexOf(s[f].toLowerCase());break}else switch(h){case"D":l=n.daysShort.indexOf(s[f])+1;break;case"l":l=n.days.indexOf(s[f])+1;break;case"F":l=n.months.indexOf(s[f])+1;break;case"M":l=n.monthsShort.indexOf(s[f])+1;break;case"a":case"A":l=n.meridiem.indexOf(s[f].toLowerCase());break}o[h]=l}for(var f=0,b;f<c.length;f++)b=c[f],b in o&&!isNaN(o[b])&&d[`${b}`]&&d[`${b}`](e,o[b])}return e}function Yn(e,t,n,r){var d;if(e===null)return"";const u=e.getDate();let i;if(r==="standard")i={t:e.getTime(),yy:e.getFullYear().toString().substring(2),yyyy:e.getFullYear(),m:e.getMonth()+1,M:n.monthsShort[e.getMonth()],MM:n.months[e.getMonth()],d:u,D:n.daysShort[e.getDay()],DD:n.days[e.getDay()],S:u%10&&u%10<=n.suffix.length?n.suffix[u%10-1]:n.suffix[n.suffix.length-1],p:n.meridiem.length===2?n.meridiem[e.getHours()<12?0:1]:"",h:e.getHours(),ii:(e.getMinutes()<10?"0":"")+e.getMinutes(),ss:(e.getUTCSeconds()<10?"0":"")+e.getUTCSeconds()},n.meridiem.length===2?i.H=i.h%12===0?12:i.h%12:i.H=i.h,i.HH=(i.H<10?"0":"")+i.H,i.P=i.p.toUpperCase(),i.hh=(i.h<10?"0":"")+i.h,i.i=i.ii,i.s=i.ss,i.dd=(i.d<10?"0":"")+i.d,i.mm=(i.m<10?"0":"")+i.m;else if(r==="php")i={y:e.getFullYear().toString().substring(2),Y:e.getFullYear(),F:n.months[e.getMonth()],M:n.monthsShort[e.getMonth()],n:e.getMonth()+1,t:qd(e.getFullYear(),e.getMonth()),j:e.getDate(),l:n.days[e.getDay()],D:n.daysShort[e.getDay()],w:e.getDay(),N:e.getDay()===0?7:e.getDay(),S:u%10&&u%10<=n.suffix.length?n.suffix[u%10-1]:n.suffix[n.suffix.length-1],a:n.meridiem.length===2?n.meridiem[e.getHours()<12?0:1]:"",g:e.getHours()%12===0?12:e.getHours()%12,G:e.getHours(),i:e.getMinutes(),s:e.getSeconds(),U:Math.floor(e.getTime()/1e3)},i.m=(i.n<10?"0":"")+i.n,i.d=(i.j<10?"0":"")+i.j,i.A=i.a.toString().toUpperCase(),i.h=(i.g<10?"0":"")+i.g,i.H=(i.G<10?"0":"")+i.G,i.i=(i.i<10?"0":"")+i.i,i.s=(i.s<10?"0":"")+i.s;else throw new Error("Invalid format type.");let a=[];const s=dn.parseFormat(t,r);for(var o=0,c=((d=s.parts)==null?void 0:d.length)||0;o<c;o++)s.separators.length&&a.push(s.separators.shift()),a.push(i[s.parts[o]]);return s.separators.length&&a.push(s.separators.shift()),a.join("")}function qd(e,t){return[31,e%4===0&&e%100!==0||e%400===0?29:28,31,30,31,30,31,31,30,31,30,31][t]}const dn={validParts:function(e){if(e==="standard")return/t|hh?|HH?|p|P|z|ii?|ss?|dd?|DD?|S|mm?|MM?|yy(?:yy)?/g;if(e==="php")return/[dDjlNwzFmMnStyYaABgGhHisU]/g;throw new Error("Invalid format type.")},nonpunctuation:/[^ -\/:-@\[-`{-~\t\n\rTZ]+/g,parseFormat:function(e,t){var n=e.replace(this.validParts(t),"\0").split("\0"),r=e.match(this.validParts(t))||[];return(!n||!n.length||!r||r.length===0)&&console.warn("invalid date format",n,r),{separators:n,parts:r}},setters:function(e){let t,n={};return e==="standard"?(t=["yyyy","yy","m","mm","M","MM","d","dd","D","DD","hh","h","HH","H","ii","i","ss","s","S","p","P","t"],n={hh:(r,u)=>r.setHours(u),h:(r,u)=>r.setHours(u),HH:(r,u)=>r.setHours(u===12?0:u),H:(r,u)=>r.setHours(u===12?0:u),i:(r,u)=>r.setMinutes(u),s:(r,u)=>r.setSeconds(u),yyyy:(r,u)=>r.setFullYear(u),yy:(r,u)=>r.setFullYear((u<50?2e3:1900)+u),m:(r,u)=>{for(u-=1;u<0;)u+=12;for(u%=12,r.setMonth(u);r.getMonth()!==u;){if(isNaN(r.getMonth()))return r;r.setDate(r.getDate()-1)}return r},d:(r,u)=>r.setDate(u),p:(r,u)=>r.setHours(u===1&&r.getHours()<12?r.getHours()+12:r.getHours()),t:(r,u)=>r.setTime(u),mm:()=>{},M:()=>{},MM:()=>{},ii:()=>{},ss:()=>{},dd:()=>{},D:()=>{},DD:()=>{},P:()=>{}},n.mm=n.M=n.MM=n.m,n.ii=n.i,n.ss=n.s,n.dd=n.D=n.DD=n.d,n.P=n.p):(t=["Y","yy","m","M","F","n","d","D","j","l","N","S","H","G","h","g","i","s","p","P","U"],n={H:(r,u)=>r.setHours(u),G:(r,u)=>r.setHours(u),h:(r,u)=>r.setHours(u===12?0:u),g:(r,u)=>r.setHours(u===12?0:u),i:(r,u)=>r.setMinutes(u),s:(r,u)=>r.setSeconds(u),Y:(r,u)=>r.setFullYear(u),yy:(r,u)=>r.setFullYear((u<50?2e3:1900)+u),m:(r,u)=>{for(u-=1;u<0;)u+=12;for(u%=12,r.setMonth(u);r.getMonth()!==u;){if(isNaN(r.getMonth()))return r;r.setDate(r.getDate()-1)}return r},n:(r,u)=>r.setMonth(u-1),d:(r,u)=>r.setDate(u),a:(r,u)=>r.setHours(u===1?r.getHours()+12:r.getHours()),U:(r,u)=>r.setTime(u*1e3)},n.F=n.M=n.m,n.D=n.j=n.l=n.N=n.d,n.A=n.a),{setters_order:t,setters_map:n}}};function Ip(e,t,n,r,u){if(n===_e){let E=10,S=20;const T=-1,F=[];let v=[],L=e.getFullYear()-e.getFullYear()%10;L-=L%20?12:10,L%10&&(E=12,S=22);for(let M=0;M<32;M++)v.push(L+M),v.length===4&&(F.push(v),v=[]);let N=[];return t[0]||(t[0]=new Date),t[0].getFullYear()>=L&&N.push(t[0].getFullYear()%L),{years:F,todayMark:T,nextFrom:S,prevTo:E,selectionMark:N}}if(n===xn){let E=[],S=[],T=12,F=24;const v=e.toISOString().split("T")[0].substring(0,8),L=new Date(v+"01 00:00:00"),N=L.getFullYear()-1;L.setFullYear(N);let M=0;for(let $=0;$<3;$++){for(let q=0;q<12;q++)L.setMonth(q),S.push(r.monthsShort[q%12]),S.length===4&&(E.push(S),S=[]);L.setFullYear(L.getFullYear()+1)}let W=[];return t[0]||(t[0]=new Date),t[0].getFullYear()-N>=0&&t[0].getFullYear()-N<=2&&W.push(t[0].getMonth()+(t[0].getFullYear()-N||0)*12),{months:E,todayMark:M,nextFrom:F,prevTo:T,selectionMark:W}}let i=e||new Date,a=i.getFullYear(),s=i.getMonth();i.getDate(),i.getHours();let o=new Date,c=new Date(a,s-1,28,0,0,0,0),d=qd(c.getFullYear(),c.getMonth());c.setDate(d),c.setDate(d-(c.getDay()-u+7)%7);let l=new Date(c);l.setDate(l.getDate()+42);let h=l.valueOf(),f=[],m=[],b=-1,g=[],y=0,C=42,B=0;for(;c.valueOf()<h;)B++,m.push(new Date(c)),c.getFullYear()<a||c.getFullYear()===a&&c.getMonth()<s?y=B:C===42&&(c.getFullYear()>a||c.getFullYear()===a&&c.getMonth()>s)&&(C=B-1),c.setDate(c.getDate()+1),c.getFullYear()===o.getFullYear()&&c.getMonth()===o.getMonth()&&c.getDate()===o.getDate()&&(b=B),g.length!==t.length&&t.map(E=>{c.getFullYear()===E.getFullYear()&&c.getMonth()===E.getMonth()&&c.getDate()===E.getDate()&&g.push(B)}),m.length===7&&(f.push(m),m=[]);return{grid:f,days:f,todayMark:b,prevTo:y,nextFrom:C,selectionMark:g}}function Vp(e,{duration:t=400,start:n=0,end:r=1,opacity:u=0}){const i=getComputedStyle(e),a=+i.opacity,s=i.transform==="none"?"":i.transform,o=1-n,c=a*(1-u);return r=r||1,{delay:0,duration:t,easing:Rp,css:(d,l)=>`
        transform: ${s} scale(${r!==1?n+r*l:1-o*l});
        opacity: ${a-c*l};
      `}}function Xs(e,t,n){const r=e.slice();return r[48]=t[n],r[50]=n,r}function to(e,t,n){const r=e.slice();r[51]=t[n],r[55]=n;const u=r[50]*7+r[55];r[52]=u;const i=r[51].getTime();return r[53]=i,r}function eo(e,t,n){const r=e.slice();return r[56]=t[n],r}function no(e,t,n){const r=e.slice();return r[48]=t[n],r[50]=n,r}function ro(e,t,n){const r=e.slice();r[60]=t[n],r[55]=n;const u=r[50]*4+r[55];return r[52]=u,r}function uo(e,t,n){const r=e.slice();return r[48]=t[n],r[50]=n,r}function io(e,t,n){const r=e.slice();r[63]=t[n],r[55]=n;const u=r[50]*4+r[55];return r[52]=u,r}function ao(e){let t,n,r,u,i,a;return{c(){t=Q("button"),n=mn("svg"),r=mn("path"),A(r,"fill-rule","evenodd"),A(r,"d","M1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0zM8 0a8 8 0 100 16A8 8 0 008 0zm.5 4.75a.75.75 0 00-1.5 0v3.5a.75.75 0 00.471.696l2.5 1a.75.75 0 00.557-1.392L8.5 7.742V4.75z"),A(n,"class","sdt-svg svelte-hexbpx"),A(n,"xmlns","http://www.w3.org/2000/svg"),A(n,"viewBox","0 0 16 16"),A(n,"width","16"),A(n,"height","16"),A(t,"type","button"),A(t,"class","std-btn std-btn-header icon-btn sdt-time-icon svelte-hexbpx"),A(t,"title",u=e[3].timeView)},m(s,o){at(s,t,o),U(t,n),U(n,r),i||(a=ft(t,"click",e[28]),i=!0)},p(s,o){o[0]&8&&u!==(u=s[3].timeView)&&A(t,"title",u)},d(s){s&&ut(t),i=!1,a()}}}function so(e){let t,n,r,u,i,a,s,o,c=Wt(e[15].years),d=[];for(let l=0;l<c.length;l+=1)d[l]=co(uo(e,c,l));return{c(){t=Q("table"),n=Q("tbody");for(let l=0;l<d.length;l+=1)d[l].c();A(n,"class","sdt-tbody-lg svelte-hexbpx"),A(n,"style",r=`transform: translateY(-${e[11]}px); color: red`),ot(n,"animate-transition",!!e[12]),A(t,"class","sdt-table svelte-hexbpx"),Js(t,"max-height","221px"),Js(t,"height","221px")},m(l,h){at(l,t,h),U(t,n);for(let f=0;f<d.length;f+=1)d[f]&&d[f].m(n,null);a=!0,s||(o=[ft(n,"outroend",e[27]),ft(n,"transitionend",e[40])],s=!0)},p(l,h){if(e=l,h[0]&73433216){c=Wt(e[15].years);let f;for(f=0;f<c.length;f+=1){const m=uo(e,c,f);d[f]?d[f].p(m,h):(d[f]=co(m),d[f].c(),d[f].m(n,null))}for(;f<d.length;f+=1)d[f].d(1);d.length=c.length}(!a||h[0]&2048&&r!==(r=`transform: translateY(-${e[11]}px); color: red`))&&A(n,"style",r),(!a||h[0]&4096)&&ot(n,"animate-transition",!!e[12])},i(l){a||(l&&me(()=>{a&&(i&&i.end(1),u=ei(n,e[18],{duration:ur,start:e[20],opacity:1}),u.start())}),a=!0)},o(l){u&&u.invalidate(),l&&(i=Sa(n,e[18],{duration:ur,end:e[19],start:1})),a=!1},d(l){l&&ut(t),Nr(d,l),l&&i&&i.end(),s=!1,Jt(o)}}}function oo(e,t){let n,r,u=t[63]+"",i,a,s,o;function c(){return t[39](t[63])}return{key:e,first:null,c(){n=Q("td"),r=Q("button"),i=jt(u),A(r,"type","button"),A(r,"class","std-btn svelte-hexbpx"),r.disabled=a=t[22](new Date(t[63],t[7].getMonth(),t[7].getDate())),ot(r,"not-current",!t[21](t[52])),A(n,"class","sdt-cal-td svelte-hexbpx"),ot(n,"is-selected",t[15].selectionMark.includes(t[52])),this.first=n},m(d,l){at(d,n,l),U(n,r),U(r,i),s||(o=ft(r,"click",c),s=!0)},p(d,l){t=d,l[0]&32768&&u!==(u=t[63]+"")&&Kt(i,u),l[0]&32896&&a!==(a=t[22](new Date(t[63],t[7].getMonth(),t[7].getDate())))&&(r.disabled=a),l[0]&2129920&&ot(r,"not-current",!t[21](t[52])),l[0]&32768&&ot(n,"is-selected",t[15].selectionMark.includes(t[52]))},d(d){d&&ut(n),s=!1,o()}}}function co(e){let t,n=[],r=new Map,u,i=Wt(e[48]);const a=s=>s[55];for(let s=0;s<i.length;s+=1){let o=io(e,i,s),c=a(o);r.set(c,n[s]=oo(c,o))}return{c(){t=Q("tr");for(let s=0;s<n.length;s+=1)n[s].c();u=Ct(),A(t,"class","sdt-cal-td svelte-hexbpx")},m(s,o){at(s,t,o);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);U(t,u)},p(s,o){o[0]&73433216&&(i=Wt(s[48]),n=Ur(n,o,a,1,s,i,r,t,Ta,oo,u,io))},d(s){s&&ut(t);for(let o=0;o<n.length;o+=1)n[o].d()}}}function lo(e){let t,n,r,u,i,a,s,o,c=Wt(e[15].months),d=[];for(let l=0;l<c.length;l+=1)d[l]=ho(no(e,c,l));return{c(){t=Q("table"),n=Q("tbody");for(let l=0;l<d.length;l+=1)d[l].c();A(n,"class","sdt-tbody-lg svelte-hexbpx"),A(n,"style",r=`transform: translateY(-${e[11]}px)`),ot(n,"animate-transition",!!e[12]),A(t,"class","sdt-table svelte-hexbpx")},m(l,h){at(l,t,h),U(t,n);for(let f=0;f<d.length;f+=1)d[f]&&d[f].m(n,null);a=!0,s||(o=[ft(n,"outroend",e[27]),ft(n,"transitionend",e[42])],s=!0)},p(l,h){if(e=l,h[0]&73433224){c=Wt(e[15].months);let f;for(f=0;f<c.length;f+=1){const m=no(e,c,f);d[f]?d[f].p(m,h):(d[f]=ho(m),d[f].c(),d[f].m(n,null))}for(;f<d.length;f+=1)d[f].d(1);d.length=c.length}(!a||h[0]&2048&&r!==(r=`transform: translateY(-${e[11]}px)`))&&A(n,"style",r),(!a||h[0]&4096)&&ot(n,"animate-transition",!!e[12])},i(l){a||(l&&me(()=>{a&&(i&&i.end(1),u=ei(n,e[18],{duration:ur,start:e[20],opacity:1}),u.start())}),a=!0)},o(l){u&&u.invalidate(),l&&(i=Sa(n,e[18],{duration:ur,end:e[19],start:1})),a=!1},d(l){l&&ut(t),Nr(d,l),l&&i&&i.end(),s=!1,Jt(o)}}}function fo(e,t){let n,r,u=t[60]+"",i,a,s,o;function c(){return t[41](t[60])}return{key:e,first:null,c(){n=Q("td"),r=Q("button"),i=jt(u),A(r,"class","std-btn svelte-hexbpx"),A(r,"type","button"),r.disabled=a=t[22](new Date(t[7].getFullYear(),t[3].monthsShort.indexOf(t[60]),t[7].getDate())),ot(r,"not-current",!t[21](t[52])),A(n,"class","sdt-cal-td svelte-hexbpx"),ot(n,"is-selected",t[52]===t[15].selectionMark[0]),this.first=n},m(d,l){at(d,n,l),U(n,r),U(r,i),s||(o=ft(r,"click",c),s=!0)},p(d,l){t=d,l[0]&32768&&u!==(u=t[60]+"")&&Kt(i,u),l[0]&32904&&a!==(a=t[22](new Date(t[7].getFullYear(),t[3].monthsShort.indexOf(t[60]),t[7].getDate())))&&(r.disabled=a),l[0]&2129920&&ot(r,"not-current",!t[21](t[52])),l[0]&32768&&ot(n,"is-selected",t[52]===t[15].selectionMark[0])},d(d){d&&ut(n),s=!1,o()}}}function ho(e){let t,n=[],r=new Map,u,i=Wt(e[48]);const a=s=>s[55];for(let s=0;s<i.length;s+=1){let o=ro(e,i,s),c=a(o);r.set(c,n[s]=fo(c,o))}return{c(){t=Q("tr");for(let s=0;s<n.length;s+=1)n[s].c();u=Ct(),A(t,"class","sdt-cal-td svelte-hexbpx")},m(s,o){at(s,t,o);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);U(t,u)},p(s,o){o[0]&73433224&&(i=Wt(s[48]),n=Ur(n,o,a,1,s,i,r,t,Ta,fo,u,ro))},d(s){s&&ut(t);for(let o=0;o<n.length;o+=1)n[o].d()}}}function mo(e){let t,n,r,u,i,a,s,o,c,d=Wt(e[17]),l=[];for(let m=0;m<d.length;m+=1)l[m]=po(eo(e,d,m));let h=Wt(e[15].days),f=[];for(let m=0;m<h.length;m+=1)f[m]=Do(Xs(e,h,m));return{c(){t=Q("table"),n=Q("tbody"),r=Q("tr");for(let m=0;m<l.length;m+=1)l[m].c();u=Ct();for(let m=0;m<f.length;m+=1)f[m].c();A(r,"class","sdt-cal-td svelte-hexbpx"),A(t,"class","sdt-table sdt-table-height svelte-hexbpx")},m(m,b){at(m,t,b),U(t,n),U(n,r);for(let g=0;g<l.length;g+=1)l[g]&&l[g].m(r,null);U(n,u);for(let g=0;g<f.length;g+=1)f[g]&&f[g].m(n,null);s=!0,o||(c=ft(n,"outroend",e[27]),o=!0)},p(m,b){if(e=m,b[0]&131072){d=Wt(e[17]);let g;for(g=0;g<d.length;g+=1){const y=eo(e,d,g);l[g]?l[g].p(y,b):(l[g]=po(y),l[g].c(),l[g].m(r,null))}for(;g<l.length;g+=1)l[g].d(1);l.length=d.length}if(b[0]&1684127845|b[1]&1){h=Wt(e[15].days);let g;for(g=0;g<h.length;g+=1){const y=Xs(e,h,g);f[g]?f[g].p(y,b):(f[g]=Do(y),f[g].c(),f[g].m(n,null))}for(;g<f.length;g+=1)f[g].d(1);f.length=h.length}},i(m){s||(m&&me(()=>{s&&(a&&a.end(1),i=ei(n,e[18],{duration:ur,start:.5,opacity:1}),i.start())}),s=!0)},o(m){i&&i.invalidate(),m&&(a=Sa(n,e[18],{duration:ur,start:Math.abs(e[9])})),s=!1},d(m){m&&ut(t),Nr(l,m),Nr(f,m),m&&a&&a.end(),o=!1,c()}}}function po(e){let t,n=e[56]+"",r;return{c(){t=Q("th"),r=jt(n),A(t,"class","sdt-cal-th svelte-hexbpx")},m(u,i){at(u,t,i),U(t,r)},p(u,i){i[0]&131072&&n!==(n=u[56]+"")&&Kt(r,n)},d(u){u&&ut(t)}}}function go(e,t){let n,r,u=t[51].getDate()+"",i,a,s,o;function c(){return t[43](t[51])}return{key:e,first:null,c(){n=Q("td"),r=Q("button"),i=jt(u),A(r,"type","button"),A(r,"class","std-btn sdt-btn-day svelte-hexbpx"),r.disabled=a=(t[16]||t[2]||t[6])&&t[22](t[51]),ot(r,"not-current",!t[21](t[50]*7+t[55])),A(n,"class","sdt-cal-td svelte-hexbpx"),ot(n,"sdt-today",t[52]===t[15].todayMark),ot(n,"in-range",t[30](t[53])),ot(n,"is-selected",t[14].includes(t[53])),ot(n,"in-range-hover",t[5]&&t[31](t[53],t[0])),this.first=n},m(d,l){at(d,n,l),U(n,r),U(r,i),s||(o=[ft(r,"click",c),ft(n,"mouseover",function(){en(t[29](t[51]))&&t[29](t[51]).apply(this,arguments)}),ft(n,"mouseout",t[29]())],s=!0)},p(d,l){t=d,l[0]&32768&&u!==(u=t[51].getDate()+"")&&Kt(i,u),l[0]&98372&&a!==(a=(t[16]||t[2]||t[6])&&t[22](t[51]))&&(r.disabled=a),l[0]&2129920&&ot(r,"not-current",!t[21](t[50]*7+t[55])),l[0]&32768&&ot(n,"sdt-today",t[52]===t[15].todayMark),l[0]&1073774592&&ot(n,"in-range",t[30](t[53])),l[0]&49152&&ot(n,"is-selected",t[14].includes(t[53])),l[0]&32801|l[1]&1&&ot(n,"in-range-hover",t[5]&&t[31](t[53],t[0]))},d(d){d&&ut(n),s=!1,Jt(o)}}}function Do(e){let t,n=[],r=new Map,u,i=Wt(e[48]);const a=s=>s[55];for(let s=0;s<i.length;s+=1){let o=to(e,i,s),c=a(o);r.set(c,n[s]=go(c,o))}return{c(){t=Q("tr");for(let s=0;s<n.length;s+=1)n[s].c();u=Ct()},m(s,o){at(s,t,o);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);U(t,u)},p(s,o){o[0]&1684127845|o[1]&1&&(i=Wt(s[48]),n=Ur(n,o,a,1,s,i,r,t,Ta,go,u,to))},d(s){s&&ut(t);for(let o=0;o<n.length;o+=1)n[o].d()}}}function jp(e){let t,n,r,u,i,a,s,o,c,d,l,h,f,m,b,g,y,C,B,E,S,T,F=e[4]&&e[1].length&&ao(e),v=e[8]===_e&&so(e),L=e[8]===xn&&lo(e),N=e[8]===be&&mo(e);return{c(){t=Q("div"),n=Q("button"),r=jt(e[13]),u=Ct(),F&&F.c(),i=Ct(),a=Q("button"),s=mn("svg"),o=mn("path"),d=Ct(),l=Q("button"),h=mn("svg"),f=mn("path"),b=Ct(),g=Q("div"),g.innerHTML="",y=Ct(),C=Q("div"),v&&v.c(),B=Ct(),L&&L.c(),E=Ct(),N&&N.c(),A(n,"type","button"),A(n,"class","std-btn std-btn-header sdt-toggle-btn svelte-hexbpx"),A(o,"d","M4.427 9.573l3.396-3.396a.25.25 0 01.354 0l3.396 3.396a.25.25 0 01-.177.427H4.604a.25.25 0 01-.177-.427z"),A(s,"class","sdt-svg svelte-hexbpx"),A(s,"xmlns","http://www.w3.org/2000/svg"),A(s,"viewBox","0 0 16 16"),A(s,"width","24"),A(s,"height","24"),A(a,"type","button"),A(a,"class","std-btn std-btn-header icon-btn svelte-hexbpx"),a.disabled=c=e[23](e[7],-1,e[8]),A(f,"d","M4.427 7.427l3.396 3.396a.25.25 0 00.354 0l3.396-3.396A.25.25 0 0011.396 7H4.604a.25.25 0 00-.177.427z"),A(h,"class","sdt-svg svelte-hexbpx"),A(h,"xmlns","http://www.w3.org/2000/svg"),A(h,"viewBox","0 0 16 16"),A(h,"width","24"),A(h,"height","24"),A(l,"type","button"),A(l,"class","std-btn std-btn-header icon-btn svelte-hexbpx"),l.disabled=m=e[23](e[7],1,e[8]),A(g,"class","sdt-nav-btns svelte-hexbpx"),A(t,"class","sdt-thead-nav svelte-hexbpx"),A(C,"class","sdt-calendar svelte-hexbpx"),ot(C,"is-grid",e[10])},m(M,W){at(M,t,W),U(t,n),U(n,r),U(t,u),F&&F.m(t,null),U(t,i),U(t,a),U(a,s),U(s,o),U(t,d),U(t,l),U(l,h),U(h,f),U(t,b),U(t,g),at(M,y,W),at(M,C,W),v&&v.m(C,null),U(C,B),L&&L.m(C,null),U(C,E),N&&N.m(C,null),S||(T=[ft(n,"click",e[25]),ft(a,"click",e[37]),ft(l,"click",e[38])],S=!0)},p(M,W){W[0]&8192&&Kt(r,M[13]),M[4]&&M[1].length?F?F.p(M,W):(F=ao(M),F.c(),F.m(t,i)):F&&(F.d(1),F=null),W[0]&384&&c!==(c=M[23](M[7],-1,M[8]))&&(a.disabled=c),W[0]&384&&m!==(m=M[23](M[7],1,M[8]))&&(l.disabled=m),M[8]===_e?v?(v.p(M,W),W[0]&256&&xt(v,1)):(v=so(M),v.c(),xt(v,1),v.m(C,B)):v&&(Ge(),$t(v,1,1,()=>{v=null}),Ke()),M[8]===xn?L?(L.p(M,W),W[0]&256&&xt(L,1)):(L=lo(M),L.c(),xt(L,1),L.m(C,E)):L&&(Ge(),$t(L,1,1,()=>{L=null}),Ke()),M[8]===be?N?(N.p(M,W),W[0]&256&&xt(N,1)):(N=mo(M),N.c(),xt(N,1),N.m(C,null)):N&&(Ge(),$t(N,1,1,()=>{N=null}),Ke()),W[0]&1024&&ot(C,"is-grid",M[10])},i(M){xt(v),xt(L),xt(N)},o(M){$t(v),$t(L),$t(N)},d(M){M&&(ut(t),ut(y),ut(C)),F&&F.d(),v&&v.d(),L&&L.d(),N&&N.d(),S=!1,Jt(T)}}}let ur=400;const Pe=222,vr=148;function Hp(e,t,n){var zt;let r,u,i,a,s,o,c,d,{wid:l}=t,{dates:h}=t,{startDate:f=null}=t,{endDate:m=null}=t,{weekStart:b=1}=t,{initialView:g=be}=t,{i18n:y}=t,{enableTimeToggle:C=!1}=t,{isRange:B=!1}=t,{hoverDate:E=null}=t,{additionalDisableFn:S}=t;function T(R,G){if(N!==be){n(8,N=be),n(9,M=1),n(7,v=new Date(F||new Date));return}if(!F){it(new Date,{keyboard:!0});return}let et=new Date(F);switch(R){case"PageDown":G=!0;case"ArrowDown":G?et.setMonth(F.getMonth()+1):et.setDate(F.getDate()+7),G&&et.getMonth()===F.getMonth()&&et.setDate(0),it(et,{keyboard:!0});break;case"PageUp":G=!0;case"ArrowUp":G?et.setMonth(F.getMonth()-1):et.setDate(F.getDate()-7),G&&et.getMonth()===F.getMonth()&&et.setDate(0),it(et,{keyboard:!0});break;case"ArrowLeft":G?et.setFullYear(F.getFullYear()-1):et.setDate(F.getDate()-1),it(et,{keyboard:!0});break;case"ArrowRight":G?et.setFullYear(F.getFullYear()+1):et.setDate(F.getDate()+1),it(et,{keyboard:!0});break}}let F=h[l]||null,v=l===1?(()=>{if(h.length===2&&h[1]&&(h[0].getMonth()!=h[1].getMonth()||h[0].getFullYear()!=h[1].getFullYear()))return h[1];const R=new Date(h[0]||new Date);return R.setMonth(R.getMonth()+1),R})():new Date(((zt=h[0])==null?void 0:zt.valueOf())||new Date);const L=ti();let N=g,M=-2,W=!1,$=Pe,q=null;function z(R){return o.prevTo<=R&&R<o.nextFrom}function gt(R){switch(N){case be:if(r&&r>R||m&&m<=R||S&&S(R))return!0;break;case xn:const G=R.getFullYear(),et=r==null?void 0:r.getFullYear(),dt=m==null?void 0:m.getFullYear();if(r&&(et===G&&r.getMonth()>R.getMonth()||et>G)||m&&(dt===G&&m.getMonth()<R.getMonth()||dt<G))return!0;break;case _e:if(r&&r.getFullYear()>R.getFullYear()||m&&m.getFullYear()<R.getFullYear())return!0;break}return!1}function ht(R,G,et){const dt=G===1,Ht=dt?m:r;return Ht?et===_e?dt?R.getFullYear()+G*10>Ht.getFullYear():R.getFullYear()+G*10<Ht.getFullYear():et===xn?dt?R.getFullYear()+G>Ht.getFullYear():R.getFullYear()+G<Ht.getFullYear():dt?R.getFullYear()*100+R.getMonth()+G>Ht.getFullYear()*100+Ht.getMonth():R.getFullYear()*100+R.getMonth()+G<Ht.getFullYear()*100+Ht.getMonth():!1}function pt(R){const G=N===_e?120:N===xn?12:1,et=new Date(v);et.getDate()>28&&et.setDate(et.getDate()-3),et.setMonth(v.getMonth()+R*G),n(7,v=et),n(12,q=null),n(11,$=N===_e?v.getFullYear()%20>=10?Pe:vr:Pe)}function Ft(R){if(N===be)return pt(R);if(n(12,q=()=>{pt(R)}),N===_e){n(11,$=$===vr?R===-1?$-Pe:Pe+vr:R===-1?$-Pe:$+vr);return}n(11,$=R===-1?$-Pe:$+Pe)}function Bt(){if(n(9,M=-1),n(10,W=!0),N&&n(8,N--,N),N===_e){const R=Math.floor(v.getFullYear()/10)*10%20===0;n(11,$=R?vr:Pe)}}function it(R,{keyboard:G}={}){switch(n(9,M=1),n(10,W=!0),N){case 0:v.setFullYear(R),n(7,v);break;case 1:v.setMonth(y.monthsShort.indexOf(R)),n(7,v);break;case 2:if(gt(R)||S&&S(R))return;const et=new Date(R.getFullYear(),R.getMonth(),R.getDate());F&&(et.setMinutes(F.getMinutes()),et.setHours(F.getHours())),F=et,G&&(v.getFullYear()!==et.getFullYear()||v.getFullYear()===et.getFullYear()&&v.getMonth()!==et.getMonth())&&(v.setFullYear(et.getFullYear()),v.getDate()>28&&v.setDate(28),v.setMonth(et.getMonth()),n(7,v)),L("date",{value:F,update:"date",isKeyboard:G});break}N<be&&n(8,N++,N),n(11,$=Pe)}function St(){n(10,W=!1)}function _(){L("switch","time")}function nt(R,G){switch(R){case _e:const et=o.years[Math.floor(o.prevTo/4)][o.prevTo%4],dt=o.years[Math.floor(o.nextFrom/4)][o.nextFrom%4];return`${et} - ${dt}`;case xn:return G.getFullYear();case be:return y.months[G.getMonth()]+" "+G.getFullYear()}}function Dt(R=null){return function(G){n(0,E=(R==null?void 0:R.getTime())||null),L("internal_hoverUpdate",E)}}function bt(R){return s.length===2?R>=s[0]&&R<s[1]:!1}function At(R,G){return G&&s.length===1&&(R<=G&&s[0]<=R||R>=G&&s[0]>=R)}const Tt=()=>Ft(-1),lt=()=>Ft(1),vt=R=>{it(R)},kt=()=>q&&q(),Qt=R=>{it(R)},yt=()=>q&&q(),ct=R=>{it(R)};return e.$$set=R=>{"wid"in R&&n(32,l=R.wid),"dates"in R&&n(1,h=R.dates),"startDate"in R&&n(33,f=R.startDate),"endDate"in R&&n(2,m=R.endDate),"weekStart"in R&&n(34,b=R.weekStart),"initialView"in R&&n(35,g=R.initialView),"i18n"in R&&n(3,y=R.i18n),"enableTimeToggle"in R&&n(4,C=R.enableTimeToggle),"isRange"in R&&n(5,B=R.isRange),"hoverDate"in R&&n(0,E=R.hoverDate),"additionalDisableFn"in R&&n(6,S=R.additionalDisableFn)},e.$$.update=()=>{e.$$.dirty[1]&4&&n(16,r=f?new Date(f.getFullYear(),f.getMonth(),f.getDate(),0,0,0,0):null),e.$$.dirty[0]&512&&n(20,u=M<1?1.5:.5),e.$$.dirty[0]&512&&n(19,i=M<1?1:1.5),e.$$.dirty[0]&512&&n(18,a=M===-2?On:M!==null?Vp:()=>({})),e.$$.dirty[0]&2&&n(14,s=h.map(R=>(R=new Date(R),R.setHours(0,0),R.getTime()))),e.$$.dirty[0]&394|e.$$.dirty[1]&8&&n(15,o=Ip(v,h,N,y,b)),e.$$.dirty[0]&8|e.$$.dirty[1]&8&&n(17,c=y.daysMin.concat(...y.daysMin.slice(1)).slice(b,7+b)),e.$$.dirty[0]&2&&h.length===0&&(F=null),e.$$.dirty[0]&392&&n(13,d=y&&nt(N,v))},[E,h,m,y,C,B,S,v,N,M,W,$,q,d,s,o,r,c,a,i,u,z,gt,ht,Ft,Bt,it,St,_,Dt,bt,At,l,f,b,g,T,Tt,lt,vt,kt,Qt,yt,ct]}class qp extends ii{constructor(t){super(),ui(this,t,Hp,jp,Qu,{wid:32,dates:1,startDate:33,endDate:2,weekStart:34,initialView:35,i18n:3,enableTimeToggle:4,isRange:5,hoverDate:0,additionalDisableFn:6,handleGridNav:36},null,[-1,-1,-1])}get handleGridNav(){return this.$$.ctx[36]}}function bo(e,t,n){const r=e.slice();return r[37]=t[n],r[39]=n,r}function yo(e,t,n){const r=e.slice();return r[37]=t[n],r[39]=n,r}function vo(e){let t,n,r,u,i,a;return{c(){t=Q("button"),n=mn("svg"),r=mn("path"),A(r,"fill-rule","evenodd"),A(r,"d","M6.75 0a.75.75 0 01.75.75V3h9V.75a.75.75 0 011.5 0V3h2.75c.966 0 1.75.784 1.75 1.75v16a1.75 1.75 0 01-1.75 1.75H3.25a1.75 1.75 0 01-1.75-1.75v-16C1.5 3.784 2.284 3 3.25 3H6V.75A.75.75 0 016.75 0zm-3.5 4.5a.25.25 0 00-.25.25V8h18V4.75a.25.25 0 00-.25-.25H3.25zM21 9.5H3v11.25c0 .138.112.25.25.25h17.5a.25.25 0 00.25-.25V9.5z"),A(n,"class","sdt-svg svelte-bn8ebp"),A(n,"xmlns","http://www.w3.org/2000/svg"),A(n,"viewBox","0 0 24 24"),A(n,"width","20"),A(n,"height","20"),A(t,"type","button"),A(t,"class","sdt-time-btn sdt-back-btn svelte-bn8ebp"),A(t,"title",u=e[5].backToDate)},m(s,o){at(s,t,o),U(t,n),U(n,r),i||(a=ft(t,"click",e[22]),i=!0)},p(s,o){o[0]&32&&u!==(u=s[5].backToDate)&&A(t,"title",u)},d(s){s&&ut(t),i=!1,a()}}}function Up(e){let t,n=e[16](e[9],e[3])+"",r,u,i;function a(c,d){return c[3]?Wp:Zp}let s=a(e),o=s(e);return{c(){t=Q("span"),r=jt(n),u=Ct(),o.c(),i=Xu(),A(t,"class","sdt-time-figure svelte-bn8ebp")},m(c,d){at(c,t,d),U(t,r),at(c,u,d),o.m(c,d),at(c,i,d)},p(c,d){d[0]&520&&n!==(n=c[16](c[9],c[3])+"")&&Kt(r,n),s===(s=a(c))&&o?o.p(c,d):(o.d(1),o=s(c),o&&(o.c(),o.m(i.parentNode,i)))},d(c){c&&(ut(t),ut(u),ut(i)),o.d(c)}}}function Yp(e){let t,n=e[16](e[9],e[3])+"",r,u,i,a,s,o=e[16](e[8],!1)+"",c,d,l;return{c(){t=Q("button"),r=jt(n),u=Ct(),i=Q("span"),i.textContent=":",a=Ct(),s=Q("button"),c=jt(o),A(t,"type","button"),A(t,"class","sdt-time-btn sdt-time-figure svelte-bn8ebp"),ot(t,"is-active",!e[6]),A(s,"type","button"),A(s,"class","sdt-time-btn sdt-time-figure svelte-bn8ebp"),ot(s,"is-active",e[6])},m(h,f){at(h,t,f),U(t,r),at(h,u,f),at(h,i,f),at(h,a,f),at(h,s,f),U(s,c),d||(l=[ft(t,"click",e[28]),ft(s,"click",e[29])],d=!0)},p(h,f){f[0]&520&&n!==(n=h[16](h[9],h[3])+"")&&Kt(r,n),f[0]&64&&ot(t,"is-active",!h[6]),f[0]&256&&o!==(o=h[16](h[8],!1)+"")&&Kt(c,o),f[0]&64&&ot(s,"is-active",h[6])},d(h){h&&(ut(t),ut(u),ut(i),ut(a),ut(s)),d=!1,Jt(l)}}}function Zp(e){let t,n,r;return{c(){t=Q("span"),t.textContent=":",n=Ct(),r=Q("span"),r.textContent="00",A(r,"class","sdt-time-figure svelte-bn8ebp")},m(u,i){at(u,t,i),at(u,n,i),at(u,r,i)},p:Qe,d(u){u&&(ut(t),ut(n),ut(r))}}}function Wp(e){let t,n=(e[13]?e[5].meridiem[1]:e[5].meridiem[0]).toUpperCase()+"",r;return{c(){t=Q("span"),r=jt(n),A(t,"class","sdt-time-figure svelte-bn8ebp")},m(u,i){at(u,t,i),U(t,r)},p(u,i){i[0]&8224&&n!==(n=(u[13]?u[5].meridiem[1]:u[5].meridiem[0]).toUpperCase()+"")&&Kt(r,n)},d(u){u&&ut(t)}}}function wo(e){let t,n,r=(e[13]?e[5].meridiem[1]:e[5].meridiem[0])+"",u,i,a,s;return{c(){t=Q("div"),n=Q("button"),u=jt(r),A(n,"type","button"),A(n,"class","sdt-time-btn sdt-time-figure is-active svelte-bn8ebp"),A(n,"data-value",i=e[13]?e[9]%12:e[9]+12),A(t,"class","sdt-meridian svelte-bn8ebp")},m(o,c){at(o,t,c),U(t,n),U(n,u),a||(s=ft(n,"click",e[20]),a=!0)},p(o,c){c[0]&8224&&r!==(r=(o[13]?o[5].meridiem[1]:o[5].meridiem[0])+"")&&Kt(u,r),c[0]&8704&&i!==(i=o[13]?o[9]%12:o[9]+12)&&A(n,"data-value",i)},d(o){o&&ut(t),a=!1,s()}}}function Eo(e,t){let n,r=t[37].val+"",u,i,a,s,o,c;return{key:e,first:null,c(){n=Q("button"),u=jt(r),A(n,"type","button"),A(n,"style",i=`left:${t[37].x}px; top:${t[37].y}px;`),A(n,"class","sdt-tick svelte-bn8ebp"),A(n,"data-value",a=t[37].val),n.disabled=s=(t[0]||t[1])&&t[7]&&t[18](t[37].val,!1),ot(n,"outer-tick",t[6]),ot(n,"is-selected",t[17](t[9],t[37].val,t[39])),this.first=n},m(d,l){at(d,n,l),U(n,u),c=!0},p(d,l){t=d,(!c||l[0]&32768)&&r!==(r=t[37].val+"")&&Kt(u,r),(!c||l[0]&32768&&i!==(i=`left:${t[37].x}px; top:${t[37].y}px;`))&&A(n,"style",i),(!c||l[0]&32768&&a!==(a=t[37].val))&&A(n,"data-value",a),(!c||l[0]&32899&&s!==(s=(t[0]||t[1])&&t[7]&&t[18](t[37].val,!1)))&&(n.disabled=s),(!c||l[0]&64)&&ot(n,"outer-tick",t[6]),(!c||l[0]&164352)&&ot(n,"is-selected",t[17](t[9],t[37].val,t[39]))},i(d){c||(d&&me(()=>{c&&(o||(o=rr(n,On,{duration:200},!0)),o.run(1))}),c=!0)},o(d){d&&(o||(o=rr(n,On,{duration:200},!1)),o.run(0)),c=!1},d(d){d&&ut(n),d&&o&&o.end()}}}function Fo(e){let t,n=e[37].val+"",r,u,i,a,s,o;return{c(){t=Q("button"),r=jt(n),A(t,"type","button"),A(t,"style",u=`left:${e[37].x}px; top:${e[37].y}px;`),A(t,"class","sdt-tick svelte-bn8ebp"),A(t,"data-value",i=e[37].val),t.disabled=a=(e[0]||e[1])&&e[7]&&e[18](e[37].val,!1),ot(t,"outer-tick",e[3]&&!e[6]),ot(t,"is-selected",e[17](e[6]?e[8]:e[9],e[37].val,e[39]))},m(c,d){at(c,t,d),U(t,r),o=!0},p(c,d){(!o||d[0]&16384)&&n!==(n=c[37].val+"")&&Kt(r,n),(!o||d[0]&16384&&u!==(u=`left:${c[37].x}px; top:${c[37].y}px;`))&&A(t,"style",u),(!o||d[0]&16384&&i!==(i=c[37].val))&&A(t,"data-value",i),(!o||d[0]&16515&&a!==(a=(c[0]||c[1])&&c[7]&&c[18](c[37].val,!1)))&&(t.disabled=a),(!o||d[0]&72)&&ot(t,"outer-tick",c[3]&&!c[6]),(!o||d[0]&148288)&&ot(t,"is-selected",c[17](c[6]?c[8]:c[9],c[37].val,c[39]))},i(c){o||(c&&me(()=>{o&&(s||(s=rr(t,On,{duration:200},!0)),s.run(1))}),o=!0)},o(c){c&&(s||(s=rr(t,On,{duration:200},!1)),s.run(0)),o=!1},d(c){c&&ut(t),c&&s&&s.end()}}}function zp(e){let t,n,r,u,i,a,s,o,c,d,l,h=[],f=new Map,m,b,g,y,C,B=e[4]&&vo(e);function E($,q){return $[2]?Up:Yp}let S=E(e),T=S(e),F=e[3]&&wo(e),v=Wt(e[15]);const L=$=>$[37].val;for(let $=0;$<v.length;$+=1){let q=yo(e,v,$),z=L(q);f.set(z,h[$]=Eo(z,q))}let N=Wt(e[14]),M=[];for(let $=0;$<N.length;$+=1)M[$]=Fo(bo(e,N,$));const W=$=>$t(M[$],1,1,()=>{M[$]=null});return{c(){t=Q("div"),n=Q("div"),B&&B.c(),r=Ct(),T.c(),u=Ct(),F&&F.c(),i=Ct(),a=Q("div"),s=Q("div"),o=Ct(),c=Q("div"),d=Q("div"),l=Ct();for(let $=0;$<h.length;$+=1)h[$].c();m=Ct();for(let $=0;$<M.length;$+=1)M[$].c();A(n,"class","sdt-time-head svelte-bn8ebp"),A(s,"class","sdt-middle-dot svelte-bn8ebp"),A(d,"class","sdt-hand-circle svelte-bn8ebp"),A(c,"class","sdt-hand-pointer svelte-bn8ebp"),A(c,"style",e[12]),A(a,"class","sdt-clock svelte-bn8ebp"),ot(a,"is-minute-view",e[6]),A(t,"class","sdt-timer svelte-bn8ebp")},m($,q){at($,t,q),U(t,n),B&&B.m(n,null),U(n,r),T.m(n,null),U(n,u),F&&F.m(n,null),U(t,i),U(t,a),U(a,s),U(a,o),U(a,c),U(c,d),U(a,l);for(let z=0;z<h.length;z+=1)h[z]&&h[z].m(a,null);U(a,m);for(let z=0;z<M.length;z+=1)M[z]&&M[z].m(a,null);e[31](a),g=!0,y||(C=[ft(a,"click",Nd(e[19])),ft(a,"mousedown",e[21]),ft(a,"mousemove",e[30]),ft(a,"mouseup",e[21])],y=!0)},p($,q){if($[4]?B?B.p($,q):(B=vo($),B.c(),B.m(n,r)):B&&(B.d(1),B=null),S===(S=E($))&&T?T.p($,q):(T.d(1),T=S($),T&&(T.c(),T.m(n,u))),$[3]?F?F.p($,q):(F=wo($),F.c(),F.m(n,null)):F&&(F.d(1),F=null),(!g||q[0]&4096)&&A(c,"style",$[12]),q[0]&426691&&(v=Wt($[15]),Ge(),h=Ur(h,q,L,1,$,v,f,a,Hd,Eo,m,yo),Ke()),q[0]&410571){N=Wt($[14]);let z;for(z=0;z<N.length;z+=1){const gt=bo($,N,z);M[z]?(M[z].p(gt,q),xt(M[z],1)):(M[z]=Fo(gt),M[z].c(),xt(M[z],1),M[z].m(a,null))}for(Ge(),z=N.length;z<M.length;z+=1)W(z);Ke()}(!g||q[0]&64)&&ot(a,"is-minute-view",$[6])},i($){if(!g){for(let q=0;q<v.length;q+=1)xt(h[q]);for(let q=0;q<N.length;q+=1)xt(M[q]);$&&(b||me(()=>{b=ei(t,On,{duration:200}),b.start()})),g=!0}},o($){for(let q=0;q<h.length;q+=1)$t(h[q]);M=M.filter(Boolean);for(let q=0;q<M.length;q+=1)$t(M[q]);g=!1},d($){$&&ut(t),B&&B.d(),T.d(),F&&F.d();for(let q=0;q<h.length;q+=1)h[q].d();Nr(M,$),e[31](null),y=!1,Jt(C)}}}function Gp(e,t,n){let r,u,i,a,s,o,c,{wid:d}=t,{date:l=null}=t,{startDate:h=null}=t,{endDate:f=null}=t,{hourOnly:m=!1}=t,{minuteIncrement:b=1}=t,{showMeridian:g=!1}=t,{hasDateComponent:y=!1}=t,{i18n:C}=t;function B(){n(6,T=!0)}function E(_){T?(_=_*5+i,_%5!==0&&(_=_<i?_+(5-_%5):_-_%5)):_=r+_,z({type:"keyboard",target:{tagName:"BUTTON",dataset:{value:_}}})}let S,T=!1,F=!1,v=l||new Date;l||(l=v,l.setHours(0,0,0,0));const L=ti();let N="";function M(_,nt,Dt,bt,At){const Tt=_/2;nt=nt||Tt;const lt=[0,1-.5,1-.134,1,1-.134,1-.5],vt=lt.concat(lt),kt=lt.slice(3).concat(lt).concat(lt.slice(0,3)),Qt=[];for(let yt=0;yt<12;yt++)Qt.push({x:Math.abs(vt[yt]*Tt+(yt<=6?1:-1)*nt),y:Math.abs(kt[yt]*Tt+(yt>=9||yt<3?-1:1)*nt),val:bt?yt*5||Dt:yt?yt+At:Dt});return Qt}function W(_,nt){return nt?u&&_===12?12:_<10||_%12<10?`0${_%12}`:_%12:_<10?`0${_}`:_}function $(_,nt,Dt){return T?nt===_||Dt===0&&Dt===_:g?u&&nt==12&&_===12||!u&&nt==12&&_===0?!0:nt===(_?_%12:12):+nt>12?(Dt?a*Dt+12:0)===_:nt==="00"||nt==="12"?_===12&&parseInt(nt)==12||nt==="00"&&_===0:nt===_}function q(_,nt=!1){return typeof _=="string"&&(_=parseInt(_)),h&&f&&s?T||nt?h.getHours()===v.getHours()&&h.getMinutes()>_||f.getHours()===v.getHours()&&f.getMinutes()<_:h.getHours()>_||f.getHours()<_:h&&h.getDate()===v.getDate()&&h.getMonth()===v.getMonth()&&h.getFullYear()===v.getFullYear()?T||nt?h.getHours()===v.getHours()&&h.getMinutes()>_:h.getHours()>_:f&&f.getDate()===v.getDate()&&f.getMonth()===v.getMonth()&&f.getFullYear()===v.getFullYear()?T||nt?f.getHours()===v.getHours()&&f.getMinutes()<_:f.getHours()<_:!1}function z(_){if(!_.target)return;let nt=0,Dt=0;if(_.target.tagName==="BUTTON"){let bt=parseInt(_.target.dataset.value);const At=T?"setMinutes":"setHours";if(!T&&u&&(bt+=12),T&&b!==1){if(_.isKeyboard)bt=bt>i?i+b:i-b;else if(bt%b!==0){const Tt=bt%b,lt=bt-Tt,vt=lt+b;bt=vt-bt<bt-lt?vt:lt}}v[At](bt)}else if(T){const bt=S.getBoundingClientRect(),At=_.clientX-bt.left,Tt=_.clientY-bt.top,lt=110,vt=110;let kt=null;switch(At>lt?kt=Tt>vt?2:1:kt=Tt>vt?3:4,kt){case 1:nt=At-lt,Dt=vt-Tt;break;case 2:nt=At-lt,Dt=Tt-vt;break;case 3:nt=lt-At,Dt=Tt-vt;break;case 4:nt=lt-At,Dt=vt-Tt;break}const Qt=Math.sqrt(nt*nt+Dt*Dt),yt=90-Math.asin(nt/Qt)*(180/Math.PI);let ct=0;switch(kt){case 1:ct=90-yt;break;case 2:ct=yt+90;break;case 3:ct=270-yt;break;case 4:ct=yt+270;break}ct=Math.round(ct/6/b)*b,ct>=60&&(ct=0),v.setMinutes(ct)}n(7,v),n(23,l),F||(L(T?"minute":"hour",{value:v,isKeyboard:_.type==="keyboard",dateIndex:d}),_.type!=="keyboard"&&!T&&!m&&n(6,T=!0))}function gt(_){const nt=parseInt(_.target.dataset.value);v.setHours(nt),n(7,v),n(23,l),L(T?"minute":"hour",{value:v,isKeyboard:!0})}function ht(_){n(11,F=_.type==="mousedown")}function pt(){L("switch","date")}const Ft=()=>n(6,T=!1),Bt=()=>n(6,T=!0),it=_=>{F&&z(_)};function St(_){Kn[_?"unshift":"push"](()=>{S=_,n(10,S)})}return e.$$set=_=>{"wid"in _&&n(24,d=_.wid),"date"in _&&n(23,l=_.date),"startDate"in _&&n(0,h=_.startDate),"endDate"in _&&n(1,f=_.endDate),"hourOnly"in _&&n(2,m=_.hourOnly),"minuteIncrement"in _&&n(25,b=_.minuteIncrement),"showMeridian"in _&&n(3,g=_.showMeridian),"hasDateComponent"in _&&n(4,y=_.hasDateComponent),"i18n"in _&&n(5,C=_.i18n)},e.$$.update=()=>{if(e.$$.dirty[0]&8388736&&l!==v&&l&&n(7,v=l),e.$$.dirty[0]&131){let _=!1;h&&h.toDateString()===v.toDateString()&&(q(v.getHours())&&(v.setHours(h.getHours()),_=!0),q(v.getMinutes(),!0)&&(v.setMinutes(h.getMinutes()),_=!0)),f&&f.toDateString()===v.toDateString()&&(q(v.getHours())&&(v.setHours(f.getHours()),_=!0),q(v.getMinutes(),!0)&&(v.setMinutes(f.getMinutes()),_=!0)),_&&Vd().then(()=>L("time",v))}if(e.$$.dirty[0]&128&&n(9,r=v?v.getHours():0),e.$$.dirty[0]&520&&n(13,u=g?r>=12:!1),e.$$.dirty[0]&128&&n(8,i=v?v.getMinutes():0),e.$$.dirty[0]&840){let _=T?i*6:r%12*30;n(12,N=T||g||r<12?`transform: rotateZ(${_}deg);`:`transform: rotateZ(${_}deg); height: calc(25% + 1px)`)}e.$$.dirty[0]&64&&(a=T?5:1),e.$$.dirty[0]&3&&(s=h&&f&&["getFullYear","getMonth","getDate"].every(_=>f[_]()===h[_]())),e.$$.dirty[0]&64&&n(15,o=M(T?220:180,110,"00",!1,0)),e.$$.dirty[0]&64&&n(14,c=M(T?180:120,110,T?"00":"12",T,12)),e.$$.dirty[0]&64&&L("time-switch",T)},[h,f,m,g,y,C,T,v,i,r,S,F,N,u,c,o,W,$,q,z,gt,ht,pt,l,d,b,B,E,Ft,Bt,it,St]}class Kp extends ii{constructor(t){super(),ui(this,t,Gp,zp,Qu,{wid:24,date:23,startDate:0,endDate:1,hourOnly:2,minuteIncrement:25,showMeridian:3,hasDateComponent:4,i18n:5,showMinuteView:26,makeTick:27},null,[-1,-1])}get showMinuteView(){return this.$$.ctx[26]}get makeTick(){return this.$$.ctx[27]}}const Ru=Math.min,An=Math.max,$u=Math.round,du=Math.floor,bn=e=>({x:e,y:e}),Jp={left:"right",right:"left",bottom:"top",top:"bottom"},Qp={start:"end",end:"start"};function Co(e,t,n){return An(e,Ru(t,n))}function Oa(e,t){return typeof e=="function"?e(t):e}function ir(e){return e.split("-")[0]}function Ma(e){return e.split("-")[1]}function Ud(e){return e==="x"?"y":"x"}function Yd(e){return e==="y"?"height":"width"}function Ba(e){return["top","bottom"].includes(ir(e))?"y":"x"}function Zd(e){return Ud(Ba(e))}function Xp(e,t,n){n===void 0&&(n=!1);const r=Ma(e),u=Zd(e),i=Yd(u);let a=u==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Iu(a)),[a,Iu(a)]}function tg(e){const t=Iu(e);return[Ki(e),t,Ki(t)]}function Ki(e){return e.replace(/start|end/g,t=>Qp[t])}function eg(e,t,n){const r=["left","right"],u=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?u:r:t?r:u;case"left":case"right":return t?i:a;default:return[]}}function ng(e,t,n,r){const u=Ma(e);let i=eg(ir(e),n==="start",r);return u&&(i=i.map(a=>a+"-"+u),t&&(i=i.concat(i.map(Ki)))),i}function Iu(e){return e.replace(/left|right|bottom|top/g,t=>Jp[t])}function rg(e){return{top:0,right:0,bottom:0,left:0,...e}}function ug(e){return typeof e!="number"?rg(e):{top:e,right:e,bottom:e,left:e}}function Vu(e){const{x:t,y:n,width:r,height:u}=e;return{width:r,height:u,top:n,left:t,right:t+r,bottom:n+u,x:t,y:n}}function ko(e,t,n){let{reference:r,floating:u}=e;const i=Ba(t),a=Zd(t),s=Yd(a),o=ir(t),c=i==="y",d=r.x+r.width/2-u.width/2,l=r.y+r.height/2-u.height/2,h=r[s]/2-u[s]/2;let f;switch(o){case"top":f={x:d,y:r.y-u.height};break;case"bottom":f={x:d,y:r.y+r.height};break;case"right":f={x:r.x+r.width,y:l};break;case"left":f={x:r.x-u.width,y:l};break;default:f={x:r.x,y:r.y}}switch(Ma(t)){case"start":f[a]-=h*(n&&c?-1:1);break;case"end":f[a]+=h*(n&&c?-1:1);break}return f}const ig=async(e,t,n)=>{const{placement:r="bottom",strategy:u="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),o=await(a.isRTL==null?void 0:a.isRTL(t));let c=await a.getElementRects({reference:e,floating:t,strategy:u}),{x:d,y:l}=ko(c,r,o),h=r,f={},m=0;for(let b=0;b<s.length;b++){const{name:g,fn:y}=s[b],{x:C,y:B,data:E,reset:S}=await y({x:d,y:l,initialPlacement:r,placement:h,strategy:u,middlewareData:f,rects:c,platform:a,elements:{reference:e,floating:t}});d=C??d,l=B??l,f={...f,[g]:{...f[g],...E}},S&&m<=50&&(m++,typeof S=="object"&&(S.placement&&(h=S.placement),S.rects&&(c=S.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:u}):S.rects),{x:d,y:l}=ko(c,h,o)),b=-1)}return{x:d,y:l,placement:h,strategy:u,middlewareData:f}};async function Wd(e,t){var n;t===void 0&&(t={});const{x:r,y:u,platform:i,rects:a,elements:s,strategy:o}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:l="floating",altBoundary:h=!1,padding:f=0}=Oa(t,e),m=ug(f),g=s[h?l==="floating"?"reference":"floating":l],y=Vu(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(g)))==null||n?g:g.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:o})),C=l==="floating"?{x:r,y:u,width:a.floating.width,height:a.floating.height}:a.reference,B=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),E=await(i.isElement==null?void 0:i.isElement(B))?await(i.getScale==null?void 0:i.getScale(B))||{x:1,y:1}:{x:1,y:1},S=Vu(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:C,offsetParent:B,strategy:o}):C);return{top:(y.top-S.top+m.top)/E.y,bottom:(S.bottom-y.bottom+m.bottom)/E.y,left:(y.left-S.left+m.left)/E.x,right:(S.right-y.right+m.right)/E.x}}const ag=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:u,middlewareData:i,rects:a,initialPlacement:s,platform:o,elements:c}=t,{mainAxis:d=!0,crossAxis:l=!0,fallbackPlacements:h,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:b=!0,...g}=Oa(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const y=ir(u),C=ir(s)===s,B=await(o.isRTL==null?void 0:o.isRTL(c.floating)),E=h||(C||!b?[Iu(s)]:tg(s));!h&&m!=="none"&&E.push(...ng(s,b,m,B));const S=[s,...E],T=await Wd(t,g),F=[];let v=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&F.push(T[y]),l){const W=Xp(u,a,B);F.push(T[W[0]],T[W[1]])}if(v=[...v,{placement:u,overflows:F}],!F.every(W=>W<=0)){var L,N;const W=(((L=i.flip)==null?void 0:L.index)||0)+1,$=S[W];if($)return{data:{index:W,overflows:v},reset:{placement:$}};let q=(N=v.filter(z=>z.overflows[0]<=0).sort((z,gt)=>z.overflows[1]-gt.overflows[1])[0])==null?void 0:N.placement;if(!q)switch(f){case"bestFit":{var M;const z=(M=v.map(gt=>[gt.placement,gt.overflows.filter(ht=>ht>0).reduce((ht,pt)=>ht+pt,0)]).sort((gt,ht)=>gt[1]-ht[1])[0])==null?void 0:M[0];z&&(q=z);break}case"initialPlacement":q=s;break}if(u!==q)return{reset:{placement:q}}}return{}}}},sg=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:u}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:g=>{let{x:y,y:C}=g;return{x:y,y:C}}},...o}=Oa(e,t),c={x:n,y:r},d=await Wd(t,o),l=Ba(ir(u)),h=Ud(l);let f=c[h],m=c[l];if(i){const g=h==="y"?"top":"left",y=h==="y"?"bottom":"right",C=f+d[g],B=f-d[y];f=Co(C,f,B)}if(a){const g=l==="y"?"top":"left",y=l==="y"?"bottom":"right",C=m+d[g],B=m-d[y];m=Co(C,m,B)}const b=s.fn({...t,[h]:f,[l]:m});return{...b,data:{x:b.x-n,y:b.y-r}}}}};function yn(e){return zd(e)?(e.nodeName||"").toLowerCase():"#document"}function he(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function nn(e){var t;return(t=(zd(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zd(e){return e instanceof Node||e instanceof he(e).Node}function Xe(e){return e instanceof Element||e instanceof he(e).Element}function je(e){return e instanceof HTMLElement||e instanceof he(e).HTMLElement}function xo(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof he(e).ShadowRoot}function Yr(e){const{overflow:t,overflowX:n,overflowY:r,display:u}=Fe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(u)}function og(e){return["table","td","th"].includes(yn(e))}function La(e){const t=Pa(),n=Fe(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function cg(e){let t=ar(e);for(;je(t)&&!ai(t);){if(La(t))return t;t=ar(t)}return null}function Pa(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ai(e){return["html","body","#document"].includes(yn(e))}function Fe(e){return he(e).getComputedStyle(e)}function si(e){return Xe(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function ar(e){if(yn(e)==="html")return e;const t=e.assignedSlot||e.parentNode||xo(e)&&e.host||nn(e);return xo(t)?t.host:t}function Gd(e){const t=ar(e);return ai(t)?e.ownerDocument?e.ownerDocument.body:e.body:je(t)&&Yr(t)?t:Gd(t)}function Rr(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const u=Gd(e),i=u===((r=e.ownerDocument)==null?void 0:r.body),a=he(u);return i?t.concat(a,a.visualViewport||[],Yr(u)?u:[],a.frameElement&&n?Rr(a.frameElement):[]):t.concat(u,Rr(u,[],n))}function Kd(e){const t=Fe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const u=je(e),i=u?e.offsetWidth:n,a=u?e.offsetHeight:r,s=$u(n)!==i||$u(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function Na(e){return Xe(e)?e:e.contextElement}function Qn(e){const t=Na(e);if(!je(t))return bn(1);const n=t.getBoundingClientRect(),{width:r,height:u,$:i}=Kd(t);let a=(i?$u(n.width):n.width)/r,s=(i?$u(n.height):n.height)/u;return(!a||!Number.isFinite(a))&&(a=1),(!s||!Number.isFinite(s))&&(s=1),{x:a,y:s}}const dg=bn(0);function Jd(e){const t=he(e);return!Pa()||!t.visualViewport?dg:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function lg(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==he(e)?!1:t}function Mn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const u=e.getBoundingClientRect(),i=Na(e);let a=bn(1);t&&(r?Xe(r)&&(a=Qn(r)):a=Qn(e));const s=lg(i,n,r)?Jd(i):bn(0);let o=(u.left+s.x)/a.x,c=(u.top+s.y)/a.y,d=u.width/a.x,l=u.height/a.y;if(i){const h=he(i),f=r&&Xe(r)?he(r):r;let m=h,b=m.frameElement;for(;b&&r&&f!==m;){const g=Qn(b),y=b.getBoundingClientRect(),C=Fe(b),B=y.left+(b.clientLeft+parseFloat(C.paddingLeft))*g.x,E=y.top+(b.clientTop+parseFloat(C.paddingTop))*g.y;o*=g.x,c*=g.y,d*=g.x,l*=g.y,o+=B,c+=E,m=he(b),b=m.frameElement}}return Vu({width:d,height:l,x:o,y:c})}const fg=[":popover-open",":modal"];function Qd(e){return fg.some(t=>{try{return e.matches(t)}catch{return!1}})}function hg(e){let{elements:t,rect:n,offsetParent:r,strategy:u}=e;const i=u==="fixed",a=nn(r),s=t?Qd(t.floating):!1;if(r===a||s&&i)return n;let o={scrollLeft:0,scrollTop:0},c=bn(1);const d=bn(0),l=je(r);if((l||!l&&!i)&&((yn(r)!=="body"||Yr(a))&&(o=si(r)),je(r))){const h=Mn(r);c=Qn(r),d.x=h.x+r.clientLeft,d.y=h.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-o.scrollLeft*c.x+d.x,y:n.y*c.y-o.scrollTop*c.y+d.y}}function mg(e){return Array.from(e.getClientRects())}function Xd(e){return Mn(nn(e)).left+si(e).scrollLeft}function pg(e){const t=nn(e),n=si(e),r=e.ownerDocument.body,u=An(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=An(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Xd(e);const s=-n.scrollTop;return Fe(r).direction==="rtl"&&(a+=An(t.clientWidth,r.clientWidth)-u),{width:u,height:i,x:a,y:s}}function gg(e,t){const n=he(e),r=nn(e),u=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,o=0;if(u){i=u.width,a=u.height;const c=Pa();(!c||c&&t==="fixed")&&(s=u.offsetLeft,o=u.offsetTop)}return{width:i,height:a,x:s,y:o}}function Dg(e,t){const n=Mn(e,!0,t==="fixed"),r=n.top+e.clientTop,u=n.left+e.clientLeft,i=je(e)?Qn(e):bn(1),a=e.clientWidth*i.x,s=e.clientHeight*i.y,o=u*i.x,c=r*i.y;return{width:a,height:s,x:o,y:c}}function So(e,t,n){let r;if(t==="viewport")r=gg(e,n);else if(t==="document")r=pg(nn(e));else if(Xe(t))r=Dg(t,n);else{const u=Jd(e);r={...t,x:t.x-u.x,y:t.y-u.y}}return Vu(r)}function tl(e,t){const n=ar(e);return n===t||!Xe(n)||ai(n)?!1:Fe(n).position==="fixed"||tl(n,t)}function bg(e,t){const n=t.get(e);if(n)return n;let r=Rr(e,[],!1).filter(s=>Xe(s)&&yn(s)!=="body"),u=null;const i=Fe(e).position==="fixed";let a=i?ar(e):e;for(;Xe(a)&&!ai(a);){const s=Fe(a),o=La(a);!o&&s.position==="fixed"&&(u=null),(i?!o&&!u:!o&&s.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||Yr(a)&&!o&&tl(e,a))?r=r.filter(d=>d!==a):u=s,a=ar(a)}return t.set(e,r),r}function yg(e){let{element:t,boundary:n,rootBoundary:r,strategy:u}=e;const a=[...n==="clippingAncestors"?bg(t,this._c):[].concat(n),r],s=a[0],o=a.reduce((c,d)=>{const l=So(t,d,u);return c.top=An(l.top,c.top),c.right=Ru(l.right,c.right),c.bottom=Ru(l.bottom,c.bottom),c.left=An(l.left,c.left),c},So(t,s,u));return{width:o.right-o.left,height:o.bottom-o.top,x:o.left,y:o.top}}function vg(e){const{width:t,height:n}=Kd(e);return{width:t,height:n}}function wg(e,t,n){const r=je(t),u=nn(t),i=n==="fixed",a=Mn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const o=bn(0);if(r||!r&&!i)if((yn(t)!=="body"||Yr(u))&&(s=si(t)),r){const l=Mn(t,!0,i,t);o.x=l.x+t.clientLeft,o.y=l.y+t.clientTop}else u&&(o.x=Xd(u));const c=a.left+s.scrollLeft-o.x,d=a.top+s.scrollTop-o.y;return{x:c,y:d,width:a.width,height:a.height}}function To(e,t){return!je(e)||Fe(e).position==="fixed"?null:t?t(e):e.offsetParent}function el(e,t){const n=he(e);if(!je(e)||Qd(e))return n;let r=To(e,t);for(;r&&og(r)&&Fe(r).position==="static";)r=To(r,t);return r&&(yn(r)==="html"||yn(r)==="body"&&Fe(r).position==="static"&&!La(r))?n:r||cg(e)||n}const Eg=async function(e){const t=this.getOffsetParent||el,n=this.getDimensions,r=await n(e.floating);return{reference:wg(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Fg(e){return Fe(e).direction==="rtl"}const Cg={convertOffsetParentRelativeRectToViewportRelativeRect:hg,getDocumentElement:nn,getClippingRect:yg,getOffsetParent:el,getElementRects:Eg,getClientRects:mg,getDimensions:vg,getScale:Qn,isElement:Xe,isRTL:Fg};function kg(e,t){let n=null,r;const u=nn(e);function i(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function a(s,o){s===void 0&&(s=!1),o===void 0&&(o=1),i();const{left:c,top:d,width:l,height:h}=e.getBoundingClientRect();if(s||t(),!l||!h)return;const f=du(d),m=du(u.clientWidth-(c+l)),b=du(u.clientHeight-(d+h)),g=du(c),C={rootMargin:-f+"px "+-m+"px "+-b+"px "+-g+"px",threshold:An(0,Ru(1,o))||1};let B=!0;function E(S){const T=S[0].intersectionRatio;if(T!==o){if(!B)return a();T?a(!1,T):r=setTimeout(()=>{a(!1,1e-7)},1e3)}B=!1}try{n=new IntersectionObserver(E,{...C,root:u.ownerDocument})}catch{n=new IntersectionObserver(E,C)}n.observe(e)}return a(!0),i}function xg(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:u=!0,ancestorResize:i=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:o=!1}=r,c=Na(e),d=u||i?[...c?Rr(c):[],...Rr(t)]:[];d.forEach(y=>{u&&y.addEventListener("scroll",n,{passive:!0}),i&&y.addEventListener("resize",n)});const l=c&&s?kg(c,n):null;let h=-1,f=null;a&&(f=new ResizeObserver(y=>{let[C]=y;C&&C.target===c&&f&&(f.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var B;(B=f)==null||B.observe(t)})),n()}),c&&!o&&f.observe(c),f.observe(t));let m,b=o?Mn(e):null;o&&g();function g(){const y=Mn(e);b&&(y.x!==b.x||y.y!==b.y||y.width!==b.width||y.height!==b.height)&&n(),b=y,m=requestAnimationFrame(g)}return n(),()=>{var y;d.forEach(C=>{u&&C.removeEventListener("scroll",n),i&&C.removeEventListener("resize",n)}),l==null||l(),(y=f)==null||y.disconnect(),f=null,o&&cancelAnimationFrame(m)}}const Sg=sg,Tg=ag,Ag=(e,t,n)=>{const r=new Map,u={platform:Cg,...n},i={...u.platform,_c:r};return ig(e,t,{...u,platform:i})};function Og(e){if((e==null?void 0:e.previousElementSibling)===null)return;const t=e==null?void 0:e.previousElementSibling,n=xg(t,e,()=>Ag(t,e,{placement:"bottom-start",middleware:[Sg({padding:5}),Tg()]}).then(({x:r,y:u})=>{Object.assign(e.style,{left:`${r}px`,top:`${u}px`})}));return{destroy(){n()}}}function Mg(e,t,n,r,u){let i=e?Array.isArray(e)?e:e.split(","):[];i=i.map(o=>o.replace(/(:\d+):\d+/,"$1"));let a=i,s=t?Array.isArray(t)?t:[t]:a.map(o=>Fr(o,n,r,u));return s&&t&&(i=s.map(o=>Yn(o,n,r,u))),{valueArray:i,prevValue:a,innerDates:s}}function Bg(e,t){return e==="auto"?t.match(/g|hh?|ii?/i)&&t.match(/y|m|d/i)?"datetime":t.match(/g|hh?|ii?/i)?"time":"date":e}const Lg=e=>({isTodayDisabled:e[1]&2048,currentMode:e[0]&67108864,i18n:e[0]&16777216}),Ao=e=>({onCancel:e[49],onConfirm:e[78],onClear:e[48],onToday:e[47],isTodayDisabled:e[42],currentMode:e[26],i18n:e[24]});function Oo(e,t,n){const r=e.slice();return r[98]=t[n],r[99]=t,r[100]=n,r}const Pg=e=>({value:e[0]&1,displayValue:e[1]&1,disabled:e[0]&32,isDirty:e[1]&128}),Mo=e=>({value:e[0],displayValue:e[31],disabled:e[5],isDirty:e[38],onKeyDown:e[50],onInputFocus:e[53],onInputBlur:e[54]});function Bo(e){let t,n,r,u=!e[9]&&Lo(e);return{c(){t=Q("input"),n=Ct(),u&&u.c(),r=Xu(),A(t,"type","hidden"),A(t,"name",e[4]),t.value=e[0]},m(i,a){at(i,t,a),at(i,n,a),u&&u.m(i,a),at(i,r,a)},p(i,a){a[0]&16&&A(t,"name",i[4]),a[0]&1&&(t.value=i[0]),i[9]?u&&(u.d(1),u=null):u?u.p(i,a):(u=Lo(i),u.c(),u.m(r.parentNode,r))},d(i){i&&(ut(t),ut(n),ut(r)),u&&u.d(i)}}}function Lo(e){let t,n,r,u,i,a,s;return{c(){t=Q("input"),A(t,"type","text"),A(t,"id",e[3]),A(t,"tabindex","0"),A(t,"name",n=e[4].endsWith("]")?e[4].substring(0,e[4].length-1)+"_input]":e[4]+"_input"),t.value=e[31],A(t,"placeholder",e[6]),t.disabled=e[5],t.required=e[7],A(t,"autocomplete","off"),A(t,"inputmode",r=e[11]?"text":"none"),A(t,"class",u=Ie(e[17])+" svelte-ergyxs"),t.readOnly=i=e[25]&&!e[11]&&!e[8],ot(t,"value-dirty",!e[22]&&e[38])},m(o,c){at(o,t,c),e[74](t),a||(s=[ft(t,"input",function(){en(e[11]?e[51]:jo)&&(e[11]?e[51]:jo).apply(this,arguments)}),Md(e[43].call(null,t,e[44])),ft(t,"focus",e[53]),ft(t,"blur",e[54]),ft(t,"click",e[75]),ft(t,"input",e[72]),ft(t,"change",e[73]),ft(t,"keydown",e[50])],a=!0)},p(o,c){e=o,c[0]&8&&A(t,"id",e[3]),c[0]&16&&n!==(n=e[4].endsWith("]")?e[4].substring(0,e[4].length-1)+"_input]":e[4]+"_input")&&A(t,"name",n),c[1]&1&&t.value!==e[31]&&(t.value=e[31]),c[0]&64&&A(t,"placeholder",e[6]),c[0]&32&&(t.disabled=e[5]),c[0]&128&&(t.required=e[7]),c[0]&2048&&r!==(r=e[11]?"text":"none")&&A(t,"inputmode",r),c[0]&131072&&u!==(u=Ie(e[17])+" svelte-ergyxs")&&A(t,"class",u),c[0]&33556736&&i!==(i=e[25]&&!e[11]&&!e[8])&&(t.readOnly=i),c[0]&4325376|c[1]&128&&ot(t,"value-dirty",!e[22]&&e[38])},d(o){o&&ut(t),e[74](null),a=!1,Jt(s)}}}function Ng(e){let t,n=!e[1]&&Bo(e);return{c(){n&&n.c(),t=Xu()},m(r,u){n&&n.m(r,u),at(r,t,u)},p(r,u){r[1]?n&&(n.d(1),n=null):n?n.p(r,u):(n=Bo(r),n.c(),n.m(t.parentNode,t))},d(r){r&&ut(t),n&&n.d(r)}}}function Po(e){let t,n,r=[],u=new Map,i,a,s,o,c,d,l=Wt(e[37]);const h=g=>g[100];for(let g=0;g<l.length;g+=1){let y=Oo(e,l,g),C=h(y);u.set(C,r[g]=No(C,y))}const f=e[70]["action-row"],m=xd(f,e,e[69],Ao),b=m||$g(e);return{c(){t=Q("div"),n=Q("div");for(let g=0;g<r.length;g+=1)r[g].c();i=Ct(),b&&b.c(),A(n,"class","sdt-widget-wrap svelte-ergyxs"),A(t,"class",a="std-calendar-wrap "+e[12]+" svelte-ergyxs"),ot(t,"is-popup",!e[40]),ot(t,"is-range-wrap",e[8])},m(g,y){at(g,t,y),U(t,n);for(let C=0;C<r.length;C+=1)r[C]&&r[C].m(n,null);U(t,i),b&&b.m(t,null),o=!0,c||(d=[Md(e[39].call(null,t)),ft(t,"mousedown",Nd(e[71]))],c=!0)},p(g,y){y[0]&2105664772|y[1]&52445258&&(l=Wt(g[37]),Ge(),r=Ur(r,y,h,1,g,l,u,n,Hd,No,null,Oo),Ke()),m?m.p&&(!o||y[0]&83886080|y[1]&2048|y[2]&128)&&Ad(m,f,g,g[69],o?Td(f,g[69],y,Lg):Od(g[69]),Ao):b&&b.p&&(!o||y[0]&87818240|y[1]&2080)&&b.p(g,o?y:[-1,-1,-1,-1]),(!o||y[0]&4096&&a!==(a="std-calendar-wrap "+g[12]+" svelte-ergyxs"))&&A(t,"class",a),(!o||y[0]&4096|y[1]&512)&&ot(t,"is-popup",!g[40]),(!o||y[0]&4352)&&ot(t,"is-range-wrap",g[8])},i(g){if(!o){for(let y=0;y<l.length;y+=1)xt(r[y]);xt(b,g),g&&me(()=>{o&&(s||(s=rr(t,e[41],{duration:200},!0)),s.run(1))}),o=!0}},o(g){for(let y=0;y<r.length;y+=1)$t(r[y]);$t(b,g),g&&(s||(s=rr(t,e[41],{duration:200},!1)),s.run(0)),o=!1},d(g){g&&ut(t);for(let y=0;y<r.length;y+=1)r[y].d();b&&b.d(g),g&&s&&s.end(),c=!1,Jt(d)}}}function _g(e){let t,n=e[99],r=e[100],u;const i=()=>e[77](t,n,r),a=()=>e[77](null,n,r);let s={wid:e[100],date:e[30][e[100]],startDate:e[29],endDate:e[28],hasDateComponent:e[27]!=="time",showMeridian:e[13].match(e[14]==="php"?"a|A":"p|P")!==null,i18n:e[24],minuteIncrement:e[15],hourOnly:e[23]};return t=new Kp({props:s}),i(),t.$on("hour",e[45]),t.$on("minute",e[45]),t.$on("switch",e[52]),t.$on("time-switch",e[56]),{c(){Aa(t.$$.fragment)},m(o,c){ni(t,o,c),u=!0},p(o,c){(n!==o[99]||r!==o[100])&&(a(),n=o[99],r=o[100],i());const d={};c[1]&64&&(d.wid=o[100]),c[0]&1073741824|c[1]&64&&(d.date=o[30][o[100]]),c[0]&536870912&&(d.startDate=o[29]),c[0]&268435456&&(d.endDate=o[28]),c[0]&134217728&&(d.hasDateComponent=o[27]!=="time"),c[0]&24576&&(d.showMeridian=o[13].match(o[14]==="php"?"a|A":"p|P")!==null),c[0]&16777216&&(d.i18n=o[24]),c[0]&32768&&(d.minuteIncrement=o[15]),c[0]&8388608&&(d.hourOnly=o[23]),t.$set(d)},i(o){u||(xt(t.$$.fragment,o),u=!0)},o(o){$t(t.$$.fragment,o),u=!1},d(o){a(),ri(t,o)}}}function Rg(e){var u;let t,n,r={wid:e[100],dates:e[30],isRange:e[8],startDate:e[29],endDate:e[28],enableTimeToggle:(u=e[27])==null?void 0:u.includes("time"),initialView:e[2]>2?2:e[2],hoverDate:e[32],additionalDisableFn:e[10],i18n:e[24],weekStart:e[16]};return t=new qp({props:r}),e[76](t),t.$on("date",e[45]),t.$on("switch",e[52]),t.$on("internal_hoverUpdate",e[55]),{c(){Aa(t.$$.fragment)},m(i,a){ni(t,i,a),n=!0},p(i,a){var o;const s={};a[1]&64&&(s.wid=i[100]),a[0]&1073741824&&(s.dates=i[30]),a[0]&256&&(s.isRange=i[8]),a[0]&536870912&&(s.startDate=i[29]),a[0]&268435456&&(s.endDate=i[28]),a[0]&134217728&&(s.enableTimeToggle=(o=i[27])==null?void 0:o.includes("time")),a[0]&4&&(s.initialView=i[2]>2?2:i[2]),a[1]&2&&(s.hoverDate=i[32]),a[0]&1024&&(s.additionalDisableFn=i[10]),a[0]&16777216&&(s.i18n=i[24]),a[0]&65536&&(s.weekStart=i[16]),t.$set(s)},i(i){n||(xt(t.$$.fragment,i),n=!0)},o(i){$t(t.$$.fragment,i),n=!1},d(i){e[76](null),ri(t,i)}}}function No(e,t){let n,r,u,i,a;const s=[Rg,_g],o=[];function c(d,l){return d[26]==="date"?0:1}return r=c(t),u=o[r]=s[r](t),{key:e,first:null,c(){n=Q("div"),u.c(),i=Ct(),A(n,"class","sdt-widget svelte-ergyxs"),this.first=n},m(d,l){at(d,n,l),o[r].m(n,null),U(n,i),a=!0},p(d,l){t=d;let h=r;r=c(t),r===h?o[r].p(t,l):(Ge(),$t(o[h],1,1,()=>{o[h]=null}),Ke(),u=o[r],u?u.p(t,l):(u=o[r]=s[r](t),u.c()),xt(u,1),u.m(n,i))},i(d){a||(xt(u),a=!0)},o(d){$t(u),a=!1},d(d){d&&ut(n),o[r].d()}}}function _o(e){let t,n,r=!e[36]&&Ro(e),u=(e[20]||e[21])&&$o(e);return{c(){t=Q("div"),r&&r.c(),n=Ct(),u&&u.c(),A(t,"class","sdt-btn-row svelte-ergyxs")},m(i,a){at(i,t,a),r&&r.m(t,null),U(t,n),u&&u.m(t,null)},p(i,a){i[36]?r&&(r.d(1),r=null):r?r.p(i,a):(r=Ro(i),r.c(),r.m(t,n)),i[20]||i[21]?u?u.p(i,a):(u=$o(i),u.c(),u.m(t,null)):u&&(u.d(1),u=null)},d(i){i&&ut(t),r&&r.d(),u&&u.d()}}}function Ro(e){let t,n,r=e[24].cancelBtn+"",u,i,a,s,o=e[24].okBtn+"",c,d,l,h;return{c(){t=Q("span"),n=Q("button"),u=jt(r),a=Ct(),s=Q("button"),c=jt(o),A(n,"type","button"),A(n,"class",i=Ie(e[19])+" svelte-ergyxs"),A(s,"type","button"),A(s,"class",d=Ie(e[18])+" svelte-ergyxs")},m(f,m){at(f,t,m),U(t,n),U(n,u),U(t,a),U(t,s),U(s,c),l||(h=[ft(n,"click",e[49]),ft(s,"click",e[79])],l=!0)},p(f,m){m[0]&16777216&&r!==(r=f[24].cancelBtn+"")&&Kt(u,r),m[0]&524288&&i!==(i=Ie(f[19])+" svelte-ergyxs")&&A(n,"class",i),m[0]&16777216&&o!==(o=f[24].okBtn+"")&&Kt(c,o),m[0]&262144&&d!==(d=Ie(f[18])+" svelte-ergyxs")&&A(s,"class",d)},d(f){f&&ut(t),l=!1,Jt(h)}}}function $o(e){let t,n,r=e[20]&&e[26]==="date"&&Io(e),u=e[21]&&Vo(e);return{c(){t=Q("span"),r&&r.c(),n=Ct(),u&&u.c()},m(i,a){at(i,t,a),r&&r.m(t,null),U(t,n),u&&u.m(t,null)},p(i,a){i[20]&&i[26]==="date"?r?r.p(i,a):(r=Io(i),r.c(),r.m(t,n)):r&&(r.d(1),r=null),i[21]?u?u.p(i,a):(u=Vo(i),u.c(),u.m(t,null)):u&&(u.d(1),u=null)},d(i){i&&ut(t),r&&r.d(),u&&u.d()}}}function Io(e){let t,n=e[24].todayBtn+"",r,u,i,a;return{c(){t=Q("button"),r=jt(n),A(t,"type","button"),A(t,"class",u=Ie(e[18])+" svelte-ergyxs"),t.disabled=e[42]},m(s,o){at(s,t,o),U(t,r),i||(a=ft(t,"click",e[47]),i=!0)},p(s,o){o[0]&16777216&&n!==(n=s[24].todayBtn+"")&&Kt(r,n),o[0]&262144&&u!==(u=Ie(s[18])+" svelte-ergyxs")&&A(t,"class",u),o[1]&2048&&(t.disabled=s[42])},d(s){s&&ut(t),i=!1,a()}}}function Vo(e){let t,n=e[24].clearBtn+"",r,u,i,a;return{c(){t=Q("button"),r=jt(n),A(t,"type","button"),A(t,"class",u=Ie(e[19])+" svelte-ergyxs")},m(s,o){at(s,t,o),U(t,r),i||(a=ft(t,"click",e[48]),i=!0)},p(s,o){o[0]&16777216&&n!==(n=s[24].clearBtn+"")&&Kt(r,n),o[0]&524288&&u!==(u=Ie(s[19])+" svelte-ergyxs")&&A(t,"class",u)},d(s){s&&ut(t),i=!1,a()}}}function $g(e){let t,n=(!e[36]||!0)&&_o(e);return{c(){n&&n.c(),t=Xu()},m(r,u){n&&n.m(r,u),at(r,t,u)},p(r,u){r[36],n?n.p(r,u):(n=_o(r),n.c(),n.m(t.parentNode,t))},d(r){r&&ut(t),n&&n.d(r)}}}function Ig(e){let t,n,r;const u=e[70].inputs,i=xd(u,e,e[69],Mo),a=i||Ng(e);let s=e[35]&&e[25]&&Po(e);return{c(){t=Q("span"),a&&a.c(),n=Ct(),s&&s.c(),A(t,"class","std-component-wrap svelte-ergyxs")},m(o,c){at(o,t,c),a&&a.m(t,null),U(t,n),s&&s.m(t,null),r=!0},p(o,c){i?i.p&&(!r||c[0]&33|c[1]&129|c[2]&128)&&Ad(i,u,o,o[69],r?Td(u,o[69],c,Pg):Od(o[69]),Mo):a&&a.p&&(!r||c[0]&37882875|c[1]&149)&&a.p(o,r?c:[-1,-1,-1,-1]),o[35]&&o[25]?s?(s.p(o,c),c[0]&33554432|c[1]&16&&xt(s,1)):(s=Po(o),s.c(),xt(s,1),s.m(t,null)):s&&(Ge(),$t(s,1,1,()=>{s=null}),Ke())},i(o){r||(xt(a,o),xt(s),r=!0)},o(o){$t(a,o),$t(s),r=!1},d(o){o&&ut(t),a&&a.d(o),s&&s.d()}}}const ee=_p;function Vg(e){return e?[{ref:null},{ref:null}]:[{ref:null}]}const jo=()=>{};function jg(e,t,n){let r,u,i,a,s,o,c,d,l,h,f,m,{$$slots:b={},$$scope:g}=t,{inputId:y=""}=t,{name:C="date"}=t,{disabled:B=!1}=t,{placeholder:E=null}=t,{required:S=!1}=t,{value:T=null}=t,{initialDate:F=null}=t,{isRange:v=!1}=t,{startDate:L=null}=t,{endDate:N=null}=t,{pickerOnly:M=!1}=t,{startView:W=be}=t,{mode:$="auto"}=t,{disableDatesFn:q=null}=t,{manualInput:z=!1}=t,{theme:gt=ee.theme}=t,{format:ht=ee.format}=t,{formatType:pt=ee.formatType}=t,{displayFormat:Ft=ee.displayFormat}=t,{displayFormatType:Bt=ee.displayFormatType}=t,{minuteIncrement:it=ee.minuteIncrement}=t,{weekStart:St=ee.weekStart}=t,{inputClasses:_=ee.inputClasses}=t,{todayBtnClasses:nt=ee.todayBtnClasses}=t,{clearBtnClasses:Dt=ee.clearBtnClasses}=t,{todayBtn:bt=ee.todayBtn}=t,{clearBtn:At=ee.clearBtn}=t,{clearToggle:Tt=ee.clearToggle}=t,{autocommit:lt=ee.autocommit}=t,{hourOnly:vt=ee.hourOnly}=t,{i18n:kt=ee.i18n}=t,{validatorAction:Qt=null}=t,{ce_valueElement:yt=null}=t,{ce_displayElement:ct=null}=t,{positionResolver:zt=Og}=t;const R=ti();let{valueArray:G,prevValue:et,innerDates:dt}=Mg(T,F,ht,kt,pt);!T&&F&&(T=v?G:G[0]);let Ht=ht,Ce=M,pe=[...G],te=w(),ge=Nn(),Ln,Nt=W===$p?"time":"date",vn=!1,fr=ct,ci=Qt?Qt.shift():()=>{},di=Qt||[],Pn,hr;function Zr(k,Lt,Mt,_n,mr,wn){return!k||Lt&&(Mt==="datetime"||mr.length!==2)?!1:_n==="minute"||Mt===_n||wn&&_n==="hour"}function Wr(k){k.join("")!==et.join("")&&(n(30,dt=k.filter(Lt=>Lt).map(Lt=>Fr(Lt,ht,kt,pt))),et=k,te=w(),n(31,ge=Nn()))}function zr(k){const Lt=Array.isArray(k)?k.join(","):k;te!==Lt&&(n(67,G=(Lt||"").split(",")),pe=G)}function Gr(k,Lt){Ht!==k&&dt.length&&(n(67,G=dt.map(Mt=>Yn(Mt,k,kt,pt))),et=G,n(31,ge=Nn()),Ht=k,$==="auto"&&n(27,c=k.match(/g|hh?|ii?/i)&&k.match(/y|m|d/i)?"datetime":k.match(/g|hh?|ii?/i)?"time":"date"),te=w(),j(!0))}function Nn(){return dt.sort((k,Lt)=>k-Lt).map(k=>Yn(k,Ft||ht,kt,Bt||pt)).join(" - ")}function p(){return v?G.length===2?G:null:G[0]||null}function w(){return G.join(",")}function x(k){return k.join(",")!==pe.join(",")}function P(){n(2,W=be),vn=!1,c==="datetime"?setTimeout(()=>{M||n(35,r=!1),n(26,Nt="date")},lt?300:0):M||n(35,r=!1)}function V(k,Lt){k==="date"&&c==="datetime"&&(v&&G.length===2||!v)?n(26,Nt="time"):k==="hour"&&!vt&&o[Lt].ref.showMinuteView()}function H({type:k,detail:Lt}){let{value:Mt,isKeyboard:_n,dateIndex:mr}=Lt;if(Mt&&!v&&dt.length&&dt[0].getFullYear()===Mt.getFullYear()&&dt[0].getMonth()===Mt.getMonth()&&dt[0].getDate()===Mt.getDate()&&c==="date"&&!S&&Tt&&(Mt=null),v){if(k==="date")n(30,dt=Mt?(dt.length===2?[Mt]:dt.concat(Mt)).map(wn=>wn.getTime()).sort().map(wn=>new Date(wn)):[]);else if(Mt&&mr!==void 0)n(30,dt[mr]=Mt,dt);else if(k==="datetime")n(30,dt[0]=Mt,dt),n(30,dt[1]=Mt,dt);else throw new Error(`Unhandled event type: '${k}'`);n(67,G=dt.map(wn=>Yn(wn,ht,kt,pt)))}else n(30,dt=Mt?[Mt]:[]),n(67,G=Mt?[Yn(Mt,ht,kt,pt)]:[]);_n||(n(68,hr=k),V(k,mr||0)),Vd().then(()=>l&&j(!_n))}function j(k){n(0,T=p()),te=w(),pe=[...G],n(31,ge=Nn()),n(38,m=x(G)),li(),R("change",v?G:G[0]||null),R("dateChange",{value:v?G:G[0]||null,dateValue:v?dt:dt[0]||null,displayValue:ge,valueFormat:ht,displayFormat:Ft}),k&&P()}function st(){const k=new Date,Lt=dt[0]||k;H(new CustomEvent(c,{detail:{dateIndex:0,value:new Date(k.getFullYear(),k.getMonth(),k.getDate(),v?0:Lt.getHours(),v?0:Lt.getMinutes(),0),isKeyboard:!1}})),v&&H(new CustomEvent(c,{detail:{dateIndex:1,value:new Date(k.getFullYear(),k.getMonth(),k.getDate(),23,59,59,999),isKeyboard:!1}})),j(!0)}function wt(){n(67,G=[]),et=[],n(30,dt=[]),te="",lt&&j()}function Gt(){n(67,G=[...pe]),te=w(),P(),R("cancel")}function qt(k){if(r||(["Backspace","Delete"].includes(k.key)&&!S&&wt(),k.key==="Enter"&&ie()),!r&&k.key!=="Tab"){n(35,r=k.key!=="Shift"),k.preventDefault();return}switch(k.key){case"PageDown":case"PageUp":case"ArrowDown":case"ArrowUp":case"ArrowLeft":case"ArrowRight":if(k.preventDefault(),v)return;Nt==="date"?Pn.handleGridNav(k.key,k.shiftKey):o[0].ref.makeTick(["ArrowDown","ArrowLeft","PageDown"].includes(k.key)?-1:1);break;case"Escape":lt?wt():Gt();break;case"Backspace":if(z&&!v)return;case"Delete":!S&&wt();break;case"Enter":if(Ce&&k.preventDefault(),G.length===0){n(35,r=!1);return}if(Nt==="time"&&!vn)return o[0].ref.showMinuteView();if(c==="datetime"&&Nt!=="time"){n(26,Nt="time");return}j(c==="date"||c.includes("time")&&vn);break;case"Tab":r&&(n(35,r=!1),k.preventDefault());case"F5":break;default:!z&&k.preventDefault()}}function ke(k){k.preventDefault(),k.stopPropagation();const Lt=Fr(k.target.value,Ft||ht,kt,Bt||pt);Yn(Lt,Ft||ht,kt,Bt||pt)===k.target.value&&H(new CustomEvent("date",{detail:{value:Lt,isKeyboard:!0}}))}function ue(k){n(2,W=be),n(26,Nt=k.detail),vn=!1}function ie(){n(25,Ce=!0),n(35,r=!0)}function He(){n(25,Ce=!1),d?j(!1):Gt(),!ct&&R("blur")}function li(k){yt&&ct&&(n(57,yt.value=G.join(",")||"",yt),n(1,ct.value=ge,ct),yt.dispatchEvent(new Event("input")),ct.dispatchEvent(new Event("input"))),R("input",te)}function It({detail:k}){n(32,Ln=k)}function rn(k){vn=k.detail}Op(()=>{ct&&(n(1,ct.onfocus=ie,ct),n(1,ct.onblur=He,ct),n(1,ct.onclick=()=>!r&&ie(),ct),n(1,ct.onkeydown=qt,ct))});function un(k){xi.call(this,e,k)}function qe(k){xi.call(this,e,k)}function an(k){xi.call(this,e,k)}function sn(k){Kn[k?"unshift":"push"](()=>{fr=k,n(33,fr)})}const rl=()=>{!r&&ie()};function ul(k){Kn[k?"unshift":"push"](()=>{Pn=k,n(34,Pn)})}function il(k,Lt,Mt){Kn[k?"unshift":"push"](()=>{Lt[Mt].ref=k,n(37,o),n(8,v)})}const al=()=>j(!0),sl=()=>j(!0);return e.$$set=k=>{"inputId"in k&&n(3,y=k.inputId),"name"in k&&n(4,C=k.name),"disabled"in k&&n(5,B=k.disabled),"placeholder"in k&&n(6,E=k.placeholder),"required"in k&&n(7,S=k.required),"value"in k&&n(0,T=k.value),"initialDate"in k&&n(58,F=k.initialDate),"isRange"in k&&n(8,v=k.isRange),"startDate"in k&&n(59,L=k.startDate),"endDate"in k&&n(60,N=k.endDate),"pickerOnly"in k&&n(9,M=k.pickerOnly),"startView"in k&&n(2,W=k.startView),"mode"in k&&n(61,$=k.mode),"disableDatesFn"in k&&n(10,q=k.disableDatesFn),"manualInput"in k&&n(11,z=k.manualInput),"theme"in k&&n(12,gt=k.theme),"format"in k&&n(13,ht=k.format),"formatType"in k&&n(14,pt=k.formatType),"displayFormat"in k&&n(62,Ft=k.displayFormat),"displayFormatType"in k&&n(63,Bt=k.displayFormatType),"minuteIncrement"in k&&n(15,it=k.minuteIncrement),"weekStart"in k&&n(16,St=k.weekStart),"inputClasses"in k&&n(17,_=k.inputClasses),"todayBtnClasses"in k&&n(18,nt=k.todayBtnClasses),"clearBtnClasses"in k&&n(19,Dt=k.clearBtnClasses),"todayBtn"in k&&n(20,bt=k.todayBtn),"clearBtn"in k&&n(21,At=k.clearBtn),"clearToggle"in k&&n(64,Tt=k.clearToggle),"autocommit"in k&&n(22,lt=k.autocommit),"hourOnly"in k&&n(23,vt=k.hourOnly),"i18n"in k&&n(24,kt=k.i18n),"validatorAction"in k&&n(65,Qt=k.validatorAction),"ce_valueElement"in k&&n(57,yt=k.ce_valueElement),"ce_displayElement"in k&&n(1,ct=k.ce_displayElement),"positionResolver"in k&&n(66,zt=k.positionResolver),"$$scope"in k&&n(69,g=k.$$scope)},e.$$.update=()=>{e.$$.dirty[0]&512&&n(35,r=M),e.$$.dirty[0]&16801792|e.$$.dirty[1]&268435456&&n(29,u=L?Fr(L,ht,kt,pt):null),e.$$.dirty[0]&16801792|e.$$.dirty[1]&536870912&&n(28,i=N?new Date(Fr(N,ht,kt,pt).setSeconds(1)):null),e.$$.dirty[0]&805306368&&n(42,a=u&&u>new Date||i&&i<new Date),e.$$.dirty[0]&512&&n(41,s=M?()=>({}):On),e.$$.dirty[0]&256&&n(37,o=Vg(v)),e.$$.dirty[0]&8192|e.$$.dirty[1]&1073741824&&n(27,c=Bg($,ht)),e.$$.dirty[0]&201326592&&c==="time"&&Nt!==c&&n(26,Nt=c),e.$$.dirty[0]&138412288&&n(36,d=lt&&(v&&c==="date"||!v)),e.$$.dirty[0]&146800896|e.$$.dirty[2]&96&&(l=Zr(lt,v,c,hr,G,vt)),e.$$.dirty[0]&33554434&&ct&&n(1,ct.readOnly=Ce,ct),e.$$.dirty[0]&512&&n(40,h=!!M),e.$$.dirty[0]&512|e.$$.dirty[2]&16&&n(39,f=M?()=>{}:zt),e.$$.dirty[2]&32&&n(38,m=x(G)),e.$$.dirty[0]&1&&zr(T),e.$$.dirty[2]&32&&Wr(G),e.$$.dirty[0]&8192|e.$$.dirty[2]&1&&Gr(ht)},[T,ct,W,y,C,B,E,S,v,M,q,z,gt,ht,pt,it,St,_,nt,Dt,bt,At,lt,vt,kt,Ce,Nt,c,i,u,dt,ge,Ln,fr,Pn,r,d,o,m,f,h,s,a,ci,di,H,j,st,wt,Gt,qt,ke,ue,ie,He,It,rn,yt,F,L,N,$,Ft,Bt,Tt,Qt,zt,G,hr,g,b,un,qe,an,sn,rl,ul,il,al,sl]}class Hg extends ii{constructor(t){super(),ui(this,t,jg,Ig,Qu,{inputId:3,name:4,disabled:5,placeholder:6,required:7,value:0,initialDate:58,isRange:8,startDate:59,endDate:60,pickerOnly:9,startView:2,mode:61,disableDatesFn:10,manualInput:11,theme:12,format:13,formatType:14,displayFormat:62,displayFormatType:63,minuteIncrement:15,weekStart:16,inputClasses:17,todayBtnClasses:18,clearBtnClasses:19,todayBtn:20,clearBtn:21,clearToggle:64,autocommit:22,hourOnly:23,i18n:24,validatorAction:65,ce_valueElement:57,ce_displayElement:1,positionResolver:66},null,[-1,-1,-1,-1])}}function Ho(e){let t,n,r;return n=new Hg({props:{pickerOnly:!0,mode:e[2],autocommit:!0,initialDate:e[2]==="date"?O.moment(e[1],"YYYY-MM-DD").toDate():O.moment(e[1],"HH:mm:ss").toDate(),format:e[2]==="time"?"hh:ii:ss":"yyyy-mm-dd",positionResolver:Yg,$$slots:{"action-row":[qg,({i18n:u,onToday:i})=>({8:u,9:i}),({i18n:u,onToday:i})=>(u?256:0)|(i?512:0)]},$$scope:{ctx:e}}}),n.$on("dateChange",e[4]),n.$on("blur",e[5]),n.$on("cancel",e[5]),{c(){t=Q("div"),Aa(n.$$.fragment),A(t,"class","float-date-picker svelte-a9w8cw")},m(u,i){at(u,t,i),ni(n,t,null),r=!0},p(u,i){const a={};i&4&&(a.mode=u[2]),i&6&&(a.initialDate=u[2]==="date"?O.moment(u[1],"YYYY-MM-DD").toDate():O.moment(u[1],"HH:mm:ss").toDate()),i&4&&(a.format=u[2]==="time"?"hh:ii:ss":"yyyy-mm-dd"),i&1792&&(a.$$scope={dirty:i,ctx:u}),n.$set(a)},i(u){r||(xt(n.$$.fragment,u),r=!0)},o(u){$t(n.$$.fragment,u),r=!1},d(u){u&&ut(t),ri(n)}}}function qg(e){let t,n,r,u=e[8].cancelBtn+"",i,a,s,o=e[8].todayBtn+"",c,d,l;return{c(){t=Q("div"),n=Q("span"),r=Q("button"),i=jt(u),a=Ct(),s=Q("button"),c=jt(o),A(r,"type","button"),A(r,"class","sdt-action-btn"),A(s,"type","button"),A(s,"class","sdt-action-btn"),A(t,"class","flex justify-end mt-2")},m(h,f){at(h,t,f),U(t,n),U(n,r),U(r,i),U(n,a),U(n,s),U(s,c),d||(l=[ft(r,"click",e[5]),ft(s,"click",function(){en(e[9])&&e[9].apply(this,arguments)})],d=!0)},p(h,f){e=h,f&256&&u!==(u=e[8].cancelBtn+"")&&Kt(i,u),f&256&&o!==(o=e[8].todayBtn+"")&&Kt(c,o)},d(h){h&&ut(t),d=!1,Jt(l)}}}function Ug(e){let t,n,r,u,i,a,s,o=e[3]&&Ho(e);return{c(){t=Q("div"),n=Q("button"),r=jt(e[0]),u=Ct(),o&&o.c(),A(n,"class","summary svelte-a9w8cw"),A(t,"class","details svelte-a9w8cw")},m(c,d){at(c,t,d),U(t,n),U(n,r),U(t,u),o&&o.m(t,null),i=!0,a||(s=ft(n,"click",e[6]),a=!0)},p(c,[d]){(!i||d&1)&&Kt(r,c[0]),c[3]?o?(o.p(c,d),d&8&&xt(o,1)):(o=Ho(c),o.c(),xt(o,1),o.m(t,null)):o&&(Ge(),$t(o,1,1,()=>{o=null}),Ke())},i(c){i||(xt(o),i=!0)},o(c){$t(o),i=!1},d(c){c&&ut(t),o&&o.d(),a=!1,s()}}}const Yg=()=>{};function Zg(e,t,n){let{date:r=O.moment().format("YYYY-MM-DD")}=t,{type:u="date"}=t,{value:i=r}=t,a=!1;const s=ti();function o(l){l.detail.value&&(s("select",l.detail),n(0,i=l.detail.value))}function c(){n(3,a=!1)}const d=()=>{n(3,a=!a)};return e.$$set=l=>{"date"in l&&n(1,r=l.date),"type"in l&&n(2,u=l.type),"value"in l&&n(0,i=l.value)},[i,r,u,a,o,c,d]}class Wg extends ii{constructor(t){super(),ui(this,t,Zg,Ug,Qu,{date:1,type:2,value:0})}}class qo extends J.WidgetType{constructor(t,n,r,u,i){super(),this.view=t,this.date=n,this.from=r,this.to=u,this.type=i,this.error=!1}eq(t){return t.date===this.date&&t.from===this.from&&t.to===this.to}toDOM(){const t=createEl("span",{cls:"cm-date-button-container"});return this.component=new Wg({target:t,props:{date:this.date,type:this.type}}),this.component.$on("select",n=>{const{value:r}=n.detail;r&&this.view.dispatch({changes:{from:this.from,to:this.to,insert:r}})}),t}}function zg(){class e{constructor(r){this.match=new J.MatchDecorator({regexp:/\b((?<date>\d{4}-\d{2}-\d{2})|(?<time>\d{2}:\d{2}(:\d{2})?))\b/g,decorate:(u,i,a,s,o)=>{var d,l,h;const c=this.shouldRender(o,i,a);try{c&&i!==a&&((d=s.groups)!=null&&d.date?u(i,a,J.Decoration.replace({widget:new qo(o,s[1],i,a,"date")})):(l=s.groups)!=null&&l.time&&u(i,a,J.Decoration.replace({widget:new qo(o,((h=s[1])==null?void 0:h.length)===5?s[1]+":00":s[1],i,a,"time")})))}catch(f){console.error(f)}}}),this.decorations=J.Decoration.none,this.view=r,this.updateDecorations(r)}update(r){this.updateDecorations(r.view,r)}destroy(){this.decorations=J.Decoration.none}updateDecorations(r,u){!u||this.decorations.size===0?this.decorations=this.match.createDeco(r):this.decorations=this.match.updateDeco(u,this.decorations)}isLivePreview(r){return r.field(O.editorLivePreviewField)}shouldRender(r,u,i){return!r.state.selection.ranges.some(s=>s.from<=u?s.to>=u:s.from<=i)&&this.isLivePreview(r.state)}}const t={decorations:n=>n.decorations.update({filter:(r,u,i)=>{const a=i.spec.widget;return a&&a.error?!1:r===u||!n.view.state.selection.ranges.filter(s=>{const o=s.from,c=s.to;return o<=r?c>=r:o<=u}).length}})};return J.ViewPlugin.fromClass(e,t)}const Cr=rt.Annotation.define();function Gg(e,t){const n=[],r=e.doc.lines,u=e.doc.lineAt(t);if(u.number===1)return null;const i=e.doc.line(u.number-1);if(!/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/g.test(i.text.trim()))return null;n.push(u.to);for(let a=u.number;a<=r;a++){const s=e.doc.line(a);if(!/^\s+/.test(s.text)||a===r||/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/g.test(s.text.trim())){const o=n.pop();if(o!==void 0)return{from:o,to:s.from-1}}}return null}function Xn(e){const t=[],n=[];for(let r=1;r<=e.doc.lines;r++){const u=e.doc.line(r);if(/^\s+/.test(u.text)&&!/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/g.test(u.text.trim())){const i=e.doc.line(r-1);if(!i||!/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/g.test(i.text.trim()))continue;n.push(u.to)}if(!/^\s+/.test(u.text)||r===e.doc.lines||/^(-|\*|(\d{1,}\.))(\s(\[.\]))?/g.test(u.text.trim())){const i=n.pop();i!==void 0&&u.from-1!==i&&t.push({from:i,to:u.from-1})}}return t}function Kg(e,t,n){const r=Gg(e,t);return!r||r.to<=r.from?null:r}const Jg=rt.StateField.define({create:e=>Xn(e),update(e,t){return t.docChanged?Xn(t.state):e}}),Qg=()=>rt.EditorState.transactionFilter.of(e=>{if(e.state.field(Lr,!1)){const u=e.state.field(Lr,!1);if(!e.effects.some(i=>i.is(Be)||i.is(xu)||i.is(pn)||i.is(er))&&u&&!e.docChanged){let i=!1;const a=[];if(e.state.selection.ranges.forEach(s=>{u.between(s.from,s.to,(o,c)=>{if(!o&&!c)return;const d=e.startState.selection,l=d.main.from>e.state.selection.main.from,h=d.main.to<e.state.selection.main.to,f=e.state.selection;l?f.main.from<=c&&(a.push(rt.EditorSelection.range(c,Math.max(c,s.to))),i=!0):h&&f.main.to>=o&&(a.push(rt.EditorSelection.range(Math.min(s.from,o),o)),i=!0)})}),i)return[e,{selection:rt.EditorSelection.create(a,e.state.selection.mainIndex)}]}}if(e.effects.some(u=>u.is(Vt.unfoldEffect))){const u=e.effects.map(a=>a.value),i=e.state.selection;return u.length===1?[e,{selection:rt.EditorSelection.create([rt.EditorSelection.range(u[0].to,u[0].to)],i.mainIndex)}]:e}if(e.effects.some(u=>u.is(Be)||u.is(xu)||u.is(pn))){const i=Xn(e.state).map(a=>Vt.unfoldEffect.of({from:a.from,to:a.to}));return[e,{effects:i,annotations:[Cr.of("outliner.unfold")]}]}if(e.effects.some(u=>u.is(er)))return e;const t=Xn(e.state),n=e.state.selection,r=t.find(u=>{const i=e.state.doc.lineAt(n.main.from),a=e.state.doc.lineAt(n.main.to),s=e.state.doc.lineAt(u.from),o=e.state.doc.lineAt(u.to);return i.number>=s.number&&i.number<=o.number||a.number>=s.number&&a.number<=o.number});if(r)return e.effects.some(u=>u.is(Vt.foldEffect))?[e,{effects:[Vt.foldEffect.of(r)],annotations:[Cr.of("outliner.fold")]}]:[e,{effects:[Vt.unfoldEffect.of(r)],annotations:[Cr.of("outliner.unfold")]}];if(!e.docChanged&&e.newSelection&&e.startState.selection!==e.newSelection){const i=Xn(e.state).map(a=>Vt.foldEffect.of({from:a.from,to:a.to}));return[e,{effects:i,annotations:[Cr.of("outliner.fold")]}]}return e}),Xg=[Qg(),Jg,Vt.foldService.of(Kg)];class t2 extends J.WidgetType{constructor(t,n,r){super(),this.view=t,this.from=n,this.to=r,this.error=!1}eq(t){return t.from===this.from&&t.to===this.to}toDOM(){const t=createEl("span",{cls:"cm-block-id-container"});return O.setIcon(t,"fingerprint"),t.onclick=n=>{const r=new O.Menu;r.addItem(u=>{u.setIcon("copy").setTitle("Copy").onClick(async()=>{const i=this.view.state.doc.sliceString(this.from,this.to).replace(/%%/g,"");await navigator.clipboard.writeText(i)})}),r.showAtMouseEvent(n)},t}}function e2(){class e{constructor(r){this.match=new J.MatchDecorator({regexp:/\^[a-zA-Z0-9-]{1,}$/g,decorate:(u,i,a,s,o)=>{const c=this.shouldRender(o,i,a);try{c&&i!==a&&u(i,a,J.Decoration.replace({widget:new t2(o,i,a),inclusive:!0}))}catch(d){console.error(d)}}}),this.decorations=J.Decoration.none,this.view=r,this.updateDecorations(r)}update(r){this.updateDecorations(r.view,r)}destroy(){this.decorations=J.Decoration.none}updateDecorations(r,u){!u||this.decorations.size===0?this.decorations=this.match.createDeco(r):this.decorations=this.match.updateDeco(u,this.decorations)}isLivePreview(r){return r.field(O.editorLivePreviewField)}shouldRender(r,u,i){return!r.state.selection.ranges.some(s=>s.from<=u?s.to>=u:s.from<=i)&&this.isLivePreview(r.state)}}const t={decorations:n=>n.decorations.update({filter:(r,u,i)=>{const a=i.spec.widget;return a&&a.error?!1:r===u||!n.view.state.selection.ranges.filter(s=>{const o=s.from,c=s.to;return o<=r?c>=r:o<=u}).length}})};return J.ViewPlugin.fromClass(e,t)}const n2=()=>rt.EditorState.transactionFilter.of(e=>{if(e.newSelection){const t=e.state.doc.sliceString(0,e.newSelection.ranges[0].from),n=/\^[a-zA-Z0-9-]+$/,r=t.match(n),u=e.newSelection;if(r){const i=r?{from:e.newSelection.ranges[0].from-r[0].length,to:e.newSelection.ranges[0].from}:null;return i?[e,{selection:rt.EditorSelection.create(u.ranges.map(a=>rt.EditorSelection.range(i==null?void 0:i.from,i.from)),u.mainIndex)}]:e}}return e});function r2(e){const t=e.embedRegistry.embedByExtension.md({app:e,containerEl:document.createElement("div")},null,"");t.editable=!0,t.showEditor();const n=Object.getPrototypeOf(Object.getPrototypeOf(t.editMode));return t.unload(),n.constructor}const Uo={cursorLocation:{anchor:0,head:0},value:"",cls:"",placeholder:"",view:void 0,type:"embed",foldByDefault:!0,disableTimeFormat:!1,path:"",toggleMode:()=>"",getViewType:()=>"",getDisplayText:()=>"",onFocus:()=>{},onEnter:()=>!1,onEscape:()=>{},onSubmit:()=>{},onBlur:()=>{},onPaste:()=>{},onChange:()=>{},onDelete:()=>!1,onIndent:()=>!1,onArrowUp:()=>!1,onArrowDown:()=>!1,onArrowLeft:()=>!1,onArrowRight:()=>!1};class _a extends r2(window.app){constructor(t,n,r){var i;super(t,n,{app:t,onMarkdownScroll:()=>{},toggleMode:()=>this.options.toggleMode(),getMode:()=>"source",getDisplayText:()=>this.options.getDisplayText()}),this.readOnlyDepartment=new rt.Compartment,this.KeepOnlyZoomedContentVisible=new qr,this.options={...Uo,...r},this.initial_value=this.options.value,this.scope=new O.Scope(this.app.scope),((i=this.options)==null?void 0:i.type)==="outliner"&&this.scope.register(["Mod"],"f",(a,s)=>{var o;return this.view&&((o=this.view)==null||o.search()),!0}),this.owner.editMode=this,this.owner.editor=this.editor,this.owner.getViewType=this.options.getViewType;const u=this;if(this.set(r.value||""),this.register(Oe(this.app.workspace,{setActiveLeaf:a=>(s,o)=>{this.activeCM.hasFocus||a.call(this.app.workspace,s,o)}})),this.register(Oe(this.editor.constructor.prototype,{getClickableTokenAt:a=>function(...s){const o=a.call(this,...s);if(o&&o.type==="tag"){const c=u.app.workspace.activeEditor.editMode.view;if(c.getViewType()!=="outliner-editor-view")return o;c==null||c.searchWithText(o.text);return}return o}})),this.options.onBlur!==Uo.onBlur&&this.editor.cm.contentDOM.addEventListener("blur",()=>{var a,s;this.app.keymap.popScope(this.scope),(this._loaded||((a=this.options)==null?void 0:a.type)==="embed")&&this.options.onBlur(this,(s=this.options)==null?void 0:s.path)}),this.editor.cm.contentDOM.addEventListener("focusin",a=>{this.app.keymap.pushScope(this.scope),this.app.workspace.activeEditor=this.owner,this._loaded&&this.options.onFocus(this)}),r.cls&&this.editorEl.classList.add(r.cls),r.cursorLocation&&this.editor.cm.dispatch({selection:rt.EditorSelection.range(r.cursorLocation.anchor,r.cursorLocation.head)}),this.view=this.options.view,this.editor.cm.contentDOM.toggleClass("embed-editor",this.options.type==="embed"),this.options.foldByDefault){const s=Xn(this.editor.cm.state).filter(o=>{o.from<o.to}).map(o=>Vt.foldEffect.of({from:o.from,to:o.to}));this.editor.cm.dispatch({effects:s,annotations:[Cr.of("outliner.fold")]})}}get value(){return this.editor.cm.state.doc.toString()}onUpdate(t,n){var r;super.onUpdate(t,n),n&&this.options.onChange(t,(r=this.options)==null?void 0:r.path)}buildLocalExtensions(){var n;const t=super.buildLocalExtensions();return t.push(rt.Prec.highest(J.keymap.of([{key:"Enter",run:r=>this.options.onEnter(this,!1,!1),shift:r=>this.options.onEnter(this,!1,!0)},{key:"Mod-Enter",run:r=>this.options.onEnter(this,!0,!1),shift:r=>this.options.onEnter(this,!0,!0)},{key:"Escape",run:r=>(this.options.onEscape(this),!0),preventDefault:!0},{key:"Backspace",run:r=>this.options.onDelete(this)},{key:"Delete",run:r=>this.options.onDelete(this)},{key:"Tab",run:r=>this.options.onIndent(this,!1,!1),shift:r=>this.options.onIndent(this,!1,!0)},{key:"ArrowLeft",run:r=>this.options.onArrowLeft(this,!1,!1),shift:r=>this.options.onArrowLeft(this,!1,!0)},{key:"ArrowRight",run:r=>this.options.onArrowRight(this,!1,!1),shift:r=>this.options.onArrowRight(this,!1,!0)},{key:"ArrowUp",run:r=>this.options.onArrowUp(this,!1,!1),shift:r=>this.options.onArrowUp(this,!1,!0)},{key:"ArrowDown",run:r=>this.options.onArrowDown(this,!1,!1),shift:r=>this.options.onArrowDown(this,!1,!0)},{key:"Mod-ArrowUp",run:r=>this.options.onArrowUp(this,!0,!1),shift:r=>this.options.onArrowUp(this,!0,!0)},{key:"Mod-ArrowDown",run:r=>this.options.onArrowDown(this,!0,!1),shift:r=>this.options.onArrowDown(this,!0,!0)}]))),t.push([this.readOnlyDepartment.of(rt.EditorState.readOnly.of(!1)),wp,rt.Prec.highest((n=this.KeepOnlyZoomedContentVisible)==null?void 0:n.getExtension()),gp(),Xg]),this.options.disableTimeFormat||t.push([zg()]),this.options.type==="outliner"&&t.push([em,bp,Kh,rm]),this.options.type==="embed"&&t.push([rt.Prec.default(e2()),n2()]),t}destroy(){this._loaded&&this.unload(),this.app.keymap.popScope(this.scope),this.app.workspace.activeEditor=null,this.containerEl.empty(),super.destroy()}onunload(){super.onunload(),this.destroy()}}function u2(e){return e.containerEl.matches(".tv-block.tv-leaf-view .workspace-leaf")}const Te="outliner-editor-view";class Hn extends O.TextFileView{constructor(t,n){super(t),this.plugin=n,this.filePath="",this.frontmatter="",this.fileContentData="",this.filteredValue="",this.KeepOnlyZoomedContentVisible=new qr,this.changedBySelf=!1,this.inlineTitleEl=createEl("div",{cls:"inline-title"}),this.hideCompleted=!1,this.app=this.plugin.app,this.scope=new O.Scope(this.app.scope)}getViewType(){return Te}getDisplayText(){var t;return((t=this.file)==null?void 0:t.basename)||this.filePath}setViewData(t,n){t.replace(this.frontmatter,"").trimStart()!==this.fileContentData.trimStart()&&this.editor&&(this.editor.replaceRange(t.replace(this.frontmatter,"").trimStart(),{line:0,ch:0},this.editor.offsetToPos(this.editor.cm.state.doc.length)),this.data=t)}getViewData(){return this.data||""}getIcon(){return"list"}clear(){}getSelection(){var t;return(t=this.editor)==null?void 0:t.getSelection()}onLoadFile(t){return super.onLoadFile(t)}onUnloadFile(t){return super.onUnloadFile(t)}onPaneMenu(t,n){super.onPaneMenu(t,n),t.addItem(r=>{var u,i;(i=(u=r.setIcon("file-edit").setTitle("Open as Markdown View").onClick(async()=>{this.plugin.outlinerFileModes[this.leaf.id]="markdown",await this.plugin.setMarkdownView(this.leaf)})).setSection)==null||i.call(u,"pane")})}async onRename(t){return(t==null?void 0:t.extension)!=="md"?super.onRename(t):(this.filePath=t.path,this.file=t,await this.setState({...this.getState(),file:t.path},{history:!1}),this.updateHeader(),super.onRename(t))}updateTitleBreadcrumbs(){var n,r;this.titleParentEl.empty();const t=(r=(n=this.file)==null?void 0:n.parent)==null?void 0:r.path;if(t&&t!=="/"){const u=t.split("/");u.forEach((i,a)=>{const s=u.slice(0,a+1).join("/");this.titleParentEl.createSpan({cls:"view-header-breadcrumb",text:i}).addEventListener("click",()=>{const c=this.app.internalPlugins.getEnabledPluginById("file-explorer");if(c){const d=this.app.vault.getAbstractFileByPath(s);d&&c.revealInFolder(d)}}),this.titleParentEl.createSpan({cls:"view-header-breadcrumb-separator",text:"/"})})}}updateHeader(){var t,n;this.titleEl.setText(((t=this.file)==null?void 0:t.basename)||this.filePath),this.leaf.updateHeader(),setTimeout(()=>{var r;this.leaf.tabHeaderInnerTitleEl.setText(((r=this.file)==null?void 0:r.basename)||this.filePath)},20),this.updateTitleBreadcrumbs(),this.titleEl.onclick=()=>{this.file&&this.app.fileManager.promptForFileRename(this.file)},this.inlineTitleEl.setText(((n=this.file)==null?void 0:n.basename)||this.filePath)}createEditor(t){const n=new _a(this.app,t,{onEnter:(r,u,i)=>{var a,s,o,c,d,l,h,f;if(!i){const{line:m,ch:b}=r.editor.getCursor(),g=r.editor.getLine(m),y=(a=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:a.getZoomRange(r.editor),C=ze(this.app);if(y){const E=y.from.line,S=y.to.line,T=((o=(s=r.editor.getLine(E))==null?void 0:s.match(/^\s*/))==null?void 0:o[0])||"",F=r.editor.getLine(S),v=r.editor.getCursor(),L=r.editor.getLine(v.line);if(/^((-|\*|\d+\.)(\s\[.\])?)/g.test(L.trim())){const M=v.line,$=((c=r.editor.getLine(M).match(/^\s*/))==null?void 0:c[0])||"";return r.editor.transaction({changes:[{text:`
${$}${$.length>T.length?"":C}- `,from:{line:v.line,ch:v.ch||0}}],selection:{from:{line:v.line+1,ch:2+`${$}${$.length>T.length?"":C}`.length},to:{line:v.line+1,ch:2+`${$}${$.length>T.length?"":C}`.length}}}),!0}const N=(d=F==null?void 0:F.match(/^\s*/))==null?void 0:d[0];if(/^((-|\*|\d+\.)(\s\[.\])?)$/g.test(F.trim())&&N===T+C)return r.editor.transaction({changes:[{text:`
${T}${C}- `,from:{line:S,ch:F.length||0}}],selection:{from:{line:S+1,ch:2+`${T}${C}`.length},to:{line:S+1,ch:2+`${T}${C}`.length}}}),!0}const B=m>0?r.editor.getLine(m-1):"";if(g.trimStart().startsWith("- ")){const E=Vt.foldable(r.editor.cm.state,r.editor.posToOffset({line:m,ch:0}),r.editor.posToOffset({line:m+1,ch:0})-1),S=((l=g.match(/^\s+/))==null?void 0:l[0])||"";if(b!==g.length||!E)return!1;if(E){const T=r.editor.cm.state.doc.line(m+1).to,F=E.to,v=r.editor.cm.state.doc.lineAt(F);let L=!1;for(let M=r.editor.cm.state.doc.lineAt(T+1).number;M<=v.number;M++)if(r.editor.cm.state.doc.line(M).text.trimStart().startsWith("- ")){L=!0;break}let N=T;for(let M=r.editor.cm.state.doc.lineAt(T+1).number;M<=v.number;M++)if(r.editor.cm.state.doc.line(M).text.trimStart().startsWith("- ")){N=r.editor.cm.state.doc.line(M).from-1;break}if(L){const M=`
${S}${L?C:""}- `,W=N,$=r.editor.cm.state.doc.lineAt(W);r.editor.transaction({changes:[{text:M,from:{line:$.number-1,ch:$.text.length}}]}),setTimeout(()=>{r.editor.transaction({selection:{from:{line:$.number,ch:2},to:{line:$.number,ch:2}}})},20)}else{const M=r.editor.cm.state.doc.lineAt(F);r.editor.transaction({changes:[{text:`
${S}- `,from:{line:M.number-1,ch:M.text.length}}]}),setTimeout(()=>{r.editor.transaction({selection:{from:{line:M.number,ch:0},to:{line:M.number,ch:0}}})},20)}return!0}return r.editor.transaction({changes:[{text:`
- `,from:{line:m,ch:b}}],selection:{from:{line:m,ch:b+3},to:{line:m,ch:b+3}}}),!0}else{if(!g.trim()&&/^(-|\*|\d+\.)(\s\[.\])?/g.test(B.trim()))return r.editor.transaction({changes:[{text:"- ",from:{line:m,ch:0}}],selection:{from:{line:m,ch:2},to:{line:m,ch:2}}}),!0;if(/^\s+/g.test(g)&&!/^(-|\*|\d+\.)(\s\[.\])?/g.test(g.trim())){const E=((h=g.match(/^\s+/))==null?void 0:h[0])||"";return r.editor.transaction({changes:[{text:`
${E}`,from:{line:m,ch:b}}],selection:{from:{line:m+1,ch:E.length},to:{line:m+1,ch:E.length}}}),!0}}if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(g.trim())){const E=Vt.foldable(r.editor.cm.state,r.editor.posToOffset({line:m,ch:0}),r.editor.posToOffset({line:m+1,ch:0})-1),S=ze(this.app),T=((f=g.match(/^\s+/))==null?void 0:f[0])||"";if(E){let F=!1;const v=r.editor.cm.state.doc.lineAt(E.from).number;for(let L=v+1;L<r.editor.cm.state.doc.lines;L++){const N=r.editor.cm.state.doc.line(L),M=N.text;if(/^\s+/.test(M)&&!/^(-|\*|\d+\.)\s/.test(M.trimStart()))F=!0;else return F?r.editor.cm.state.doc.line(L-1).to===E.to?(r.editor.transaction({changes:[{text:`${T}- 
`,from:{line:L-1,ch:0}}]}),r.editor.cm.dispatch({selection:{head:N.from,anchor:N.from}}),!0):(r.editor.cm.dispatch({changes:{insert:`${T}${S}- 
`,from:N.from}}),r.editor.cm.dispatch({selection:{head:N.from,anchor:N.from}}),!0):!1}}return!1}}if(i){const{line:m,ch:b}=r.editor.getCursor(),g=r.editor.posToOffset({line:m,ch:b}),y=r.editor.cm.state.doc.lineAt(g);if(/^\s+/g.test(y.text)&&!/^(-|\*|\d+\.)(\s\[.\])?/g.test(y.text.trimStart())){const C=y.number;for(let B=C;B>=1;B--){const S=r.editor.cm.state.doc.line(B).text;if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(S.trimStart())){const T=r.editor.cm.state.doc.line(B);return r.editor.cm.dispatch({selection:{head:T.to,anchor:T.to},annotations:$e.of("arrow.up.selection")}),!0}}}else if(/^(-|\*|\d+\.)(\s\[.\])?/g.test(y.text.trimStart())){const C=y.number;let B=!1;for(let E=C+1;E<r.editor.cm.state.doc.lines;E++){const T=r.editor.cm.state.doc.line(E).text;if(/^\s+/.test(T)&&!/^(-|\*|\d+\.)\s/.test(T.trimStart()))B=!0;else{if(B){const F=r.editor.cm.state.doc.line(E-1);return r.editor.cm.dispatch({selection:{head:F.to,anchor:F.to},annotations:$e.of("arrow.up.selection")}),!0}return!1}}if(B){const E=r.editor.cm.state.doc.line(r.editor.cm.state.doc.lines-1);return r.editor.cm.dispatch({selection:{head:E.to,anchor:E.to}}),!0}return!1}}return!1},onDelete:r=>{var d;const{line:u,ch:i}=r.editor.getCursor(),a=r.editor.getLine(u),s=r.editor.posToOffset({line:u,ch:0}),o=r.editor.posToOffset({line:u+1,ch:0})-1,c=Vt.foldable(r.editor.cm.state,s,o);if(/^(\s*?)((-|\*|\d+\.)(\s\[.\])?)\s/g.test(a)&&/^((-|\*|\d+\.)(\s\[.\])?)$/g.test(a.trim())){if(u===0||c)return!0;const l=(d=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:d.getZoomRange(r.editor);return l&&l.from.line===u||r.editor.transaction({changes:[{text:"",from:{line:u-1,ch:r.editor.getLine(u-1).length},to:{line:u,ch:i}}]}),!0}else if(/^\s+$/g.test(a))return r.editor.transaction({changes:[{text:"",from:{line:u-1,ch:r.editor.getLine(u-1).length},to:{line:u,ch:i}}]}),!0;return!1},onIndent:(r,u,i)=>{var a,s,o,c;if(i){const d=(a=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:a.getZoomRange(r.editor);if(d){const l=d.from.line,h=d.to.line,f=(o=(s=r.editor.getLine(l))==null?void 0:s.match(/^\s*/))==null?void 0:o[0],m=r.editor.getLine(h),b=(c=m==null?void 0:m.match(/^\s*/))==null?void 0:c[0],g=ze(this.app);if(l===h||f===b||b===f+g)return!0}}return!1},onArrowUp:(r,u,i)=>{if(i){let a=r.editor.cm.state.doc.lineAt(r.editor.cm.state.selection.main.from);r.editor.cm.state.selection.ranges[0].from===a.from&&(a=r.editor.cm.state.doc.lineAt(a.from-1));const o=Vt.foldable(r.editor.cm.state,a.from,a.to);return o?/^(-|\*|\d{1,}\.)/.test(a.text.trim())?(r.editor.cm.dispatch({selection:{head:o.from-a.length,anchor:o.to},annotations:$e.of("arrow.up.selection")}),!0):(r.editor.cm.dispatch({selection:{head:o.from-a.length,anchor:a.to},annotations:$e.of("arrow.up.selection")}),!0):(r.editor.cm.dispatch({selection:{head:r.editor.cm.state.doc.line(a.number).to,anchor:r.editor.cm.state.doc.line(a.number).from},annotations:$e.of("arrow.up.selection")}),!0)}return!1},onArrowDown:(r,u,i)=>!1,getDisplayText:()=>this.getDisplayText(),getViewType:()=>this.getViewType(),onChange:r=>{this.data=`${this.frontmatter}

`+r.state.doc.toString(),this.requestSave()},value:this.fileContentData||"- ",view:this,type:"outliner",foldByDefault:!0,disableTimeFormat:!this.plugin.settings.timeFormatWidget});return this.editor=n.editor,this.editor.getValue=()=>this.data||"",this.addChild(n)}async setState(t,n){if(t&&typeof t=="object"&&"file"in t){this.filePath=t.file;const r=this.app.vault.getFileByPath(t.file);if(r){const u=await this.app.vault.read(r),i=/^---\n[\s\S]*\n---/m.exec(u);let a=u;if(i){const s=i.index+i[0].length;this.frontmatter=i[0],a=u.substring(s)}this.fileContentData=a.trimStart();try{this.loadEditor()}catch{}setTimeout(()=>{var o;if(!this.editor)return;this.editor.focus();const s=this.editor.getValue();this.editor.setCursor(s.length-1),(o=this.editor.editorComponent.sizerEl)==null||o.prepend(this.inlineTitleEl),this.inlineTitleEl.setText((r==null?void 0:r.basename)||this.filePath)},200)}}await super.setState(t,n)}getState(){return{file:this.filePath}}loadEditor(){const t=this.containerEl.children[1];t.empty(),t.style.display="flex",t.style.flexDirection="column",t.style.gap="1%";const n=t.createDiv({cls:"outliner-editor "});n.style.height="100%",this.createEditor(n)}filter(t,n){var u,i,a;if(n===""){if((u=this.plugin.KeepOnlyZoomedContentVisible)==null||u.showAllContent(t),this.filteredValue="",this.contentEl.toggleClass("filtered",!1),!this.editor)return;t.dispatch({effects:Ii.of()});return}const r=this.plugin.calculateRangeForZooming.calculateRangesBasedOnSearch(t,((i=this.editor)==null?void 0:i.getAllFoldableLines())||[],n);this.filteredValue=n,(a=this.plugin.KeepOnlyZoomedContentVisible)==null||a.keepRangesVisible(t,r),this.contentEl.toggleClass("filtered",!0)}hideCompletedItems(t){var r;if(this.hideCompleted){(r=this.plugin.KeepOnlyZoomedContentVisible)==null||r.showAllContent(t),this.hideCompleted=!1;return}const n=this.plugin.calculateRangeForZooming.calculateRangesBasedOnType(t,"completed");this.editor&&(this.editor.cm.dispatch({effects:[wc.of({ranges:n})]}),this.hideCompleted=!0)}registerSearchActionBtn(){const t=this.addAction("check","Show Completed",n=>{this.editor&&this.hideCompletedItems(this.editor.cm),t.toggleClass("hide-completed",this.hideCompleted)});this.searchActionEl=this.addAction("search","Search",n=>{var s;const r=new O.Menu;r.dom.toggleClass("search-menu",!0);let u=!1;if(r.addItem(o=>{const c=o.dom;o.setIsLabel(!0);const d=new O.Setting(c).setName("Filter").addSearch(l=>{l.setValue(this.filteredValue).onChange(h=>{u||(this.editor&&this.editor.cm.dispatch({effects:Ii.of()}),this.editor&&this.filter(this.editor.cm,h))}),l.clearButtonEl.addEventListener("click",()=>{var h;this.filter((h=this.editor)==null?void 0:h.cm,""),r.hide()}),l.inputEl.addEventListener("compositionstart",()=>{u=!0}),l.inputEl.addEventListener("compositionend",()=>{u=!1,this.editor&&this.filter(this.editor.cm,l.inputEl.value)})});o.onClick(l=>{l.preventDefault(),l.stopImmediatePropagation(),d.components[0].inputEl.focus()})}),!this.searchActionEl)return;const{x:i,y:a}=this.searchActionEl.getBoundingClientRect();r.showAtPosition({x:i+this.searchActionEl.offsetHeight,y:a+this.searchActionEl.offsetHeight}),r.onHide(()=>{this.editor&&this.editor.focus()}),(s=document.body.find(".search-menu input"))==null||s.focus()}),this.clearFilterBtn=this.addAction("filter-x","Clear Filter",n=>{this.editor&&this.filter(this.editor.cm,"")}),this.clearFilterBtn.toggleClass("filter-clear",!0)}search(){var t;this.searchActionEl&&(this.searchActionEl.click(),(t=document.body.find(".search-menu input"))==null||t.focus())}searchWithText(t){if(!this.searchActionEl)return;this.searchActionEl.click();const n=document.body.find(".search-menu input");n==null||n.focus(),n.value=t,n.dispatchEvent(new Event("input"))}async onOpen(){this.load(),this.registerSearchActionBtn()}onunload(){super.onunload(),this.searchActionEl&&this.searchActionEl.detach(),this.clearFilterBtn&&this.clearFilterBtn.detach(),this.filteredValue=""}}const i2=(e,t,n)=>{const r=e.posToOffset({line:t,ch:0}),u=e.cm.state.doc.lineAt(r);return oi(u.text)?s2(e,u.number):$r(u.text)?o2(e,u.number):!1},a2=(e,t,n,r)=>{const u=t>0?e.getLine(t-1):"",i=ze(e.app);return r.startsWith("- ")?(c2(e,t,n),!0):!r.trim()&&$r(u.trim())?(d2(e,t),!0):oi(r)?(l2(e,t,n,r),!0):$r(r.trim())?f2(e,t,r,i):!1},oi=e=>/^\s+/g.test(e)&&!$r(e.trim()),$r=e=>/^(-|\*|\d+\.)(\s\[.\])?/g.test(e),s2=(e,t)=>{for(let n=t-1;n>=1;n--){const r=e.cm.state.doc.line(n).text;if($r(r.trim())){const u=e.cm.state.doc.line(n);return e.cm.dispatch({selection:{head:u.to,anchor:u.to},annotations:$e.of("arrow.up.selection")}),!0}}return!1},o2=(e,t)=>{let n=!1;for(let r=t+1;r<=e.cm.state.doc.lines;r++){const u=e.cm.state.doc.line(r).text;if(oi(u))n=!0;else if(n){const i=e.cm.state.doc.line(r-1);return e.cm.dispatch({selection:{head:i.to,anchor:i.to},annotations:$e.of("arrow.up.selection")}),!0}}if(n){const r=e.cm.state.doc.line(e.cm.state.doc.lines);return e.cm.dispatch({selection:{head:r.to,anchor:r.to}}),!0}return!1},c2=(e,t,n)=>{e.transaction({changes:[{text:`
- `,from:{line:t,ch:n}}],selection:{from:{line:t,ch:n+3},to:{line:t,ch:n+3}}})},d2=(e,t)=>{e.transaction({changes:[{text:"- ",from:{line:t,ch:0}}],selection:{from:{line:t,ch:2},to:{line:t,ch:2}}})},l2=(e,t,n,r)=>{var i;const u=((i=r.match(/^\s+/))==null?void 0:i[0])||"";e.transaction({changes:[{text:`
${u}`,from:{line:t,ch:n}}],selection:{from:{line:t+1,ch:u.length},to:{line:t+1,ch:u.length}}})},f2=(e,t,n,r)=>{var o;const u=Vt.foldable(e.cm.state,e.posToOffset({line:t,ch:0}),e.posToOffset({line:t+1,ch:0})-1),i=((o=n.match(/^\s+/))==null?void 0:o[0])||"";if(!u)return!1;let a=!1;const s=e.cm.state.doc.lineAt(u.from).number;for(let c=s+1;c<e.cm.state.doc.lines;c++){const d=e.cm.state.doc.line(c).text;if(oi(d))a=!0;else if(a)return h2(e,c,i,r,u)}return!1},h2=(e,t,n,r,u)=>{const i=e.cm.state.doc.line(t-1),a=`${n}${i.to===u.to?`- 
`:`${r}- 
`}`;return e.transaction({changes:[{text:a,from:{line:t-1,ch:0}}]}),e.cm.dispatch({selection:{head:e.cm.state.doc.line(t).from,anchor:e.cm.state.doc.line(t).from}}),!0};class Ti extends O.Component{constructor(t,n,r,u,i,a){super(),this.targetRange=i,this.initData=a,this.calculateRangeForZooming=new wa,this.KeepOnlyZoomedContentVisible=new qr,this.updateFile=O.debounce(async s=>{var o,c;if(s.path===((o=this.file)==null?void 0:o.path)){const d=await this.app.vault.read(s);if(this.data===d)return;this.data=d,(c=this.editor)==null||c.replaceRange(d,{line:0,ch:0},this.editor.offsetToPos(this.editor.cm.state.doc.length));const l=this.getRange(s);this.range={from:l.from,to:l.to},l.type!=="whole"&&this.updateVisibleRange(this.editor,this.range,l.type),l.type==="whole"&&this.plugin.settings.hideFrontmatter&&this.updateFrontMatterVisible(this.editor,this.file)}},800),this.debounceHover=O.debounce((s,o)=>{this.file&&this.app.workspace.trigger("hover-link",{event:o,source:"outliner-md",hoverParent:this.containerEl,targetEl:s.extraSettingsEl,linktext:this.file.path})},200),this.requestSave=O.debounce(async(s,o)=>{s&&(this.data=o,await this.app.vault.modify(s,o))},400),this.longWaitUpdate=O.debounce(s=>{this.data=s}),this.plugin=t,this.app=n.app,this.containerEl=n.containerEl,this.sourcePath=n.sourcePath,this.file=r,this.subpath=u}async onload(){super.onload();const t=this.file;t&&!this.initData&&this.app.vault.read(t).then(n=>{if(this.data=n,!t)return;const r=this.getRange(t);this.createEditor(this.containerEl,t==null?void 0:t.path,this.data||"",r)}),this.initData&&t&&this.targetRange&&(this.data=this.initData,this.createEditor(this.containerEl,t==null?void 0:t.path,this.data||"",{...this.targetRange,type:"part"})),this.registerEvent(this.app.metadataCache.on("changed",n=>{this.updateFile(n)}))}async onunload(){super.onunload()}onFileChanged(t){}loadFile(t,n){}createEditor(t,n,r,u){var s,o,c;const i=new _a(this.app,t,{onEnter:(d,l,h)=>{if(!d.view)return!1;const f=d.editor,m=f.getCursor(),{line:b,ch:g}=m,y=f.getLine(b);return h?i2(f,b):a2(f,b,g,y)},onDelete:d=>{var y;if(!d.view)return!1;const{line:l,ch:h}=d.editor.getCursor(),f=d.editor.getLine(l),m=d.editor.posToOffset({line:l,ch:0}),b=d.editor.posToOffset({line:l+1,ch:0})-1,g=Vt.foldable(d.editor.cm.state,m,b);if(/^(\s*?)((-|\*|\d+\.)(\s\[.\])?)\s/g.test(f)&&/^((-|\*|\d+\.)(\s\[.\])?)$/g.test(f.trim())){if(l===0||g)return!0;const C=(y=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:y.getZoomRange(d.editor);return C&&C.from.line===l||d.editor.transaction({changes:[{text:"",from:{line:l-1,ch:d.editor.getLine(l-1).length},to:{line:l,ch:h}}]}),!0}else if(/^\s+$/g.test(f))return d.editor.transaction({changes:[{text:"",from:{line:l-1,ch:d.editor.getLine(l-1).length},to:{line:l,ch:h}}]}),!0;return!1},onIndent:(d,l,h)=>{var f,m,b,g;if(!d.view)return!1;if(h){const y=(f=this.app.plugins.getPlugin("obsidian-zoom"))==null?void 0:f.getZoomRange(d.editor);if(y){const C=y.from.line,B=y.to.line,E=(b=(m=d.editor.getLine(C))==null?void 0:m.match(/^\s*/))==null?void 0:b[0],S=d.editor.getLine(B),T=(g=S==null?void 0:S.match(/^\s*/))==null?void 0:g[0],F=ze(this.app);if(C===B||E===T||T===E+F)return!0}}return!1},onBlur:(d,l)=>{var h;if(this.file&&this.targetRange){const f=(h=d.editor)==null?void 0:h.cm.state.doc.toString();if(this.data===f)return;this.requestSave(this.file,f)}},onChange:(d,l)=>{if(l){if(!d.docChanged||this.data===d.state.doc.toString())return;!this.targetRange&&this.file&&this.requestSave(this.file,d.state.doc.toString())}},toggleMode:()=>{const d=this.containerEl.cmView;d&&d.widget.editor.owner.toggleMode()},type:"embed",value:r||"",path:(s=this.file)==null?void 0:s.path,disableTimeFormat:!this.plugin.settings.timeFormatWidget});this.range={from:u.from,to:u.to},this.editor=i.editor,this.targetRange&&!this.plugin.settings.livePreviewForBacklinks&&((o=this.editor)==null||o.editorComponent.toggleSource()),this.targetRange&&this.containerEl&&this.containerEl.createEl("div",{cls:"backlink-btn"},d=>{const l=new O.ExtraButtonComponent(d).setIcon("file-symlink");this.registerDomEvent(l.extraSettingsEl,"mouseover",h=>{var b;if(!this.file||!this.targetRange)return;const f=(b=this.editor)==null?void 0:b.cm.state.doc.lineAt(this.targetRange.from+1);if(!f)return;const m={scroll:f.number};this.app.workspace.trigger("hover-link",{event:h,source:"outliner-md",hoverParent:this.containerEl,targetEl:l.extraSettingsEl,linktext:this.file.path,state:m})})}),u.type!=="whole"&&this.updateVisibleRange(i.editor,u,u.type),u.type==="whole"&&this.plugin.settings.hideFrontmatter&&this.updateFrontMatterVisible(this.editor,this.file);const a=this.containerEl.getAttr("alt");if(a&&(a==="readonly"||a.contains("readonly"))){(c=this.editor)==null||c.cm.dispatch({effects:[i.readOnlyDepartment.reconfigure(rt.EditorState.readOnly.of(!0))]}),this.containerEl.toggleClass("readonly",!0);const d=this.containerEl.createEl("div",{cls:"lock-btn"});let l=!0;const h=new O.ExtraButtonComponent(d).setIcon("lock").onClick(()=>{var f;(f=this.editor)==null||f.cm.dispatch({effects:i.readOnlyDepartment.reconfigure(rt.EditorState.readOnly.of(!l))}),l=!l,h.setIcon(l?"lock":"unlock"),this.containerEl.toggleClass("readonly",l)})}if(u.type!=="part"){const d=this.containerEl.createEl("div",{cls:"source-btn embedded-editor-btn"}),l=new O.ExtraButtonComponent(d).setIcon("link");this.registerDomEvent(l.extraSettingsEl,"click",async h=>{if(O.Keymap.isModEvent(h)){const f=this.app.workspace.getLeaf();await f.setViewState({type:"markdown"}),this.file&&await f.openFile(this.file)}}),this.registerDomEvent(l.extraSettingsEl,"mouseover",h=>this.debounceHover(l,h))}return this.addChild(i)}updateRange(t){this.range={from:t.from,to:t.to},this.updateVisibleRange(this.editor,t,"part")}updateVisibleRange(t,n,r){var u,i,a,s;if(Array.isArray(n)){const o=n.map(c=>this.calculateRangeForZooming.calculateRangeForZooming(t.cm.state,c.from)||{from:c.from,to:c.to});this.KeepOnlyZoomedContentVisible.keepRangesVisible(t.cm,o);for(const c of o){const l=(u=t.cm.state.doc.lineAt(c.from).text.match(/^\s*/))==null?void 0:u[0];l&&t.cm.dispatch({effects:pn.of({range:{from:c.from,to:c.to},indent:l})})}}else{if(r==="part"){const c=this.app.metadataCache.getFileCache(this.file);((i=c==null?void 0:c.sections)==null?void 0:i.find(l=>l.type==="table"&&l.position.start.offset<n.from&&l.position.end.offset>n.to))&&((a=this.editor)==null||a.editorComponent.toggleSource()),t.cm.dispatch({effects:[Be.of({from:n.from,to:n.to,type:"part",container:this.targetRange?void 0:this.containerEl})]}),this.containerEl.toggleClass("embedded-part",!0);return}const o=this.calculateRangeForZooming.calculateRangeForZooming(t.cm.state,n.from);if(o){t.cm.dispatch({effects:[Be.of(o)]});const d=(s=t.cm.state.doc.lineAt(o.from).text.match(/^\s*/))==null?void 0:s[0];d&&t.cm.dispatch({effects:pn.of({range:{from:o.from,to:o.to},indent:d})})}else t.cm.dispatch({effects:[Be.of(n)]})}}getRange(t){var u,i,a,s;const n=this.app.metadataCache.getFileCache(t);if(!this.subpath&&this.targetRange)return{from:this.targetRange.from,to:this.targetRange.to,type:"part"};if(!this.subpath){const o=(u=this.containerEl.getAttr("alt"))==null?void 0:u.replace("readonly","");if(o){const c=this.data,d=`%%${o}%%`;if(!c)return{from:0,to:0,type:"whole"};const l=c.indexOf(d),h=c.indexOf(d,l+1);if(l!==-1&&h!==-1)return{from:l+d.length+1,to:h-1,type:"part"}}}let r={from:0,to:((i=this.data)==null?void 0:i.length)||0,type:"whole"};if(n&&this.subpath){if(/#\^/.test(this.subpath)){const o=this.subpath.slice(2),c=Object.values((n==null?void 0:n.blocks)||{}).find(d=>d.id===o);return c&&(r={from:(a=c.position)==null?void 0:a.start.offset,to:(s=c.position)==null?void 0:s.end.offset,type:"block"}),r}else if(/^#/.test(this.subpath)){const o=this.subpath.slice(1),c=Object.values((n==null?void 0:n.headings)||{}).find(d=>o.trim()&&d.heading.replace(/((\[\[)|(\]\]))/g,"").trim()===o.trim());return c&&(r={from:c.position.start.offset,to:c.position.end.offset,type:"heading"}),r}}return r}updateIndentVisible(t,n){var i;const u=(i=t.cm.state.doc.lineAt(n.from).text.match(/^\s*/))==null?void 0:i[0];u&&t.cm.dispatch({effects:pn.of({range:{from:n.from,to:n.to},indent:u})})}updateFrontMatterVisible(t,n){const r=this.app.metadataCache.getFileCache(n),u=r==null?void 0:r.frontmatterPosition;u&&t.cm.dispatch({effects:Ec.of({range:{from:u.start.offset,to:u.end.offset}})})}}class m2 extends O.Component{constructor(t,n,r){super(),this.updateFile=O.debounce(u=>{var i;u.path===((i=this.file)==null?void 0:i.path)&&this.app.vault.read(u).then(async a=>{if(this.data===a)return;this.data=a;const s=this.getRange(u);this.range={from:s.from,to:s.to},this.childComponent&&await this.updateContent(a,this.containerEl,u.path,this.childComponent)})},800),this.app=t.app,this.containerEl=t.containerEl,this.sourcePath=t.sourcePath||n.path||"",this.file=n,this.subpath=r}async onload(){super.onload();const t=this.file;t&&this.app.vault.read(t).then(n=>{if(this.data=n,!t)return;const r=this.getRange(t);this.createEmbed(this.containerEl,t==null?void 0:t.path,this.data||"",r)}),this.registerEvent(this.app.metadataCache.on("changed",n=>{this.childComponent&&this.updateFile(n)}))}async onunload(){super.onunload()}onFileChanged(t){}loadFile(t,n){}async updateContent(t,n,r,u){await O.MarkdownRenderer.render(this.app,t,n,r,u)}async createEmbed(t,n,r,u){this.childComponent=new O.Component;const i=r.slice(u.from,u.to+1);await O.MarkdownRenderer.render(this.app,i,t,n,this.childComponent),this.range={from:u.from,to:u.to};const a=this.containerEl.createEl("div",{cls:"source-btn embedded-rendered-btn"});this.containerEl.toggleClass("embedded-part",!0);const s=new O.ExtraButtonComponent(a).setIcon("link").onClick(async()=>{const o=this.app.workspace.getLeaf();await o.setViewState({type:"markdown"}),this.file&&await o.openFile(this.file)});return this.registerDomEvent(s.extraSettingsEl,"mouseover",o=>{this.file&&this.app.workspace.trigger("hover-link",{event:o,source:"outliner-md",hoverParent:this.containerEl,targetEl:s.extraSettingsEl,linktext:this.file.path})}),this.addChild(this.childComponent)}getRange(t){var u,i,a,s;const n=this.app.metadataCache.getFileCache(t);if(this.sourcePath&&!this.subpath){const o=(u=this.containerEl.getAttr("alt"))==null?void 0:u.replace("readonly","");if(o){const c=this.data,d=`%%${o}%%`;if(!c)return{from:0,to:0,type:"whole"};const l=c.indexOf(d),h=c.indexOf(d,l+1);if(l!==-1&&h!==-1)return{from:l+d.length,to:h-1,type:"part"}}}let r={from:0,to:((i=this.data)==null?void 0:i.length)||0,type:"whole"};if(n&&this.subpath){if(/#\^/.test(this.subpath)){const o=this.subpath.slice(2),c=Object.values((n==null?void 0:n.blocks)||{}).find(d=>d.id===o);return c&&(r={from:(a=c.position)==null?void 0:a.start.offset,to:(s=c.position)==null?void 0:s.end.offset,type:"block"}),r}else if(/^#/.test(this.subpath)){const o=this.subpath.slice(1),c=Object.values((n==null?void 0:n.headings)||{}).find(d=>o.trim()&&d.heading.trim()===o.trim());return c&&(r={from:c.position.start.offset,to:c.position.end.offset,type:"heading"}),r}}return r}}async function Yo(e,t,n){var a;const r=e.state.field(O.editorInfoField),u=e.state.doc.sliceString(t.from,t.to).replace(/%%/g,"");if(!r||!r.file)return;const i=r.app.fileManager.generateMarkdownLink(r.file,((a=r.file)==null?void 0:a.path)||"","",u);await navigator.clipboard.writeText((n==="embed"?"!":"")+i)}class p2 extends J.WidgetType{constructor(t,n,r){super(),this.view=t,this.from=n,this.to=r,this.error=!1}eq(t){return t.from===this.from&&t.to===this.to}toDOM(){const t=createEl("span",{cls:"cm-text-fragment-container"});return O.setIcon(t,"book-dashed"),t.onclick=n=>{const r=new O.Menu;r.addItem(u=>{u.setIcon("copy").setTitle("Copy link to embed text fragment").onClick(async()=>{Yo(this.view,{from:this.from,to:this.to},"embed")})}),r.addItem(u=>{u.setIcon("copy").setTitle("Copy link to text fragment").onClick(async()=>{Yo(this.view,{from:this.from,to:this.to},"link")})}),r.showAtMouseEvent(n)},t}}function g2(){class e{constructor(r){this.match=new J.MatchDecorator({regexp:/%%(o-([^%]*))%%/g,decorate:(u,i,a,s,o)=>{const c=this.shouldRender(o,i,a);try{c&&i!==a&&u(i,a,J.Decoration.replace({widget:new p2(o,i,a),inclusiveEnd:!0}))}catch(d){console.error(d)}}}),this.decorations=J.Decoration.none,this.view=r,this.updateDecorations(r)}update(r){this.updateDecorations(r.view,r)}destroy(){this.decorations=J.Decoration.none}updateDecorations(r,u){!u||this.decorations.size===0?this.decorations=this.match.createDeco(r):this.decorations=this.match.updateDeco(u,this.decorations)}isLivePreview(r){return r.field(O.editorLivePreviewField)}shouldRender(r,u,i){return!r.state.selection.ranges.some(s=>s.from<=u?s.to>=u:s.from<=i)&&this.isLivePreview(r.state)}}const t={decorations:n=>n.decorations.update({filter:(r,u,i)=>{const a=i.spec.widget;return a&&a.error?!1:r===u||!n.view.state.selection.ranges.filter(s=>{const o=s.from,c=s.to;return o<=r?c>=r:o<=u}).length}})};return J.ViewPlugin.fromClass(e,t)}const Zo={editableBacklinks:!0,livePreviewForBacklinks:!1,editableBlockEmbeds:!0,hideFrontmatter:!1,timeFormatWidget:!0,dragAndDrop:!0,noteAsNotebook:!1,markForSplitSections:"%%SECTION{NAME}%%",showFullBtnAtLeftSide:!1,alwaysShowSectionHeader:!0,showSectionTitle:!0,autoHideEmptySectionHeader:!1,markForSplitPages:"++PAGE{NAME}++",foldTaskGroup:!0,taskGroupQuery:"#now",paperLayout:!0};class D2 extends O.PluginSettingTab{constructor(t,n){super(t,n),this.debounceApplySettingsUpdate=O.debounce(async()=>{await this.plugin.saveSettings()},200,!0),this.debounceDisplay=O.debounce(async()=>{await this.display()},400,!0),this.plugin=n}applySettingsUpdate(){this.debounceApplySettingsUpdate()}async display(){await this.plugin.loadSettings();const{containerEl:t}=this,n=this.plugin.settings;t.toggleClass("outliner-view-setting-container",!0),t.empty(),this.initGlobalSettings(t,n),this.initBacklinkSettings(t,n),this.initBlockEmbedSettings(t,n),this.initPageBookSettings(t,n),this.initTaskGroupSettings(t,n),this.styleSettings(t,n)}initGlobalSettings(t,n){const r=document.createDocumentFragment();r.createEl("div",{text:"You can disable some internal editor plugins from Outliner.md."});const u=document.createDocumentFragment();u.createEl("span","",s=>{O.setIcon(s,"pencil")}),u.createEl("span",{text:" Editor"}),new O.Setting(t).setHeading().setName(u).setDesc(r);const i=new O.Setting(t).setName("Time picker").addButton(s=>s.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(s=>s.setValue(n.timeFormatWidget).onChange(async o=>{n.timeFormatWidget=o,this.applySettingsUpdate(),i.settingEl.toggleClass("show-reload-button",!0)})),a=new O.Setting(t).setName("Drag and drop [Experimental]").addButton(s=>s.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(s=>s.setValue(n.dragAndDrop).onChange(async o=>{n.dragAndDrop=o,this.applySettingsUpdate(),a.settingEl.toggleClass("show-reload-button",!0)}))}initBacklinkSettings(t,n){const r=document.createDocumentFragment();r.createEl("div",{text:"Backlinks are the links that point to the current note. You can make backlinks editable and enable live preview for backlinks."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Live preview for backlinks",attr:{href:"https://docs.outliner.md/pages/20240514151617"}}),r.createEl("span",{text:"After "},a=>{a.createEl("a",{text:"0.1.9",attr:{href:"https://github.com/quorafind/outliner.md/releases/tag/0.1.9"}}),a.createEl("span",{text:", you can edit search result and also query in page."})});const u=document.createDocumentFragment();u.createEl("span","",a=>{O.setIcon(a,"link")}),u.createEl("span",{text:" Backlink & Search result"}),new O.Setting(t).setHeading().setName(u).setDesc(r);const i=new O.Setting(t).setName("Editable backlinks/search result").addButton(a=>a.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(a=>a.setValue(n.editableBacklinks).onChange(async s=>{n.editableBacklinks=s,this.applySettingsUpdate(),i.settingEl.toggleClass("show-reload-button",!0)}));if(n.editableBacklinks){const a=new O.Setting(t).setName("Live preview for backlinks").addButton(s=>s.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(s=>s.setValue(n.livePreviewForBacklinks).onChange(async o=>{n.livePreviewForBacklinks=o,this.applySettingsUpdate(),a.settingEl.toggleClass("show-reload-button",!0)}))}}initBlockEmbedSettings(t,n){const r=document.createDocumentFragment();r.createEl("div",{text:"Block embeds are the blocks that are embedded in the current note. You can make block embeds editable."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Block embeds",attr:{href:"https://docs.outliner.md/pages/20240517162521"}});const u=document.createDocumentFragment();u.createEl("span","",a=>{O.setIcon(a,"toy-brick")}),u.createEl("span",{text:" Block embed"}),new O.Setting(t).setHeading().setName(u).setDesc(r);const i=new O.Setting(t).setName("Editable block embeds").setDesc(r).addButton(a=>a.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(a=>a.setValue(n.editableBlockEmbeds).onChange(async s=>{n.editableBlockEmbeds=s,this.applySettingsUpdate(),i.settingEl.toggleClass("show-reload-button",!0)}));if(n.editableBlockEmbeds){const a=new O.Setting(t).setName("Hide frontmatter").addButton(s=>s.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(s=>s.setValue(n.hideFrontmatter).onChange(async o=>{n.hideFrontmatter=o,this.applySettingsUpdate(),a.settingEl.toggleClass("show-reload-button",!0)}))}}initPageBookSettings(t,n){const r=document.createDocumentFragment();r.createEl("span","",i=>{O.setIcon(i,"book-open-text")}),r.createEl("span",{text:"Note as notebook"},i=>{i.createEl("span",{text:"  "}),i.createEl("mark",{text:" new!"})});const u=document.createDocumentFragment();if(u.createEl("div",{text:"Turn the note into a notebook, you can split the note into sections. You need to reload the Obsidian after changing this setting."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Note as book",attr:{href:"https://docs.outliner.md/pages/20240517162521"}}),new O.Setting(t).setHeading().setName(r).setDesc(u),new O.Setting(t).setName("Turn the note into a notebook").addToggle(i=>i.setValue(n.noteAsNotebook).onChange(async a=>{n.noteAsNotebook=a,this.applySettingsUpdate(),this.debounceDisplay()})),n.noteAsNotebook){const i=document.createDocumentFragment();i.createEl("p",{text:"The mark to split the note into sections. Currently, it is not configurable."}),i.createEl("strong",{text:"By default, it is "}),i.createEl("code",{text:"%%SECTION{NAME}%%"}),i.createEl("strong",{text:". Or section mark from lineage: "}),i.createEl("code",{text:"<!--section: DIGIT-->"}),i.createEl("span",{text:" Note: "}).createEl("a",{text:"Lineage",attr:{href:"https://github.com/ycnmhd/obsidian-lineage"}}),new O.Setting(t).setName("Mark to split the note into sections").setDesc(i),new O.Setting(t).setName("Always show section tabs header").addToggle(a=>a.setValue(n.alwaysShowSectionHeader).onChange(async s=>{n.alwaysShowSectionHeader=s,this.applySettingsUpdate()})),new O.Setting(t).setName("Move show full document button to the left").addToggle(a=>a.setValue(n.showFullBtnAtLeftSide).onChange(async s=>{n.showFullBtnAtLeftSide=s,this.applySettingsUpdate(),document.body.toggleClass("omd-change-section-btns-order",s)})),new O.Setting(t).setName("Hide empty section header automatically").addToggle(a=>a.setValue(n.autoHideEmptySectionHeader).onChange(async s=>{n.autoHideEmptySectionHeader=s,this.applySettingsUpdate(),document.body.toggleClass("omd-hide-empty-section-header",s)})),new O.Setting(t).setName("Show section title").addToggle(a=>a.setValue(n.showSectionTitle).onChange(async s=>{n.showSectionTitle=s,this.applySettingsUpdate(),document.body.toggleClass("omd-show-section-title",s)}))}}initTaskGroupSettings(t,n){const r=document.createDocumentFragment();r.createEl("div",{text:"Task group is a group of tasks that you can fold and unfold. You can set a custom query string for task group."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Task group",attr:{href:"https://docs.outliner.md/pages/20240516203128"}});const u=document.createDocumentFragment();u.createEl("span","",a=>{O.setIcon(a,"check-square")}),u.createEl("span",{text:" Task group"}),new O.Setting(t).setHeading().setName(u).setDesc(r);const i=new O.Setting(t).setName("Fold task group when loading page").addButton(a=>a.setButtonText("Reload").onClick(()=>{window.location.reload()})).addToggle(a=>a.setValue(n.foldTaskGroup).onChange(async s=>{n.foldTaskGroup=s,this.applySettingsUpdate(),i.settingEl.toggleClass("show-reload-button",!0)}));new O.Setting(t).setName("Task group query string").setDesc("Custom query string for task group, you can set a tag or word for task querying. After changing this setting, you need to reload the page.").addText(a=>{a.setPlaceholder("#now").setValue(n.taskGroupQuery).onChange(async s=>{n.taskGroupQuery=s,this.applySettingsUpdate()})})}styleSettings(t,n){const r=document.createDocumentFragment();r.createEl("div",{text:"You can change the style of Outliner.md."});const u=document.createDocumentFragment();u.createEl("span","",s=>{O.setIcon(s,"palette")}),u.createEl("span",{text:" Style"}),new O.Setting(t).setHeading().setName(u).setDesc(r);const i=document.createDocumentFragment();i.createEl("div",{text:"Paper layout is a layout that looks like a paper. You can enable or disable it."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Paper layout",attr:{href:"https://docs.outliner.md/pages/20240519131652"}}),new O.Setting(t).setName("Paper layout").setDesc(i).addToggle(s=>s.setValue(n.paperLayout).onChange(async o=>{n.paperLayout=o,this.applySettingsUpdate(),document.body.toggleClass("outliner-paper-layout",o)}));const a=document.createDocumentFragment();a.createEl("div",{text:"More style settings are in the Obsidian style settings plugin."}).createEl("span",{text:" Note: "}).createEl("a",{text:"Obsidian style settings",attr:{href:"https://github.com/mgmeyers/obsidian-style-settings"}}),new O.Setting(t).setName("More style settings").setDesc(a).addButton(s=>s.setButtonText("Open").onClick(()=>{this.app.plugins.getPlugin("obsidian-style-settings")?this.app.setting.openTabById("obsidian-style-settings"):window.open("obsidian://show-plugin?id=obsidian-style-settings")}))}}function b2(e,t){const{sections:n,selected:r,onClick:u,createSection:i,view:a}=t,s=e.createEl("div","omd-container");return new y2({parentContainer:s,sections:n,selected:r,onClick:u,createSection:i,view:a}).onload(),s}class y2 extends O.Component{constructor({parentContainer:t,sections:n,selected:r,onClick:u,createSection:i,view:a}){super(),this.selected=null,this.currentTab=null,this.parentContainer=t,this.sections=n,this.selected=r,this.onClick=u,this.createSection=i,this.view=a,this.app=this.view.state.field(O.editorInfoField).app}onload(){var t,n;this.parentContainer.createEl("div","omd-section-tab-container",r=>{for(let u=0;u<this.sections.length;u++){const i=this.sections[u];r.createEl("div","omd-section-tab",a=>{a.dataset.start=String(i.start),a.dataset.end=String(i.end);const s=new O.ButtonComponent(a).setButtonText(i.name);this.selected===u&&(this.currentTab=a,this.currentTab.toggleClass("omd-section-tab-active",!0)),s.onClick(o=>{o.preventDefault();const c=a.dataset.start,d=a.dataset.end;c&&d&&this.selected!==u?(this.onClick({start:+c,end:+d,index:u}),this.submitEvent({start:+c,end:+d,index:u},this.view),this.updateParentContainerDataset(+c,+d)):(this.onClick(null),this.submitEvent(null,this.view),this.updateParentContainerDataset(null,null)),this.currentTab&&this.currentTab.toggleClass("omd-section-tab-active",!1),this.currentTab=a,this.currentTab.toggleClass("omd-section-tab-active",!0)}),this.registerDomEvent(a,"mouseover",o=>{w2(this.view,o,a,this.app)}),this.registerDomEvent(a,"contextmenu",o=>{o.preventDefault();const c=new O.Menu;c.addItem(d=>{d.setDisabled(this.sections[u].type==="lineage").setSection("action").setIcon("pencil").setTitle("Rename").onClick(()=>{new v2(this.app,l=>{const h=this.sections[u],f=this.view.state.doc.lineAt(h.start-1),m=f.text.replace(/%%SECTION\{.*?\}%%/,`%%SECTION{${l}}%%`);this.view.dispatch({changes:[{from:f.from,to:f.to,insert:m}]}),this.submitEvent({start:h.start,end:h.end,index:u},this.view),this.updateParentContainerDataset(h.start,h.end)}).open()})}).addItem(d=>{d.setSection("danger").setIcon("trash").setTitle("Delete").onClick(()=>{const l=this.sections[u],h=this.view.state.doc.lineAt(l.start-1),f=this.view.state.doc.lineAt(l.end);this.view.dispatch({changes:[{from:h.from,to:f.to===this.view.state.doc.length?f.to:f.to+2,insert:""}]}),this.selected===u?(this.onClick(null),this.submitEvent(null,this.view),this.updateParentContainerDataset(null,null)):this.submitEvent({start:l.start,end:l.end,index:u},this.view)}),d.dom.toggleClass("is-warning",!0)}),c.showAtMouseEvent(o)})})}}),this.parentContainer.createEl("div","omd-section-tab-add-button",r=>{new O.ExtraButtonComponent(r).setIcon("plus").setTooltip("Add section").onClick(()=>{this.createSection()})}),this.parentContainer.createEl("div","omd-section-tab-spacer"),this.parentContainer.createEl("div","omd-back-to-full",r=>{new O.ExtraButtonComponent(r).setIcon("book-open-text").setTooltip("Back to full document").onClick(()=>{var i;(i=this.currentTab)==null||i.toggleClass("omd-section-tab-active",!1),this.onClick(null),this.submitEvent(null,this.view),this.updateParentContainerDataset(null,null)})}),this.updateParentContainerDataset(this.selected!==null&&this.sections&&this.sections.length>0?(t=this.sections[this.selected])==null?void 0:t.start:null,this.selected!==null&&this.sections&&this.sections.length>0?(n=this.sections[this.selected])==null?void 0:n.end:null)}submitEvent(t,n){const r=n.state.field(O.editorInfoField).file;r&&this.app.workspace.trigger("omd-section-update",t,r)}updateParentContainerDataset(t,n){t!==null&&n!==null&&typeof t=="number"&&typeof n=="number"?(this.parentContainer.dataset.start=String(t),this.parentContainer.dataset.end=String(n)):(this.parentContainer.removeAttribute("data-start"),this.parentContainer.removeAttribute("data-end"))}onunload(){this.parentContainer.empty()}}class v2 extends O.Modal{constructor(t,n){super(t),this.cb=n}onOpen(){this.setTitle("Rename section"),this.contentEl.toggleClass("omd-section-rename",!0);const t=document.createDocumentFragment(),n=t.createEl("input",{cls:"rename-textarea"});n.onkeydown=r=>{if(r.key==="Enter"){const u=n.value;u&&(this.cb(u),this.close())}},t.createEl("div","modal-button-container",r=>{new O.ButtonComponent(r).setCta().setButtonText("Rename").onClick(()=>{const a=n.value;a&&(this.cb(a),this.close())});const i=new O.ButtonComponent(r).setButtonText("Cancel");i.buttonEl.toggleClass("mod-cancel",!0),i.onClick(()=>{this.close()})}),this.setContent(t)}onClose(){this.contentEl.empty()}}const w2=O.debounce((e,t,n,r)=>{const u=e.state.field(O.editorInfoField).file;u&&r.workspace.trigger("hover-link",{event:t,source:"outliner-md",hoverParent:e.dom,targetEl:n,linktext:u.path})},200);function ye({state:e}){const t=/%%SECTION\{([^}]*)\}%%|<!--section: ([^-]+)-->/,n=[],r=e.doc.sliceString(0,e.doc.length).split(`
`),u=r.map((i,a)=>{const s=i.match(t);return s?{index:a,match:s}:null}).filter(i=>i!==null);for(let i=0;i<u.length;i++){const a=u[i],s=u[i+1]||{index:r.length};if(!a)continue;const o=a.index,c=s.index,d=a.match;let l="Unnamed Section",h="outliner";d[1]?(l=d[1],h="outliner"):d[2]&&(l=`Section ${d[2]}`,h="lineage");const f=e.doc.line(o+1).to+1,m=c===r.length?e.doc.line(c).to:e.doc.line(c).from-1;n.push({name:l,start:f,end:m,type:h})}return n}O.debounce(({state:e})=>ye({state:e}),200);async function Wo(e,t,n){const r=ye({state:e.cm.state}),u=t.app.fileManager.getMarkdownNewFileParent(),i=e.cm.state.field(O.editorInfoField).file,a=(i==null?void 0:i.basename)||"untitled",s=(i==null?void 0:i.path)||"",o=[];for(let d=0;d<r.length;d++){const l=e.cm.state.doc.sliceString(r[d].start,r[d].end),h=e.cm.state.doc.lineAt(r[d].start-1).from,f=await t.app.fileManager.createNewMarkdownFile(u,`${a}/section-${r[d].name}`);await t.app.vault.append(f,l);const m=t.app.fileManager.generateMarkdownLink(f,s,"",r[d].name);o.push({start:h,end:r[d].end,insert:(n==="embed"?"!":"")+m})}const c=o.map(d=>({from:d.start,to:d.end,insert:d.insert}));e.cm.dispatch({changes:c})}const Ji=rt.StateEffect.define(),nl=rt.StateEffect.define(),Du=rt.StateEffect.define(),lu=rt.StateField.define({create:()=>null,update:(e,t)=>{if(t.docChanged&&e){const n=ye({state:t.state});return{...e,sections:n,selected:e.selected}}for(const n of t.effects)n.is(Du)&&(e!=null&&e.sections)&&typeof n.value=="number"&&(e={...e,selected:n.value}),n.is(Ji)&&(e=n.value),n.is(nl)&&(e=null);return e},provide:e=>J.showPanel.from(e,t=>t?n=>({top:!0,dom:b2(n.dom,{view:n,sections:t.sections,selected:t.selected,onClick:r=>t.onClick(n,r),createSection:()=>t.createSection(n)})}):null)});class E2 extends O.Component{constructor(t){super(),this.index=0,this.zoomed=!1,this.type="outliner",this.sections=[],this.indexMap=new Map,this.onClick=(n,r)=>{r===null?(this.showTitle(n),n.dispatch({effects:[er.of(),Du.of(-1)]}),this.index=0,setTimeout(()=>{n.scrollDOM.scrollTo(0,0)})):(n.dispatch({effects:[Be.of({from:r.start,to:r.end,type:"block"}),Du.of(r.index)]}),this.index=r.index,this.hideTitle(n))},this.hideTitle=n=>{var r,u;this.zoomed=!0,(u=(r=n.state.field(O.editorInfoField).editor)==null?void 0:r.editorComponent)==null||u.sizerEl.toggleClass("hide-sections-tabs",!0)},this.showTitle=n=>{var r,u;this.zoomed=!1,(u=(r=n.state.field(O.editorInfoField).editor)==null?void 0:r.editorComponent)==null||u.sizerEl.toggleClass("hide-sections-tabs",!1)},this.createSection=(n,r="end")=>{var o;const u=ye({state:n.state}),i=(o=n.state.field(lu))==null?void 0:o.selected;i!==void 0&&(this.index=i);let a=`

%%SECTION{New Section}%%

`;if(u.length>0&&u[0].type==="lineage"){const c=this.sections[this.sections.length-1].name.match(/section: (\d)/);a=c?`

<!--section: ${+c[1]+1}-->

`:`

<!--section: 1-->

`}let s=n.state.doc.length;if(!this.zoomed)n.dispatch({changes:[{from:s,to:s,insert:a}]});else switch(r){case"end":n.dispatch({changes:[{from:s,to:s,insert:a}]});break;case"start":if(u.length>0){const c=u[0];s=n.state.doc.lineAt(c.start-1).from===0?0:n.state.doc.lineAt(c.start-1).from-1}n.dispatch({changes:[{from:s,to:s,insert:a}]});break;case"before":u.length>0&&(s=n.state.doc.lineAt(u[this.index].start-1).from===0?0:n.state.doc.lineAt(u[this.index].start-1).from-1),n.dispatch({changes:[{from:s,to:s,insert:a}]});break;case"after":u.length>0&&(s=n.state.doc.lineAt(u[this.index].end).to),n.dispatch({changes:[{from:s,to:s,insert:a}]});break;default:n.dispatch({changes:[{from:s,to:s,insert:a}]});break}setTimeout(()=>{const c=ye({state:n.state});let d=c.length-1;switch(r){case"end":d=c.length-1;break;case"start":d=0;break;case"before":d=c.length>1&&this.index>0?this.index:0;break;case"after":d=c.length>1?this.index+1:0;break;default:d=c.length-1;break}n.dispatch({effects:[Be.of({from:s+a.length-1,to:s+a.length,type:"block"}),Ji.of({sections:c,selected:d,onClick:this.onClick,createSection:this.createSection})]}),this.index=d,this.hideTitle(n)},20)},this.plugin=t}getExtension(){return lu}onload(){super.onload(),this.registerEvent(this.plugin.app.workspace.on("zoom-into-section",(t,n)=>{const r=ye({state:t.state}),u=r.find(i=>i.start<=n&&n<=i.end);u&&this.onClick(t,{start:u.start,end:u.end,index:r.indexOf(u)})})),this.registerEvent(this.plugin.app.workspace.on("files-menu",(t,n)=>{t.addItem(r=>{r.setTitle("Merge notes into sections"),r.setIcon("merge"),r.onClick(async u=>{var c;const i=n.filter(d=>d instanceof O.TFile&&d.extension==="md");let a="";for(const d of i){a+=`

%%SECTION{${d.basename}}%%
`;const l=(c=this.plugin.app.metadataCache.getFileCache(d))==null?void 0:c.frontmatterPosition;if(l){const h=await this.plugin.app.vault.cachedRead(d);a+=h.slice(l.end.offset+1).trim()}else a+=(await this.plugin.app.vault.read(d)).trim()}const s=this.plugin.app.fileManager.getMarkdownNewFileParent(),o=await this.plugin.app.fileManager.createNewMarkdownFile(s,"merged-notes");await this.plugin.app.vault.modify(o,a),new O.Notice("Sections are merged into a single file")})})})),this.plugin.addCommand({id:"show-section-tabs",name:"Show section tabs",editorCallback:t=>{this.showSectionTabs(t.cm,ye({state:t.cm.state}))}}),this.plugin.addCommand({id:"hide-section-tabs",name:"Hide section tabs",editorCallback:t=>{this.hideSectionTabs(t.cm)}}),this.plugin.addCommand({id:"zoom-in-section-contains-cursor",name:"Zoom in section contains cursor",editorCheckCallback:(t,n,r)=>{const u=ye({state:n.cm.state});if(u.length>0){if(!t){const i=n.getCursor(),a=n.posToOffset(i),s=u.find(o=>o.start<=a&&a<=o.end);s&&this.onClick(n.cm,{start:s.start,end:s.end,index:u.indexOf(s)})}return!0}}}),this.plugin.addCommand({id:"create-section-end",name:"Create section at end",editorCallback:t=>{this.createSection(t.cm,"end")}}),this.plugin.addCommand({id:"create-section-start",name:"Create section at start",editorCallback:t=>{this.createSection(t.cm,"start")}}),this.plugin.addCommand({id:"create-section-before",name:"Create section before",editorCallback:t=>{this.createSection(t.cm,"before")}}),this.plugin.addCommand({id:"create-section-after",name:"Create section after",editorCallback:t=>{this.createSection(t.cm,"after")}}),this.plugin.addCommand({id:"select-next-section",name:"Select next section",editorCallback:t=>{this.selectNextSection(t.cm)}}),this.plugin.addCommand({id:"select-prev-section",name:"Select previous section",editorCallback:t=>{this.selectPrevSection(t.cm)}}),this.plugin.addCommand({id:"split-sections-in-notes-to-links",name:"Split sections in notes and link them",editorCallback:async t=>{await Wo(t,this.plugin,"link"),this.onClick(t.cm,null),new O.Notice("Sections are split into new files")}}),this.plugin.addCommand({id:"split-sections-in-notes-to-embeds",name:"Split sections in notes and embed them",editorCallback:async t=>{await Wo(t,this.plugin,"embed"),this.onClick(t.cm,null),new O.Notice("Sections are split into new files")}}),this.plugin.addCommand({id:"show-whole-document",name:"Show whole document",editorCallback:t=>{this.showFullDocument(t.cm)}})}showFullDocument(t){this.onClick(t,null),this.showTitle(t)}selectNextSection(t){var u;const n=ye({state:t.state});if(n.length===0)return;this.sections=n;const r=(u=t.state.field(lu))==null?void 0:u.selected;r!==void 0&&(this.index=r),this.index=this.zoomed?(this.index+1)%n.length:this.index,this.onClick(t,{start:n[this.index].start,end:n[this.index].end,index:this.index})}selectPrevSection(t){var u;const n=ye({state:t.state});if(n.length===0)return;this.sections=n;const r=(u=t.state.field(lu))==null?void 0:u.selected;r!==void 0&&(this.index=r),this.index=this.zoomed?(this.index-1+n.length)%n.length:this.index,this.onClick(t,{start:n[this.index].start,end:n[this.index].end,index:this.index})}showSectionTabs(t,n,r=0){t.dispatch({effects:[Ji.of({sections:n,selected:r,onClick:this.onClick,createSection:this.createSection})]})}hideSectionTabs(t){var n,r;t.dispatch({effects:[nl.of(),er.of(),Du.of(-1)]}),(r=(n=t.state.field(O.editorInfoField).editor)==null?void 0:n.editorComponent)==null||r.sizerEl.toggleClass("hide-sections-tabs",!1)}}function F2(e){const t=Oe(O.MarkdownView.prototype,{setViewData:n=>function(r,u){const i=n.call(this,r,u);if(e.settings.alwaysShowSectionHeader){const a=ye({state:this.editor.cm.state});e.sectionTabsNavigation.showSectionTabs(this.editor.cm,a)}return e.dragDropManager.initDragManager(this.editor.cm),i}});e.register(t)}const C2=rt.StateEffect.define(),Ra=rt.StateField.define({create:()=>null,update:(e,t)=>(t.docChanged,e)});class k2 extends J.WidgetType{constructor(t,n,r,u){super(),this.view=t,this.from=n,this.to=r,this.name=u,this.error=!1}eq(t){return t.from===this.from&&t.to===this.to}createTitleEl(){const t=createEl("div",{cls:"omd-section-line",text:this.name,attr:{dir:"ltr"}},n=>{n.createEl("hr",{attr:{"aria-label":"Section "+this.name}})});return t.onclick=n=>{if(O.Keymap.isModEvent(n)){n.preventDefault();const r=this.view.state.field(O.editorInfoField).app;if(r){r.workspace.trigger("zoom-into-section",this.view,this.to+1);return}}},t}createNoTitleEl(){const t=createEl("hr",{attr:{"aria-label":"Section "+this.name}});return t.onclick=n=>{if(O.Keymap.isModEvent(n)){n.preventDefault();const r=this.view.state.field(O.editorInfoField).app;if(r){r.workspace.trigger("zoom-into-section",this.view,this.to+1);return}}},t}toDOM(){const t=this.view.state.field(Ra);return t?t.plugin.settings.showSectionTitle?this.createTitleEl():this.createNoTitleEl():(this.error=!0,this.createTitleEl())}}function x2(){class e{constructor(r){this.match=new J.MatchDecorator({regexp:/^%%SECTION{([^}]+)}%%$|^<!--section: ([^-]+)-->$/g,decorate:(u,i,a,s,o)=>{const c=this.shouldRender(o,i,a);try{c&&i!==a&&u(i,a,J.Decoration.replace({widget:new k2(o,i,a,s[1]||s[2])}))}catch(d){console.error(d)}}}),this.decorations=J.Decoration.none,this.view=r,this.updateDecorations(r)}update(r){this.updateDecorations(r.view,r)}destroy(){this.decorations=J.Decoration.none}updateDecorations(r,u){!u||this.decorations.size===0?this.decorations=this.match.createDeco(r):this.decorations=this.match.updateDeco(u,this.decorations)}isLivePreview(r){return r.field(O.editorLivePreviewField)}shouldRender(r,u,i){return!r.state.selection.ranges.some(s=>s.from<=u?s.to>=u:s.from<=i)&&this.isLivePreview(r.state)}}const t={decorations:n=>n.decorations.update({filter:(r,u,i)=>{const a=i.spec.widget;return a&&a.error?!1:r===u||!n.view.state.selection.ranges.filter(s=>{const o=s.from,c=s.to;return o<=r?c>=r:o<=u}).length}})};return J.ViewPlugin.fromClass(e,t)}class S2 extends O.Component{constructor(t){super(),this.ghostEl=null,this.targetLineEl=null,this.isDragging=!1,this.initEditorView=null,this.initContent="",this.prevFrom=0,this.prevTo=0,this.firstLineRect=null,this.lastLineRect=null,this.focusLine=null,this.plugin=t}onload(){super.onload(),this.plugin.registerEditorExtension([T2,this.registerEditorEvents()]),this.createTargetLine(),this.registerDragEvents()}registerDragEvents(){this.plugin.registerDomEvent(window,"dragover",this.onDragOver.bind(this)),this.plugin.registerDomEvent(window,"dragend",this.onDragEnd.bind(this))}registerEditorEvents(){return J.EditorView.domEventHandlers({mouseenter:this.handleEditorMouseEnter.bind(this),mouseleave:this.handleEditorMouseLeave.bind(this),dragover:this.handleEditorDragOver.bind(this),dragenter:this.handleEditorDragEnter.bind(this),drop:this.handleEditorDrop.bind(this)})}handleEditorMouseEnter(t,n){this.currentEditorView=n}handleEditorMouseLeave(t,n){this.hideTargetLine()}handleEditorDragOver(t,n){if(!this.isDragging)return;this.currentEditorView=n,this.updateLineRects();const r=this.currentEditorView.dom.getBoundingClientRect(),u=this.getLastLineDomInfo(),i=t.clientY-r.top;i<=5?this.handleFirstLineDragOver(r):u&&i>=u.rect.bottom-r.top-5?this.handleLastLineDragOver(r,u):this.handleMiddleLineDragOver(t)}getLastLineDomInfo(){if(!this.currentEditorView)return null;const n=this.currentEditorView.state.doc.length,r=this.currentEditorView.domAtPos(n),u=r.node.nodeType===Node.TEXT_NODE?r.node.parentElement:r.node;if(!u||!(u instanceof Element))return null;const i=u.getBoundingClientRect();return{element:u,rect:i}}updateLineRects(){if(!this.currentEditorView)return;this.firstLineRect=this.currentEditorView.coordsAtPos(0);const t=this.currentEditorView.state.doc.length-1;this.lastLineRect=this.currentEditorView.coordsAtPos(t)}handleFirstLineDragOver(t){const n=this.currentEditorView.state.doc.line(1).from,r=this.currentEditorView.coordsAtPos(n);r&&this.moveTargetLine({top:t.top,left:r.left,height:0},this.currentEditorView.contentDOM.clientWidth,!1)}handleLastLineDragOver(t,n){const{rect:r}=n,u=this.currentEditorView.contentDOM,i=r.left,a=r.bottom;this.moveTargetLine({top:a,left:i,height:r.height},u.clientWidth,!0)}handleMiddleLineDragOver(t){const n=this.currentEditorView.posAtCoords({x:t.clientX,y:t.clientY});if(n===null)return;const u=this.currentEditorView.state.doc.lineAt(n).from;if(n>this.prevFrom&&n<this.prevTo)this.hideTargetLine();else{const i=this.currentEditorView.coordsAtPos(u);i&&this.moveTargetLine(i,this.currentEditorView.contentDOM.clientWidth,!1)}}handleDragOverPosition(t,n){if(!this.currentEditorView)return;const r=this.currentEditorView.state.doc,u=n===0,i=n===r.length;if(u&&this.firstLineRect)this.moveTargetLine(this.firstLineRect,this.currentEditorView.contentDOM.clientWidth,!1);else if(i&&this.lastLineRect)this.moveTargetLine(this.lastLineRect,this.currentEditorView.contentDOM.clientWidth,!0);else{const s=r.lineAt(n).from;n>this.prevFrom&&n<this.prevTo?this.hideTargetLine():this.moveTargetLine(this.currentEditorView.coordsAtPos(s),this.currentEditorView.contentDOM.clientWidth,!1)}}moveTargetLine(t,n,r){if(!t||!n||!this.isDragging||!this.targetLineEl)return;const u=t.left,i=r?t.top:t.top-2;this.targetLineEl.style.width=`${n}px`,this.targetLineEl.style.transform=`translate(${u}px, ${i}px)`,this.targetLineEl.style.display="block"}handleEditorDragEnter(t,n){var r;this.isDragging&&(this.currentEditorView=n,this.isInsideEditor=!0,(r=this.targetLineEl)==null||r.show())}handleEditorDrop(t,n){this.isDragging&&(this.currentEditorView=n,this.handleDrop(t),this.cleanupDrag())}async handleDragStart(t,n,r,u){t.stopPropagation(),this.isDragging=!0;const i=u.field(O.editorEditorField);if(this.currentEditorView=i,this.initEditorView=i,this.currentEditorView.focus(),await this.createGhostElement(t,u,n,r),t.dataTransfer){const a=document.createElement("div");a.style.display="none",document.body.appendChild(a),t.dataTransfer.setDragImage(a,0,0),setTimeout(()=>{document.body.removeChild(a)},0)}}onDragOver(t){this.isDragging&&(t.preventDefault(),t.dataTransfer&&(t.dataTransfer.dropEffect="move"),this.updateGhostPosition(t))}onDragLeave(t){this.isDragging&&(t.clientY<=0||t.clientY>=window.innerHeight||t.clientX<=0||t.clientX>=window.innerWidth)&&this.hideTargetLine()}onDrop(t){this.isDragging&&(this.handleDrop(t),this.cleanupDrag())}onDragEnd(){this.isDragging&&this.cleanupDrag()}cleanupDrag(){this.isDragging=!1,this.ghostEl&&(this.ghostEl.remove(),this.ghostEl=null),this.hideTargetLine()}hideTargetLine(){var t;(t=this.targetLineEl)==null||t.hide()}isValidDragTarget(t){return t.hasClass("cm-drag-handler-container")}createTargetLine(){var t;this.targetLineEl&&this.targetLineEl.detach(),this.targetLineEl=(t=O.requireApiVersion("0.15.0")?activeDocument:document)==null?void 0:t.body.createEl("div",{cls:"drag-target-line"}),this.targetLineEl.hide()}async createGhostElement(t,n,r,u){var c;this.ghostEl&&this.ghostEl.detach();const i=this.calculateRangeForTransform(n,r),a=n.doc.lineAt(r);i?(this.prevFrom=i.from,this.prevTo=i.to===n.doc.length?i.to:i.to+1):(this.prevFrom=a.from,this.prevTo=a.to===n.doc.length?a.to:a.to<u?u+1:a.to+1),this.initContent=n.doc.sliceString(this.prevFrom,this.prevTo),this.isDragging=!0,this.ghostEl=(c=O.requireApiVersion("0.15.0")?activeDocument:document)==null?void 0:c.body.createEl("div",{cls:"drag-ghost-element"});const s=this.ghostEl.createEl("div",{cls:"drag-ghost-icon"});O.setIcon(s,"grip-vertical"),s.style.float="left";const o=this.ghostEl.createEl("div",{cls:["drag-ghost-content","markdown-rendered"]});o.style.float="right",await O.MarkdownRenderer.render(this.plugin.app,this.initContent.trim(),o,"",this)}getEditorFromState(t){return t.field(O.editorInfoField).editor}initDragManager(t){t.dispatch({effects:[C2.of(this.plugin)]})}handleDrop(t){var c;if(!this.currentEditorView)return;t.preventDefault();const n=(c=this.targetLineEl)==null?void 0:c.getBoundingClientRect();if(!n)return;const r=this.currentEditorView.posAtCoords({x:n.left,y:n.bottom});if(r===null)return;const u=this.getEditorFromState(this.currentEditorView.state),i=this.getEditorFromState(this.initEditorView.state);if(!u||!i)return;const a=u.getValue()!==i.getValue();if(!u)return;let s=u.offsetToPos(r-1);if(!a&&u.offsetToPos(this.prevTo).line===u.offsetToPos(r).line)return;let o=s.line;if(!a&&u.offsetToPos(this.prevFrom).line<u.offsetToPos(r).line){const d=u.offsetToPos(this.prevTo).line-u.offsetToPos(this.prevFrom).line+1;s={line:s.line-d,ch:0},d===1&&(o=s.line),d>1&&(o=s.line+d-1)}else o=s.line;this.handleNormalDrop(u,r,o,!a),this.cleanupDrag()}handleCtrlDrop(t,n,r,u){}handleAltDrop(t,n,r,u){}handleNormalDrop(t,n,r,u){if(!this.currentEditorView)return;let i=this.adjustIndentation(this.initContent,t.getLine(r));i=i.replace(/^\n/,"").replace(/\n$/,"");let a="",s="";n>t.posToOffset({line:r,ch:0})&&n!==t.getValue().length&&(s=`
`),(n<t.posToOffset({line:r,ch:t.getLine(r).length})||n===t.getValue().length)&&(a=`
`);const o=a+i+s;u?this.currentEditorView.dispatch({changes:[{from:this.prevFrom,to:this.prevTo,insert:""},{from:n,to:n,insert:o}]}):(this.currentEditorView.dispatch({changes:{from:n,to:n,insert:o}}),this.initEditorView&&this.initEditorView.dispatch({changes:{from:this.prevFrom,to:this.prevTo,insert:""}}))}shouldReplaceToken(t){var u;const n=t.startsWith("	");if(!(!t.startsWith(" ")&&!n))return(u=t.match(/^\s*/g))==null?void 0:u[0]}adjustIndentation(t,n){const r=t.split(`
`),u=this.plugin.app.vault.getConfig("tabSize"),i=this.plugin.app.vault.getConfig("useTab"),a=m=>{var y,C;const b=((y=m.match(/^ */))==null?void 0:y[0])??"",g=((C=m.match(/^\t*/))==null?void 0:C[0])??"";return g.length>0?g.length:Math.floor(b.length/u)},s=m=>i?"	".repeat(Math.max(0,m)):" ".repeat(Math.max(0,m*u)),o=m=>{const b=m.trim();return/^(\d+\.|-|\*)\s/.test(b)},c=a(n),d=a(r[0]),h=n.trim()!==""&&(c>0||o(n.trim()))?c+1:0,f=(m,b)=>{if(m.trim()===""&&b===r.length-1)return m;const g=a(m);let y;if(b===0)y=h;else{const C=g-d;y=Math.max(0,h+C)}return s(y)+m};return`
`+r.map(f).join(`
`)}updateGhostPosition(t){this.ghostEl&&this.isDragging&&(this.ghostEl.style.transform=`translate(${t.clientX-12}px, ${t.clientY-12}px)`)}calculateRangeForTransform(t,n){const r=t.doc.lineAt(n),u=Vt.foldable(t,r.from,r.to);return u?{from:r.from,to:u.to}:null}unload(){super.unload(),this.dragHandlerEl&&this.dragHandlerEl.detach(),this.targetLineEl&&this.targetLineEl.detach()}}class Ai extends J.WidgetType{constructor(t,n,r,u){super(),this.app=t,this.view=n,this.from=r,this.to=u}eq(t){return t.from===this.from&&t.to===this.to&&t.view===this.view}toDOM(){if(this.dragHandlerContainerEl=createEl("span",{cls:"cm-drag-handler-container",attr:{draggable:"true"}}),this.dragHandlerContainerEl.createEl("span",{cls:"clickable-icon"},t=>{O.setIcon(t,"grip-vertical")}),this.plugin=this.getPlugin(),this.plugin&&this.plugin.dragDropManager){const{plugin:t}=this;this.plugin.registerDomEvent(this.dragHandlerContainerEl,"dragstart",n=>{t.dragDropManager&&t.dragDropManager.handleDragStart(n,this.from,this.to,this.view)})}return this.dragHandlerContainerEl}getPlugin(){var t;try{return(t=this.view.field(Ra))==null?void 0:t.plugin}catch(n){console.warn("pluginInfoField is not present in the state",n);return}}}const Oi=["hmd-callout","math-block","hmd-codeblock","quote"],T2=rt.StateField.define({create(){return J.Decoration.none},update(e,t){var c;const n=new rt.RangeSetBuilder,r=t.state.field(O.editorInfoField);let u=!1,i=0,a=0,s=[],o=!1;for(let d=1;d<=t.state.doc.lines;d++){const l=t.state.doc.line(d),f=((c=Vt.syntaxTree(t.state).resolveInner(l.from+1).type.prop(Vt.tokenClassNodeProp))==null?void 0:c.split(" "))??[];if(f.includes("hmd-frontmatter"))continue;if(o&&(o=!1),(f.length===0||s!==f&&s.includes("hmd-codeblock")&&!f.includes("hmd-codeblock")||d===t.state.doc.lines)&&s.length>0){if(u){const y=t.state.doc.line(a).from,C=(s.includes("quote")||s.includes("hmd-codeblock"))&&!s.includes("hmd-callout")?y:y-1;u=!1,n.add(C,C,J.Decoration.widget({widget:new Ai(r.app,t.state,y,l.to),side:-1,inlineOrder:!0}))}s=[]}if(!l.text.trim()){i=l.to;continue}const m=l.text.match(/^\s*/)[0].length;if(o=Oi.some(y=>f.includes(y))&&!u,o?u=!0:u&&!Oi.some(y=>f.includes(y))&&(u=!1),u&&Oi.some(y=>f.includes(y))&&!o)continue;let b,g;if(o?(a=d,b=s.includes("quote")||s.includes("hmd-codeblock")?l.from:i,g=-1):(b=l.from+m,g=-1),u){if(s=f,d===t.state.doc.lines){const y=t.state.doc.line(a).from,C=(s.includes("quote")||s.includes("hmd-codeblock"))&&!s.includes("hmd-callout")?y:y-1;u=!1,n.add(C,C,J.Decoration.widget({widget:new Ai(r.app,t.state,y,l.to),side:-1,inlineOrder:!0}))}continue}n.add(b,b,J.Decoration.widget({widget:new Ai(r.app,t.state,b,l.to),side:g,inlineOrder:!0})),i=l.to}return n.finish()},provide:e=>J.EditorView.decorations.from(e)}),fu="outliner";class A2{constructor(){this.queue=[],this.isProcessing=!1,this.processQueue=O.debounce(()=>{if(this.isProcessing||this.queue.length===0)return;this.isProcessing=!0;const t=this.queue.shift();t==null||t(),this.isProcessing=!1,this.processQueue()},100)}enqueue(t){this.queue.push(t),this.processQueue()}}class O2 extends O.Plugin{constructor(){super(...arguments),this.outlinerFileModes={},this.settings=Zo,this.calculateRangeForZooming=new wa,this.KeepOnlyZoomedContentVisible=new qr,this.sectionTabsNavigation=new E2(this),this.dragDropManager=new S2(this),this.taskQueue=new A2}async onload(){await this.loadSettings(),this.sectionTabsNavigation.onload(),this.settings.dragAndDrop&&this.dragDropManager.onload(),this.addSettingTab(new D2(this.app,this)),this.registerView(Te,t=>new Hn(t,this)),this.registerEditorExtension([g2(),Ra.init(t=>({plugin:this}))]),this.registerRibbons(),this.patchMarkdownView(this),await this.noteAsNotebook(),this.patchEmbedView(),this.patchWorkspaceLeaf(),this.patchItemView(),this.patchBacklinks(),this.initOutlinerView(),this.registerMenu(),this.registerCommands()}onunload(){this.dragDropManager.unload(),this.settings.noteAsNotebook&&document.body.findAll(".hide-sections-tabs").forEach(t=>t.toggleClass("hide-sections-tabs",!1)),document.body.toggleClass("omd-change-section-btns-order",!1),document.body.toggleClass("outliner-paper-layout",!1),document.body.toggleClass("omd-hide-empty-section-header",!1),Promise.all(this.app.workspace.getLeavesOfType(Te).map(t=>(this.outlinerFileModes[t.id]="markdown",this.setMarkdownView(t)))).then(()=>{super.unload()})}async noteAsNotebook(){this.settings.noteAsNotebook&&(F2(this),this.registerEditorExtension([this.sectionTabsNavigation.getExtension(),Lr,x2()]),this.registerHoverLinkSource("outliner-md",{display:"Section tab preview",defaultMod:!0}),document.body.toggleClass("omd-change-section-btns-order",this.settings.showFullBtnAtLeftSide),document.body.toggleClass("omd-hide-empty-section-header",this.settings.autoHideEmptySectionHeader),await this.initAllMarkdownView())}registerRibbons(){this.addRibbonIcon("list","New outliner file",async()=>{const t=this.app.fileManager.getMarkdownNewFileParent();if(!t){new O.Notice("No folder to create file in");return}if(t){const n=await this.app.vault.create((t.path?`${t.path}/`:"")+`outliner-${O.moment().format("YYYYMMDDHHmmss")}.md`,`---
outliner: true
---

- `);await this.app.workspace.getLeaf(!0).setViewState({type:Te,state:{file:n.path},active:!0})}})}async setMarkdownView(t,n=!0){await t.setViewState({type:"markdown",state:t.view.getState(),popstate:!0},{focus:n})}initOutlinerView(){const t=this.app.workspace.getLeavesOfType("markdown");for(const n of t){const r=n.view.file;if(n.isDeferred)continue;const u=this.app.metadataCache.getFileCache(r);u!=null&&u.frontmatter&&u.frontmatter[fu]&&(this.outlinerFileModes[n.id]=Te,this.setOutlinerView(n,{file:r.path}))}}async setOutlinerView(t,{file:n}){await t.setViewState({type:Te,state:{...t.view.getState(),file:n},popstate:!0})}patchMarkdownView(t){const n=Oe(O.Workspace.prototype,{getActiveViewOfType:u=>function(i){const a=u.call(this,i);if(!a&&(i==null?void 0:i.VIEW_TYPE)==="markdown"){const s=this.activeLeaf;if((s==null?void 0:s.view)instanceof Hn)return s.view}return a}});this.register(n),(u=>{const i=u.app.embedRegistry.embedByExtension.md({app:u.app,containerEl:document.createElement("div")},null,"");i.editable=!0,i.showEditor();const a=Object.getPrototypeOf(Object.getPrototypeOf(i.editMode)),s=Oe(a.constructor.prototype,{triggerClickableToken:o=>async function(...c){if(c[0].type==="internal-link"){if(c[0].displayText&&/^o-(.*)?/g.test(c[0].displayText)&&!c[0].text.includes("#")){const d=c[0].displayText.replace("readonly",""),l=u.app.metadataCache.getFirstLinkpathDest(c[0].text,"");if(l){const h=await u.app.vault.read(l),f=`%%${d}%%`,m=h.indexOf(f),b=h.indexOf(f,m+1);if(m===-1||b===-1)return o.apply(this,c);const g=[m,b+f.length+1];try{setTimeout(async()=>{await u.app.workspace.getLeaf(c[1]).openFile(l,{eState:{match:{content:h,matches:[g]}}})},100);return}catch(y){return console.error(y),o.apply(this,c)}}return o.apply(this,c)}return o.apply(this,c)}return o.apply(this,c)}});this.register(s),i.unload()})(this)}async initAllMarkdownView(){const t=this.app.workspace.getLeavesOfType("markdown");for(const n of t){if(n.isDeferred)continue;const r=n.view.editMode.editor.cm,u=ye({state:r.state});this.sectionTabsNavigation.showSectionTabs(r,u)}}patchEmbedView(){if(!this.settings.editableBlockEmbeds)return;const t=this.app.embedRegistry.embedByExtension,n=(u,i,a)=>{const s=this.app.metadataCache.getFileCache(i);return s!=null&&s.frontmatter&&s.frontmatter["excalidraw-plugin"]?!1:new Ti(this,{...u,sourcePath:i.path},i,a)},r=(u,i,a)=>{const s=this.app.metadataCache.getFileCache(i);return s!=null&&s.frontmatter&&s.frontmatter["excalidraw-plugin"]||!u.containerEl.getAttribute("alt")||!/o-(.*)?/g.test(u.containerEl.getAttribute("alt"))?!1:new m2(u,i,a)};this.register(Oe(t,{md:u=>function(i,a,s){if(i&&i.displayMode===!1&&i.showInline){if(a.path.contains(".excalidraw.md"))return u.apply(this,[i,a,s]);const o=n(i,a,s);return o||u.apply(this,[i,a,s])}else if(i&&i.displayMode===void 0&&i.showInline){if(a.path.contains(".excalidraw.md"))return u.apply(this,[i,a,s]);const o=r(i,a,s);return o||u.apply(this,[i,a,s])}return u.apply(this,[i,a,s])}}))}patchWorkspaceLeaf(){const t=this;this.register(Oe(O.WorkspaceLeaf.prototype,{detach(n){return function(){var u;const r=(u=this.view)==null?void 0:u.getState();return r!=null&&r.file&&t.outlinerFileModes[this.id||r.file]&&delete t.outlinerFileModes[this.id||r.file],n.apply(this)}},setViewState(n){return function(r,...u){var i;if(t._loaded&&r.type==="markdown"&&((i=r.state)!=null&&i.file)&&t.outlinerFileModes[this.id||r.state.file]!=="markdown"){const a=t.app.metadataCache.getCache(r.state.file);if(a!=null&&a.frontmatter&&a.frontmatter[fu]){const s={...r,type:Te,state:{...r.state}};return t.outlinerFileModes[r.state.file]=Te,n.apply(this,[s,...u])}}return n.apply(this,[r,...u])}},getRoot(n){return function(){const r=n.call(this);return(r==null?void 0:r.getRoot)===this.getRoot?r:r==null?void 0:r.getRoot()}},setPinned(n){return function(r){n.call(this,r),u2(this)&&!r&&this.setPinned(!0)}}}))}patchItemView(){const t=this,[n,r]=O.View.prototype.onPaneMenu?[O.View,"onPaneMenu"]:[O.ItemView,"onMoreOptionsMenu"],u=Oe(n.prototype,{[r](i){return function(a,...s){var d,l,h;const o=((d=this.leaf)==null?void 0:d.view)instanceof Hn;if(((l=this.leaf)==null?void 0:l.view)instanceof O.MarkdownView&&!o){const f=(h=this.leaf)==null?void 0:h.view.file,m=t.app.metadataCache.getFileCache(f);m!=null&&m.frontmatter&&m.frontmatter[fu]&&a.addItem(b=>{var g,y;(y=(g=b.setIcon("list").setTitle("Open as Outliner View").onClick(async()=>{var C,B;t.outlinerFileModes[((C=this.leaf)==null?void 0:C.view.leaf).id||f.path]=Te,await t.setOutlinerView((B=this.leaf)==null?void 0:B.view.leaf,{file:f.path})})).setSection)==null||y.call(g,"pane")})}return i.call(this,a,...s)}}});this.register(u)}patchBacklinks(){if(!this.settings.editableBacklinks)return;const t=(r,u)=>{const i=Oe(u.constructor.prototype,{render:o=>function(c,d){const l=o.call(this,c,d),h=this.parentDom.parentDom.el.closest(".mod-global-search")||this.parentDom.parentDom.el.closest(".internal-query");if(this.isBacklink=!!h,this.isBacklink){if(!this.embeddedEditor){if(this.btnEl||this.editBtn)return;this.btnEl=this.el.createEl("div",{cls:"create-embedded-editor-btn"},f=>{this.editBtn=new O.ExtraButtonComponent(f).setIcon("pencil").setTooltip("Edit this block"),f.onclick=m=>{m.preventDefault(),m.stopPropagation(),f.detach(),r.taskQueue.enqueue(()=>{this.embeddedEditor||(this.embeddedEditor=new Ti(r,{sourcePath:this.parentDom.file.path,app:this.parentDom.app,containerEl:this.el},this.parentDom.file,"",{from:this.start,to:this.end},this.content),this.embeddedEditor.load())})}})}return l}if(this.embeddedEditor){const f=this.embeddedEditor;if(f)return f.updateRange(this.currentRange||{from:this.start,to:this.end}),l}else this.parentDom.file&&r.taskQueue.enqueue(()=>{this.embeddedEditor||(this.embeddedEditor=new Ti(r,{sourcePath:this.parentDom.file.path,app:this.parentDom.app,containerEl:this.el},this.parentDom.file,"",{from:this.start,to:this.end},this.content),this.embeddedEditor.load())});return l},onResultClick:o=>function(c){if(!(this.embeddedEditor&&!c.target.closest(".backlink-btn")))return o.call(this,c)},showMoreAfter:o=>function(){const c=o.call(this);return this.currentRange={from:this.start,to:this.end},c},showMoreBefore:o=>function(){const c=o.call(this);return this.currentRange={from:this.start,to:this.end},c}});this.register(i);const a=u.parent,s=Oe(a.constructor.prototype,{renderContentMatches:o=>function(){const c=this.vChildren._children;for(let d=0;d<c.length;d++){const l=c[d];l!=null&&l.embeddedEditor&&l.embeddedEditor.unload()}return o.call(this)}});this.register(s)},n=async r=>{const u=this.app.workspace.getLeavesOfType("search")[0];u&&O.requireApiVersion("1.7.2")&&await(u==null?void 0:u.loadIfDeferred());const i=u.view;if(!i)return!1;const a=i.dom.constructor,s=Oe(a.prototype,{stopLoader(o){return function(){var c,d;o.call(this),(d=(c=this==null?void 0:this.vChildren)==null?void 0:c.children)==null||d.forEach(l=>{l!=null&&l.file&&!(l!=null&&l.pathEl)&&l.vChildren._children[0]&&(t(r,l.vChildren._children[0]),s(),setTimeout(()=>{l.vChildren.owner.renderContentMatches()},800))})}}});return this.register(s),!0};this.app.workspace.onLayoutReady(async()=>{if(!await n(this)){const u=this.app.workspace.on("layout-change",async()=>{await n(this)&&this.app.workspace.offref(u)});this.registerEvent(u)}})}registerMenu(){this.registerEvent(this.app.workspace.on("file-menu",(t,n)=>{n instanceof O.TFolder&&t.addItem(r=>{r.setSection("action-primary").setIcon("list").setTitle("New outliner file").onClick(async()=>{const u=await this.app.vault.create(`${n.path}/outliner-${O.moment().format("YYYYMMDDHHmmss")}.md`,`---
outliner: true
---

- `);this.app.workspace.getLeaf(!0).setViewState({type:Te,state:{file:u.path},active:!0})})})})),this.registerEvent(this.app.workspace.on("editor-menu",(t,n,r)=>{n.somethingSelected()&&r!=null&&r.file&&t.addItem(u=>{u.setSection("selection-link").setIcon("list").setTitle("Copy link to embed text fragment").onClick(()=>{nu(n,r,"embed")})}).addItem(u=>{u.setSection("selection-link").setIcon("list").setTitle("Copy link to text fragment").onClick(()=>{nu(n,r,"link")})})}))}registerCommands(){this.addCommand({id:"duplicate-current-bullet-and-its-children",name:"Duplicate current bullet and its children",checkCallback:t=>{const n=this.app.workspace.getActiveViewOfType(Hn);if(n){if(!t){const r=n.editor,u=r==null?void 0:r.getCursor();if(u===void 0)return;const i=r==null?void 0:r.posToOffset(u);if(i===void 0)return;const a=r==null?void 0:r.cm.state.doc.lineAt(i);if(!a||!(r!=null&&r.cm.state))return;const s=this.calculateRangeForZooming.calculateRangeForZooming(r==null?void 0:r.cm.state,a==null?void 0:a.from);if(s){const o=s.from+(r==null?void 0:r.cm.state.doc.slice(s.from,s.to).length);r==null||r.cm.dispatch({changes:[{from:s.to,to:s.to,insert:`
`+(r==null?void 0:r.cm.state.doc.slice(s.from,s.to))}],selection:{head:r==null?void 0:r.cm.state.doc.lineAt(o).to,anchor:r==null?void 0:r.cm.state.doc.lineAt(o).to}})}else{const o=a.from+(r==null?void 0:r.cm.state.doc.slice(a.from,a.to).length);r==null||r.cm.dispatch({changes:[{from:a.to,to:a.to,insert:`
`+(r==null?void 0:r.cm.state.doc.slice(a.from,a.to))}],selection:{head:r==null?void 0:r.cm.state.doc.lineAt(o).to,anchor:r==null?void 0:r.cm.state.doc.lineAt(o).to}})}}return!0}}}),this.addCommand({id:"search-in-current-file",name:"Search in current file",checkCallback:t=>{const n=this.app.workspace.getActiveViewOfType(Hn);if(n)return t||n.search(),!0}}),this.addCommand({id:"open-as-outliner-view",name:"Open as Outliner View",checkCallback:t=>{var r,u,i;const n=this.app.workspace.getActiveViewOfType(O.MarkdownView);if(n&&n.getViewType()==="markdown"&&((u=(r=this.app.metadataCache.getFileCache(n.file))==null?void 0:r.frontmatter)!=null&&u[fu]))return t||(this.outlinerFileModes[n.leaf.id]=Te,this.setOutlinerView(n.leaf,{file:(i=n.file)==null?void 0:i.path})),!0}}),this.addCommand({id:"open-as-markdown-view",name:"Open as Markdown View",checkCallback:t=>{const n=this.app.workspace.getActiveViewOfType(Hn);if(n)return t||(this.outlinerFileModes[n.leaf.id]="markdown",this.setMarkdownView(n.leaf)),!0}}),this.addCommand({id:"copy-link-to-embed-text-fragment",name:"Copy link to embed text fragment",editorCallback:(t,n)=>{nu(t,n,"embed")}}),this.addCommand({id:"copy-link-to-text-fragment",name:"Copy link to text fragment",editorCallback:(t,n)=>{nu(t,n,"link")}})}async loadSettings(){this.settings=Object.assign({},Zo,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}}module.exports=O2;
