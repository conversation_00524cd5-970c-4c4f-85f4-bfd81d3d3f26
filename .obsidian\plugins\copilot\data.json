{"isPlusUser": false, "plusLicenseKey": "", "openAIApiKey": "", "openAIOrgId": "", "huggingfaceApiKey": "", "cohereApiKey": "", "anthropicApiKey": "", "azureOpenAIApiKey": "", "azureOpenAIApiInstanceName": "", "azureOpenAIApiDeploymentName": "", "azureOpenAIApiVersion": "", "azureOpenAIApiEmbeddingDeploymentName": "", "googleApiKey": "", "openRouterAiApiKey": "", "defaultChainType": "llm_chain", "defaultModelKey": "deepseek-ai/DeepSeek-R1|3rd party (openai-format)", "embeddingModelKey": "BAAI/bge-large-zh-v1.5|3rd party (openai-format)", "temperature": 0.6, "maxTokens": 8100, "contextTurns": 50, "userSystemPrompt": "", "openAIProxyBaseUrl": "", "openAIEmbeddingProxyBaseUrl": "", "stream": true, "defaultSaveFolder": "copilot-conversations", "defaultConversationTag": "copilot-conversation", "autosaveChat": true, "defaultOpenArea": "view", "customPromptsFolder": "copilot-custom-prompts", "indexVaultToVectorStore": "ON MODE SWITCH", "qaExclusions": "", "qaInclusions": "", "chatNoteContextPath": "", "chatNoteContextTags": [], "enableIndexSync": true, "debug": false, "enableEncryption": false, "maxSourceChunks": 60, "groqApiKey": "", "mistralApiKey": "", "activeModels": [{"name": "copilot-plus-flash", "provider": "copilot-plus", "enabled": false, "isBuiltIn": true, "core": true, "plusExclusive": true, "capabilities": ["vision"]}, {"name": "gpt-4o", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-4o-mini", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "claude-3-5-sonnet-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-4-turbo", "provider": "openai", "enabled": false, "isBuiltIn": true}, {"name": "claude-3-5-haiku-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true}, {"name": "command-r", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "command-r-plus", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "gemini-1.5-pro", "provider": "google", "enabled": false, "isBuiltIn": true}, {"name": "gemini-1.5-flash", "provider": "google", "enabled": false, "isBuiltIn": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": false, "isBuiltIn": true}, {"name": "deepseek-ai/DeepSeek-R1", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api.siliconflow.cn/v1", "apiKey": "sk-vcrrwbvijkoirlujaugjqdbdxqbzvnkjypfxafxaxuaswhxz", "isEmbeddingModel": false, "temperature": 0.1, "context": 1000, "stream": true}], "activeEmbeddingModels": [{"name": "copilot-plus-small", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true}, {"name": "copilot-plus-large", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "believerExclusive": true, "dimensions": 1024}, {"name": "copilot-plus-multilingual", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "dimensions": 512}, {"name": "text-embedding-3-small", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "copilot-plus-large", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "believerExclusive": true}, {"name": "text-embedding-3-large", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "embed-multilingual-light-v3.0", "provider": "cohereai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "text-embedding-004", "provider": "google", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "BAAI/bge-large-zh-v1.5", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api.siliconflow.cn/v1/embeddings", "apiKey": "", "isEmbeddingModel": true, "enableCors": true}], "embeddingRequestsPerMin": 90, "embeddingBatchSize": 16, "disableIndexOnMobile": true, "showSuggestedPrompts": true, "showRelevantNotes": true, "numPartitions": 1, "enabledCommands": {}, "promptUsageTimestamps": {}, "defaultConversationNoteName": "{$topic}@{$date}_{$time}", "embeddingRequestsPerSecond": 10}