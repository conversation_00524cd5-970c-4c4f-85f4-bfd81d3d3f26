{"manifest": {"translationVersion": 1741566996492, "pluginVersion": "0.5.67"}, "description": {"original": "Complex data views for the data-obsessed.", "translation": "为数据爱好提供的复杂数据视图。"}, "dict": {".setName(\"Enable inline queries\")": ".setName(\"启用内联查询\")", ".setName(\"Enable JavaScript queries\")": ".setName(\"启用 JavaScript 查询\")", ".setName(\"Enable inline JavaScript queries\")": ".setName(\"启用内联JavaScript查询\")", ".setName(\"Enable inline field highlighting in reading view\")": ".setName(\"在阅读视图中启用内联字段高亮显示\")", ".setName(\"Enable inline field highlighting in Live Preview\")": ".setName(\"在实时预览中启用内联字段高亮显示\")", ".setName(\"Codeblocks\")": ".setName(\"代码块\")", ".setName(\"DataviewJS keyword\")": ".setName(\"数据视图JS关键字\")", ".setName(\"Inline query prefix\")": ".setName(\"内联查询前缀\")", ".setName(\"JavaScript inline query prefix\")": ".setName(\"JavaScript 内联查询前缀\")", ".setName(\"Code block inline queries\")": ".setName(\"代码块内查询\")", ".setName(\"View\")": ".setName(\"查看\")", ".setName(\"Display result count\")": ".setName(\"显示结果数量\")", ".setName(\"Warn on empty result\")": ".setName(\"在结果为空时警告\")", ".setName(\"Render null as\")": ".setName(\"将 null 渲染为\")", ".setName(\"Automatic view refreshing\")": ".setName(\"自动视图刷新\")", ".setName(\"Refresh interval\")": ".setName(\"刷新间隔\")", ".setName(\"Date format\")": ".setName(\"日期格式\")", ".setName(\"Date + time format\")": ".setName(\"日期 + 时间格式\")", ".setName(\"Tables\")": ".setName(\"表格\")", ".setName(\"Primary column name\")": ".setName(\"主列名称\")", ".setName(\"Grouped column name\")": ".setName(\"分组列名称\")", ".setName(\"Tasks\")": ".setName(\"任务\")", ".setName(\"Automatic task completion tracking\")": ".setName(\"自动任务完成跟踪\")", ".setName(\"Use emoji shorthand for completion\")": ".setName(\"使用表情符号简写完成任务\")", ".setName(\"Completion field name\")": ".setName(\"完成字段名称\")", ".setName(\"Completion date format\")": ".setName(\"完成日期格式\")", ".setName(\"Recursive sub-task completion\")": ".setName(\"递归子任务完成\")", ".setDesc(\"Enable or disable executing regular inline Dataview queries.\")": ".setDesc(\"启用或禁用执行常规的内联 Dataview 查询。\")", ".setDesc(\"Enable or disable executing DataviewJS queries.\")": ".setDesc(\"启用或禁用执行 DataviewJS 查询。\")", ".setDesc(\"Enable or disable executing inline DataviewJS queries. Requires that DataviewJS queries are enabled.\")": ".setDesc(\"启用或禁用执行内联 DataviewJS 查询。需要 DataviewJS 查询已启用。\")", ".setDesc(\"Enables or disables visual highlighting / pretty rendering for inline fields in reading view.\")": ".setDesc(\"在阅读视图中启用或禁用内联字段的视觉高亮显示/美观渲染。\")", ".setDesc(\"Enables or disables visual highlighting / pretty rendering for inline fields in Live Preview.\")": ".setDesc(\"启用或禁用实时预览中内联字段的视觉高亮显示/美化渲染。\")", ".setDesc(\"Keyword for DataviewJS blocks. Defaults to 'dataviewjs'. Reload required for changes to take effect.\")": ".setDesc(\"数据视图JS块的关键字。默认为'dataviewjs'。更改后需要重新加载以生效。\")", ".appendText(\"Example with default field name and date format: - [x] my task [completion:: 2022-01-01]\")": ".appendText(\"默认的文件名与日期格式 : - [x] 我的任务 [completion:: 2022-01-01]\")", ".appendText(\"Example: - [x] my task ✅ 2022-01-01\")": ".appendText(\"- [x] 我的任务 ✅ 2022-01-01\")", ".appendText(\"Text used as inline field key for task completion date when toggling a task's checkbox in a Dataview view.\")": ".appendText(\"在 Dataview 视图中切换任务复选框时用作内联字段键的任务完成日期。\")", "\"that links to the source file/group.\")": "\"链接到源文件/组.\")", "\" Currently: \" +": "\" 当前: \" +", "return \"Got the end of the input\"": "return \"得到输入的结尾\"", "\"Expected one of the following:": "\"预期出现以下情况之一:", "\"Not a comment\"": "\"非释义\"", "\"-- PARSING FAILED \"": "\"-- 解析失败 \"", "message: \"Dataview: No results to show for list query.\"": "message: \"数据视图: 列表查询没有结果显示。\"", "message: \"Dataview: No results to show for task query.\"": "message: \"数据视图: 任务查询没有结果显示。\"", "message: \"Dataview: No results to show for table query.\"": "message: \"数据视图: 表查询没有结果显示。\"", "Error(\"Super expression must either be null or a function\"": "Error(\"超表达式必须为 null 或函数\"", "Error(\"Invalid unit value \"": "Error(\"单位值无效\"", "Error(\"Value format \"": "Error(\"值格式\"", "Error(\"Unknown duration argument \"": "Error(\"未知持续时间参数\"", "Error(\"need to specify a reason the Duration is invalid\"": "Error(\"需要指定持续时间无效的原因\"", "Error(\"need to specify a reason the Interval is invalid\"": "Error(\"需要指定间隔无效的原因\"", "Error(\"Can't include meridiem when specifying 24-hour format\"": "Error(\"指定24小时格式时不能包含子午线\"", "Error(\"fromSeconds requires a numerical input\"": "Error(\"fromSeconds 需要数字输入\"", "Error(\"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"": "Error(\"不能将周年/周数字单位与年/月/日或序数混合使用\"", "Error(\"Can't mix ordinal dates with month/day\"": "Error(\"不能将序号日期与月/日混合使用\"", "Error(\"fromFormat requires an input string and a format\"": "Error(\"fromFormat 需要输入字符串和格式\"", "Error(\"need to specify a reason the DateTime is invalid\"": "Error(\"需要指定 DateTime 无效的原因\"", "Error(\"min requires all arguments be DateTimes\"": "Error(\"min 要求所有参数都是 DateTimes\"", "Error(\"max requires all arguments be DateTimes\"": "Error(\"max 要求所有参数都是 DateTimes\"", "Error(\"Unknown datetime argument: \"": "Error(\"未知日期时间参数:\"", "Error(\"The bits [\"": "Error(\"比特[\"", "Error(\"not a parser: \"": "Error(\"不是解析器:\"", "Error(\"not a number: \"": "Error(\"不是数字:\"", "Error(\"not a function: \"": "Error(\"不是函数:\"", "Error(\"not a string: \"": "Error(\"不是字符串:\"", "Error(\"seqMap needs at least one argument\"": "Error(\"seqMap 至少需要一个参数\"", "Error(\"not a regexp: \"": "Error(\"不是正则表达式:\"", "Error('unsupported regexp flag \"'": "Error('不支持的正则表达式标志\"'", "Error(\"not a string, regexp, or parser: \"": "Error(\"不是字符串, 正则表达式或解析器:\"", "Error(\".parse must be called with a string or Buffer as its argument\"": "Error(\".parse 必须以字符串或 Buffer 作为参数调用\"", "Error(\"infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause\"": "Error(\"在.many()解析器中检测到无限循环——通常是在可以接受零字符的解析器上调用.mony()\"", "Error(\"not an array: \"": "Error(\"不是数组:\"", "Error(\"seqObj: duplicate key \"": "Error(\"seqObj:重复密钥\"", "Error(\"duplicate key in bitSeqObj: \"": "Error(\"bitSeqObj 中的重复密钥:\"", "Error(\"Value specified to byte constructor (\"": "Error(\"为字节构造函数指定的值(\"", "Error(\"Option columns is empty\"": "Error(\"选项列为空\"", "Error(\"Unable to serialize unrecognized input\"": "Error(\"无法序列化无法识别的输入\"", "Error(\"Comment character same as delimiter\"": "Error(\"注释字符与分隔符相同\"", "Error(\"Input must be a string\"": "Error(\"输入必须是字符串\"", "Error(\"Not implemented.\"": "Error(\"未执行。\"", "Error(\"Unrecognized transferable value: \"": "Error(\"无法识别的可转让价值:\"", "Error(\"Cannot find module '\"": "Error(\"找不到模块'\"", "Error('resolver must be a function'": "Error('解析器必须是一个函数'", "Error('Cannot resolve promise with itself'": "Error('无法自行解析 promise'", "Error('must be an array'": "Error('必须是数组'", "Error(\"Cannot call a class as a function\"": "Error(\"不能将类作为函数调用\"", "Error('Failed to get type for BinaryArray'": "Error('无法获取 BinaryArray 的类型'", "error(\"Couldn't convert value into a JSON string: \"": "error(\"无法将值转换为 JSON 字符串:\"", "Error('Unkown type: '": "Error('未知类型:'", "Error('Database version must be a number.'": "Error('数据库版本必须是数字。'", "Error('Driver not found.'": "Error('找不到驱动程序。'", "Error('No available storage method found.'": "Error('找不到可用的存储方法。'", "Error(`Unrecognized argument type for argument '${arg}'`": "Error(`参数'${arg}'的参数类型无法识别`", "Error(`No implementation of '${this.name}' found for arguments: ${types.join(\", \")}`": "Error(`找不到参数'${this.name}'的实现: ${types.join(\", \")}`", "Error(\"object() requires an even number of arguments\"": "Error(\"object()需要偶数个参数\"", "Error(\"keys should be of type string for object(key1, value1, ...)\"": "Error(\"对象的键应为字符串类型(key1, value1, …)\"", "Error(\"extract(object, key1, ...) must be called with string keys\"": "Error(\"必须使用字符串键调用 extract(object, key1, …)\"", "Error(`Invalid regexp '${pat}' in regexreplace`": "Error(`regexreplace 中的正则表达式'${pat}'无效`", "Error('Invalid argument expected string'": "Error('预期的字符串参数无效'", "Error(`Invalid argument not valid semver ('${v}' received)`": "Error(`无效参数无效 semver(收到('${v}')`", "Error('Function called outside component initialization'": "Error('调用外部组件初始化的函数'", "Error(`The number of headers (${headers.length}) must match the number of columns (${values[0].length})`": "Error(`标头的数量(${headers.length})必须与列的数量(${values[0].length})匹配`", "Error(\"reduce(array, op) supports '+', '-', '/', '*', '&', and '|'\"": "Error(\"reduce(array, op)支持'+', '-', '/', '*', '&'和'|'\"", "Error(`Unrecognized level '${level}' (expected 1, 2, 3, 4, 5, or 6)`": "Error(`无法识别的级别 '${level}' (应为1, 2, 3, 4, 5或6)`", "Error(`Could not find CSV for path '${path}' (relative to origin '${originFile !== null && originFile !== void 0 ? originFile : \"/\"}')`": "Error(`找不到路径'${path}'的 CSV(相对于源'${originFile !== null && originFile !== void 0 ? originFile : \"/\"}')`", "Error(`Failed to parse query in 'pagePaths': ${ex}`": "Error(`无法解析'页面路径'${ex}中的查询`", ".desc(\"whitespace\"": ".desc(\"空白符\"", ".desc(\"a digit\"": ".desc(\"一个数字\"", ".desc(\"optional digits\"": ".desc(\"可选数字\"", ".desc(\"a letter\"": ".desc(\"一封信\"", ".desc(\"optional letters\"": ".desc(\"可选字母\"", ".desc(\"optional whitespace\"": ".desc(\"可选空格\"", ".desc(\"none of '\"": ".desc(\"没有 '\"", ".desc(\"boolean ('true' or 'false')\"": ".desc(\"boolean('true' 或 'false')\"", ".desc(\"tag ('#hello/stuff')\"": ".desc(\"标签('#hello/stuff')\"", ".desc(\"variable identifier\"": ".desc(\"变量标识符\"", ".desc(\"file link\"": ".desc(\"文件链接\"", ".desc(\"'+' or '-'\"": ".desc(\"'+' 或 '-'\"", ".desc(\"'*' or '/' or '%'\"": ".desc(\"'*' 或 '/' 或 '%'\"", ".desc(\"'>=' or '<=' or '!=' or '=' or '>' or '<'\"": ".desc(\"'>=' 或 '<=' 或 '!=' 或 '=' 或 '>' 或 '<\"", ".desc(\"'and' or 'or'\"": ".desc(\"'and' 或 'or'\"", ".desc(\"date in format YYYY-MM[-DDTHH-MM-SS.MS]\"": ".desc(\"日期格式为 YYYY-MM[-DDTHH-MM-SS.MS]\"", ".desc(\"date in format YYYY-MM[-DDTHH-MM-SS.MS] or in shorthand\"": ".desc(\"日期格式为 YYYY-MM[-DDTHH-MM-SS.MS]或简写\"", ".desc(\"duration like 4hr2min\"": ".desc(\"持续时间约为4小时2分钟\"", ".desc(\"list ('[1, 2, 3]')\"": ".desc(\"列表('[1, 2, 3]')\"", ".desc(\"object ('{ a: 1, b: 2 }')\"": ".desc(\"对象('{ a: 1, b: 2 }')\"", ".desc(\"negated field\"": ".desc(\"否定字段\"", ".desc(\"TABLE or LIST or TASK or CALENDAR\"": ".desc(\"表格, 列表, 任务或日历\"", ".desc(\"WHERE <expression>\"": ".desc(\"WHERE<表达式>\"", ".desc(\"LIMIT <value>\"": ".desc(\"LIMIT <value>极限<值>\"", ".desc(\"FLATTEN <value> [AS <name>]\"": ".desc(\"FLATTEN<value>[AS<name>]  扁平化<字段名>[AS<替换后的字段名>]\"", ".desc(\"GROUP BY <value> [AS <name>]\"": ".desc(\"GROUP BY <value> [AS <name>]  按<值>分组[AS<替换后的值>]\"", "\"Evaluation Error: \"": "\"评估错误: \"", ".setDesc(\"If enabled, views will automatically refresh when files in your vault change; this can negatively affect\" +\n            \" some functionality like embeds in views, so turn it off if such functionality is not working.\")": ".setDesc(\"如果启用,当保险库中的文件更改时,视图将自动刷新;这可能会产生负面影响。\" +\n            \" 如果某些功能,比如视图中的嵌入功能无法正常工作,请关闭它们。\")", ".setDesc(\"The name of the default ID column in tables, when the table is on grouped data; this is the auto-generated first column\" +\n            \"that links to the source file/group.\")": ".setDesc(\"在表格对分组数据进行操作时,默认ID列的名称;这是自动生成的第一列\" +\n            \"链接到file/groupp.\")", "super(`Invalid DateTime: ${reason.toMessage()}`);": "super(`无效的 DateTime: ${reason.toMessage()}`);", "super(`Invalid Interval: ${reason.toMessage()}`);": "super(`无效的 Interval: ${reason.toMessage()}`);", "super(`Invalid Duration: ${reason.toMessage()}`);": "super(`无效的 Duration: ${reason.toMessage()}`);", "super(`Invalid unit ${unit}`);": "super(`无效的单位 ${unit}`);", "super(\"Zone is an abstract class\");": "super(\"Zone 是一个抽象类\");", "Error(`Invalid unit value ${value}`)": "Error(`无效的单位值 ${value}`)", "Error(`Value format ${format} is out of range for property format`)": "Error(`值格式 ${format} 超出了属性格式的范围`)", "Error(`Unrecognized transfer type '${transferable[\"___transfer-type\"]}'`)": "Error(`未识别的传输类型 '${transferable[\"___transfer-type\"]}'`)", "Error('Could not dynamically require \"' + path + '\". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')": "Error('无法动态加载 \"' + path + '\"。请适当配置 @rollup/plugin-commonjs 的 dynamicRequireTargets 或/和 ignoreDynamicRequires 选项以使此 require 调用生效。')", "Error(\"Can't call config() after localforage \" + 'has been used.')": "Error(\"在使用 localforage 之后不能调用 config()\")", "Error(\"Not a number for format( (${ f }): ${ d }\")": "Error(\"不是数字,格式为 (${ f }): ${ d }\")", "Error(`Can't handle format (${f}) on date string (${d})`)": "Error(`无法处理日期字符串 (${d}) 上的格式 (${f})`)", "Error(`Could not find CSV for path '${path}' (relative to origin '${originFile ?? \"/\"}')`)": "Error(`无法找到路径 '${path}' 的 CSV 文件(相对于源 '${originFile ?? \"/\"}')`)", "Error(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`": "Error(\n        `Duration.fromObject: 参数应为对象,实际得到 ${\n          obj === null ? \"null\" : typeof obj\n        }`", "Error(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`": "Error(\n        `未知的持续时间参数 ${durationLike},类型为 ${typeof durationLike}`", "Error(\n        \"Can't include meridiem when specifying 24-hour format\"": "Error(\n        \"在指定24小时格式时不能包含上午/下午标记\"", "Error(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`": "Error(\n        `fromMillis 需要数值输入,但接收到的是 ${typeof milliseconds} 类型的值 ${milliseconds}`", "Error(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"": "Error(\n        \"不能将 weekYear/weekNumber 单位与 year/month/day 或序数混合使用\"", "Error(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`": "Error(\n        `未知的 datetime 参数: ${dateTimeish},类型为 ${typeof dateTimeish}`", "Error(n+\" requires integer length in range [0, 6].\")": "Error(n+\" 需要范围在 [0, 6] 内的整数长度。\")", "Error('Method ' + methodName + ' is not implemented by the current driver')": "Error('方法 ' + method<PERSON>ame + ' 尚未由当前驱动程序实现')", "Error(\n      `Invalid operator type, expected string but got ${typeof op}`": "Error(\n      `无效的操作符类型,期望字符串但得到 ${typeof op}`", "Error(\n      `Invalid operator, expected one of ${allowedOperators.join('|')}`": "Error(\n      `无效的操作符,期望以下之一: ${allowedOperators.join('|')}`", ".warn(key + ' used as a key, but it is not a string.')": ".warn(key + ' 用作键,但它不是一个字符串。')", ".warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.')": ".warn('The database \"' + dbInfo.name + '\"' + ' 已从版本 ' + e.oldVersion + ' 升级到版本 ' + e.newVersion + ',但存储 \"' + dbInfo.storeName + '\" 已存在。')", ".warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.')": ".warn('The database \"' + dbInfo.name + '\"' + \" 不能从版本 \" + dbInfo.db.version + ' 降级到版本 ' + dbInfo.version + '。')", ".warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed')": ".warn('dropInstance 被阻塞,直到数据库 \"' + options.name + '\" 的所有打开连接关闭')", "invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`)": "invalid(\"无法解析\", `输入 \"${text}\" 无法解析为 ISO 8601`)", "invalid(\"missing or invalid start\")": "invalid(\"缺少或无效的开始\")", "invalid(\"missing or invalid end\")": "invalid(\"缺少或无效的结束\")", "invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    )": "invalid(\n      \"结束早于开始\",\n      `区间结束时间必须在其开始时间之后,但你提供的开始时间为 ${start.toISO()},结束时间为 ${end.toISO()}`\n    )", "Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  )": "Invalid(\n    \"单位超出范围\",\n    `你指定了 ${value}(类型为 ${typeof value})作为 ${unit},这是无效的`\n  )", "Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`)": "Invalid(\"不支持的时区\", `时区 \"${zone.name}\" 不被支持`)", "Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)": "Invalid(\"无法解析\", `输入 \"${text}\" 无法解析为 ${format}`)", "invalid(\"invalid input\")": "invalid(\"无效输入\")", "Invalid(\"invalid input\")": "Invalid(\"无效输入\")", "invalid(\"Timestamp out of range\")": "invalid(\"时间戳超出范围\")", "invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      )": "invalid(\n        \"不匹配的星期几\",\n        `你不能同时指定星期几为 ${normalized.weekday} 和日期为 ${inst.toISO()}`\n      )", "invalid(\"created by diffing an invalid DateTime\")": "invalid(\"由无效的 DateTime 计算差异创建\")", ".failure(`Failed to load data from path '${path}'.`)": ".failure(`未能从路径 '${path}' 加载数据。`)", ".failure(`Could not find file \"${source.file}\" during link lookup - does it exist?`)": ".failure(`在链接查找期间未找到文件 \"${source.file}\" - 文件是否存在？`)", ".failure(`Unrecognized operator '${source.op}'.`)": ".failure(`未识别的操作符 '${source.op}'。`)", ".failure(`Unrecognized value '${left}'`)": ".failure(`未识别的值 '${left}'`)", ".failure(`Unrecognized value '${right}'`)": ".failure(`未识别的值 '${right}'`)", ".failure(`No implementation found for '${leftType} ${op} ${rightType}'`)": ".failure(`未找到 '${leftType} ${op} ${rightType}' 的实现`)", ".failure(`Unrecognized function name '${func}'`)": ".failure(`未识别的函数名 '${func}'`)", ".failure(`Cannot call type '${Values.typeOf(func)}' as a function`)": ".failure(`不能将类型 '${Values.typeOf(func)}' 作为函数调用`)", ".failure(\"Can only index with a string or number\")": ".failure(\"只能使用字符串或数字进行索引\")", ".failure(\"Unrecognized object to index into: \" + object)": ".failure(\"未识别的对象用于索引: \" + object)", ".failure('can only index into objects with strings (a.b or a[\"b\"])')": ".failure('只能使用字符串对对象进行索引 (a.b 或 a[\"b\"])')", ".failure('can only index into links with strings (a.b or a[\"b\"])')": ".failure('只能使用字符串对链接进行索引 (a.b 或 a[\"b\"])')", ".failure(\"Array indexing requires either a number (to get a specific element), or a string (to map all elements inside the array)\")": ".failure(\"数组索引需要一个数字(获取特定元素)或一个字符串(映射数组中的所有元素)\")", ".failure(\"string indexing requires a numeric index (string[index])\")": ".failure(\"字符串索引需要一个数字索引 (string[index])\")", ".failure(\"date indexing requires a string representing the unit\")": ".failure(\"日期索引需要一个表示单位的字符串\")", ".failure(\"duration indexing requires a string representing the unit\")": ".failure(\"持续时间索引需要一个表示单位的字符串\")", ".failure(\"Failed to execute 'limit' statement: \" + limiting.error)": ".failure(\"执行 'limit' 语句失败: \" + limiting.error)", ".failure(`Failed to execute 'limit' statement: limit should be a number, but got '${Values.typeOf(limiting.value)}' (${limiting.value})`)": ".failure(`执行 'limit' 语句失败: limit 应该是一个数字,但得到了 '${Values.typeOf(limiting.value)}' (${limiting.value})`)", ".failure(\"Unrecognized query operation '\" + op.type + \"'\")": ".failure(\"未识别的查询操作 '\" + op.type + \"'\")", ".failure(`Every row during operation '${op.type}' failed with an error; first": ".failure(`在操作 '${op.type}' 过程中每一行都失败并出现错误;第一个", ".failure(`Every row during final data extraction failed with an error; first": ".failure(`在最终数据提取过程中每一行都失败并出现错误;第一个", ".failure(`Failed to parse expression \"${expression}\"`)": ".failure(`未能解析表达式 \"${expression}\"`)", ".failure(\"Cannot render calendar queries to markdown.\")": ".failure(\"无法将日历查询渲染为 Markdown。\")", "\"Dataview: Query returned 0 results.\"": "\"Dataview: 查询返回 0 结果.\"", "missing Intl.DateTimeFormat.formatToParts support": "缺少 Intl.DateTimeFormat.formatToParts 支持", ".info('Redefining LocalForage driver: ' + driverName)": ".info('重新定义 LocalForage 驱动程序: ' + driverName)", ".fail(`Unrecognized query type '${type}'`)": ".fail(`未识别的查询类型 '${type}'`)", "description: \"Cache metadata about files and sections in the dataview index.\"": "description: \"缓存 dataview 索引中文件和部分的元数据。\"", "`Dataview: custom view not found for '${simpleViewPath}' or '${complexViewPath}'.`": "`Dataview: 未找到自定义视图 '${simpleViewPath}' 或 '${complexViewPath}'.`", "`Dataview: Failed to execute view '${viewFile.path}'.\n\n${ex}`": "`Dataview: 执行视图 '${viewFile.path}' 失败.\n\n${ex}`", "\"Dataview JS queries are disabled. You can enable them in the Dataview settings.\"": "\"Dataview JS 查询已禁用。您可以在 Dataview 设置中启用它们。\"", "\"Dataview (for inline JS query '\" + this.script + \"'): \"": "\"Dataview (用于内联 JS 查询 '\" + this.script + \"'): \"", "\"Dataview (for inline query '\" + this.fieldText + \"'): \"": "\"Dataview(用于内联查询 '\" + this.fieldText + \"'):\"", "`Dataview (inline field '${potentialField}'): ${field.error}`": "`Dataview(内联字段 '${potentialField}'): ${field.error}`", "`Dataview (inline field '${code}'): ${field.error}`": "`Dataview(内联字段 '${code}'): ${field.error}`", "`Dataview (for inline query '${fieldValue}'): ${intermediateResult.error}`": "`Dataview(用于内联查询 '${fieldValue}'): ${intermediateResult.error}`", "\"Date-time format for task completion date when toggling a task's checkbox in a Dataview view (see Luxon date format options).\"": "\"在 Dataview 视图中切换任务复选框时的任务完成日期格式(请参见 Luxon 日期格式选项)。\"", "'Only available when \"automatic task completion tracking\" is enabled and \"use emoji shorthand for completion\" is disabled.'": "'仅在启用 \"自动任务完成跟踪\" 且禁用 \"使用表情符号简写表示完成\" 时可用。'", "\"Currently: \"": "\"当前:\"", "\"(disabled; enable in settings)\"": "\"(已禁用;在设置中启用)\"", "`Dataview (for inline JS query '${code}'): ${e}`": "`Dataview(用于内联 JS 查询 '${code}'): ${e}`", "name: \"Force refresh all views and blocks\"": "name: \"强制刷新所有视图和块\"", "name: \"Drop all cached file metadata\"": "name: \"清除所有缓存的文件元数据\"", "name: \"Rebuild current view\"": "name: \"重建当前视图\"", "name: \"Dataview Indexer \"": "name: \"Dataview 索引器 \"", ".setDesc(\"If enabled, views will automatically refresh when files in your vault change; this can negatively affect\" +\n            \" some functionality like embeds in views, so turn it off if such functionality is not working.\")": ".setDesc(\"如果启用，当保险库中的文件更改时，视图将自动刷新；这可能会产生负面影响。\" +\n            \" 如果某些功能，比如视图中的嵌入功能无法正常工作，请关闭它们。\")", ".setDesc(\"The name of the default ID column in tables, when the table is on grouped data; this is the auto-generated first column\" +\n            \"that links to the source file/group.\")": ".setDesc(\"在表格对分组数据进行操作时，默认ID列的名称；这是自动生成的第一列\" +\n            \"链接到file/groupp.\")", "Error('Could not dynamically require \"'": "Error('无法动态要求\"'", "Error(\"Can't call config() after localforage \"": "Error(\"无法在 localfeed 之后调用 config()\"", "Error('Method '": "Error('方法'", ".setDesc(\"The prefix to inline queries (to mark them as Dataview queries). Defaults to '='.\")": ".setDesc(\"内联查询的前缀（用于标记为Dataview查询）。默认为'='。\")", ".setDesc(\"The prefix to JavaScript inline queries (to mark them as DataviewJS queries). Defaults to '$='.\")": ".setDesc(\"JavaScript 内联查询的前缀（用于将其标记为 DataviewJS 查询）。默认值为 '$='。\")", ".setDesc(\"If toggled off, the small number in the result header of TASK and TABLE queries will be hidden.\")": ".setDesc(\"如果切换关闭，TASK 和 TABLE 查询结果头部的小数字将被隐藏。\")", ".setDesc(\"What null/non-existent should show up as in tables, by default. This supports Markdown notation.\")": ".setDesc(\"在表格中默认应显示什么为空/不存在。这支持Markdown语法。\")", " .setDesc(\"If enabled, inline queries will also be evaluated inside full code blocks.\")": " .setDesc(\"如果启用，内联查询也将在完整代码块中进行评估。\")", ".setDesc(\"If set, queries which return 0 results will render a warning message.\")": ".setDesc(\"如果设置了该选项，返回0结果的查询将显示警告信息。\")", ".setDesc(\"How long to wait (in milliseconds) for files to stop changing before updating views.\")": ".setDesc(\"在更新视图之前，等待文件停止更改的时间（以毫秒为单位）。\")", ".setDesc(\"The default date format (see Luxon date format options).\" ": ".setDesc(\"默认日期格式（参见Luxon日期格式选项）。\" ", ".setDesc(\"The default date and time format (see Luxon date format options).\" +": ".setDesc(\"默认日期和时间格式（参见Luxon日期格式选项）。\" +", ".setDesc(\"The name of the default ID column in tables; this is the auto-generated first column that links to the source file.\")": ".setDesc(\"表格中默认ID列的名称；这是自动生成的第一列，用于链接到源文件。\")", "setDesc(\"The name of the default ID column in tables, when the table is on grouped data; this is the auto-generated first column\"": "setDesc(\"当表格基于分组数据时，默认 ID 列的名称；这是自动生成的第一列。\"", ".appendText(\"If enabled, Dataview will automatically append tasks with their completion date when they are checked in Dataview views.\");": ".appendText(\"如果启用，Dataview将在Dataview视图中自动附加已完成任务的完成日期。\");", ".appendText('If enabled, will use emoji shorthand instead of inline field formatting to fill out implicit task field \"completion\".')": ".appendText('如果启用，将使用表情符号简写代替内联字段格式来填写隐式任务字段“完成”。')", ".appendText(\"Disable this to customize the completion date format or field name, or to use Dataview inline field formatting.\");": ".appendText(\"禁用此选项以自定义完成日期格式或字段名称，或使用Dataview内联字段格式。\");", ".appendText('Only available when \"automatic task completion tracking\" is enabled.');": ".appendText('仅在启用“自动任务完成跟踪”时可用。');", ".setDesc('Only available when \"automatic task completion tracking\" is enabled.');": ".setDesc('仅在启用“自动任务完成跟踪”时可用。');", ".setDesc(\"If enabled, completing a task in a Dataview will automatically complete its subtasks too.\")": ".setDesc(\"如果启用，在数据视图中完成一个任务将自动完成其子任务。\")"}}