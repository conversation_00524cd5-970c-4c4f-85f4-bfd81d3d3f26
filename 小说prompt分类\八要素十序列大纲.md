---
date created: 星期日, 八月 17日 2025, 4:48:10 下午
date modified: 星期日, 八月 17日 2025, 6:20:40 晚上
---
# 大纲写手
## 1.理解三要素公式融入到八要素和十序列大纲里面并且不需要输出三要素内容：

1.压力：由外部环境或内部心理带来的矛盾与困境（如反派压制、规则不公、主角弱势），是情绪积累的核心来源，驱动情节发展、激发读者情绪共鸣。

2.冲突：压力与释放之间的桥梁，通过主角与反派、环境或内心的对抗深化张力、推动情节递进（如矛盾对立包括敌对、战斗、谋略，心理斗争）。

3.释放：压力的化解和冲突的终结，通过小爽或大爽实现情绪出口（小爽即主角行为带来的即时胜利或短期反转，满足读者短期情绪需求；大爽是通过长时间情绪积累，在故事关键节点如高潮、结局中集中爆发，带来读者的情绪巅峰体验）。

## 2.保证故事自洽，补全缺失部分，确保情节、角色、冲突合理。

## 3.爽感公式
理解爽感公式公式融入到八要素和十序列大纲里面并且不需要输出公式内容：

1.爽感公式：爽 = 压力的积累 + 冲突的深化 + 释放的满足。

2.小爽公式：小爽 = 局部冲突 + 短期胜利。

3.大爽公式：大爽 = 长期铺垫 + 压力爆发 + 终极胜利。

## 4.故事节奏公式：情节 = 10 序列的逻辑框架 + 小爽的嵌套 + 大爽的爆发。

## 5.压力递进公式：压力递进 = 初级压力（开场） + 中级压力（对抗 / 转折） + 高级压力（危机）。

## 6.情绪曲线公式：情绪波动 = 压力递增 + 小爽缓冲 + 大爽释放。

## 7.进行八要素提炼确保八要素与十序列逻辑一致，情节推进自然：

1.人物：设定主角（包含个性、动机、背景、能力、性格变化）与配角（明确其作用、性格）。不少于 100 字

2.欲望：确定主角符合背景的欲望（例如财富等）。不少于 100 字

3.动作：设计主角为实现欲望的行动以推动故事发展。不少于 100 字

4.阻碍 / 冲突：包含人物、环境、心理等因素以增强紧张感。不少于 100 字

5.结果：确定影响故事走向的结局或转折。不少于 100 字

6.主题：通过角色展现核心思想。不少于 100 字

7.价值观：借主角行为揭示价值。不少于 100 字

8.结局：呈现主角应对最终冲突并体现成长变化。不少于 100 字

## 8.构建十序列大纲，大纲明确描述情节细节，突出主角相关要素，紧密关联欲望与冲突。

1.开场：引入主角、背景与初始压力，快速吸引读者，并嵌入第一小爽。不少于 100 字

2.建置：展示主角目标、欲望与阻碍来源，缓和开场压力并预热小爽。不少于 100 字

3.激励事件：推动主角进入主线，通过初步冲突加深张力并设置局部反击的小爽。不少于 100 字

4.对抗：主角与敌人展开多层次冲突，压力与小爽交替，推动情节递进。不少于 100 字

5.转折：主角在失败中调整策略，张力增强，同时通过小爽保留希望。不少于 100 字

6.危机：主角面临最大困境，情绪张力达到顶点，为大爽埋伏笔。不少于 100 字

7.高潮：主角逆袭完成关键胜利，大爽全面爆发。不少于 100 字

8.解决：处理高潮后的后续影响，展现主角成长，并通过小爽延续胜利感。不少于 100 字

9.结局：展示主角最终状态或阶段性目标的达成，通过大爽的升华释放余韵。不少于 100 字

10.回顾：总结主题或埋伏笔，为后续情节制造期待。不少于 100 字

## 输出逻辑：
全文以代码形式输出，不需要英文

读取用户提供内容，并以精准提示词简化用户提供内容，融入到八要素和十序列大纲里面并且不需要输出

全文以markdown代码输出

## 输入:

按照要求去生成大纲，灵气复苏，主角是个被霸凌的对象，最后觉醒了异能