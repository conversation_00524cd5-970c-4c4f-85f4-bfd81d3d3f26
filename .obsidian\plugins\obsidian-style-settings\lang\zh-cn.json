{"manifest": {"translationVersion": 1740803299534, "pluginVersion": "1.0.9"}, "description": {"original": "Offers controls for adjusting theme, plugin, and snippet CSS variables.", "translation": "提供用于调整主题、插件和片段 CSS 变量的控件。"}, "dict": {"text:\"Copy to clipboard\"": "text:\"复制到剪贴板\"", "text:\"Download\"": "text:\"下载\"", "text:\"Import from file\"": "text:\"从文件导入\"", "text:\"Import\"": "text:\"导入\"", "text:\"Export\"": "text:\"导出\"", ".export(\"All settings\"": ".export(\"所有设置\"", ".setButtonText(\"Save\")": ".setButtonText(\"保存\")", ".setName(\"Import style setting\")": ".setName(\"导入样式设置\")", ".setTooltip(\"Export settings\")": ".setTooltip(\"导出设置\")", ".createEl(\"a\",{text:\"Click here for details and examples.\",href:\"https://github.com/mgmeyers/obsidian-style-settings#obsidian-style-settings-plugin\"})": ".createEl(\"a\",{text:\"点击此处获得细节与示例。\",href:\"https://github.com/mgmeyers/obsidian-style-settings#obsidian-style-settings-plugin\"})", ".setTooltip('Reset all settings to default')": ".setTooltip('将所有设置重置为默认值')", ".setTooltip('Export settings')": ".setTooltip('导出设置')", ".setName('Import style setting')": ".setName('导入样式设置')", ".setDesc('Import an entire or partial configuration. Warning: this may override existing settings')": ".setDesc('导入整个或部分配置。警告：这可能会覆盖现有设置')", ".setPlaceholder('Paste config here...')": ".setPlaceholder('在此处粘贴配置...')", ".setButtonText('Save')": ".setButtonText('保存')", "text: 'Error importing config'": "text: '导入配置时出错'", "text: 'No style settings found'": "text: '未找到样式设置'", "text: 'Click here for details and examples.'": "text: '点击此处查看详细信息和示例。'", "text: 'Default (light): '": "text: '默认（浅色）: '", "text: 'Default (dark): '": "text: '默认（深色）: '", ".setText(`Error importing style settings: ${p}`)": ".setText(`导入样式设置错误: ${p}`)", ".setText(\"Error importing style settings: config is empty\")": ".setText(\"导入样式设置错误: 配置为空\")", ".appendText(\"Style settings configured by theme and plugin authors will show up here. You can also create your own configuration by creating a CSS snippet in your vault. \")": ".appendText(\"由主题和插件作者配置的样式设置将显示在这里。您也可以通过在您的仓库中创建 CSS 片段来创建自己的配置。\")", "name:\"Show style settings view\"": "name:\"显示样式设置视图\"", "name:\"Style Settings\"": "name:\"样式设置\"", "text:\"No style settings found\"": "text:\"未找到样式设置\"", "\"aria:btn:clear\":\"clear and close\"": "\"aria:btn:clear\":\"清除并关闭\"", "\"aria:input\":\"color input field\"": "\"aria:input\":\"颜色输入字段\"", "\"aria:hue\":\"hue selection slider\"": "\"aria:hue\":\"色调选择滑块\"", "name:`Toggle ${a.title}`": "name:`切换 ${a.title}`", "text:\"Error importing config\"": "text:\"导入配置错误\"", "text:\"Click here for details and examples.\"": "text:\"点击此处查看详细信息和示例。\"", "\"ui:dialog\":\"color picker dialog\"": "\"ui:dialog\":\"颜色选择对话框\"", "\"btn:toggle\":\"toggle color picker dialog\"": "\"btn:toggle\":\"切换颜色选择对话框\"", "\"btn:swatch\":\"color swatch\"": "\"btn:swatch\":\"颜色样本\"", "\"btn:last-color\":\"use previous color\"": "\"btn:last-color\":\"使用上一个颜色\"", "\"aria:palette\":\"color selection area\"": "\"aria:palette\":\"颜色选择区域\"", "\"aria:opacity\":\"selection slider\"": "\"aria:opacity\":\"透明度选择滑块\"", ".error(\"Error: Style Settings locale not found\",ct)": ".error(\"错误：找不到 Style Settings 语言包\",ct)", ".error(`${de(\"Error:\")": ".error(`${de(\"错误:\")", ".error(\"Style Settings | Failed to render section\",p)": ".error(\"Style Settings | 渲染部分失败\",p)", "text:`${de(\"Default:\")} `": "text:`${de(\"默认:\")} `", "text:\"Default (light): \"": "text:\"默认（浅色）: \"", "text:\"Default (dark): \"": "text:\"默认（深色）: \"", "text:`Error: ${i.name}`": "text:`错误: ${i.name}`", "Error(\"unknown format: \"+s)": "Error(\"未知格式: \"+s)", "Error(\"unknown hex color: \"+r)": "Error(\"未知十六进制颜色: \"+r)", "Error(\"unknown color name: \"+r)": "Error(\"未知颜色名称: \"+r)", "Error(\"unknown num color: \"+r)": "Error(\"未知数字颜色: \"+r)", "Error(\"unknown channel \"+g+\" in mode \"+o)": "Error(\"模式 \"+o+\" 中未知通道 \"+g)", "Error(\"interpolation mode \"+y+\" is not defined\")": "Error(\"插值模式 \"+y+\" 未定义\")", "Error(\"unsupported value for Color.set\")": "Error(\"Color.set 不支持的值\")", "Error(\"unknown channel \"+y+\" in mode \"+b)": "Error(\"模式 \"+b+\" 中未知通道 \"+y)", "Error(\"unknown blend mode \"+o)": "Error(\"未知混合模式 \"+o)", "Error(\"Logarithmic scales are only possible for values > 0\")": "Error(\"对数刻度仅适用于值 > 0\")", "Error(\"Popper- or reference-element missing.\")": "Error(\"缺少 Popper 或参考元素。\")", "Error(\"Expected a string\")": "Error(\"期望字符串\")", "Error(\"Date resolve error\")": "Error(\"日期解析错误\")", "Error(\"Function yaml.\"+e+\" is removed in js-yaml 4. Use yaml.\"+n+\" instead, which is now safe by default.\")": "Error(\"函数 yaml.\"+e+\" 在 js-yaml 4 中已被移除。请改用 yaml.\"+n+\"，现在默认是安全的。\")", "W(n,\"duplication of %YAML directive\")": "W(n,\"%YAML 指令重复\")", "W(n,\"YAML directive accepts exactly one argument\")": "W(n,\"YAML 指令只接受一个参数\")", "W(n,\"ill-formed argument of the YAML directive\")": "W(n,\"YAML 指令的参数格式不正确\")", "W(n,\"unacceptable YAML version of the document\")": "W(n,\"文档的 YAML 版本不被接受\")", "W(n,\"TAG directive accepts exactly two arguments\")": "W(n,\"TAG 指令只接受两个参数\")", "W(n,\"ill-formed tag handle (first argument)\")": "W(n,\"标签句柄格式不正确（第一个参数）\")", "W(n,'there is a previously declared suffix for \"'+i+'\" tag handle')": "W(n,'已有先前声明的后缀 \"'+i+'\" 标签句柄')", "W(n,\"ill-formed tag prefix (second argument)\")": "W(n,\"标签前缀格式不正确（第二个参数）\")", "W(n,\"tag prefix is malformed: \"+l)": "W(n,\"标签前缀格式不正确: \"+l)", "W(e,\"expected valid JSON character\")": "W(e,\"期望有效的 JSON 字符\")", "W(e,\"the stream contains non-printable characters\")": "W(e,\"流中包含不可打印的字符\")", "W(e,\"cannot merge mappings; the provided source object is unacceptable\")": "W(e,\"无法合并映射；提供的源对象不被接受\")", "W(e,\"nested arrays are not supported inside keys\")": "W(e,\"嵌套数组在键内部不被支持\")", "W(e,\"duplicated mapping key\")": "W(e,\"重复的映射键\")", "W(e,\"a line break is expected\")": "W(e,\"期望换行符\")", "W(e,\"unexpected end of the document within a single quoted scalar\")": "W(e,\"在单引号标量内意外结束文档\")", "W(e,\"unexpected end of the stream within a single quoted scalar\")": "W(e,\"在单引号标量内意外结束流\")", "W(e,\"expected hexadecimal character\")": "W(e,\"期望十六进制字符\")", "W(e,\"unknown escape sequence\")": "W(e,\"未知的转义序列\")", "W(e,\"unexpected end of the document within a double quoted scalar\")": "W(e,\"在双引号标量内意外结束文档\")", "W(e,\"unexpected end of the stream within a double quoted scalar\")": "W(e,\"在双引号标量内意外结束流\")", "W(e,\"expected the node content, but found ','\")": "W(e,\"期望节点内容，但找到了 ','\")", "W(e,\"missed comma between flow collection entries\")": "W(e,\"流集合条目之间缺少逗号\")", "W(e,\"unexpected end of the stream within a flow collection\")": "W(e,\"在流集合内意外结束流\")", "W(e,\"repeat of a chomping mode identifier\")": "W(e,\"重复的折叠模式标识符\")", "W(e,\"bad explicit indentation width of a block scalar; it cannot be less than one\")": "W(e,\"块标量的显式缩进宽度不正确；它不能小于一\")", "W(e,\"repeat of an indentation width identifier\")": "W(e,\"重复的缩进宽度标识符\")", "W(e,\"tab characters must not be used in indentation\")": "W(e,\"缩进中不允许使用制表符\")", "W(e,\"bad indentation of a sequence entry\")": "W(e,\"序列条目的缩进不正确\")", "W(e,\"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line\")": "W(e,\"显式映射对不完整；缺少键节点；或后面跟着一个非制表符空行\")", "W(e,\"a whitespace character is expected after the key-value separator within a block mapping\")": "W(e,\"块映射中的键值分隔符后期望空格字符\")", "W(e,\"can not read an implicit mapping pair; a colon is missed\")": "W(e,\"无法读取隐式映射对；缺少冒号\")", "W(e,\"can not read a block mapping entry; a multiline key may not be an implicit key\")": "W(e,\"无法读取块映射条目；多行键不能是隐式键\")", "W(e,\"bad indentation of a mapping entry\")": "W(e,\"映射条目的缩进不正确\")", "W(e,\"duplication of a tag property\")": "W(e,\"标签属性重复\")", "W(e,\"unexpected end of the stream within a verbatim tag\")": "W(e,\"在逐字标签内意外结束流\")", "W(e,\"tag suffix cannot contain exclamation marks\")": "W(e,\"标签后缀不能包含感叹号\")", "W(e,\"named tag handle cannot contain such characters\")": "W(e,\"命名标签句柄不能包含此类字符\")", "W(e,\"tag suffix cannot contain flow indicator characters\")": "W(e,\"标签后缀不能包含流指示符字符\")", "W(e,\"tag name cannot contain such characters: \"+l)": "W(e,\"标签名称不能包含此类字符: \"+l)", "W(e,\"tag name is malformed: \"+l)": "W(e,\"标签名称格式不正确: \"+l)", "W(e,'undeclared tag handle \"'+i+'\"')": "W(e,'未声明的标签句柄 \"'+i+'\"')", "W(e,\"duplication of an anchor property\")": "W(e,\"锚属性重复\")", "W(e,\"name of an anchor node must contain at least one character\")": "W(e,\"锚节点的名称必须至少包含一个字符\")", "W(e,\"name of an alias node must contain at least one character\")": "W(e,\"别名节点的名称必须至少包含一个字符\")", "W(e,'unidentified alias \"'+t+'\"')": "W(e,'未识别的别名 \"'+t+'\"')", "W(e,\"alias node should not have any properties\")": "W(e,\"别名节点不应有任何属性\")", "W(e,'unacceptable node kind for !<?> tag; it should be \"scalar\", not \"'+e.kind+'\"')": "W(e,'!<?> 标签的节点类型不被接受；应该是 \"scalar\"，而不是 \"'+e.kind+'\"')", "W(e,\"unknown tag !<\"+e.tag+\">\")": "W(e,\"未知标签 !<\"+e.tag+\">\")", "W(e,\"unacceptable node kind for !<\"+e.tag+'> tag; it should be \"'+K.kind+'\", not \"'+e.kind+'\"')": "W(e,\"!<\"+e.tag+'> 标签的节点类型不被接受；应该是 \"'+K.kind+'\"，而不是 \"'+e.kind+'\"')", "W(e,\"cannot resolve a node with !<\"+e.tag+\"> explicit tag\")": "W(e,\"无法解析带有 !<\"+e.tag+\"> 显式标签的节点\")", "W(e,\"directive name must not be less than one character in length\")": "W(e,\"指令名称长度不得少于一个字符\")", "W(e,\"directives end mark is expected\")": "W(e,\"期望指令结束标记\")", "W(e,\"end of the stream or a document separator is expected\")": "W(e,\"期望流的结束或文档分隔符\")", "W(t,\"null byte is not allowed in input\")": "W(t,\"输入中不允许出现空字节\")", "$e('Unknown option \"'+t+'\" is met in definition of \"'+e+'\" YAML type.')": "$e('在定义 \"'+e+'\" YAML 类型时遇到了未知选项 \"'+t+'\"')", "$e('Unknown kind \"'+this.kind+'\" is specified for \"'+e+'\" YAML type.')": "$e('为 \"'+e+'\" YAML 类型指定了未知类型 \"'+this.kind+'\"')", "$e(\"Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })\")": "$e(\"Schema.extend 参数应为 Type、[ Type ] 或 schema 定义 ({ implicit: [...], explicit: [...] })\")", "$e(\"Specified list of YAML types (or a single Type object) contains a non-Type object.\")": "$e(\"指定的 YAML 类型列表（或单个 Type 对象）包含非 Type 对象。\")", "$e(\"There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.\")": "$e(\"schema 的隐式列表中存在非标量类型。此类类型的隐式解析不受支持。\")", "$e(\"There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.\")": "$e(\"schema 的隐式列表中存在多类型。多标签只能列为显式。\")", "$e(\"expected a single document in the stream, but found more\")": "$e(\"期望流中只有一个文档，但发现更多\")", "$e(\"code point within a string may not be greater than 0xFFFFFFFF\")": "$e(\"字符串中的码点不能大于 0xFFFFFFFF\")", "$e(\"impossible error: invalid scalar style\")": "$e(\"不可能的错误：无效的标量样式\")", "$e(\"sortKeys must be a boolean or a function\")": "$e(\"sortKeys 必须是布尔值或函数\")", "$e(\"!<\"+f.tag+'> tag resolver accepts not \"'+p+'\" style');e.dump=a}return!0}return!1}function Je(e,n,t,a,i,l,c){e.tag=null,e.dump=t,Zi(e,t,!1)||Zi(e,t,!0);var f=La.call(e.dump),p=a,w;a&&(a=e.flowLevel<0||e.flowLevel>n);var S=f===\"[object Object]\"||f===\"[object Array]\",E,B;if(S&&(E=e.duplicates.indexOf(t),B=E!==-1),(e.tag!==null&&e.tag!==\"?\"||B||e.indent!==2&&n>0)&&(i=!1),B&&e.usedDuplicates[E])e.dump=\"*ref_\"+E;else{if(S&&B&&!e.usedDuplicates[E]&&(e.usedDuplicates[E]=!0),f===\"[object Object]\")": "$e(\"!<\"+f.tag+'> 标签解析器不接受 \"'+p+'\" 样式');e.dump=a}return!0}return!1}function Je(e,n,t,a,i,l,c){e.tag=null,e.dump=t,Zi(e,t,!1)||Zi(e,t,!0);var f=La.call(e.dump),p=a,w;a&&(a=e.flowLevel<0||e.flowLevel>n);var S=f===\"[object Object]\"||f===\"[object Array]\",E,B;if(S&&(E=e.duplicates.indexOf(t),B=E!==-1),(e.tag!==null&&e.tag!==\"?\"||B||e.indent!==2&&n>0)&&(i=!1),B&&e.usedDuplicates[E])e.dump=\"*ref_\"+E;else{if(S&&B&&!e.usedDuplicates[E]&&(e.usedDuplicates[E]=!0),f===\"[object Object]\")", "$e(\"unacceptable kind of an object to dump \"+f)}e.tag!==null&&e.tag!==\"?\"&&(w=encodeURI(e.tag[0]===\"!\"?e.tag.slice(1):e.tag).replace(/!/g,\"%21\")": "$e(\"不可接受的对象类型以转储 \"+f)}e.tag!==null&&e.tag!==\"?\"&&(w=encodeURI(e.tag[0]===\"!\"?e.tag.slice(1):e.tag).replace(/!/g,\"%21\")", "\"btn:save\":\"Save\"": "\"btn:save\":\"保存\"", "\"btn:cancel\":\"Cancel\"": "\"btn:cancel\":\"取消\"", "\"btn:clear\":\"Clear\"": "\"btn:clear\":\"清除\"", "\"aria:btn:save\":\"save and close\"": "\"aria:btn:save\":\"保存并关闭\"", "\"aria:btn:cancel\":\"cancel and close\"": "\"aria:btn:cancel\":\"取消并关闭\"", "\"btn:save\":\"Save\",\"btn:cancel\":\"Cancel\"": "\"btn:save\":\"保存\",\"btn:cancel\":\"取消\"", "\"aria:btn:save\":\"save and close\",\"aria:btn:cancel\":\"cancel and close\",": "\"aria:btn:save\":\"保存并关闭\",\"aria:btn:cancel\":\"取消并关闭\",", "text:this.filterMode?`${this.filterResultCount} Results`": "text:this.filterMode?`${this.filterResultCount} 个结果`", "\"Restore default\"": "\"恢复默认\"", "\"Import an entire or partial configuration. Warning: this may override existing settings\"": "\"导入完整或部分配置。警告：此操作可能会覆盖现有设置\"", "\"Paste config here...\"": "\"请在此处粘贴配置...\"", "\"Search Style Settings...\"": "\"搜索样式设置...\""}}