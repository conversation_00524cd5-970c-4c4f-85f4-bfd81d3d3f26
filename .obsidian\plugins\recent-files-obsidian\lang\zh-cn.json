{"manifest": {"translationVersion": 1737116460269, "pluginVersion": "1.7.4"}, "description": {"original": "List files by most recently opened", "translation": "按最近打开时间列出文件"}, "dict": {"Notice(\"List length must be a positive integer\")": "Notice(\"列表长度必须为正整数\")", "name:\"Open\"": "name:\"打开\"", "text:\"Recent Files List\"": "text:\"最近文件列表\"", ".setName(\"List length\")": ".setName(\"列表长度\")", ".setDesc(\"Maximum number of filenames to keep in the list.\")": ".setDesc(\"列表中文件名的最大数量。\")", ".setTitle(\"Open in new tab\")": ".setTitle(\"新标签中打开\")", ".setTitle(\"Clear list\")": ".setTitle(\"清空列表\")", ".setTitle(\"Close\")": ".setTitle(\"关闭\")", ".append(\" for help.\")": ".append(\" 寻求帮助.\")", ".createEl(\"h2\",{text:\"Recent Files List\"})": ".createEl(\"h2\",{text:\"最近文件列表\"})", ".append(\"RegExp patterns to ignore. One pattern per line. See \"),e.append(C),e.append(\" for help.\")": ".append(\"忽略正则模式。每行一个规则。请参阅 \"),e.append(C),e.append(\" 以获取帮助.\")", ".Notice(\"Cannot find a file with that name\")": ".Notice(\"找不到该名称的文件\")", ".Notice(\"List length must be a positive integer\")": ".Notice(\"列表长度必须为正整数\")", ".setName(\"Omit bookmarked files\")": ".setName(\"忽略已收藏的文件\")", "Notice(\"Cannot find a file with that name\")": "Notice(\"找不到同名文件\")", ".log(\"Recent Files: Loading plugin v\"+this.manifest.version)": ".log(\"最近文件: 加载插件 v\"+this.manifest.version)", ".setName(\"Omitted pathname patterns\")": ".setName(\"忽略路径名模式\")", ".setName(\"Omitted frontmatter tags\")": ".setName(\"忽略属性标签\")", ".appendText(\"If this plugin adds value for you and you would like to help support continued development, please use the buttons below:\")": ".appendText(\"如果此插件对您有帮助，并且您愿意支持插件继续开发，请使用下方的按钮：\")", ".append(\"RegExp patterns to ignore. One pattern per line. See \")": ".append(\"正则表达式忽略模式。每行一个模式。见 \")", ".append(\"Frontmatter tags patterns to ignore. One pattern per line\")": ".append(\"属性标签忽略模式。每行一个模式。\")", ".setName(\"Omitted frontmatter-tag patterns\")": ".setName(\"省略的属性标记模式\")", ".append(\"Frontmatter-tag patterns to ignore. One pattern per line.\")": ".append(\"属性标签忽略模式。每行一个模式。\")", ".error(\"Recent Files: Invalid regex pattern: \"+i)": ".error(\"Recent Files插件: 正则表达式模式无效: \"+i)", ".error(\"Recent Files: Invalid regex pattern: \"+g)": ".error(\"Recent Files插件: 正则表达式模式无效: \"+g)", ".setName(\"Update list when file is:\")": ".setName(\"当文件处于以下状态时更新列表\")", ".addOption(\"file-edit\",\"Changed\")": ".addOption(\"file-edit\",\"更改\")", ".addOption(\"file-open\",\"Opened\")": ".addOption(\"file-open\",\"打开\")"}}