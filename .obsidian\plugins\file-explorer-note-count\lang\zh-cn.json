{"manifest": {"translationVersion": 1731915556413, "pluginVersion": "1.2.3"}, "description": {"original": "The plugin helps you to see the number of notes under each folder within the file explorer.", "translation": "该插件可以帮助您在文件资源管理器中查看每个文件夹下的笔记数量。"}, "dict": {".log('file-explorer not found, retrying...')": ".log('未找到文件资源管理器，正在重试...')", ".log('loading FileExplorerNoteCount')": ".log('正在加载 FileExplorerNoteCount')", ".log('unloading FileExplorerNoteCount')": ".log('正在卸载 FileExplorerNoteCount')", ".error('failed to get file-explorer')": ".error('获取文件资源管理器失败')", ".error('fileExplorer missing')": ".error('缺少文件资源管理器')", ".error('file-explorer not found')": ".error('未找到文件资源管理器')", "text: 'File Explorer Note Count Settings'": "text: '文件资源管理器笔记数量设置'", ".setName('Show All Numbers')": ".setName('显示所有数字')", ".setName('Add Root Folder')": ".setName('添加根文件夹')", ".setName('Show Only Markdown Notes')": ".setName('仅显示 Markdown 笔记')", ".setName('Filter List')": ".setName('过滤列表')", ".setName('Enable Blacklist')": ".setName('启用黑名单')", ".setDesc('Turn on this option if you want to see the number of notes even after you expand the collapsed folders')": ".setDesc('开启此选项后，即使展开已折叠的文件夹也能看到笔记数量')", ".setDesc('By default, there is no root folder provided by Obsidian. It is moved to drop-down menu to switch between vaults. ' +\n            'Enable this option if you want to see root folder and its count in the file explorer')": ".setDesc('默认情况下，Obsidian 不提供根文件夹，它被移动到下拉菜单用于切换保管库。' +\n            '启用此选项可在文件资源管理器中查看根文件夹及其数量')", ".setDesc('Turn off this option to choose file that should be counted')": ".setDesc('关闭此选项以选择需要计数的文件')", ".setDesc('Turn on this option to use Filter List to exclude files')": ".setDesc('开启此选项以使用过滤列表排除文件')", ".setPlaceholder('Leave it empty to count all types of files')": ".setPlaceholder('留空以计数所有类型的文件')", ".appendText('Extension list to include/exclude file during counting')": ".appendText('计数时包含/排除文件的扩展名列表')", ".appendText('Separated by comma')": ".appendText('用逗号分隔')"}}