/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => OpenInNewTabPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");

// node_modules/monkey-around/mjs/index.js
function around(obj, factories) {
  const removers = Object.keys(factories).map((key) => around1(obj, key, factories[key]));
  return removers.length === 1 ? removers[0] : function() {
    removers.forEach((r) => r());
  };
}
function around1(obj, method, createWrapper) {
  const original = obj[method], hadOwn = obj.hasOwnProperty(method);
  let current = createWrapper(original);
  if (original)
    Object.setPrototypeOf(current, original);
  Object.setPrototypeOf(wrapper, current);
  obj[method] = wrapper;
  return remove;
  function wrapper(...args) {
    if (current === original && obj[method] === wrapper)
      remove();
    return current.apply(this, args);
  }
  function remove() {
    if (obj[method] === wrapper) {
      if (hadOwn)
        obj[method] = original;
      else
        delete obj[method];
    }
    if (current === original)
      return;
    current = original;
    Object.setPrototypeOf(wrapper, original || Function);
  }
}

// main.ts
var OpenInNewTabPlugin = class extends import_obsidian.Plugin {
  async onload() {
    this.monkeyPatchOpenLinkText();
    this.registerDomEvent(document, "click", this.generateClickHandler(this.app), {
      capture: true
    });
  }
  onunload() {
    this.uninstallMonkeyPatch && this.uninstallMonkeyPatch();
  }
  monkeyPatchOpenLinkText() {
    this.uninstallMonkeyPatch = around(import_obsidian.Workspace.prototype, {
      openLinkText(oldOpenLinkText) {
        return async function(linkText, sourcePath, newLeaf, openViewState) {
          var _a;
          const fileName = (_a = linkText.split("#")) == null ? void 0 : _a[0];
          const isSameFile = fileName === "" || `${fileName}.md` === sourcePath;
          let fileAlreadyOpen = false;
          if (!isSameFile) {
            this.app.workspace.iterateAllLeaves((leaf) => {
              var _a2, _b, _c, _d;
              const viewState = leaf.getViewState();
              const matchesMarkdownFile = viewState.type === "markdown" && ((_b = (_a2 = viewState.state) == null ? void 0 : _a2.file) == null ? void 0 : _b.endsWith(`${fileName}.md`));
              const matchesNonMarkdownFile = viewState.type !== "markdown" && ((_d = (_c = viewState.state) == null ? void 0 : _c.file) == null ? void 0 : _d.endsWith(fileName));
              if (matchesMarkdownFile || matchesNonMarkdownFile) {
                this.app.workspace.setActiveLeaf(leaf);
                fileAlreadyOpen = true;
              }
            });
          }
          let openNewLeaf = true;
          if (isSameFile) {
            openNewLeaf = newLeaf || false;
          } else if (fileAlreadyOpen) {
            openNewLeaf = false;
          }
          oldOpenLinkText && oldOpenLinkText.apply(this, [
            linkText,
            sourcePath,
            openNewLeaf,
            openViewState
          ]);
        };
      }
    });
  }
  generateClickHandler(appInstance) {
    return function(event) {
      var _a, _b;
      const target = event.target;
      const isNavFile = ((_a = target == null ? void 0 : target.classList) == null ? void 0 : _a.contains("nav-file-title")) || ((_b = target == null ? void 0 : target.classList) == null ? void 0 : _b.contains("nav-file-title-content"));
      const titleEl = target == null ? void 0 : target.closest(".nav-file-title");
      const pureClick = !event.shiftKey && !event.ctrlKey && !event.metaKey && !event.shiftKey && !event.altKey;
      if (isNavFile && titleEl && pureClick) {
        const path = titleEl.getAttribute("data-path");
        if (path) {
          let result = false;
          appInstance.workspace.iterateAllLeaves((leaf) => {
            var _a2;
            const viewState = leaf.getViewState();
            if (((_a2 = viewState.state) == null ? void 0 : _a2.file) === path) {
              appInstance.workspace.setActiveLeaf(leaf);
              result = true;
            }
          });
          const emptyLeaves = appInstance.workspace.getLeavesOfType("empty");
          if (emptyLeaves.length > 0) {
            appInstance.workspace.setActiveLeaf(emptyLeaves[0]);
            return;
          }
          if (!result) {
            event.stopPropagation();
            appInstance.workspace.openLinkText(path, path, true);
          }
        }
      }
    };
  }
};
